import os

MIDDLEWARE = [
    "izberg_correlation_id.integrations.django.middleware.CorrelationIdMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "drf_izberg.middlewares.DRFUrlConfMiddleware",
    "ims.api.middleware.ErrorHandlingMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.gzip.GZipMiddleware",
    "iprestrict.middleware.IPRestrictMiddleware",
    "lib.middleware.rate_limiting.RateLimitMiddleware",
    "mp_utils.middlewares.session.SessionMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "lib.middleware.CSPMiddleware",
]

MAINTENANCE_MIDDLEWARE_ENABLED = os.environ.get(
    "MAINTENANCE_MIDDLEWARE_ENABLED", "0"
).lower() in ("1", "true")
if MAINTENANCE_MIDDLEWARE_ENABLED:
    MIDDLEWARE += [
        "ims.api.middleware.IzbergMaintenanceMiddleware",
    ]

MIDDLEWARE += [
    "ims.api.middleware.IzbergAuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django_otp.middleware.OTPMiddleware",
    "mp_utils.middlewares.two_factor.EnforceTwoFactorStaff",
    # Localization
    "django.middleware.locale.LocaleMiddleware",
    "ims.api.middleware.IzbergResponseHeaderMiddleware",
    "lib.middleware.VersioningMiddleware",
]

SQL_COMMENT_ENABLED = os.environ.get("SQL_COMMENT_ENABLED", "1") == "1"
if SQL_COMMENT_ENABLED:
    insert_index = MIDDLEWARE.index("django.middleware.gzip.GZipMiddleware")  # noqa
    MIDDLEWARE.insert(  # noqa
        insert_index,
        "izberg_correlation_id.integrations.django.middleware.SqlCommentMiddleware",
    )
