# -*- coding: utf-8 -*-
import logging
from copy import copy

from django.apps import apps
from django.conf import settings
from django.contrib.contenttypes.models import ContentType
from django.core.cache import cache
from django.core.exceptions import ImproperlyConfigured
from django.db import models
from django.db.models.query import QuerySet
from guardian.models import UserObjectPermission
from guardian.shortcuts import get_objects_for_user
from ims.api.authorization.common import IzbergCommonAuthorization
from redis import OutOfMemoryError
from tastypie.exceptions import BadRequest, Unauthorized

logger = logging.getLogger(__name__)

PERMISSION_CACHE_DURATION = 5 * 60


def user_has_perm(user, obj, permission, force_recache=False):
    """
    Optimized cached permission checker.
    NB:
    - does not check the user groups
    - is_active/is_superuser are check each time (not cached)
    """
    if user and not user.is_active:
        return False
    elif user and user.is_superuser:
        return True
    ctype = ContentType.objects.get_for_model(obj)
    perm_codename = permission.split(".")[-1]
    cache_key = "user:{uid}:has_perm:{perm}:obj:{ctype}|{obj_id}".format(
        uid=user.id, perm=perm_codename, ctype=ctype.id, obj_id=obj.id
    )
    cached_result = cache.get(cache_key, None) if not force_recache else None
    if cached_result is not None:
        return bool(cached_result)
    result = UserObjectPermission.objects.filter(
        user=user,
        object_pk=obj.id,
        content_type=ctype,
        permission__codename=perm_codename,
        permission__content_type=ctype,
    ).exists()
    try:
        cache.set(cache_key, result, PERMISSION_CACHE_DURATION)
    except OutOfMemoryError as e:
        logger.error(
            "Out of memory error while trying to set {} cache key: {}".format(
                cache_key, e
            )
        )
    return result


class AllBlockedAuthorization(IzbergCommonAuthorization):
    def read_list(self, object_list, bundle):
        logger.warning(
            "[%s] User %s tried to access the non readable list resource"
            % (self.__class__.__name__, bundle.request.user.id)
        )
        return object_list.none()

    def create_list(self, object_list, bundle):
        logger.warning(
            "[%s] User %s tried to create the non creatable list resource"
            % (self.__class__.__name__, bundle.request.user.id)
        )
        return object_list.none()

    def update_list(self, object_list, bundle):
        logger.warning(
            "[%s] User %s tried to update the non updatable list resource"
            % (self.__class__.__name__, bundle.request.user.id)
        )
        return object_list.none()

    def delete_list(self, object_list, bundle):
        logger.warning(
            "[%s] User %s tried to delete the non deletable list resource"
            % (self.__class__.__name__, bundle.request.user.id)
        )
        return object_list.none()

    def read_detail(self, object_list, bundle):
        logger.warning(
            "[%s] User %s tried to access the non readable detail "
            "resource %s"
            % (self.__class__.__name__, bundle.request.user.id, bundle.obj.id)
        )
        return Unauthorized("You are not allowed to access that resource.")

    def create_detail(self, object_list, bundle):
        logger.warning(
            "[%s] User %s tried to create the non creatable detail "
            "resource %s"
            % (self.__class__.__name__, bundle.request.user.id, bundle.obj.id)
        )
        raise Unauthorized("You are not allowed to access that resource.")

    def update_detail(self, object_list, bundle):
        logger.warning(
            "[%s] User %s tried to update the non updatable detail "
            "resource %s"
            % (self.__class__.__name__, bundle.request.user.id, bundle.obj.id)
        )
        raise Unauthorized("You are not allowed to access that resource.")

    def delete_detail(self, object_list, bundle):
        logger.warning(
            "[%s] User %s tried to delete the non deletable detail "
            "resource %s"
            % (self.__class__.__name__, bundle.request.user.id, bundle.obj.id)
        )
        raise Unauthorized("You are not allowed to access that resource.")


class LegacyBaseAuthorization(AllBlockedAuthorization):
    authorized_entities = {
        "read_list": [],
        "read_detail": [],
        "update_list": [],
        "update_detail": [],
        "create_list": [],
        "create_detail": [],
        "delete_list": [],
        "delete_detail": [],
    }

    allow_null_value = {}

    """
    New way to declare who can do CRUD actions on resources.
    You just need to fill the dict authorized_entities with lists
    of the following entities:
    - everybody: Anonymous or authenticated user
    - anonymous: Anonymous user
    - authenticated_user: Authenticated user
    - authenticated_application_user: Any user authenticated with an application token
    - application_through_merchant: Access resources owned by application with a merchant token + an app id as get param
    - anonymous_application_user: Anonymous application user
    - object_user: User linked to the object (deprecated)
    - object_session: User session linked to the object
    - application_user: Access to resources owned by the user logged within the application (from token)
    - application: User having rights on the application
    - merchant: User having rights on the merchant
    - staff: Izberg staff

    NB : Order matters, especially for a actions on _list, because the
    authorization will check the result for each entity and will stop
    if it finds something. For instance, let's say a resource has :
        authorized_entities = { "read_list":['staff', 'object_user'] }
    If a staff member does a read_list, the authentication will check the
    entity 'staff' and as the user is staff, will return all the objects.
    Now, let's say the resource has:
        authorized_entities = { "read_list":['object_user', 'staff'] }
    If a staff member does a read_list, the authentication will check the
    entity 'object_user' first, so if the user owns 3 of the objects, the
    authorization will return these 3 and wont return all the objects
    (because it didnt get the chance to check the 'staff' entity).

    ADVANCED MODE : Instead of a list of entities, you can define
    an OrderDict containing the entity as key and a filter dict
    as value. Ex:
        authorized_entities = {
            "read_list":OrderedDict([
                ("authenticated_user",{}),
                ("anonymous",{"status": "visible"}),
            ]),
            "read_detail":OrderedDict([
                ("authenticated_user",{}),
                ("anonymous",{"status": "visible"}),
            ]),
        }
    In this example, an authenticated user can access all of the
    objects (empty filter dict) whereas an anonymous would only access
    the objects with status attibute set to "visible".


    If this behavior is problematic or not sufficient, you still can
    overwrite the desired function(s). Ex:

        def read_list(self, object_list, bundle):
            if cache.get("TEMPORARY_BLOCK_READ_RESOURCES"):
                return object_list.none() # returning empty results (Unauthorized)
            else:
                # return default authorized_entities behavior
                return super(MerchantResourceAuthorization, self).read_list(object_list, bundle)

        def read_detail(self, object_list, bundle):
            if cache.get("TEMPORARY_BLOCK_READ_RESOURCES"):
                return False # Unauthorized
            else:
                # return default authorized_entities behavior
                return super(MerchantResourceAuthorization, self).read_detail(object_list, bundle)
    """

    """
    Default paths to objects used to define in which entity(ies) the user is.
    Ex: - the model ProductOffer has a field merchant so path_to_merchant = "merchant"
        - the model Product has a (virtual) field offers so path_to_merchant = "offers.merchant"

    Now possible to define a list of paths to objects in 'paths_to_[entity]'.
    NB: if the list 'paths_to_[entity]' is define, the 'path_to_[entity]' will be ignored

    """
    path_to_user = "user"
    path_to_user_id = "user_id"
    path_to_session = "session"
    path_to_merchant = "merchant"
    path_to_application = "application"
    paths_to_user = []
    paths_to_user_id = []
    paths_to_session = []
    paths_to_merchant = []
    paths_to_application = []

    """
    Default permissions.
    """
    permissions = ["stores.change_merchant"]
    application_permissions = ["ice_applications.change_application"]

    @property
    def logger(self):
        return logging.getLogger(self.__class__.__name__)

    def get_application_model(self):
        if not hasattr(self, "_application_model"):
            APPLICATION_MODEL_PATH = getattr(
                settings, "APPLICATION_MODEL_PATH", "ice_applications.Application"
            )
            app_label, model_name = APPLICATION_MODEL_PATH.rsplit(".", 1)
            model = apps.get_model(app_label, model_name)
            if not model:
                raise ImproperlyConfigured(
                    "APPLICATION_MODEL_PATH=%s is not valid" % APPLICATION_MODEL_PATH
                )
            self._application_model = model
        return self._application_model

    def get_merchant_model(self):
        if not hasattr(self, "_merchant_model"):
            MERCHANT_MODEL_PATH = getattr(
                settings, "MERCHANT_MODEL_PATH", "stores.Merchant"
            )
            app_label, model_name = MERCHANT_MODEL_PATH.rsplit(".", 1)
            model = apps.get_model(app_label, model_name)
            if not model:
                raise ImproperlyConfigured(
                    "MERCHANT_MODEL_PATH=%s is not valid" % MERCHANT_MODEL_PATH
                )
            self._merchant_model = model
        return self._merchant_model

    """
    Auth Helpers
    """

    def get_users_authorized_merchants(self, bundle, permissions=None):
        """
        Return all the merchants the user can control
        """
        permissions = permissions or self.permissions
        return get_objects_for_user(
            bundle.request.user, permissions, self.get_merchant_model(), any_perm=True
        )

    def is_authenticated_application(self, bundle):
        """
        auth is application-based if there is an application in request
        """
        res = (
            bundle.request
            and hasattr(bundle.request, "application")
            and bundle.request.application
        )
        return bool(res)

    def is_authenticated_staff(self, bundle):
        """
        staff if not application-based auth and user is staff
        """
        res = (
            not self.is_authenticated_application(bundle)
            and bundle.request
            and hasattr(bundle.request, "user")
            and bundle.request.user.is_staff
        )
        return res

    def is_authenticated_user(self, bundle):
        """
        authenticated user if not application-based auth and
        user is not anonymous
        """
        res = (
            not self.is_authenticated_application(bundle)
            and bundle.request
            and hasattr(bundle.request, "user")
            and bool(bundle.request.user.is_authenticated)
        )
        return res

    def is_authenticated_application_user(self, bundle):
        """
        authenticated app user if application-based auth
        and user is not anonymous
        """
        res = (
            self.is_authenticated_application(bundle)
            and bundle.request
            and hasattr(bundle.request, "user")
            and bool(bundle.request.user.is_authenticated)
        )
        return res

    def is_application_staff_user(self, bundle):
        res = bool(
            self.is_authenticated_application_user(bundle)
            and bundle.request.application.is_application_staff(bundle.request.user)
        )
        return res

    def is_anonymous_user(self, bundle):
        """
        anonymous user if not application-based auth and user is anonymous
        """
        res = (
            not self.is_authenticated_application(bundle)
            and bundle.request
            and hasattr(bundle.request, "user")
            and not bool(bundle.request.user.is_authenticated)
        )
        return res

    def is_anonymous_application_user(self, request):
        """
        anonymous app user if application-based auth and user is anonymous
        """
        is_anonymous = (
            not hasattr(request, "user")
            or not request.user
            or not bool(request.user.is_authenticated)
        )
        res = is_anonymous and self.is_authenticated_application(request)
        return res

    def get_authenticated_entity(self, bundle):
        if self.is_authenticated_application(bundle):  # application
            return bundle.request.application
        elif self.is_authenticated_user(bundle):  # user or staff
            return bundle.request.user
        return None  # anonymous

    def get_filters_for_entity(self, entity, resource_action):
        """
        Check if authorized_entities of the resource_action is a dict
        containing filters to apply and return them
        """

        authorized_entities = self.authorized_entities.get(resource_action, [])
        simple_perms = not authorized_entities or isinstance(
            authorized_entities, (tuple, list)
        )
        if simple_perms:
            return {}, False
        filters = authorized_entities.get(entity)
        #  Check if it is an "and" Q request
        operation_type = "or"
        possibilities = ["or", "and"]
        if isinstance(filters, (list, tuple)) and len(filters) >= 2:
            # Check the second element of the iterable
            operation_type = filters[1].lower() in possibilities and filters[1]

        return copy(filters or {}), operation_type

    def get_entities_to_check(self, bundle, limited_to=None):
        """
        Smartly decide which entities we really need to check.
        Ex: if not authenticated user, no need to check for "object_user", etc...
        """
        entities_to_check = []

        if limited_to == []:  # Empty list is different than None
            return []
        if ("staff" in limited_to or not limited_to) and self.is_authenticated_staff(
            bundle
        ):
            entities_to_check.append("staff")
        if ("application" in limited_to or not limited_to) and (
            self.is_authenticated_application_user(bundle)
            or self.is_authenticated_user(bundle)
        ):
            entities_to_check.append("application")
        if ("application_user" in limited_to or not limited_to) and (
            self.is_authenticated_application_user(bundle)
        ):
            entities_to_check.append("application_user")
        if (
            "object_user" in limited_to or not limited_to
        ) and self.is_authenticated_user(bundle):
            entities_to_check.append("object_user")
        if "object_session" in limited_to or not limited_to:
            entities_to_check.append("object_session")
        if (
            "authenticated_user" in limited_to or not limited_to
        ) and self.is_authenticated_user(bundle):
            entities_to_check.append("authenticated_user")
        if (
            "authenticated_application_user" in limited_to or not limited_to
        ) and self.is_authenticated_application_user(bundle):
            entities_to_check.append("authenticated_application_user")
        if (
            "anonymous_application_user" in limited_to or not limited_to
        ) and self.is_anonymous_application_user(bundle):
            entities_to_check.append("anonymous_application_user")
        if "merchant" in limited_to or not limited_to:
            entities_to_check.append("merchant")
        if "application_through_merchant" in limited_to or not limited_to:
            entities_to_check.append("application_through_merchant")
        if ("anonymous" in limited_to or not limited_to) and self.is_anonymous_user(
            bundle
        ):
            entities_to_check.append("anonymous")
        if "everybody" in limited_to or not limited_to:
            entities_to_check.append("everybody")
        if limited_to:
            return [
                entity for entity in limited_to if entity in entities_to_check
            ]  # re-ordering entities_to_check
        else:
            return entities_to_check

    def get_allowed_result_list(self, object_list, bundle, entity, resource_action):
        filters, operation_type = self.get_filters_for_entity(entity, resource_action)
        handlers = {
            "staff": "staff_list",
            "application_through_merchant": "application_through_merchant_list",
            "merchant": "merchant_list",
            "application": "application_list",
            "application_user": "application_user_list",
            "object_user": "obj_user_list",
            "authenticated_user": "authenticated_user_list",
            "authenticated_application_user": "authenticated_application_user_list",
            "anonymous_application_user": "anonymous_application_user_list",
            "anonymous": "anonymous_user_list",
            "everybody": "everybody_list",
            "object_session": "obj_session_list",
        }
        if entity in handlers:
            handler = getattr(self, handlers[entity])
            result = handler(
                object_list, bundle, filters=filters, operation_type=operation_type
            )
        else:
            self.logger.error("Unhandled entity %s in get_allowed_result_list" % entity)
            result = object_list.none()

        return result

    def get_obj_pk(self, bundle):
        """Return the object primary key.

        Look into data dict if obj pk is not defined because of the
        caching mecanism that consists of caching the bundle.data
        and avoid fetching bundle.obj from DB
        """
        return bundle.obj.pk or bundle.data.get("pk") or bundle.data.get("id")

    def get_allowed_result_detail(self, object_list, bundle, entity, resource_action):
        object_list = object_list.filter(pk=self.get_obj_pk(bundle))
        filters, operation_type = self.get_filters_for_entity(entity, resource_action)

        allow_null_value = self.allow_null_value.get(resource_action, False)
        handlers = {
            "staff": "staff_detail",
            "application_through_merchant": "application_through_merchant_detail",
            "merchant": "merchant_detail",
            "application": "application_detail",
            "application_user": "application_user_detail",
            "object_user": "obj_user_detail",
            "authenticated_user": "authenticated_user_detail",
            "authenticated_application_user": "authenticated_application_user_detail",
            "anonymous_application_user": "anonymous_application_user_detail",
            "anonymous": "anonymous_user_detail",
            "everybody": "everybody_detail",
            "object_session": "obj_session_detail",
        }
        if entity in handlers:
            handler = getattr(self, handlers[entity])
            result = handler(
                object_list,
                bundle,
                filters=filters,
                allow_null_value=allow_null_value,
                operation_type=operation_type,
                resource_action=resource_action,
            )
        else:
            logger.error("Unhandlend entity %s in get_allowed_result_detail" % entity)
            result = False

        if settings.DEBUG:
            logger.debug(
                "%s.get_allowed_result_detail(authenticated_entity=%s, "
                "check_entity=%s, resource_action=%s, bundle.obj=%s, "
                "filters=%s) >> %s"
                % (
                    self.__class__.__name__,
                    self.get_authenticated_entity(bundle),
                    entity,
                    resource_action,
                    bundle.obj,
                    filters,
                    result,
                )
            )
        return result

    """
    # Generic crud list/detail authorizations based on self.authorized_entities
    """

    def read_list(self, object_list, bundle):
        return self._authorized_entity(object_list, bundle, "read_list", as_list=True)

    def read_detail(self, object_list, bundle):
        return self._authorized_entity(object_list, bundle, "read_detail")

    def update_list(self, object_list, bundle):
        return self._authorized_entity(object_list, bundle, "update_list", as_list=True)

    def update_detail(self, object_list, bundle):
        return self._authorized_entity(object_list, bundle, "update_detail")

    def create_list(self, object_list, bundle):
        return self._authorized_entity(object_list, bundle, "create_list", as_list=True)

    def create_detail(self, object_list, bundle):
        return self._authorized_entity(object_list, bundle, "create_detail")

    def delete_list(self, object_list, bundle):
        return self._authorized_entity(object_list, bundle, "delete_list", as_list=True)

    def delete_detail(self, object_list, bundle):
        return self._authorized_entity(object_list, bundle, "delete_detail")

    def authorized_custom_action(
        self, object_list, bundle, custom_action, as_list=False
    ):
        return self._authorized_entity(
            object_list, bundle, custom_action, as_list=as_list
        )

    """
    # private functions
    """

    def _get_authorized_entities_to_check(self, object_list, bundle, requested_action):
        authorized_entities = self._get_entities(requested_action)
        entities_to_check = self.get_entities_to_check(
            bundle, limited_to=authorized_entities
        )

        logger.debug(
            "[{class_name}._authorized_entity.{action} - ]: "
            "authorized_entities={authorized_entities} , "
            "entities_to_check={entities_to_check}".format(
                class_name=self.__class__.__name__,
                action=requested_action,
                authorized_entities=authorized_entities,
                entities_to_check=entities_to_check,
            )
        )
        entities_to_check = [  # filter out unauthorized entities_to_check
            authorized_entity
            for authorized_entity in authorized_entities
            if authorized_entity in entities_to_check
        ]
        return entities_to_check

    def _authorized_entity(self, object_list, bundle, requested_action, as_list=False):
        self.matched_at_least_one_auth = False
        default_value = object_list.none() if as_list else False
        entities_to_check = self._get_authorized_entities_to_check(
            object_list, bundle, requested_action
        )
        if not entities_to_check:
            return default_value

        for entity in entities_to_check:
            method_name = (
                "get_allowed_result_list" if as_list else "get_allowed_result_detail"
            )
            result = getattr(self, method_name)(
                object_list, bundle, entity, requested_action
            )
            result_valid = (
                as_list and isinstance(result, QuerySet) and result.exists()
            ) or (not as_list and result)
            if result_valid:
                bundle.request.legacy_auth_type = entity
                return result
        logger.debug(
            "[{class_name}._authorized_entity.{action}] No result for "
            "entities_to_check={entities_to_check}, returning False".format(
                class_name=self.__class__.__name__,
                action=requested_action,
                entities_to_check=entities_to_check,
            )
        )
        return default_value

    def _path_entity_to_filter_key(self, path_entity):
        """
        Transform path_to_entity into filter key.

        - Transform . to __
        - Remove `_set` suffix added by Django
        >>> LegacyBaseAuthorization()._path_entity_to_filter_key("merchant.user")
        'merchant__user'
        >>> LegacyBaseAuthorization()._path_entity_to_filter_key("company.application_set")
        'company__application'
        """
        if path_entity:
            filter_key = path_entity.replace(".", "__")
            if filter_key.endswith("_set"):
                filter_key = filter_key[:-4]
        else:
            filter_key = "id"  # directly the entity model
        return filter_key

    def _get_entities(self, entity_name):
        entities = self.authorized_entities.get(entity_name, [])
        if not entities and entity_name not in self.authorized_entities:
            logger.info('"%s" entity is not defined, defaulting to []' % (entity_name))
        return entities

    def _boolean_operate_q(self, q1, q2, operator):
        return q1 | q2 if operator == "or" else q1 & q2

    def _apply_detail_filters(self, object_list, filters, has_access, operation_type):
        if isinstance(filters, models.Q):
            should_filter = not has_access or (has_access and operation_type == "and")
            if should_filter:
                qs = object_list.filter(filters)
                has_access = qs.exists()
        elif isinstance(filters, dict):
            if has_access:
                qs = object_list.filter(**filters)
                has_access = qs.exists()
        else:
            raise NotImplementedError(
                "filters of type {} are not implemented".format(type(filters))
            )
        logger.debug(
            "[%s] has_access after filters %s >> %s"
            % (self.__class__.__name__, filters, has_access)
        )
        return has_access

    """
    # ANONYMOUS USER AUTHS
    """

    def anonymous_user_list(
        self, object_list, bundle, filters=None, operation_type="or", **kwargs
    ):
        """
        Full access to logged users
        """
        filters = filters or {}
        if self.is_anonymous_user(bundle):
            self.matched_at_least_one_auth = True
            try:
                logger.debug(
                    "[%s] Anonymous user has access to %s"
                    % (self.__class__.__name__, object_list.__dict__["model"])
                )
            except Exception:
                pass
            return object_list.filter(**filters)
        else:
            return object_list.none()

    def anonymous_user_detail(
        self, object_list, bundle, filters=None, operation_type="or", **kwargs
    ):
        filters = filters or {}
        if self.is_anonymous_user(bundle):
            has_perm = True
            object_id = (bundle.obj and bundle.obj.id) or bundle.data.get("id", None)
            if object_id is not None and filters:
                has_perm = (
                    has_perm
                    and bundle.obj.__class__.objects.filter(
                        id=object_id, **filters
                    ).exists()
                )
            logger.debug(
                "[%s] Anonymous access to %s %s = %s"
                % (
                    self.__class__.__name__,
                    bundle.obj.__class__.__name__,
                    object_id,
                    has_perm,
                )
            )
            return has_perm
        else:
            return False

    def anonymous_application_user_list(
        self, object_list, bundle, filters=None, operation_type="or", **kwargs
    ):
        filters = filters or {}
        if not self.is_anonymous_application_user(bundle):
            return object_list.none()
        self.matched_at_least_one_auth = True
        paths_to_application = self.paths_to_application or [self.path_to_application]
        for path_to_application in paths_to_application:
            current_filters = copy(filters)
            try:
                logger.debug(
                    "[%s] Anonymous App user has access to %s"
                    % (self.__class__.__name__, object_list.__dict__["model"])
                )
            except Exception:
                pass
            app = self.get_authenticated_entity(bundle)
            authorized_application_id = app.id
            self.logger.debug(
                "authorized_application_id=%s, path_to_application=%s"
                % (authorized_application_id, path_to_application)
            )
            if path_to_application:
                app_filter_key = path_to_application.replace(".", "__") + "_id"
            else:
                app_filter_key = "id"  # directly the app model

            if isinstance(current_filters, models.Q):  # Q filter
                results = object_list.filter(
                    self._boolean_operate_q(
                        current_filters,
                        models.Q(**{app_filter_key: authorized_application_id}),
                        operation_type,
                    )
                )
            else:
                current_filters[app_filter_key] = authorized_application_id
                results = object_list.filter(**current_filters)

            if len(paths_to_application) == 1 or results.exists():
                return results

        return object_list.none()

    def anonymous_application_user_detail(self, *args, **kwargs):
        return self.anonymous_application_user_list(*args, **kwargs).exists()

    """
    # EVERYBODY AUTHS
    """

    def everybody_list(
        self, object_list, bundle, filters=None, operation_type="or", **kwargs
    ):
        """
        Full access to everybody (with possible filters)
        """
        filters = filters or {}
        if isinstance(filters, models.Q):
            raise NotImplementedError(
                r"Q object for everybody_list makes no sense. "
                r"Please use {\} instead"
            )
        self.matched_at_least_one_auth = True
        return object_list.filter(**filters)

    def everybody_detail(self, *args, **kwargs):
        """
        Full access to everybody (with possible filters)
        """
        return self.everybody_list(*args, **kwargs).exists()

    """
    # LOGGED USER AUTHS
    """

    def authenticated_user_list(
        self, object_list, bundle, filters=None, operation_type="or", **kwargs
    ):
        """
        Full access to logged users
        """
        filters = filters or {}
        if self.is_authenticated_user(bundle):
            self.matched_at_least_one_auth = True
            logger.debug(
                "[%s] Authenticated user %s has full access to %s"
                % (
                    self.__class__.__name__,
                    bundle.request.user.id,
                    object_list.__dict__["model"],
                )
            )
            return object_list.filter(**filters)
        else:
            return object_list.none()

    def authenticated_user_detail(
        self, object_list, bundle, filters=None, operation_type="or", **kwargs
    ):
        if not self.is_authenticated_user(bundle):
            return False
        filters = filters or {}
        has_perm = True
        object_id = (bundle.obj and bundle.obj.id) or bundle.data.get("id", None)
        if object_id is not None and filters:
            has_perm = (
                has_perm
                and bundle.obj.__class__.objects.filter(
                    id=object_id, **filters
                ).exists()
            )
        logger.debug(
            "[%s] Authenticated user %s has access to %s %s = %s"
            % (
                self.__class__.__name__,
                bundle.request.user.id,
                bundle.obj.__class__.__name__,
                bundle.obj.id,
                has_perm,
            )
        )
        return has_perm

    def authenticated_application_user_list(
        self, object_list, bundle, filters=None, operation_type="or", **kwargs
    ):
        """
        Full access to logged users
        """
        filters = filters or {}
        if not self.is_authenticated_application_user(bundle):
            return object_list.none()
        self.matched_at_least_one_auth = True
        paths_to_application = self.paths_to_application or [self.path_to_application]
        # If path_to_application == "*" application filtering for
        # authenticated_application_user auth is disabled.
        # Usefull for shared read only resources without an application context
        if self.path_to_application == "*":
            if isinstance(filters, models.Q):
                return object_list.filter(filters)
            else:
                return object_list.filter(**filters)
        for path_to_application in paths_to_application:
            current_filters = copy(filters)
            authorized_application_id = bundle.request.application.id
            filter_key = self._path_entity_to_filter_key(path_to_application)

            if isinstance(current_filters, models.Q):
                current_filters = self._boolean_operate_q(
                    current_filters,
                    models.Q(**{filter_key: authorized_application_id}),
                    operation_type,
                )
            else:
                current_filters[filter_key] = authorized_application_id

            if isinstance(current_filters, models.Q):
                results = object_list.filter(current_filters)
            else:
                results = object_list.filter(**current_filters)

            if len(paths_to_application) == 1 or results.exists():
                return results

        return object_list.none()

    def authenticated_application_user_detail(
        self, object_list, bundle, filters=None, operation_type="or", **kwargs
    ):
        object_id = (bundle.obj and bundle.obj.id) or bundle.data.get("id", None)
        has_perm = self.authenticated_application_user_list(
            object_list.filter(id=object_id),
            bundle,
            filters=filters,
            operation_type=operation_type,
            **kwargs,
        ).exists()
        logger.debug(
            "[%s] Authenticated user %s has access to %s %s = %s",
            self.__class__.__name__,
            bundle.request.user.id,
            bundle.obj.__class__.__name__,
            bundle.obj.id,
            has_perm,
        )
        return has_perm

    """
    # STAFF AUTHS
    """

    def staff_list(self, object_list, bundle, operation_type="or", **kwargs):
        """
        Full access to staff members
        """
        if self.is_authenticated_staff(bundle):
            self.matched_at_least_one_auth = True
            try:
                logger.debug(
                    "[%s] Admin %s has full access to %s"
                    % (
                        self.__class__.__name__,
                        bundle.request.user.id,
                        object_list.__dict__["model"],
                    )
                )
            except Exception:
                try:
                    logger.debug(
                        "[%s] Admin %s has full access to %s"
                        % (
                            self.__class__.__name__,
                            bundle.request.user.id,
                            object_list.__dict__["_document"],
                        )
                    )
                except Exception:
                    pass
            return object_list.all()
        return object_list.none()

    def staff_detail(self, object_list, bundle, operation_type="or", **kwargs):
        """
        Full access to staff members
        """
        if self.is_authenticated_staff(bundle):
            return True
        return False

    """
    # USER AUTHS
    """

    def obj_user_list(
        self,
        object_list,
        bundle,
        path_to_user="",
        filters=None,
        operation_type="or",
        **kwargs,
    ):
        """
        User request has access to resources (field "path_to_user" ) matching their id
        Ex: path_to_user = "order.cart.user"
        """
        filters = filters or {}
        paths_to_user = self.paths_to_user or [path_to_user or self.path_to_user]

        if not self.is_authenticated_user(bundle):
            return object_list.none()
        self.matched_at_least_one_auth = True
        for path_to_user in paths_to_user:
            current_filters = copy(filters)

            authorized_user_id = bundle.request.user.id
            logger.debug(
                "authorized_user_id=%s, path_to_user=%s"
                % (authorized_user_id, path_to_user)
            )

            filter_key = self._path_entity_to_filter_key(path_to_user)

            if isinstance(current_filters, models.Q):  # Q filter
                current_filters = self._boolean_operate_q(
                    current_filters,
                    models.Q(**{filter_key: authorized_user_id}),
                    operation_type,
                )
            else:
                current_filters[filter_key] = authorized_user_id

            logger.debug("obj_user_list current_filters=%s" % current_filters)
            if isinstance(current_filters, models.Q):
                results = object_list.filter(current_filters)
            else:
                results = object_list.filter(**current_filters)

            if len(paths_to_user) == 1 or results.exists():
                return results

        return object_list.none()

    def application_user_list(
        self,
        object_list,
        bundle,
        path_to_user="",
        path_to_application="",
        filters=None,
        operation_type="or",
        **kwargs,
    ):
        """
        User request has access to resources (field "path_to_user" ) matching their id
        Ex: path_to_user = "order.cart.user"
        """
        filters = filters or {}
        path_to_application = path_to_application or self.path_to_application
        paths_to_user = self.paths_to_user or [path_to_user or self.path_to_user]

        if not self.is_authenticated_application_user(bundle):
            return object_list.none()
        self.matched_at_least_one_auth = True
        for path_to_user in paths_to_user:
            current_filters = copy(filters)

            authorized_user_id = bundle.request.user.id
            logger.debug(
                "authorized_user_id=%s, path_to_user=%s"
                % (authorized_user_id, path_to_user)
            )

            filter_key = self._path_entity_to_filter_key(path_to_user)

            authorized_application_id = bundle.request.application.id
            logger.debug(
                "authorized_application_id=%s, path_to_application=%s"
                % (authorized_application_id, path_to_application)
            )

            app_filter_key = self._path_entity_to_filter_key(path_to_application)

            if isinstance(current_filters, models.Q):  # Q filter
                current_filters = self._boolean_operate_q(
                    current_filters,
                    models.Q(
                        **{
                            filter_key: authorized_user_id,
                            app_filter_key: authorized_application_id,
                        }
                    ),
                    operation_type,
                )
            else:
                current_filters[filter_key] = authorized_user_id
                current_filters[app_filter_key] = authorized_application_id

            logger.debug("application_user_list current_filters=%s" % current_filters)
            if isinstance(current_filters, models.Q):
                results = object_list.filter(current_filters)
            else:
                results = object_list.filter(**current_filters)

            if len(paths_to_user) == 1 or results.exists():
                return results

        return object_list.none()

    def application_user_detail(
        self,
        object_list,
        bundle,
        path_to_user="",
        path_to_application="",
        allow_null_value=False,
        filters=None,
        operation_type="or",
        **kwargs,
    ):
        filters = filters or {}
        paths_to_application = self.paths_to_application or [
            path_to_application or self.path_to_application
        ]
        paths_to_user = self.paths_to_user or [path_to_user or self.path_to_user]
        if not self.is_authenticated_application_user(bundle):
            return False
        self._try_load_cached_object_if_needed(bundle)
        for path_to_application in paths_to_application:
            for path_to_user in paths_to_user:
                current_filters = copy(filters)
                user = self.get_user_from_object(bundle, path_to_user)
                if user is None:
                    user_ok = allow_null_value
                    logger.debug("user_ok = allow_null_value = %s" % (allow_null_value))
                else:
                    user_ok = user.id == bundle.request.user.id
                    logger.debug(
                        "user_ok = user.id(%s) == "
                        "bundle.request.user.id(%s) = %s"
                        % (user.id, bundle.request.user.id, user_ok)
                    )
                applications = self.get_applications_from_object(
                    bundle, path_to_application
                )
                if not applications:
                    application_ok = allow_null_value
                    logger.debug(
                        "application_ok = allow_null_value = %s" % (allow_null_value)
                    )
                else:
                    application_ok = bundle.request.application in applications
                    logger.debug(
                        "application_ok = %s in %s"
                        % (bundle.request.application, applications)
                    )

                has_access = user_ok and application_ok
                logger.debug(
                    "user_ok=%s, application_ok=%s, has_access=%s"
                    % (user_ok, application_ok, has_access)
                )
                object_id = (bundle.obj and bundle.obj.id) or bundle.data.get(
                    "id", None
                )
                logger.debug("obj_user_detail:object_id=%s" % object_id)
                if has_access and object_id is not None and current_filters:
                    # True if id matches and object matches the current_filters
                    has_access = (
                        has_access
                        and bundle.obj.__class__.objects.filter(
                            id=object_id, **current_filters
                        ).exists()
                    )

                if has_access:
                    return True  # Access granted

        return False

    def get_user_from_object(self, bundle, path_to_user):
        from ims.api.api_utils.path import reach_objects_from_path

        user_list = reach_objects_from_path(bundle.obj, path_to_user)
        if not user_list:
            return None
        else:
            user_list = list(set(user_list))  # we want unique users
            if len(user_list) > 1:
                raise Exception(
                    "get_user_from_object was expecting only 1 user at path %s"
                    % path_to_user
                )
            return user_list[0]

    def obj_user_detail(
        self,
        object_list,
        bundle,
        path_to_user="",
        allow_null_value=False,
        filters=None,
        operation_type="or",
        **kwargs,
    ):
        filters = filters or {}

        paths_to_user = self.paths_to_user or [path_to_user or self.path_to_user]

        if not self.is_authenticated_user(bundle):
            return False

        for path_to_user in paths_to_user:
            current_filters = copy(filters)
            self._try_load_cached_object_if_needed(bundle)
            user = self.get_user_from_object(bundle, path_to_user)
            if user:
                has_access = user.id == bundle.request.user.id
                object_id = (bundle.obj and bundle.obj.id) or bundle.data.get(
                    "id", None
                )
                logger.debug("obj_user_detail:object_id=%s" % object_id)
                if object_id is not None and current_filters:
                    has_access = (
                        has_access
                        and bundle.obj.__class__.objects.filter(
                            id=object_id, **current_filters
                        ).exists()
                    )
            else:
                has_access = allow_null_value

            if has_access:
                return True

        return False

    def obj_user_id_detail(
        self, object_list, bundle, path_to_user_id="", operation_type="or"
    ):
        path_to_user_id = path_to_user_id or self.path_to_user_id
        if self.is_authenticated_user(bundle):
            self._try_load_cached_object_if_needed(bundle)
            obj = bundle.obj
            if path_to_user_id:
                if path_to_user_id.split(".")[-1] != "user_id" and not getattr(
                    self, "allow_different_name", False
                ):
                    raise Exception(
                        "path_to_user_id must end with user (ends with %s)"
                        % path_to_user_id.split(".")[-1]
                    )
                for path in path_to_user_id.split("."):
                    obj = getattr(obj, path)
            return obj == bundle.request.user.id
        return False

    """
    # SESSION AUTHS
    """

    def obj_session_list(
        self, object_list, bundle, path_to_session="", operation_type="or", **kwargs
    ):
        """
        Request with session has access to resources (field "path_to_session" ) matching their session
        Ex: path_to_session = "session"
        """
        path_to_session = path_to_session or self.path_to_session
        if (
            bundle.request
            and hasattr(bundle.request, "session")
            and bundle.request.session.session_key
        ):
            self.matched_at_least_one_auth = True
            if path_to_session:
                if path_to_session.split(".")[-1] != "session" and not getattr(
                    self, "allow_different_name", False
                ):
                    raise Exception(
                        "path_to_session must end with session (ends with %s)"
                        % path_to_session.split(".")[-1]
                    )
                path_to_session_db = path_to_session.replace(".", "__")
                return object_list.filter(
                    **{"%s" % path_to_session_db: bundle.request.session.session_key}
                )
            else:
                return object_list.filter(pk=bundle.request.session.session_key)
        return object_list.none()

    def obj_session_detail(
        self,
        object_list,
        bundle,
        path_to_session="",
        allow_null_value=False,
        operation_type="or",
        **kwargs,
    ):
        # Generic code not so generic, and to be dropped soon during the Legacide
        # The path_to_session is only used in the cart context :
        # - LegacyCartShippingChoiceResourceAuthorization : "cart.session"
        # - LegacyCartShippingChoiceGroupResourceAuthorization : "cart.session"
        # - LegacyCartItemResourceAuthorization : "cart.session"
        # - LegacyCartResourceAuthorization : "session"
        path_to_session = path_to_session or self.path_to_session
        if (
            bundle.request
            and hasattr(bundle.request, "session")
            and bundle.request.session.session_key
        ):
            self._try_load_cached_object_if_needed(bundle)
            obj = bundle.obj
            parent = None
            if path_to_session:
                if path_to_session.split(".")[-1] != "session" and not getattr(
                    self, "allow_different_name", False
                ):
                    raise Exception(
                        "path_to_session must end with session (ends with %s)"
                        % path_to_session.split(".")[-1]
                    )
                for path in path_to_session.split("."):
                    if obj is None and allow_null_value:
                        return True
                    parent = obj
                    obj = getattr(obj, path)

            # Here obj is always a session and parent always a Cart (or None)
            # getattr(None, "user_id", None) == None :)
            cart_user_id = getattr(parent, "user_id", None)
            # Session_id authorization is authorized only when
            # no user is yet assigned to a cart (US 9744)
            return (
                obj
                and (obj.pk == bundle.request.session.session_key)
                and cart_user_id is None
            )
        else:
            return False

    """
    # USER OR STAFF
    """

    def obj_user_or_staff_list(
        self, object_list, bundle, path_to_user="", operation_type="or"
    ):
        """
        Full access to staff members + Access to resources where the specified user field matches the user requesting them
        """
        res = self.staff_list(object_list, bundle)
        if not isinstance(res, QuerySet) or not res.exists():
            res = self.obj_user_list(object_list, bundle, path_to_user)
        return res

    def obj_user_or_staff_detail(
        self,
        object_list,
        bundle,
        path_to_user="",
        allow_null_value=False,
        operation_type="or",
    ):
        if self.staff_detail(object_list, bundle):
            return True
        return self.obj_user_detail(object_list, bundle, path_to_user, allow_null_value)

    """
    # USER OR APPLICATION OR STAFF
    """

    def obj_user_or_application_or_staff_list(
        self,
        object_list,
        bundle,
        path_to_user="",
        path_to_application="",
        application_permissions=None,
        operation_type="or",
    ):
        """
        Full access to staff members + Access to resources where the specified
        user field matches the user requesting them + Access to applications
        based permissions
        """
        application_permissions = application_permissions or []
        return (
            self.staff_list(object_list, bundle)
            or self.application_list(
                object_list, bundle, path_to_application, application_permissions
            )
            or self.obj_user_list(object_list, bundle, path_to_user)
        )

    def obj_user_or_application_or_staff_detail(
        self,
        object_list,
        bundle,
        path_to_user="",
        path_to_application="",
        application_permissions=None,
        allow_null_value=False,
        operation_type="or",
    ):
        application_permissions = application_permissions or []
        return (
            self.staff_detail(object_list, bundle)
            or self.obj_user_detail(object_list, bundle, path_to_user, allow_null_value)
            or self.application_detail(
                object_list,
                bundle,
                path_to_application,
                application_permissions,
                allow_null_value,
            )
        )

    """
    # MERCHANT INVENTORY MANAGER
    """

    def staff_or_merchant_inventory_manager_list(
        self, object_list, bundle, operation_type="or"
    ):
        """
        Full acess to user who :
        - is staff
        - has the permission to manage at least 1 merchant or its inventory
        """
        has_access = (
            self.staff_detail(object_list, bundle)
            or get_objects_for_user(
                bundle.request.user,
                ["stores.manage_inventory_merchant", "stores.change_merchant"],
                self.get_merchant_model(),
                any_perm=True,
            ).exists()
        )
        if has_access:
            return object_list

        return object_list.none()

    def staff_or_merchant_inventory_manager_detail(
        self, object_list, bundle, operation_type="or"
    ):
        """
        Full acess to user who :
        - is staff
        - has the permission to manage at least 1 merchant or its inventory
        """
        return (
            self.staff_detail(object_list, bundle)
            or get_objects_for_user(
                bundle.request.user,
                ["stores.manage_inventory_merchant", "stores.change_merchant"],
                self.get_merchant_model(),
                any_perm=True,
            ).exists()
        )

    """
    # MERCHANT MANAGER
    """

    def _resolve_unique_app_from_merchants(self, user_authorized_merchants):
        """
        Try to identify a unique application from a list of
        authorized merchants for json (kibana) logging
        """
        from apps.ice_applications.models import Application  # noqa

        linked_app_ids = set(
            user_authorized_merchants.values_list("application_id", flat=True)
        )
        if len(linked_app_ids) == 1:
            return Application.objects.get(id=linked_app_ids.pop())

    def _has_access_to_obj_merchants(
        self,
        permissions,
        merchants,
        bundle,
        filters,
        object_list,
        operation_type,
        allow_null_value,
    ):
        user_authorized_merchants = get_objects_for_user(
            bundle.request.user, permissions, self.get_merchant_model(), any_perm=True
        )
        bundle.request.merchant_application = self._resolve_unique_app_from_merchants(
            user_authorized_merchants
        )
        if not merchants and allow_null_value:
            # HERE : merchants is empty AND allow_null_value is True
            # So we just check the user has permissions on at
            # least 1 merchant.
            return user_authorized_merchants.exists()
        has_access = user_authorized_merchants.filter(
            id__in=[m.id for m in merchants]
        ).exists()
        object_id = (bundle.obj and bundle.obj.id) or bundle.data.get("id", None)
        if object_id is not None and filters:
            return self._apply_detail_filters(
                object_list, filters, has_access, operation_type
            )
        return has_access

    def merchant_detail(
        self,
        object_list,
        bundle,
        path_to_merchant="",
        permissions=None,
        allow_null_value=False,
        filters=None,
        operation_type="or",
        resource_action=None,
        **kwargs,
    ):
        """
        generic merchant detail
        """
        from ims.api.api_utils.path import reach_objects_from_path

        if not self.is_authenticated_user(bundle):
            logger.debug(
                "User not authenticated; can't check permissions. Denying access."
            )
            # not authorized
            return False

        permissions = permissions or []
        filters = filters or {}
        paths_to_merchant = self.paths_to_merchant or [
            path_to_merchant or self.path_to_merchant
        ]
        permissions = permissions or self.permissions

        for path_to_merchant in paths_to_merchant:
            current_filters = copy(filters)
            self._try_load_cached_object_if_needed(bundle)
            if not path_to_merchant:
                # empty path means : "obj is directly the merchant"
                merchants = [bundle.obj]
            else:
                # ex: path_to_merchant = offers.merchant
                merchants = reach_objects_from_path(bundle.obj, path_to_merchant)

            has_access = self._has_access_to_obj_merchants(
                permissions,
                merchants,
                bundle,
                current_filters,
                object_list,
                operation_type,
                allow_null_value,
            )
            if has_access and resource_action == "create_detail" and current_filters:
                has_access = self._has_access_against_filters(
                    bundle.obj, current_filters
                )
            if has_access:
                return True

        return False

    def merchant_or_staff_detail(
        self,
        object_list,
        bundle,
        path_to_merchant="",
        permissions=None,
        allow_null_value=False,
        operation_type="or",
    ):
        """
        generic merchant or staff detail
        """
        permissions = permissions or []
        return self.merchant_detail(
            object_list, bundle, path_to_merchant, permissions, allow_null_value
        ) or self.staff_detail(object_list, bundle)

    def merchant_list(
        self,
        object_list,
        bundle,
        path_to_merchant="",
        permissions=None,
        filters=None,
        operation_type="or",
        **kwargs,
    ):
        """
        generic merchant list
        """
        permissions = permissions or []
        if isinstance(filters, (tuple, list)):
            filters = filters[0]
        filters = filters or {}

        paths_to_merchant = self.paths_to_merchant or [
            path_to_merchant or self.path_to_merchant
        ]
        permissions = permissions or self.permissions

        if self.is_authenticated_user(bundle):
            authorized_merchants = get_objects_for_user(
                bundle.request.user,
                permissions,
                self.get_merchant_model(),
                any_perm=True,
            )
            bundle.request.merchant_application = (
                self._resolve_unique_app_from_merchants(authorized_merchants)
            )
            authorized_merchant_ids = list(
                authorized_merchants.values_list("id", flat=True)
            )
            if authorized_merchant_ids:
                self.matched_at_least_one_auth = True

                for path_to_merchant in paths_to_merchant:
                    current_filters = copy(filters)
                    filter_key = (
                        self._path_entity_to_filter_key(path_to_merchant) + "__in"
                    )

                    if isinstance(current_filters, models.Q):  # Q filter
                        current_filters = self._boolean_operate_q(
                            filters,
                            models.Q(**{filter_key: authorized_merchant_ids}),
                            operation_type,
                        )
                    else:
                        current_filters[filter_key] = authorized_merchant_ids
                    if isinstance(current_filters, models.Q):
                        results = object_list.filter(current_filters)
                    else:
                        results = object_list.filter(**current_filters)

                    if len(paths_to_merchant) == 1 or results.exists():
                        return results

        return object_list.none()

    def application_through_merchant_detail(self, *args, **kwargs):
        """
        generic application_through_merchant detail
        """
        return self.application_through_merchant_list(*args, **kwargs).exists()

    def _get_application_id_from_body(self, bundle):
        try:
            from apps.ice_applications.api import ApplicationResource
        except ImportError:
            logger.debug(
                "ApplicationResource cannot be imported, can't check request body"
            )
            return None

        app_resource = ApplicationResource()
        try:
            data = app_resource.deserialize(bundle.request, bundle.request.body)
        except BadRequest:
            return None
        application_uri = data.get("application")
        if not application_uri:
            return None
        app = app_resource.get_via_uri(application_uri, request=bundle.request)
        return app.id

    def _get_application_id(self, bundle):
        if self.is_authenticated_application(bundle):
            return bundle.request.application.id
        get_application_id = bundle.request.GET.get(
            "application", None
        ) or bundle.request.GET.get("application_id", None)
        if bundle.request.method == "GET":
            return get_application_id
        post_application_id = self._get_application_id_from_body(bundle)
        return post_application_id or get_application_id

    def application_through_merchant_list(
        self,
        object_list,
        bundle,
        path_to_merchant="",
        path_to_application="",
        permissions=None,
        application_permissions=None,
        filters=None,
        operation_type="or",
        **kwargs,
    ):
        """
        generic application_through_merchant list
        """
        from apps.ice_applications.models import Application  # noqa

        application_permissions = (
            application_permissions or self.application_permissions
        )
        application_id = self._get_application_id(bundle)
        if not application_id:
            return object_list.none()
        else:
            application_id = int(application_id)
            try:
                app = Application.objects.get(id=application_id)
            except Application.DoesNotExist:
                return object_list.none()
        permissions = permissions or []

        filters = filters or {}

        paths_to_application = self.paths_to_application or [
            path_to_application or self.path_to_application
        ]
        permissions = permissions or self.permissions
        if not self.is_authenticated_user(bundle):
            return object_list.none()
        # look for any merchant perms on given application_id
        authorized_app = (
            self.get_users_authorized_merchants(bundle, permissions)
            .filter(application_id=application_id)
            .exists()
        )
        if not authorized_app:
            # look for app perms
            for permission in application_permissions:
                if user_has_perm(bundle.request.user, app, permission):
                    logger.debug("Application staff detected, authorizing access")
                    authorized_app = True
                    break
        if not authorized_app:
            return object_list.none()
        self.matched_at_least_one_auth = True
        for path_to_application in paths_to_application:
            current_filters = copy(filters)
            filter_key = self._path_entity_to_filter_key(path_to_application)

            if isinstance(current_filters, models.Q):  # Q filter
                current_filters = self._boolean_operate_q(
                    current_filters,
                    models.Q(**{filter_key: application_id}),
                    operation_type,
                )
            else:
                current_filters[filter_key] = application_id
            if isinstance(current_filters, models.Q):
                results = object_list.filter(current_filters)
            else:
                results = object_list.filter(**current_filters)
            if len(paths_to_application) == 1 or results.exists():
                bundle.request.merchant_application = app
                return results
        return object_list.none()

    def merchant_or_staff_list(
        self,
        object_list,
        bundle,
        path_to_merchant="",
        permissions=None,
        operation_type="or",
    ):
        """
        generic merchant or staff list
        """
        permissions = permissions or []
        res = self.merchant_list(object_list, bundle, path_to_merchant, permissions)
        if not isinstance(res, QuerySet) or not res.exists():
            res = self.staff_list(object_list, bundle)
        return res

    """
    # APPLICATION STAFF
    """

    def get_applications_from_object(self, bundle, path_to_application):
        from ims.api.api_utils.path import reach_objects_from_path

        return reach_objects_from_path(bundle.obj, path_to_application)

    def _has_access_to_obj_applications(
        self, application_permissions, applications, bundle
    ):
        has_access = False
        if self.is_authenticated_user(bundle):
            for permission in application_permissions:
                for application in applications:
                    if user_has_perm(bundle.request.user, application, permission):
                        has_access = True
                        break
        elif (
            self.is_authenticated_application_user(bundle)
            and bundle.request.application in applications
        ):
            authorized_applications = get_objects_for_user(
                bundle.request.user,
                application_permissions,
                self.get_application_model(),
                any_perm=True,
                with_superuser=False,
            )
            has_access = authorized_applications.filter(
                id=bundle.request.application.id
            ).exists()
        return has_access

    def _application_has_access_against_filters(
        self,
        bundle,
        object_list,
        filters,
        has_access,
        object_id,
        operation_type,
        resource_action,
    ):
        if object_id is not None:
            return self._apply_detail_filters(
                object_list, filters, has_access, operation_type
            )
        elif resource_action == "create_detail" and has_access:
            return self._has_access_against_filters(bundle.obj, filters)
        return has_access

    def application_detail(
        self,
        object_list,
        bundle,
        path_to_application="",
        application_permissions=None,
        allow_null_value=False,
        filters=None,
        operation_type="or",
        resource_action=None,
        **kwargs,
    ):
        """
        generic application based permissions detail
        """
        filters = filters or {}
        application_permissions = (
            application_permissions or self.application_permissions
        )
        paths_to_application = self.paths_to_application or [
            path_to_application or self.path_to_application
        ]

        if not self.is_authenticated_user(
            bundle
        ) and not self.is_authenticated_application_user(bundle):
            return False

        for path_to_application in paths_to_application:
            current_filters = copy(filters)
            self._try_load_cached_object_if_needed(bundle)
            applications = self.get_applications_from_object(
                bundle, path_to_application
            )

            has_access = self._has_access_to_obj_applications(
                application_permissions, applications, bundle
            )

            object_id = (bundle.obj and bundle.obj.id) or bundle.data.get("id", None)
            if object_id is None and allow_null_value:
                return True
            if current_filters:
                has_access = self._application_has_access_against_filters(
                    bundle,
                    object_list,
                    current_filters,
                    has_access,
                    object_id,
                    operation_type,
                    resource_action,
                )
            if has_access:
                return True
        return False

    def _get_object_value(self, dj_filters, field_name, obj):
        field_parts = field_name.split("__", 1)
        right_part_is_dj_filter = len(field_parts) == 2 and field_parts[1] in dj_filters
        value = getattr(obj, field_parts[0])

        if len(field_parts) == 1:
            return value, None
        elif right_part_is_dj_filter:
            return value, field_parts[1]
        return self._get_object_value(dj_filters, field_parts[1], value)

    def _has_access_against_filters(self, obj, filters):
        """
        Apply filters on object to check whether object pass.
        Used on object creation as no queryset available
        """
        handled_django_filters_bits = {
            "lte": lambda v, filter_value: v <= filter_value,
            "lt": lambda v, filter_value: v < filter_value,
            "gte": lambda v, filter_value: v >= filter_value,
            "gt": lambda v, filter_value: v > filter_value,
            "startswith": lambda v, filter_value: v.startswith(filter_value),
            # 'istartswith'
            "endswith": lambda v, filter_value: v.endswith(filter_value),
            # 'iendswith'
            "in": lambda v, expected: v in expected,
            "contains": lambda v, filter_value: v in filter_value,
            # 'icontains'
            "exact": lambda v, filter_value: v == filter_value,
            # 'iexact'
            # 'isnull'
            # 'regex'
            # 'iregex
        }
        is_valid = True
        for f, filter_value in filters.items():
            if is_valid is False:
                # fast exit as already not valid
                return False
            nb_parts = f.count("__")
            if nb_parts == 0:
                is_valid = getattr(obj, f) == filter_value
            else:
                obj_value, filter_bit = self._get_object_value(
                    handled_django_filters_bits, f, obj
                )
                is_valid = handled_django_filters_bits[filter_bit](
                    obj_value, filter_value
                )
        return is_valid

    def application_or_staff_detail(
        self,
        object_list,
        bundle,
        path_to_application="",
        application_permissions=None,
        allow_null_value=False,
        operation_type="or",
    ):
        """
        generic application based permissions or staff detail
        """
        application_permissions = application_permissions or []
        return self.application_detail(
            object_list,
            bundle,
            path_to_application,
            application_permissions,
            allow_null_value,
        ) or self.staff_detail(object_list, bundle)

    def application_merchant_or_staff_detail(
        self,
        object_list,
        bundle,
        path_to_merchant="",
        merchant_permissions=None,
        path_to_application="",
        application_permissions=None,
        allow_null_value=False,
        operation_type="or",
    ):
        """
        generic application based permissions or merchant or staff detail
        """
        merchant_permissions = merchant_permissions or []
        application_permissions = application_permissions or []
        return (
            self.application_detail(
                object_list,
                bundle,
                path_to_application,
                application_permissions,
                allow_null_value,
            )
            or self.merchant_detail(
                object_list,
                bundle,
                path_to_merchant,
                merchant_permissions,
                allow_null_value,
            )
            or self.staff_detail(object_list, bundle)
        )

    def application_list(
        self,
        object_list,
        bundle,
        path_to_application="",
        application_permissions=None,
        filters=None,
        operation_type="or",
        **kwargs,
    ):
        """
        generic application based permissions list
        """
        filters = filters or {}
        paths_to_application = self.paths_to_application or [
            path_to_application or self.path_to_application
        ]
        application_permissions = (
            application_permissions or self.application_permissions
        )

        authorized_application_ids = []

        if self.is_authenticated_user(bundle):
            authorized_application_ids = list(
                get_objects_for_user(
                    bundle.request.user,
                    application_permissions,
                    self.get_application_model(),
                    any_perm=True,
                    with_superuser=False,
                ).values_list("id", flat=True)
            )
            self.matched_at_least_one_auth = authorized_application_ids != []
        elif self.is_authenticated_application_user(bundle):
            for permission in application_permissions:
                if user_has_perm(
                    bundle.request.user, bundle.request.application, permission
                ):
                    authorized_application_ids = [bundle.request.application.id]
                    self.matched_at_least_one_auth = True
                    break

        for path_to_application in paths_to_application:
            filter_key = self._path_entity_to_filter_key(path_to_application) + "__in"

            current_filters = copy(filters)

            if isinstance(current_filters, models.Q):  # Q filter
                current_filters = self._boolean_operate_q(
                    current_filters,
                    models.Q(**{filter_key: authorized_application_ids}),
                    operation_type,
                )
            else:
                current_filters[filter_key] = authorized_application_ids

            if isinstance(current_filters, models.Q):
                results = object_list.filter(current_filters)
            else:
                results = object_list.filter(**current_filters)

            if len(paths_to_application) == 1 or results.exists():
                return results

        return object_list.none()

    def application_or_staff_list(
        self,
        object_list,
        bundle,
        path_to_application="",
        application_permissions=None,
        operation_type="or",
    ):
        """
        generic application based permissions or staff list
        """
        application_permissions = application_permissions or []
        res = self.application_list(
            object_list, bundle, path_to_application, application_permissions
        )
        if not isinstance(res, QuerySet) or not res.exists():
            res = self.staff_list(object_list, bundle)
        return res

    def application_merchant_or_staff_list(
        self,
        object_list,
        bundle,
        path_to_merchant="",
        merchant_permissions=None,
        path_to_application="",
        application_permissions=None,
        operation_type="or",
    ):
        """
        generic application based permissions or merchant or staff detail
        """
        merchant_permissions = merchant_permissions or []
        application_permissions = application_permissions or []
        return (
            self.application_list(
                object_list, bundle, path_to_application, application_permissions
            )
            or self.merchant_list(
                object_list, bundle, path_to_merchant, merchant_permissions
            )
            or self.staff_list(object_list, bundle)
        )

    def _try_load_cached_object_if_needed(self, bundle):
        """
        Try to load bundle.obj based on 'id' in cached data
        """
        try_load = bundle.request.method == "GET" and bundle.obj.id is None
        if try_load:
            try:
                # might be in cached data
                bundle.obj = bundle.obj.__class__.objects.get(id=bundle.data["id"])
            except Exception:
                pass


def class_name(obj):
    return str(obj.__class__.__name__)
