from apps.testing.factories import ApplicationFactory, MerchantFactory
from drf_izberg.tests.base import BaseAPITestCase
from drf_izberg.tests.base_decorators import (
    bypass_authentication_and_permissions,
    for_all_methods,
)
from ims.tests.jwt_tokens import OMS_TOKENS
from invoicing.api.credit_note.views import CreditNoteViewSet
from invoicing.factories import CreditNoteFactory
from invoicing.models import CreditNote
from reference.status import (
    CREDIT_NOTE_STATUS_DRAFT,
    CREDIT_NOTE_STATUS_EMITTED,
    CREDIT_NOTE_STATUS_PENDING,
    PAYMENT_STATUS_FAILED,
    PAYMENT_STATUS_NOT_PAID,
    PAYMENT_STATUS_PAID,
)


class CreditNoteFiltersBackendTestCase(BaseAPITestCase):
    API_VERSION = "v2"

    def setUp(self):
        super().setUp()
        self.app1 = ApplicationFactory(id=1)
        self.merchant1 = MerchantFactory.create_for_application(self.app1, id=1)
        self.merchant2 = MerchantFactory.create_for_application(self.app1, id=2)

    def test_list_application_filter(self):
        """
        As an operator, I should see the credit note from my application
        set in the token
        """
        credit_note = CreditNoteFactory.create_for_application_and_merchant(
            self.app1, self.merchant1
        )
        CreditNoteFactory()

        self.assertEqual(CreditNote.objects.count(), 2)

        resp = self.api_client.get(
            "/{}/credit-note/".format(self.API_VERSION),
            authentication=("Bearer " + OMS_TOKENS["APPLICATION_1_READ"]["token"]),
        )

        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 1)
        self.assertEqual(data["objects"][0]["id"], credit_note.id)

    def test_list_merchant_filter(self):
        """
        As a merchant, I should see only my credit note lines
        """

        credit_note = CreditNoteFactory.create_for_application_and_merchant(
            self.app1, self.merchant1
        )
        CreditNoteFactory.create_for_application_and_merchant(self.app1, self.merchant2)

        self.assertEqual(CreditNote.objects.count(), 2)

        resp = self.api_client.get(
            "/{}/credit-note/".format(self.API_VERSION),
            authentication=(
                "Bearer " + OMS_TOKENS["APPLICATION_1_MERCHANT_1_READ"]["token"]
            ),
        )

        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 1)
        self.assertEqual(data["objects"][0]["id"], credit_note.id)


@for_all_methods(bypass_authentication_and_permissions(CreditNoteViewSet))
class CreditNoteFiltersOnFieldsTestCase(BaseAPITestCase):
    API_VERSION = "v2"

    def setUp(self):
        super().setUp()
        self.app1 = ApplicationFactory(id=1)
        self.merchant1 = MerchantFactory.create_for_application(self.app1, id=1)
        self.merchant2 = MerchantFactory.create_for_application(self.app1, id=2)
        self.merchant3 = MerchantFactory.create_for_application(self.app1, id=3)
        self.app2 = ApplicationFactory(id=2)
        self.merchant4 = MerchantFactory.create_for_application(self.app2, id=4)

    def test_filter_application(self):
        credit_note = CreditNoteFactory.create_for_application_and_merchant(
            self.app1, self.merchant1
        )
        CreditNoteFactory.create_for_application_and_merchant(self.app2, self.merchant4)

        self.assertEqual(CreditNote.objects.count(), 2)

        resp = self.api_client.get(
            "/{}/credit-note/?application=1".format(self.API_VERSION)
        )

        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 1)
        self.assertEqual(data["objects"][0]["id"], credit_note.id)

    def test_filter_status(self):
        credit_note = CreditNoteFactory.create_for_application_and_merchant(
            self.app1, self.merchant1, status=CREDIT_NOTE_STATUS_DRAFT
        )
        CreditNoteFactory.create_for_application_and_merchant(
            self.app1, self.merchant2, status=CREDIT_NOTE_STATUS_PENDING
        )

        self.assertEqual(CreditNote.objects.count(), 2)

        resp = self.api_client.get(
            "/{}/credit-note/?status={}".format(
                self.API_VERSION, CREDIT_NOTE_STATUS_DRAFT
            )
        )

        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 1)
        self.assertEqual(data["objects"][0]["id"], credit_note.id)

    def test_filter_status__in(self):
        CreditNoteFactory.create_for_application_and_merchant(
            self.app1, self.merchant1, status=CREDIT_NOTE_STATUS_DRAFT
        )
        CreditNoteFactory.create_for_application_and_merchant(
            self.app1, self.merchant2, status=CREDIT_NOTE_STATUS_PENDING
        )
        CreditNoteFactory.create_for_application_and_merchant(
            self.app1, self.merchant3, status=CREDIT_NOTE_STATUS_EMITTED
        )

        self.assertEqual(CreditNote.objects.count(), 3)

        resp = self.api_client.get(
            "/{}/credit-note/?status__in={},{}".format(
                self.API_VERSION, CREDIT_NOTE_STATUS_DRAFT, CREDIT_NOTE_STATUS_PENDING
            )
        )

        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 2)

    def test_filter_external_status(self):
        credit_note = CreditNoteFactory.create_for_application_and_merchant(
            self.app1, self.merchant1, external_status="test status"
        )
        CreditNoteFactory.create_for_application_and_merchant(
            self.app1, self.merchant2, external_status="status 123"
        )

        self.assertEqual(CreditNote.objects.count(), 2)

        resp = self.api_client.get(
            "/{}/credit-note/?external_status={}".format(
                self.API_VERSION, "test status"
            )
        )

        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 1)
        self.assertEqual(data["objects"][0]["id"], credit_note.id)

    def test_filter_external_status__in(self):
        CreditNoteFactory.create_for_application_and_merchant(
            self.app1, self.merchant1, external_status="status123"
        )
        CreditNoteFactory.create_for_application_and_merchant(
            self.app1, self.merchant2, external_status="status456"
        )
        CreditNoteFactory.create_for_application_and_merchant(
            self.app1, self.merchant3, external_status="status789"
        )

        self.assertEqual(CreditNote.objects.count(), 3)

        resp = self.api_client.get(
            "/{}/credit-note/?external_status__in={},{}".format(
                self.API_VERSION, "status123", "status456"
            )
        )

        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 2)

    def test_filter_payment_status(self):
        credit_note = CreditNoteFactory.create_for_application_and_merchant(
            self.app1, self.merchant1, payment_status=PAYMENT_STATUS_NOT_PAID
        )
        CreditNoteFactory.create_for_application_and_merchant(
            self.app1, self.merchant2, payment_status=PAYMENT_STATUS_PAID
        )

        self.assertEqual(CreditNote.objects.count(), 2)

        resp = self.api_client.get(
            "/{}/credit-note/?payment_status={}".format(
                self.API_VERSION,
                PAYMENT_STATUS_NOT_PAID,
            )
        )

        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 1)
        self.assertEqual(data["objects"][0]["id"], credit_note.id)

    def test_filter_payment_status__in(self):
        CreditNoteFactory.create_for_application_and_merchant(
            self.app1, self.merchant1, payment_status=PAYMENT_STATUS_NOT_PAID
        )
        CreditNoteFactory.create_for_application_and_merchant(
            self.app1, self.merchant2, payment_status=PAYMENT_STATUS_PAID
        )
        CreditNoteFactory.create_for_application_and_merchant(
            self.app1, self.merchant3, payment_status=PAYMENT_STATUS_FAILED
        )

        self.assertEqual(CreditNote.objects.count(), 3)

        resp = self.api_client.get(
            "/{}/credit-note/?payment_status__in={},{}".format(
                self.API_VERSION,
                PAYMENT_STATUS_NOT_PAID,
                PAYMENT_STATUS_PAID,
            )
        )

        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 2)

    def test_filter_issuer(self):
        credit_note = CreditNoteFactory.create_for_application_and_merchant(
            self.app1, self.merchant1
        )
        CreditNoteFactory.create_for_application_and_merchant(self.app1, self.merchant2)

        self.assertEqual(CreditNote.objects.count(), 2)

        resp = self.api_client.get(
            "/{}/credit-note/?issuer=1".format(
                self.API_VERSION,
            )
        )

        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 1)
        self.assertEqual(data["objects"][0]["id"], credit_note.id)

    def test_filter_receiver(self):
        credit_note = CreditNoteFactory.create_for_application_and_merchant(
            self.app1, self.merchant1
        )
        CreditNoteFactory.create_for_application_and_merchant(self.app1, self.merchant2)

        self.assertEqual(CreditNote.objects.count(), 2)

        resp = self.api_client.get(
            "/{}/credit-note/?receiver={}".format(
                self.API_VERSION, credit_note.receiver_id
            )
        )

        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 1)
        self.assertEqual(data["objects"][0]["id"], credit_note.id)

    def test_filter_invoice(self):
        credit_note = CreditNoteFactory.create_for_application_and_merchant(
            self.app1, self.merchant1
        )
        CreditNoteFactory.create_for_application_and_merchant(self.app1, self.merchant2)

        self.assertEqual(CreditNote.objects.count(), 2)

        resp = self.api_client.get(
            "/{}/credit-note/?invoice={}".format(
                self.API_VERSION, credit_note.referred_invoice_id
            )
        )

        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 1)
        self.assertEqual(data["objects"][0]["id"], credit_note.id)
