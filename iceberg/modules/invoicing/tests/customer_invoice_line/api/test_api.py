# -*- coding: utf-8 -*-
from unittest.mock import patch

from apps.ice_applications.app_conf_settings import (
    AllowProforma,
    AutoCollectOnConfirm,
    AutoGenerateInvoice,
)
from apps.payment.backends import PSP_GATEWAY
from apps.tax.models import ProductTaxGroup
from apps.testing.factories import (
    ApplicationFactory,
    PrepaymentMOCustomerInvoiceLineFactory,
    ProductOfferOrderItemFactory,
    TermPaymentCustomerInvoiceLineFactory,
)
from drf_izberg.tests.base import BaseAPITestCase
from drf_izberg.tests.base_decorators import (
    bypass_authentication_and_permissions,
    for_all_methods,
)
from ims.tests.jwt_tokens import OMS_TOKENS
from invoicing.api.customer_invoice_line.views import CustomerInvoiceLineViewSet
from invoicing.factories import CustomerInvoiceFactory, CustomerInvoiceLineFactory
from invoicing.models import AbstractAccountingDocumentLine
from reference.status import ORDER_ITEM_STATUS_CONFIRMED, ORDER_ITEM_STATUS_PROCESSED


@for_all_methods(bypass_authentication_and_permissions(CustomerInvoiceLineViewSet))
class CustomerInvoiceLineViewsTestCase(BaseAPITestCase):
    API_VERSION = "v2"

    def test_get_list(self):
        customer_invoice_line = CustomerInvoiceLineFactory(
            order_item__invoiceable_quantity=1,
            order_item__quantity=1,
            order_item__status=ORDER_ITEM_STATUS_CONFIRMED,
        )
        resp = self.api_client.get("/{}/invoice-line/".format(self.API_VERSION))

        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 1)
        self.assertEqual(data["objects"][0]["id"], customer_invoice_line.id)

    def test_get_detail(self):
        customer_invoice_line = CustomerInvoiceLineFactory(
            order_item__invoiceable_quantity=1,
            order_item__quantity=1,
            order_item__status=ORDER_ITEM_STATUS_CONFIRMED,
        )
        resp = self.api_client.get(
            "/{}/invoice-line/{}/".format(self.API_VERSION, customer_invoice_line.id)
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["id"], customer_invoice_line.id)

    def test_patch(self):
        customer_invoice_line = CustomerInvoiceLineFactory(
            order_item__invoiceable_quantity=10,
            order_item__quantity=10,
            order_item__status=ORDER_ITEM_STATUS_CONFIRMED,
        )
        resp = self.api_client.patch(
            "/{}/invoice-line/{}/".format(self.API_VERSION, customer_invoice_line.id),
            data={"quantity": 1},
        )
        self.assertHttpAccepted(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["quantity"], "1")

    def test_create(self):
        order_item = ProductOfferOrderItemFactory(
            application__id=1,
            invoiceable_quantity=654,
            quantity=654,
            status=ORDER_ITEM_STATUS_CONFIRMED,
        )
        merchant_order = order_item.merchant_order
        invoice = CustomerInvoiceFactory(
            application_id=1, merchant_order=merchant_order
        )

        resp = self.api_client.post(
            "/{}/invoice-line/".format(self.API_VERSION),
            data={
                "invoice": "/{}/customer-invoice/{}/".format(
                    self.API_VERSION, invoice.id
                ),
                "order_item": "/{}/order-item/{}/".format(
                    self.API_VERSION, order_item.id
                ),
                "merchant_order": "/{}/merchant-order/{}/".format(
                    self.API_VERSION, merchant_order.id
                ),
                "quantity": 654,
                "unit_price": 10,
                "tax_rate": 0.1,
                "sku": "123",
                "name": "test",
                "line_type": AbstractAccountingDocumentLine.PRODUCT_TYPE,
            },
        )
        self.assertHttpCreated(resp)

    def test_create_without_invoice(self):
        order_item = ProductOfferOrderItemFactory(
            application__id=1,
            invoiceable_quantity=654,
            quantity=654,
            status=ORDER_ITEM_STATUS_CONFIRMED,
        )
        merchant_order = order_item.merchant_order

        resp = self.api_client.post(
            "/{}/invoice-line/".format(self.API_VERSION),
            data={
                "order_item": "/{}/order-item/{}/".format(
                    self.API_VERSION, order_item.id
                ),
                "merchant_order": "/{}/merchant-order/{}/".format(
                    self.API_VERSION, merchant_order.id
                ),
                "quantity": 654,
                "unit_price": 10,
                "tax_rate": 0.1,
                "sku": "123",
                "name": "test",
                "line_type": AbstractAccountingDocumentLine.PRODUCT_TYPE,
            },
        )
        self.assertHttpBadRequest(resp)

    def test_delete(self):
        customer_invoice_line = CustomerInvoiceLineFactory(
            order_item__invoiceable_quantity=1,
            order_item__quantity=1,
            order_item__status=ORDER_ITEM_STATUS_CONFIRMED,
        )
        resp = self.api_client.delete(
            "/{}/invoice-line/{}/".format(self.API_VERSION, customer_invoice_line.id),
        )
        self.assertHttpNoContent(resp)
        customer_invoice_line.refresh_from_db()
        self.assertEqual(
            customer_invoice_line.status,
            customer_invoice_line.DELETED,
        )


class CustomerInvoiceLineViewWithAuthTestCase(BaseAPITestCase):
    API_VERSION = "v2"

    def test_cust_invoice_line_creation_as_operator(self):
        order_item = ProductOfferOrderItemFactory(
            application__id=1,
            invoiceable_quantity=654,
            quantity=654,
            status=ORDER_ITEM_STATUS_CONFIRMED,
        )
        merchant_order = order_item.merchant_order
        invoice = CustomerInvoiceFactory(
            application_id=1, merchant_order=merchant_order
        )

        resp = self.api_client.post(
            "/{}/invoice-line/".format(self.API_VERSION),
            data={
                "invoice": "/{}/customer-invoice/{}/".format(
                    self.API_VERSION, invoice.id
                ),
                "order_item": "/{}/order-item/{}/".format(
                    self.API_VERSION, order_item.id
                ),
                "merchant_order": "/{}/merchant-order/{}/".format(
                    self.API_VERSION, merchant_order.id
                ),
                "quantity": 654,
                "unit_price": 10,
                "tax_rate": 0.1,
                "sku": "123",
                "name": "test",
                "line_type": AbstractAccountingDocumentLine.PRODUCT_TYPE,
            },
            authentication="Bearer {}".format(
                OMS_TOKENS["APPLICATION_1_WRITE"]["token"]
            ),
        )
        self.assertHttpCreated(resp)

    def test_cust_invoice_line_creation_as_merchant(self):
        order_item = ProductOfferOrderItemFactory(
            application__id=1,
            invoiceable_quantity=654,
            quantity=654,
            status=ORDER_ITEM_STATUS_CONFIRMED,
            merchant__id=1,
        )
        merchant_order = order_item.merchant_order
        invoice = CustomerInvoiceFactory(
            application_id=1, merchant_order=merchant_order
        )

        resp = self.api_client.post(
            "/{}/invoice-line/".format(self.API_VERSION),
            data={
                "invoice": "/{}/customer-invoice/{}/".format(
                    self.API_VERSION, invoice.id
                ),
                "order_item": "/{}/order-item/{}/".format(
                    self.API_VERSION, order_item.id
                ),
                "merchant_order": "/{}/merchant-order/{}/".format(
                    self.API_VERSION, merchant_order.id
                ),
                "quantity": 654,
                "unit_price": 10,
                "tax_rate": 0.1,
                "sku": "123",
                "name": "test",
                "line_type": AbstractAccountingDocumentLine.PRODUCT_TYPE,
            },
            authentication="Bearer {}".format(
                OMS_TOKENS["APPLICATION_1_MERCHANT_1_WRITE"]["token"]
            ),
        )
        self.assertHttpCreated(resp)

    @patch("invoicing.models.CustomerInvoice.get_proforma_file_by_url", lambda *_: None)
    @patch("invoicing.actions.generate_izberg_credit_note_from_refund", lambda *_: None)
    @patch("apps.returns.models.refund.logger.error", lambda *_: None)
    @patch(
        "apps.orders.actions.order_item_actions.cancel.logger.exception",
        lambda *_: None,
    )
    def test_delete_invoice_line_and_invoice_amount_updated(self):
        app1 = ApplicationFactory(id=1)
        app1.set_setting(AutoGenerateInvoice, False)
        app1.set_setting(AllowProforma, True)
        order_item_1 = ProductOfferOrderItemFactory.create(
            application=app1,
            currency=app1.default_currency,
            quantity=20,
            price=2000,
            tax_rate=0,
            product_tax_group=ProductTaxGroup.objects.get(id=1),
            status=ORDER_ITEM_STATUS_PROCESSED,
            merchant_order__order__auto_create_payment=True,
            invoiceable_quantity=2,
            application__payment_backend=PSP_GATEWAY,
        )
        invoice_line_1 = PrepaymentMOCustomerInvoiceLineFactory(
            application=order_item_1.application,
            merchant=order_item_1.merchant_order.merchant,
            user=order_item_1.merchant_order.order.user,
            quantity=1,
            unit_price=100,
            order_item=order_item_1,
            invoice__merchant_order=order_item_1.merchant_order,
        )
        order_item_1.refresh_from_db()
        invoice_1 = invoice_line_1.invoice
        result = invoice_1.total_amount - invoice_line_1.price
        self.api_client.delete(
            "/{}/invoice-line/{}/".format(self.API_VERSION, invoice_line_1.id),
            authentication="Bearer {}".format(
                OMS_TOKENS["APPLICATION_1_WRITE"]["token"]
            ),
        )
        invoice_1.refresh_from_db()
        self.assertEqual(invoice_1.total_amount, result)

    @patch("invoicing.models.CustomerInvoice.get_proforma_file_by_url")
    def test_term_payment_invoice_line_creation_with_unit_price_greater_than_item_raises(
        self, mock1
    ):
        app1 = ApplicationFactory(id=1)
        invoice_line = TermPaymentCustomerInvoiceLineFactory.create(
            application=app1,
            quantity=2,
            order_item__quantity=20,
            order_item__invoiceable_quantity=20,
            application__app_settings={
                AutoCollectOnConfirm: False,
                AutoGenerateInvoice: False,
                AllowProforma: False,
            },
        )
        invoice_line.delete_action()  # will recreate it
        invoice = invoice_line.invoice
        order_item = invoice_line.order_item
        merchant_order = order_item.merchant_order

        response = self.api_client.post(
            f"/{self.API_VERSION}/invoice-line/",
            data={
                "invoice": "/{}/customer-invoice/{}/".format(
                    self.API_VERSION, invoice.id
                ),
                "order_item": "/{}/order-item/{}/".format(
                    self.API_VERSION, order_item.id
                ),
                "merchant_order": "/{}/merchant-order/{}/".format(
                    self.API_VERSION, merchant_order.id
                ),
                "unit_price": order_item.price + 1,
                "quantity": 1,
            },
            authentication="Bearer {}".format(
                OMS_TOKENS["APPLICATION_1_WRITE"]["token"]
            ),
        )

        self.assertHttpBadRequest(response)
        resp = self.deserialize(response)
        self.assertEqual(
            resp,
            {
                "errors": [
                    {
                        "field": "unit_price",
                        "msg": ["Cannot be greater than order item price"],
                    }
                ]
            },
        )
