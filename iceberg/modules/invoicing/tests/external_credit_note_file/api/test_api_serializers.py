import os

import mock
from apps.testing.factories import PrepaymentMOCreditNoteFactory
from django.core.files import File
from drf_izberg.tests.base import BaseSerializerAPITestCase
from drf_izberg.tests.serializers.fixtures import FAKE_FILE_METADATA
from drf_izberg.tests.serializers.formatters import format_datetime
from invoicing.api.external_credit_note_file.serializers import (
    ExternalCreditNoteFileSerializer,
)
from invoicing.models import ExternalCreditNoteFile
from mock.mock import ANY

# Get the current directory path
CURRENT_DIRECTORY = os.path.abspath(os.path.dirname(__file__))

# Get the parent directory path
TESTS_DIRECTORY = os.path.abspath(os.path.join(CURRENT_DIRECTORY, "../.."))

# fixtures directory
TEST_FIXTURES = TESTS_DIRECTORY + "/fixtures"


def create_external_credit_note_file():
    credit_note = PrepaymentMOCreditNoteFactory(application__id=1)
    with open(TEST_FIXTURES + "/sample.pdf", "rb") as document:
        ext_credit_note_file = ExternalCreditNoteFile(
            file=File(document),
            credit_note_id=credit_note.id,
        )
        ext_credit_note_file.save(from_api=True)
        return ext_credit_note_file


class ExternalCreditNoteFileSerializerTestCase(BaseSerializerAPITestCase):
    API_VERSION = "v2"
    # This attribute controls the maximum length of diffs output by assert methods
    # that report diffs on failure.
    maxDiff = None

    def test_serializer(self):
        """
        Given an instance of Credit Note
        When serializing the instance
        Then I get a valid output
        """
        external_credit_note_file = create_external_credit_note_file()

        serializer = ExternalCreditNoteFileSerializer(
            instance=external_credit_note_file,
            context={"request": self.request},
        )

        output = {
            "credit_note_id": external_credit_note_file.credit_note_id,
            "file": "http://testserver" + external_credit_note_file.file.url,
            "id": external_credit_note_file.id,
            "resource_uri": "http://testserver/{}/external-credit-note-file/{}/".format(
                self.API_VERSION, external_credit_note_file.id
            ),
            "file_name": ANY,
            "uploaded_on": format_datetime(external_credit_note_file.uploaded_on),
            "credit_note": "http://testserver/{}/credit-note/{}/".format(
                self.API_VERSION, external_credit_note_file.credit_note_id
            ),
        }

        self.assertEqual(serializer.data, output)

    @mock.patch(
        "drf_izberg.serializers.private_file_field.PrivateFileField.to_representation",
        return_value="test.pdf",
    )
    @mock.patch("drf_izberg.serializers.private_file_field.PrivateFileField._copy_file")
    @mock.patch(
        "drf_izberg.serializers.private_file_field.PrivateFileField._get_file_metadata",
        return_value=FAKE_FILE_METADATA,
    )
    def test_serializer_validation(self, *args):
        """
        Given a payload of data
        When serializing the payload
        Then I get a valid instance
        """

        credit_note = PrepaymentMOCreditNoteFactory(application__id=1)

        payload = {
            "credit_note_id": credit_note.id,
            "file": "https://test.aws.com/test.pdf",
        }

        serializer = ExternalCreditNoteFileSerializer(
            data=payload,
            context={"request": self.request},
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()

        self.assertEqual(ExternalCreditNoteFile.objects.count(), 1)
        external_credit_note_file = ExternalCreditNoteFile.objects.first()
        self.assertEqual(external_credit_note_file.credit_note_id, credit_note.id)
        self.assertIsNotNone(external_credit_note_file.file)
