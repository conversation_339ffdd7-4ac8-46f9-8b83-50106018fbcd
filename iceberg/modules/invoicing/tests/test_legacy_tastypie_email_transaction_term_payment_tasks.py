from unittest.mock import patch

from apps.ice_applications.app_conf_settings import MarketplaceContactEmail
from apps.ice_applications.tests import ApplicationSettingsTestsSetupMixin
from apps.testing.factories import (
    ActionFactory,
    ApplicationTemplateFactory,
    CustomerInvoiceSellTransactionFactory,
    MerchantFactory,
    MerchantOrderInternalSaleRefundTransactionFactory,
    OrderItemPaymentTermFactory,
    UserFactory,
)
from dateutil.relativedelta import relativedelta
from django.test.utils import override_settings
from django.utils import timezone
from ims.tests import BaseResourceTestCase, BaseTestCase
from invoicing.factories import CustomerInvoiceLineFactory
from invoicing.signals import email_transaction_term_payment_last_24h_per_merchant
from invoicing.tasks import (
    EMAIL_ACTION,
    build_transactions_context,
    filter_invoice_transactions,
    get_transactions_for_merchant,
    has_active_mailer_actions,
    send_email_transaction_term_payment_last_24h_per_application,
    send_email_transaction_term_payment_last_24h_per_merchant,
    send_email_transactions_term_payment_last_24h_to_merchants,
    timezoned_date,
    yesterday_range_date_for_timezone_app,
)
from mailer.exceptions import Mailer<PERSON>orbidden
from mailer.models import ActionWorkflow
from reference.status import TRANSACTION_STATUS_CONFIRMED, TRANSACTION_STATUS_PROCESSING


@patch(
    "django.utils.timezone.now",
    return_value=timezone.datetime(year=2022, month=1, day=15, hour=12),
)
class EmailTransactionTermPaymentTestCase(
    ApplicationSettingsTestsSetupMixin, BaseResourceTestCase
):
    def setUp(self):
        ApplicationSettingsTestsSetupMixin.set_up(self)

        with patch(
            "django.utils.timezone.now",
            return_value=timezone.datetime(year=2022, month=1, day=15, hour=12),
        ):
            self.now = timezone.now()

            self.yesterday_from_now = timezone.now().astimezone(
                self.application.timezone
            ) - relativedelta(days=1)

            self.distant_past = timezone.now().astimezone(
                self.application.timezone
            ) - relativedelta(days=10)

    @patch(
        "invoicing.tasks."
        "send_email_transactions_term_payment_last_24h_to_merchants.delay"
    )
    def test_send_email_transaction_term_payment_last_24h_per_application(
        self, mocked_task, mocked_date
    ):
        mailer_action = ActionFactory(alias=EMAIL_ACTION)
        ApplicationTemplateFactory(action=mailer_action, application=self.application)

        result = send_email_transaction_term_payment_last_24h_per_application()
        self.assertEqual(list(result.keys())[0], self.application.id)
        mocked_task.assert_called_once()

    @patch(
        "invoicing.tasks."
        "send_email_transaction_term_payment_last_24h_per_merchant.delay"
    )
    def test_send_email_transactions_term_payment_last_24h_to_merchants(
        self, mocked_task, mocked_date
    ):
        merchant = MerchantFactory(
            application=self.application,
            default_currency=self.application.default_currency,
        )

        result = send_email_transactions_term_payment_last_24h_to_merchants(
            self.application.id
        )
        self.assertEqual(list(result.keys())[0], merchant.id)
        mocked_task.assert_called_once()

    def test_has_active_mailer_actions(self, *args):
        mailer_action = ActionFactory(alias=EMAIL_ACTION)
        ApplicationTemplateFactory(action=mailer_action, application=self.application)

        result = has_active_mailer_actions(self.application.id)

        self.assertEqual(result, True)

    def test_has_active_mailer_actions__without_valide_actions(self, *args):
        # Case: No template
        result = has_active_mailer_actions(self.application.id)
        self.assertEqual(result, False)

        # Case: No action
        ApplicationTemplateFactory(application=self.application)
        result = has_active_mailer_actions(self.application.id)
        self.assertEqual(result, False)

        # Case: action is inactive
        mailer_action_1 = ActionFactory(
            alias=EMAIL_ACTION, status=ActionWorkflow.INACTIVE
        )
        ApplicationTemplateFactory(action=mailer_action_1, application=self.application)
        # Case: action has wrong alias
        mailer_action_2 = ActionFactory(alias="NOT_GOOD")
        ApplicationTemplateFactory(action=mailer_action_2, application=self.application)

        result = has_active_mailer_actions(self.application.id)
        self.assertEqual(result, False)

    # we patch the full cleans to avoid having to build a fully coherent
    # marketplace, as we don't care about that for the test
    @patch("invoicing.models.CustomerInvoice.full_clean", lambda _: None)
    @patch("invoicing.models.CustomerInvoiceLine.full_clean", lambda _: None)
    @patch("apps.stores.actions.MerchantActionsManager.get_merchant_users")
    def test_send_email_transaction_term_payment_last_24h_per_merchant(
        self, mock_merchant_user, mocked_date
    ):
        merchant_user = UserFactory()
        mock_merchant_user.return_value = [merchant_user]

        payment_term = OrderItemPaymentTermFactory(application=self.application)

        invoice_line = CustomerInvoiceLineFactory(
            order_item=payment_term.order_item,
            invoice__merchant_order__application=self.application,
        )
        transaction = CustomerInvoiceSellTransactionFactory(
            amount=10.0,
            application=self.application,
            currency=self.application.default_currency,
            status=TRANSACTION_STATUS_CONFIRMED,
            customer_invoice=invoice_line.invoice,
            merchant=invoice_line.invoice.merchant_order.merchant,
            settlement_notification_date=self.yesterday_from_now,
        )

        yesterday_date_range = yesterday_range_date_for_timezone_app(self.application)

        with patch.object(
            email_transaction_term_payment_last_24h_per_merchant, "send"
        ) as mocked_task:
            result = send_email_transaction_term_payment_last_24h_per_merchant(
                self.application.id, transaction.merchant.id, yesterday_date_range
            )

        mocked_task.assert_called_once_with(
            transactions=build_transactions_context([transaction]),
            sender=transaction.merchant,
            application=self.application,
            merchant=transaction.merchant,
            to_name=f"{merchant_user.first_name} {merchant_user.last_name}",
            first_name=merchant_user.first_name,
            last_name=merchant_user.last_name,
            to_email=merchant_user.email,
            from_name=self.application.name,
            from_email=self.application.get_setting(MarketplaceContactEmail),
            date=self.yesterday_from_now.strftime("%d/%m/%Y"),
        )

        self.assertEqual(
            result,
            {
                "transactions_count": 1,
                "email_sent_sucessfully": [merchant_user.email],
                "email_in_error": [],
            },
        )

    def test_send_email_transaction_term_payment_last_24h_per_merchant__no_transactions(
        self, mocked_date
    ):
        merchant = MerchantFactory(
            application=self.application,
            default_currency=self.application.default_currency,
        )
        yesterday_range_date = yesterday_range_date_for_timezone_app(self.application)
        with patch.object(
            email_transaction_term_payment_last_24h_per_merchant, "send"
        ) as mocked_task:
            result = send_email_transaction_term_payment_last_24h_per_merchant(
                self.application.id, merchant.id, yesterday_range_date
            )

        mocked_task.assert_not_called()

        self.assertEqual(result, "No transactions to be processed")

    # we patch the full cleans to avoid having to build a fully coherent
    # marketplace, as we don't care about that for the test
    @patch("invoicing.models.CustomerInvoice.full_clean", lambda _: None)
    @patch("invoicing.models.CustomerInvoiceLine.full_clean", lambda _: None)
    @patch("apps.stores.actions.MerchantActionsManager.get_merchant_users")
    def test_send_email_transaction_term_payment_last_24h_per_merchant__send_error(
        self, mock_merchant_user, mocked_date
    ):
        merchant_user = UserFactory()
        mock_merchant_user.return_value = [merchant_user]

        payment_term = OrderItemPaymentTermFactory(application=self.application)

        invoice_line = CustomerInvoiceLineFactory(
            order_item=payment_term.order_item,
            invoice__merchant_order__application=self.application,
        )
        transaction = CustomerInvoiceSellTransactionFactory(
            amount=10.0,
            application=self.application,
            currency=self.application.default_currency,
            status=TRANSACTION_STATUS_CONFIRMED,
            customer_invoice=invoice_line.invoice,
            merchant=invoice_line.invoice.merchant_order.merchant,
            settlement_notification_date=self.yesterday_from_now,
        )

        yesterday_range_date = yesterday_range_date_for_timezone_app(self.application)

        with patch.object(
            email_transaction_term_payment_last_24h_per_merchant, "send"
        ) as mocked_task:
            mocked_task.side_effect = MailerForbidden()
            result = send_email_transaction_term_payment_last_24h_per_merchant(
                self.application.id, transaction.merchant.id, yesterday_range_date
            )

        mocked_task.assert_called_once_with(
            transactions=build_transactions_context([transaction]),
            sender=transaction.merchant,
            application=self.application,
            merchant=transaction.merchant,
            to_name=f"{merchant_user.first_name} {merchant_user.last_name}",
            first_name=merchant_user.first_name,
            last_name=merchant_user.last_name,
            to_email=merchant_user.email,
            from_name=self.application.name,
            from_email=self.application.get_setting(MarketplaceContactEmail),
            date=self.yesterday_from_now.strftime("%d/%m/%Y"),
        )

        self.assertEqual(
            result,
            {
                "transactions_count": 1,
                "email_sent_sucessfully": [],
                "email_in_error": [merchant_user.email],
            },
        )

    def test_yesterday_range_date_for_timezone_app(self, mocked_date):
        dt_yesterday_begin = timezone.datetime(
            year=2022, month=1, day=14, hour=0, minute=0, second=0, microsecond=0
        ).astimezone(self.application.timezone)

        dt_yesterday_end = timezone.datetime(
            year=2022,
            month=1,
            day=15,
            hour=0,
            minute=0,
            second=0,
            microsecond=0,
        ).astimezone(self.application.timezone)

        yesterday_range_date = yesterday_range_date_for_timezone_app(self.application)
        self.assertEqual(yesterday_range_date["begin"], dt_yesterday_begin.isoformat())
        self.assertEqual(yesterday_range_date["end"], dt_yesterday_end.isoformat())

    def test_get_transactions_for_merchant(self, mocked_date):
        transaction = CustomerInvoiceSellTransactionFactory(
            amount=10.0,
            application=self.application,
            currency=self.application.default_currency,
            status=TRANSACTION_STATUS_CONFIRMED,
            settlement_notification_date=self.yesterday_from_now,
        )

        transaction_from_method = get_transactions_for_merchant(
            transaction.merchant,
            yesterday_range_date_for_timezone_app(self.application),
        )
        self.assertEqual(transaction_from_method.first(), transaction)

    def test_get_transactions_for_merchant__wrong_params(self, mocked_date):
        transaction = CustomerInvoiceSellTransactionFactory(
            amount=10.0,
            application=self.application,
            currency=self.application.default_currency,
            status=TRANSACTION_STATUS_CONFIRMED,
            settlement_notification_date=self.distant_past,
        )

        merchant = transaction.merchant

        # with wrong status
        CustomerInvoiceSellTransactionFactory(
            amount=10.0,
            merchant=merchant,
            application=self.application,
            currency=self.application.default_currency,
            status=TRANSACTION_STATUS_PROCESSING,
            settlement_notification_date=self.distant_past,
        )

        # with wrong transaction_type
        MerchantOrderInternalSaleRefundTransactionFactory(
            currency=self.application.default_currency,
            merchant=merchant,
            application=self.application,
            settlement_notification_date=self.distant_past,
        )

        # with no invoice
        transaction_no_invoice = CustomerInvoiceSellTransactionFactory(
            amount=10.0,
            merchant=merchant,
            application=self.application,
            currency=self.application.default_currency,
            status=TRANSACTION_STATUS_CONFIRMED,
            settlement_notification_date=self.distant_past,
        )
        transaction_no_invoice.invoice = None
        transaction_no_invoice.save()

        transaction_from_method = get_transactions_for_merchant(
            transaction.merchant,
            yesterday_range_date_for_timezone_app(self.application),
        )
        self.assertEqual(transaction_from_method.count(), 0)

    def test_get_transactions_for_merchant__ordering(self, mocked_date):
        """
        When calling get_transactions_for_merchant
        With:
            - 1 invoice containing 2 transactions
              from 2 different settlement_notification_date
            - 1 invoice containing 1 transaction
            - These invoices have different issued_on datetime
        Then the list is order as follows (transaction_3, transaction_1, transaction_2)
        """

        # transaction yesterday
        transaction_1 = CustomerInvoiceSellTransactionFactory(
            amount=10.0,
            application=self.application,
            currency=self.application.default_currency,
            status=TRANSACTION_STATUS_CONFIRMED,
            settlement_notification_date=self.yesterday_from_now,
        )
        invoice = transaction_1.customer_invoice
        merchant = transaction_1.merchant

        # transaction yesterday minus 1h same invoice as transaction_1
        transaction_2 = CustomerInvoiceSellTransactionFactory(
            amount=20.0,
            application=self.application,
            currency=self.application.default_currency,
            status=TRANSACTION_STATUS_CONFIRMED,
            settlement_notification_date=(
                self.yesterday_from_now + relativedelta(hours=1)
            ),
            customer_invoice=invoice,
            merchant=merchant,
        )

        # transaction yesterday different invoice with issued_on minus 1h
        transaction_3 = CustomerInvoiceSellTransactionFactory(
            amount=30.0,
            application=self.application,
            currency=self.application.default_currency,
            status=TRANSACTION_STATUS_CONFIRMED,
            settlement_notification_date=self.yesterday_from_now,
            merchant=merchant,
        )
        transaction_3.customer_invoice.issued_on = (
            self.yesterday_from_now - relativedelta(hours=1)
        )
        transaction_3.customer_invoice.save()

        transaction_from_method = get_transactions_for_merchant(
            transaction_1.merchant,
            yesterday_range_date_for_timezone_app(self.application),
        )

        self.assertEqual(
            list(transaction_from_method), [transaction_3, transaction_1, transaction_2]
        )

    # we patch the full cleans to avoid having to build a fully coherent
    # marketplace, as we don't care about that for the test
    @patch("invoicing.models.CustomerInvoice.full_clean", lambda _: None)
    @patch("invoicing.models.CustomerInvoiceLine.full_clean", lambda _: None)
    def test_filter_invoice_transactions(self, mocked_date):
        # invoice with has_payment_terms to true
        payment_term = OrderItemPaymentTermFactory()
        invoice_line = CustomerInvoiceLineFactory(
            order_item=payment_term.order_item,
        )
        transaction_with_payment_terms = CustomerInvoiceSellTransactionFactory(
            application=self.application,
            status=TRANSACTION_STATUS_CONFIRMED,
            currency=self.application.default_currency,
            customer_invoice=invoice_line.invoice,
        )

        # invoice  with has_payment_terms to false by default
        transaction_without_payment_terms = CustomerInvoiceSellTransactionFactory(
            application=self.application,
            status=TRANSACTION_STATUS_CONFIRMED,
            currency=self.application.default_currency,
        )

        filtered_transactions = filter_invoice_transactions(
            [transaction_with_payment_terms, transaction_without_payment_terms]
        )
        self.assertEqual(len(filtered_transactions), 1)
        self.assertEqual(filtered_transactions[0], transaction_with_payment_terms)

    def test_build_transactions_context(self, mocked_date):
        transaction_1 = CustomerInvoiceSellTransactionFactory(
            amount=10.0,
            application=self.application,
            currency=self.application.default_currency,
            status=TRANSACTION_STATUS_CONFIRMED,
            settlement_notification_date=self.yesterday_from_now,
        )
        invoice_1 = transaction_1.customer_invoice
        expectation_1 = {
            "payment_date": timezoned_date(
                transaction_1.settlement_notification_date,
                self.application.timezone,
            ),
            "payment_amount": transaction_1.amount,
            "currency": transaction_1.currency.code,
            "invoice_amount": invoice_1.expected_amount,
            "invoice_id": invoice_1.id,
            "invoice_ref": invoice_1.id_number,
            "invoice_receiver_name": invoice_1.receiver.display_name,
            "invoice_date": timezoned_date(
                invoice_1.issued_on, self.application.timezone
            ),
            "remaining_amount": invoice_1.remaining_amount,
        }

        transaction_2 = CustomerInvoiceSellTransactionFactory(
            amount=999.0,
            application=self.application,
            currency=self.application.default_currency,
            status=TRANSACTION_STATUS_CONFIRMED,
            settlement_notification_date=self.distant_past,
        )
        invoice_2 = transaction_2.customer_invoice
        expectation_2 = {
            "payment_date": timezoned_date(
                transaction_2.settlement_notification_date,
                self.application.timezone,
            ),
            "payment_amount": transaction_2.amount,
            "currency": transaction_2.currency.code,
            "invoice_amount": invoice_2.expected_amount,
            "invoice_id": invoice_2.id,
            "invoice_ref": invoice_2.id_number,
            "invoice_receiver_name": invoice_2.receiver.display_name,
            "invoice_date": timezoned_date(
                invoice_2.issued_on, self.application.timezone
            ),
            "remaining_amount": invoice_2.remaining_amount,
        }

        result = build_transactions_context([transaction_1, transaction_2])

        self.assertDictEqual(
            result[0],
            expectation_1,
        )

        self.assertDictEqual(
            result[1],
            expectation_2,
        )


@override_settings(CELERY_TASK_ALWAYS_EAGER=True)
@override_settings(CELERY_TASK_EAGER_PROPAGATES_EXCEPTIONS=True)
@patch(
    "django.utils.timezone.now",
    return_value=timezone.datetime(year=2022, month=1, day=15, hour=12),
)
class EmailTransactionTermPaymentEndToEndTestCase(
    ApplicationSettingsTestsSetupMixin, BaseTestCase
):
    def setUp(self):
        ApplicationSettingsTestsSetupMixin.set_up(self)

        with patch(
            "django.utils.timezone.now",
            return_value=timezone.datetime(year=2022, month=1, day=15, hour=12),
        ):
            self.yesterday_from_now = timezone.now().astimezone(
                self.application.timezone
            ) - relativedelta(days=1)

    # we patch the full cleans to avoid having to build a fully coherent
    # marketplace, as we don't care about that for the test
    @patch("invoicing.models.CustomerInvoice.full_clean", lambda _: None)
    @patch("invoicing.models.CustomerInvoiceLine.full_clean", lambda _: None)
    @patch("apps.stores.actions.MerchantActionsManager.get_merchant_users")
    def test_send_email_transaction_term_payment(self, mock_merchant_user, mocked_date):
        merchant_user = UserFactory()
        mock_merchant_user.return_value = [merchant_user]
        payment_term = OrderItemPaymentTermFactory(application=self.application)

        invoice_line = CustomerInvoiceLineFactory(
            order_item=payment_term.order_item,
            invoice__merchant_order__application=self.application,
        )
        transaction = CustomerInvoiceSellTransactionFactory(
            amount=10.0,
            application=self.application,
            currency=self.application.default_currency,
            status=TRANSACTION_STATUS_CONFIRMED,
            customer_invoice=invoice_line.invoice,
            merchant=invoice_line.invoice.merchant_order.merchant,
            settlement_notification_date=self.yesterday_from_now,
        )

        transaction.refresh_from_db()

        mailer_action = ActionFactory(alias=EMAIL_ACTION)
        ApplicationTemplateFactory(action=mailer_action, application=self.application)

        with patch.object(
            email_transaction_term_payment_last_24h_per_merchant, "send"
        ) as mocked_send:
            send_email_transaction_term_payment_last_24h_per_application()

        mocked_send.assert_called_with(
            transactions=build_transactions_context([transaction]),
            sender=transaction.merchant,
            application=self.application,
            merchant=transaction.merchant,
            to_name=f"{merchant_user.first_name} {merchant_user.last_name}",
            first_name=merchant_user.first_name,
            last_name=merchant_user.last_name,
            to_email=merchant_user.email,
            from_name=self.application.name,
            from_email=self.application.get_setting(MarketplaceContactEmail),
            date=self.yesterday_from_now.strftime("%d/%m/%Y"),
        )


@override_settings(CELERY_TASK_ALWAYS_EAGER=True)
@override_settings(CELERY_TASK_EAGER_PROPAGATES_EXCEPTIONS=True)
@patch.object(
    email_transaction_term_payment_last_24h_per_merchant,
    "send",
    side_effect=lambda *_, **__: None,
)
@patch(
    "django.utils.timezone.now",
    return_value=timezone.datetime(year=2022, month=1, day=15, hour=12),
)
class EmailTransactionTermPaymentPerformanceTestCase(
    ApplicationSettingsTestsSetupMixin, BaseResourceTestCase
):
    def setUp(self):
        ApplicationSettingsTestsSetupMixin.set_up(self)

        with patch(
            "django.utils.timezone.now",
            return_value=timezone.datetime(year=2022, month=1, day=15, hour=12),
        ):
            self.yesterday_from_now = timezone.now().astimezone(
                self.application.timezone
            ) - relativedelta(days=1)

    # we patch the full cleans to avoid having to build a fully coherent
    # marketplace, as we don't care about that for the test
    @patch("invoicing.models.CustomerInvoice.full_clean", lambda _: None)
    @patch("invoicing.models.CustomerInvoiceLine.full_clean", lambda _: None)
    @patch("apps.stores.actions.MerchantActionsManager.get_merchant_users")
    def test_send_email_transaction_term_payment_last_24h_per_merchant__num_queries(
        self, mock_merchant_user, *args
    ):
        merchant_user = UserFactory()
        mock_merchant_user.return_value = [merchant_user]

        payment_term = OrderItemPaymentTermFactory(application=self.application)

        invoice_line = CustomerInvoiceLineFactory(
            order_item=payment_term.order_item,
            invoice__merchant_order__application=self.application,
        )
        transaction = CustomerInvoiceSellTransactionFactory(
            amount=10.0,
            application=self.application,
            currency=self.application.default_currency,
            status=TRANSACTION_STATUS_CONFIRMED,
            customer_invoice=invoice_line.invoice,
            merchant=invoice_line.invoice.merchant_order.merchant,
            settlement_notification_date=self.yesterday_from_now,
        )

        yesterday_date_range = yesterday_range_date_for_timezone_app(self.application)

        with self.withAssertNumQueriesLessThan(15):
            send_email_transaction_term_payment_last_24h_per_merchant(
                self.application.id, transaction.merchant.id, yesterday_date_range
            )
