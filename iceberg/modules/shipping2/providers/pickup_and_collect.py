# -*- coding: utf-8 -*-


from decimal import Decimal

from .base import BaseShippingProvider
from .methods import PICKUP_METHOD


class PickupProvider(BaseShippingProvider):
    METHOD = PICKUP_METHOD

    def clean(self, cart_items):
        price_without_vat = self.options.get("price", Decimal("0"))
        price_without_vat = Decimal(str(price_without_vat))
        tax_amount = self.tax_rate.get_tax(price_without_vat, 2)
        return {
            "price_without_vat": price_without_vat,
            "price_with_vat": price_without_vat + tax_amount,
            "tax_amount": tax_amount,
        }

    def compute_shipping(self, cart_items):
        amounts = self.clean(cart_items)
        return {
            "price_without_vat": amounts["price_without_vat"],
            "price_with_vat": amounts["price_with_vat"],
            "tax_amount": amounts["tax_amount"],
            "vat_rate": self.tax_rate.current_rate,
            "vat_rate_key": self.tax_rate.rate_key,
        }
