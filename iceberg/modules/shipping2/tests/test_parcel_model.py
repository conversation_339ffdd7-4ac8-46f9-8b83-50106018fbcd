# -*- coding: utf-8 -*-

from apps.cart.tests.test_cart_setup_mixin import CartTestSetupMixin
from apps.ice_applications.app_conf_settings import (
    EnablePackageTrackingV2,
    EnableVatB2cEuEngine,
)
from apps.orders.exceptions import ReadonlyMerchantOrderError
from apps.testing.factories import ParcelFactory, ProductOfferOrderItemFactory
from django.core.exceptions import ValidationError
from django.db import transaction
from ims.tests import BaseTestCase
from xworkflows.base import InvalidTransitionError

from ..exceptions import InvalidItemForParcel, ItemAlreadyInParcel, ItemQuantityTooBig
from ..models import Parcel, ParcelAssignment, ParcelWorkflow


class TestParcelModel(CartTestSetupMixin, BaseTestCase):
    def setUp(self):
        CartTestSetupMixin.set_up(self)
        return super(TestParcelModel, self).setUp()

    def test_parcel_auto_created_on_order_shipping_choice_creation(self):
        self.create_shipping2_rule(free_shipping=True)
        self.create_product_cart_item()
        self.application.set_setting(EnablePackageTrackingV2, True)
        self.order = self.cart.actions.create_order()
        choice = self.order.shipping_choices.get()
        self.assertEqual(choice.parcels.count(), 1)
        parcel = choice.parcels.get()
        self.assertEqual(self.application, parcel.application)
        self.assertEqual(choice, parcel.order_shipping_choice)
        self.assertEqual(choice.merchant_order, parcel.merchant_order)
        self.assertEqual(choice.carrier, parcel.carrier)
        self.assertEqual(
            set(choice.order_items.values_list("id", flat=True)),
            set(parcel.order_items.values_list("id", flat=True)),
        )
        self.assertEqual(parcel.status, ParcelWorkflow.INITIAL)

    def test_parcel_not_auto_created_on_order_shipping_choice_save(self):
        self.application.set_setting(EnablePackageTrackingV2, True)
        self.create_shipping2_rule(free_shipping=True)
        self.create_product_cart_item()
        self.order = self.cart.actions.create_order()
        choice = self.order.shipping_choices.get()
        choice.save()
        self.assertEqual(choice.parcels.count(), 1)

    def test_create_parcel_invalid_quantity(self):
        order_item = ProductOfferOrderItemFactory.build(id=1, quantity=1)
        assignment = ParcelAssignment(order_item=order_item, quantity=5)
        with self.assertRaises(ItemQuantityTooBig):
            assignment.check_item_already_in_a_parcel()

    def test_delete_shipped_parcel(self):
        p = Parcel(status="in_transit")
        with self.assertRaises(InvalidTransitionError):
            p.delete_action()

    def test_delete_received_parcel(self):
        p = Parcel(status="received")
        with self.assertRaises(InvalidTransitionError):
            p.delete_action()

    def test_cancelled_merchant_order_delete_related_parcel(self):
        parcel = ParcelFactory.create(
            application__app_settings={
                # avoid B2C EU engine checks on (non-existing) order items
                # during mo cancel
                EnableVatB2cEuEngine: False,
            }
        )
        mo = parcel.merchant_order
        mo.cancel()
        parcel.refresh_from_db()
        self.assertEqual(parcel.status, "deleted")


class TestParcelCreation(CartTestSetupMixin, BaseTestCase):
    def setUp(self, *args, **kwargs):
        CartTestSetupMixin.set_up(self)
        self.create_shipping2_rule(free_shipping=True)
        self.create_product_cart_item()
        self.order = self.cart.actions.create_order()
        Parcel.objects.all().delete()  # delete autogenerated parcels
        return super(TestParcelCreation, self).setUp(*args, **kwargs)

    def test_create_parcel_enabled(self):
        self.application.set_setting(EnablePackageTrackingV2, True)
        Parcel.objects.create_from_order_shipping_choice(
            self.order.shipping_choices.get()
        )

    def test_create_package_tracking_disabled(self):
        self.application.set_setting(EnablePackageTrackingV2, False)
        before = Parcel.objects.count()
        with self.assertRaises(ValidationError):
            Parcel.objects.create_from_order_shipping_choice(
                self.order.shipping_choices.get()
            )
        self.assertEqual(Parcel.objects.count(), before)

    def test_create_package_tracking_unset(self):
        Parcel.objects.create_from_order_shipping_choice(
            self.order.shipping_choices.get()
        )


class TestParcelSave(CartTestSetupMixin, BaseTestCase):
    def setUp(self, *args, **kwargs):
        CartTestSetupMixin.set_up(self)
        self.create_shipping2_rule(free_shipping=True)
        self.create_product_cart_item()
        order = self.cart.actions.create_order()
        self.parcel = order.merchant_orders.get().shipping_parcels.get()
        return super(TestParcelSave, self).setUp(*args, **kwargs)

    def test_create_package_tracking_enabled(self):
        self.application.set_setting(EnablePackageTrackingV2, True)
        self.parcel.save()

    def test_create_package_tracking_disabled(self):
        self.application.set_setting(EnablePackageTrackingV2, False)
        self.parcel.save()

    def test_create_package_tracking_unset(self):
        self.parcel.save()

    def test_readonly_merchant_order_still_allows_parcel_edition(self):
        self.parcel.merchant_order.actions.enable_readonly()
        try:
            self.parcel.save()
        except ReadonlyMerchantOrderError as err:
            self.fail(err)


class TestOrderItemSanityInParcel(CartTestSetupMixin, BaseTestCase):
    def setUp(self, *args, **kwargs):
        CartTestSetupMixin.set_up(self)
        self.application.set_setting("order_items_aggregation", True)
        return super(TestOrderItemSanityInParcel, self).setUp(*args, **kwargs)

    def set_auto_create_parcels(self, value):
        self.application.set_setting("enable_package_tracking_v2", value)

    def init(self, quantity=1):
        self.create_shipping2_rule(free_shipping=True)
        self.create_product_cart_item(quantity=quantity)
        self.order = self.cart.actions.create_order()
        self.choice = self.order.shipping_choices.get()
        self.set_auto_create_parcels(True)

    def test_add_item_to_parcel(self):
        self.set_auto_create_parcels(False)
        self.init()
        # check it doesn't raise ValidationError
        before = Parcel.objects.exclude(order_items=None).count()
        Parcel.objects.create_from_order_shipping_choice(self.choice)
        after = Parcel.objects.exclude(order_items=None).count()
        self.assertEqual(before, after - 1)

    def test_add_item_to_parcel_twice(self):
        self.set_auto_create_parcels(False)
        self.init()
        parcel = Parcel.objects.create_from_order_shipping_choice(self.choice)
        before = parcel.order_items.count()
        with transaction.atomic():
            with self.assertRaises(ItemQuantityTooBig):
                ParcelAssignment.objects.create(
                    parcel=parcel, order_item=self.order.order_items.get(), quantity=1
                )
        after = parcel.order_items.count()
        self.assertEqual(before, after)

    def test_add_item_to_parcel_not_from_choice(self):
        self.set_auto_create_parcels(False)
        self.init()
        parcel = Parcel.objects.create_from_order_shipping_choice(self.choice)
        before = parcel.order_items.count()
        cart2 = self.create_valid_cart()
        self.create_product_cart_item(cart=cart2)
        order2 = cart2.actions.create_order()

        # delete autogenerated parcels to avoid raising
        # ItemQuantityTooBig error because there's a test for that
        Parcel.objects.exclude(id=parcel.id).delete()
        with transaction.atomic():
            with self.assertRaises(InvalidItemForParcel):
                ParcelAssignment.objects.create(
                    parcel=parcel, order_item=order2.order_items.get(), quantity=1
                )
        after = parcel.order_items.count()
        self.assertEqual(before, after)

    def test_create_2_assignment_same_order_item_same_parcel(self):
        self.set_auto_create_parcels(False)
        self.init(quantity=2)
        parcel = Parcel.objects.create(
            application=self.application,
            order_shipping_choice=self.choice,
            carrier_id=self.choice.carrier_id,
        )
        ParcelAssignment.objects.create(
            parcel=parcel, order_item=self.choice.order_items.get(), quantity=1
        )

        with self.assertRaises(ItemAlreadyInParcel):
            ParcelAssignment.objects.create(
                parcel=parcel, order_item=self.choice.order_items.get(), quantity=1
            )
        self.assertEqual(1, parcel.order_items.count())


class TestReparcelization(CartTestSetupMixin, BaseTestCase):
    def setUp(self, *args, **kwargs):
        CartTestSetupMixin.set_up(self)
        self.application.set_setting("order_items_aggregation", True)
        self.create_shipping2_rule(free_shipping=True)
        self.cart_item = self.create_product_cart_item(quantity=2)
        self.order = self.cart.actions.create_order()
        self.shipping_choice = self.order.shipping_choices.get()
        self.parcel = self.shipping_choice.parcels.get()

        return super(TestReparcelization, self).setUp(*args, **kwargs)

    def test_create_order_shipping_choice_with_multiple_quantity(self):
        quantitized = self.parcel.assignments.get()
        self.assertEqual(quantitized.quantity, 2)

    def test_add_item_add_item_same_parcel_good_quantity(self):
        order_item = self.parcel.order_items.get()
        self.parcel.order_items.clear()
        new_parcel = Parcel.objects.create(
            carrier_id=self.parcel.carrier_id,
            order_shipping_choice_id=self.parcel.order_shipping_choice_id,
            application_id=self.parcel.application_id,
            merchant_order_id=self.parcel.merchant_order_id,
        )
        new_parcel.add_item(order_item, 1)
        new_parcel.add_item(order_item, 1)

        assignment_value = new_parcel.assignments.values(
            "order_item_id", "quantity"
        ).get()
        self.assertEqual(
            assignment_value, {"order_item_id": order_item.id, "quantity": 2}
        )

    def test_add_item_add_item_same_parcel_too_much(self):
        order_item = self.parcel.order_items.get()
        self.parcel.order_items.clear()
        new_parcel = Parcel.objects.create(
            carrier_id=self.parcel.carrier_id,
            order_shipping_choice_id=self.parcel.order_shipping_choice_id,
            application_id=self.parcel.application_id,
            merchant_order_id=self.parcel.merchant_order_id,
        )
        new_parcel.add_item(order_item, 1)
        new_parcel.add_item(order_item, 1)
        with self.assertRaises(ItemQuantityTooBig):
            new_parcel.add_item(order_item, 1)

        assignment_value = new_parcel.assignments.values(
            "order_item_id", "quantity"
        ).get()
        self.assertEqual(
            assignment_value, {"order_item_id": order_item.id, "quantity": 2}
        )

    def test_add_item_add_item_2_parcel_good_quantity(self):
        order_item = self.parcel.order_items.get()
        self.parcel.order_items.clear()
        parcel_1 = Parcel.objects.create(
            carrier_id=self.parcel.carrier_id,
            order_shipping_choice_id=self.parcel.order_shipping_choice_id,
            application_id=self.parcel.application_id,
            merchant_order_id=self.parcel.merchant_order_id,
        )
        parcel_2 = Parcel.objects.create(
            carrier_id=self.parcel.carrier_id,
            order_shipping_choice_id=self.parcel.order_shipping_choice_id,
            application_id=self.parcel.application_id,
            merchant_order_id=self.parcel.merchant_order_id,
        )
        parcel_1.add_item(order_item, 1)
        parcel_2.add_item(order_item, 1)

        assignment_value_1 = parcel_1.assignments.values(
            "order_item_id", "quantity"
        ).get()
        assignment_value_2 = parcel_1.assignments.values(
            "order_item_id", "quantity"
        ).get()
        self.assertEqual(
            assignment_value_1, {"order_item_id": order_item.id, "quantity": 1}
        )
        self.assertEqual(
            assignment_value_2, {"order_item_id": order_item.id, "quantity": 1}
        )

    def test_add_quantity_to_max_quantity_quantitized_item(self):
        order_item = self.parcel.order_items.get()
        with self.assertRaises(ItemQuantityTooBig):
            self.parcel.add_item(order_item, 1)

    def test_resave_quantitized_item(self):
        # ensure it won't fail
        self.parcel.assignments.get().full_clean()


class ParcelTrackingNumberTestCase(BaseTestCase):
    def test_tracking_url_is_autofilled_by_default(self):
        parcel = ParcelFactory.build(tracking_number="lol")

        parcel.clean_tracking_url()

        self.assertEqual(parcel.tracking_url, "https://website.com/lol")

    def test_tracking_url_is_none_if_no_id_by_default(self):
        parcel = ParcelFactory.build(tracking_number=None)

        parcel.clean_tracking_url()

        self.assertIsNone(parcel.tracking_url)

    def test_provide_tracking_url_not_overriden(self):
        parcel = ParcelFactory.build(tracking_url="http://my_custom_url.com")

        parcel.clean_tracking_url()

        self.assertEqual(parcel.tracking_url, "http://my_custom_url.com")

    def test_provide_tracking_url_and_tracking_number_not_overriden(self):
        parcel = ParcelFactory.build(
            tracking_url="http://my_custom_url.com", tracking_number="lol"
        )

        parcel.clean_tracking_url()

        self.assertEqual(parcel.tracking_url, "http://my_custom_url.com")
        self.assertEqual(parcel.tracking_number, "lol")
