# -*- coding: utf-8 -*-

import json

from apps.cart.tests.test_cart_setup_mixin import CartTestSetupMixin
from apps.stores.tests.test_merchant_setup_mixin import TestMerchantSetupMixin
from django.test import RequestFactory
from django.urls import reverse
from ims.tests import BaseResourceTestCase
from tastypie.bundle import Bundle
from zone.models import Zone
from zone.tests.utils import create_zone

from .. import choices_list as shipper_choices
from ..models import ShippingProvider
from .utils import create_shipping_provider


class ShippingProviderTest(TestMerchantSetupMixin, BaseResourceTestCase):
    def setUp(self):
        self.set_up()
        self.shipping_provider_url = reverse(
            "api_dispatch_list",
            kwargs={"resource_name": "shipping_provider", "api_name": "v1"},
        )
        return super(ShippingProviderTest, self).setUp()

    def test_get_shipping_providers_as_staff(self):
        self.provider = create_shipping_provider(
            method=shipper_choices.PER_WEIGHT_PROVIDER,
            options={
                "collection_within_hours": 48,
                "delivery_within_hours": 72,
                "price_per_weight": 2.0,
            },
        )
        resp = self.api_client.get(
            self.shipping_provider_url,
            authentication=self.get_token(self.izberg_user, app_token=False),
        )
        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], ShippingProvider.objects.count())
        self.assertEqual(len(data["objects"]), data["meta"]["total_count"])

    def test_get_shipping_provider_detail(self):
        self.provider = create_shipping_provider(
            method=shipper_choices.PER_WEIGHT_PROVIDER,
            options={
                "collection_within_hours": 48,
                "delivery_within_hours": 72,
                "price_per_weight": 2.0,
            },
        )
        resp = self.api_client.get(
            "%s%d/" % (self.shipping_provider_url, self.provider.id),
            authentication=self.get_token(self.izberg_user, app_token=False),
        )

        data = self.deserialize(resp)
        self.assertEqual(data["method"], shipper_choices.PER_WEIGHT_PROVIDER)

    def test_create_shipping_provider(self):
        self.provider = create_shipping_provider(
            method=shipper_choices.PER_WEIGHT_PROVIDER,
            options={
                "collection_within_hours": 48,
                "delivery_within_hours": 72,
                "price_per_weight": 2.0,
            },
        )
        expression = "country('FR')"
        zone = create_zone(expression=expression)
        zone_resource_uri = reverse(
            "api_dispatch_detail",
            kwargs={"resource_name": "zone", "api_name": "v1", "pk": zone.id},
        )
        method = shipper_choices.PER_WEIGHT_PROVIDER
        data = {"zone": zone_resource_uri, "integrator": True, "method": method}
        # Get ShippingProvider count before POST :
        shipping_provider_before = ShippingProvider.objects.count()
        # Do the POST request to create policy :
        resp = self.api_client.post(
            self.shipping_provider_url,
            data=data,
            authentication=self.get_token(self.izberg_user, app_token=False),
        )
        self.assertHttpCreated(resp)
        # Get ShippingProvider count after POST :
        shipping_provider_after = ShippingProvider.objects.count()
        # Now, check that a ShippingProvider have been created :
        self.assertEqual(shipping_provider_after, shipping_provider_before + 1)
        # ----
        provider = ShippingProvider.objects.get(zone__id=zone.id)
        self.assertEqual(provider.zone.id, zone.id)
        self.assertTrue(provider.integrator)
        self.assertIsNone(provider.application)
        self.assertIsNone(provider.merchant)
        self.assertEqual(provider.method, method)

    def test_update_shipping_provider(self):
        self.provider = create_shipping_provider(
            name="name",
            method=shipper_choices.PER_WEIGHT_PROVIDER,
            options={
                "collection_within_hours": 48,
                "delivery_within_hours": 72,
                "price_per_weight": 2.0,
            },
            application=self.application,
        )
        new_name = "name2"
        resp = self.api_client.put(
            "%s%d/" % (self.shipping_provider_url, self.provider.id),
            data={"name": new_name},
            authentication=self.get_app_admin_token(),
        )
        self.assertHttpAccepted(resp)
        # Reload provider from DB :
        provider = ShippingProvider.objects.get(id=self.provider.id)
        # Check that provider has been updated :
        self.assertEqual(provider.name, new_name)

    def test_delete_app_shipping_provider_as_app_admin(self):
        self.provider = create_shipping_provider(
            method=shipper_choices.PER_WEIGHT_PROVIDER,
            options={
                "collection_within_hours": 48,
                "delivery_within_hours": 72,
                "price_per_weight": 2.0,
            },
            application=self.application,
        )

        resp = self.api_client.delete(
            "%s%d/" % (self.shipping_provider_url, self.provider.id),
            authentication=self.get_app_admin_token(),
        )

        self.assertHttpNoContent(resp)
        # Reload provider from DB :
        provider = ShippingProvider.objects.get(id=self.provider.id)
        self.assertTrue(provider.status.is_deleted)

    def test_delete_integrator_shipping_provider_as_app_admin(self):
        self.provider = create_shipping_provider(
            method=shipper_choices.PER_WEIGHT_PROVIDER,
            options={
                "collection_within_hours": 48,
                "delivery_within_hours": 72,
                "price_per_weight": 2.0,
            },
            integrator=True,
        )
        resp = self.api_client.delete(
            "%s%d/" % (self.shipping_provider_url, self.provider.id),
            authentication=self.get_app_admin_token(),
        )
        self.assertHttpUnauthorized(resp)
        # Reload provider from DB :
        provider = ShippingProvider.objects.get(id=self.provider.id)
        self.assertTrue(provider.status.is_initial)

    def test_delete_app_shipping_provider_as_staff(self):
        self.provider = create_shipping_provider(
            method=shipper_choices.PER_WEIGHT_PROVIDER,
            options={
                "collection_within_hours": 48,
                "delivery_within_hours": 72,
                "price_per_weight": 2.0,
            },
            application=self.application,
        )

        resp = self.api_client.delete(
            "%s%d/" % (self.shipping_provider_url, self.provider.id),
            authentication=self.get_token(self.izberg_user, app_token=False),
        )

        self.assertHttpNoContent(resp)
        # Reload provider from DB :
        provider = ShippingProvider.objects.get(id=self.provider.id)
        self.assertTrue(provider.status.is_deleted)

    def test_delete_integrator_shipping_provider_as_staff(self):
        self.provider = create_shipping_provider(
            method=shipper_choices.PER_WEIGHT_PROVIDER,
            options={
                "collection_within_hours": 48,
                "delivery_within_hours": 72,
                "price_per_weight": 2.0,
            },
            integrator=True,
        )

        resp = self.api_client.delete(
            "%s%d/" % (self.shipping_provider_url, self.provider.id),
            authentication=self.get_token(self.izberg_user, app_token=False),
        )

        self.assertHttpNoContent(resp)
        # Reload provider from DB :
        provider = ShippingProvider.objects.get(id=self.provider.id)
        self.assertTrue(provider.status.is_deleted)

    def test_list_application_providers_as_merchant(self):
        self.provider_to_find = create_shipping_provider(
            method=shipper_choices.PER_WEIGHT_PROVIDER,
            options={
                "collection_within_hours": 48,
                "delivery_within_hours": 72,
                "price_per_weight": 2.0,
            },
            application=self.application,
        )
        # add another provider and make sure the app is different to make sure
        # permissions works well
        self.provider_other_app = create_shipping_provider(
            method=shipper_choices.PER_WEIGHT_PROVIDER,
            options={
                "collection_within_hours": 48,
                "delivery_within_hours": 72,
                "price_per_weight": 2.0,
            },
            application=self.create_application(name="app2", namespace="app2"),
        )
        self.provider_integrator = create_shipping_provider(
            method=shipper_choices.PER_WEIGHT_PROVIDER,
            options={
                "collection_within_hours": 48,
                "delivery_within_hours": 72,
                "price_per_weight": 2.0,
            },
            integrator=True,
        )

        self.assertNotEqual(self.provider_other_app.application, self.application)
        self.assertIsNotNone(self.provider_other_app.application)
        resp = self.api_client.get(
            "%s?limit=30&offset=0&application=%d"
            % (self.shipping_provider_url, self.application.id),
            authentication=self.get_merchant_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 2)
        found = []
        to_find = (self.provider_to_find.id, self.provider_integrator.id)
        for provider in data["objects"]:
            found.append(provider["id"])
        self.assertEqual(set(found), set(to_find))

    def test_list_application_providers_as_application_staff(self):
        self.provider_to_find = create_shipping_provider(
            method=shipper_choices.PER_WEIGHT_PROVIDER,
            options={
                "collection_within_hours": 48,
                "delivery_within_hours": 72,
                "price_per_weight": 2.0,
            },
            application=self.application,
        )
        # add another provider and make sure the app is different to make sure
        # permissions works well
        self.provider_other_app = create_shipping_provider(
            method=shipper_choices.PER_WEIGHT_PROVIDER,
            options={
                "collection_within_hours": 48,
                "delivery_within_hours": 72,
                "price_per_weight": 2.0,
            },
            application=self.create_application(name="app2", namespace="app2"),
        )
        self.provider_integrator = create_shipping_provider(
            method=shipper_choices.PER_WEIGHT_PROVIDER,
            options={
                "collection_within_hours": 48,
                "delivery_within_hours": 72,
                "price_per_weight": 2.0,
            },
            integrator=True,
        )

        self.assertNotEqual(self.provider_other_app.application, self.application)
        self.assertIsNotNone(self.provider_other_app.application)
        resp = self.api_client.get(
            "%s?limit=30&offset=0&application=%d"
            % (self.shipping_provider_url, self.application.id),
            authentication=self.get_app_admin_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 2)
        found = []
        to_find = (self.provider_to_find.id, self.provider_integrator.id)
        for provider in data["objects"]:
            found.append(provider["id"])
        self.assertEqual(set(found), set(to_find))

    def test_list_application_providers_as_application_staff_with_user_token(self):
        self.provider_to_find = create_shipping_provider(
            method=shipper_choices.PER_WEIGHT_PROVIDER,
            options={
                "collection_within_hours": 48,
                "delivery_within_hours": 72,
                "price_per_weight": 2.0,
            },
            application=self.application,
        )
        # add another provider and make sure the app is different to make sure
        # permissions works well
        self.provider_other_app = create_shipping_provider(
            method=shipper_choices.PER_WEIGHT_PROVIDER,
            options={
                "collection_within_hours": 48,
                "delivery_within_hours": 72,
                "price_per_weight": 2.0,
            },
            application=self.create_application(name="app2", namespace="app2"),
        )
        self.provider_integrator = create_shipping_provider(
            method=shipper_choices.PER_WEIGHT_PROVIDER,
            options={
                "collection_within_hours": 48,
                "delivery_within_hours": 72,
                "price_per_weight": 2.0,
            },
            integrator=True,
        )

        self.assertNotEqual(self.provider_other_app.application, self.application)
        self.assertIsNotNone(self.provider_other_app.application)
        resp = self.api_client.get(
            "%s?limit=30&offset=0&application=%d"
            % (self.shipping_provider_url, self.application.id),
            authentication=self.get_token(self.app_admin_user, app_token=False),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 2)
        found = []
        to_find = (self.provider_to_find.id, self.provider_integrator.id)
        for provider in data["objects"]:
            found.append(provider["id"])
        self.assertEqual(set(found), set(to_find))

    def test_get_application_provider_as_merchant(self):
        self.provider = create_shipping_provider(
            method=shipper_choices.PER_WEIGHT_PROVIDER,
            options={
                "collection_within_hours": 48,
                "delivery_within_hours": 72,
                "price_per_weight": 2.0,
            },
            application=self.application,
        )
        resp = self.api_client.get(
            "%s%s/?application=%d"
            % (self.shipping_provider_url, self.provider.id, self.application.id),
            authentication=self.get_merchant_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["id"], self.provider.id)

    def test_get_application_provider_as_anonymous_application_user(self):
        self.provider = create_shipping_provider(
            method=shipper_choices.PER_WEIGHT_PROVIDER,
            options={
                "collection_within_hours": 48,
                "delivery_within_hours": 72,
                "price_per_weight": 2.0,
            },
            application=self.application,
        )
        resp = self.api_client.get(
            "%s%s/"
            % (
                self.shipping_provider_url,
                self.provider.id,
            ),
            authentication=self.get_anonymous_application_user_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["id"], self.provider.id)

    def test_get_integrator_provider_as_anonymous_application_user(self):
        self.provider = create_shipping_provider(
            method=shipper_choices.PER_WEIGHT_PROVIDER,
            options={
                "collection_within_hours": 48,
                "delivery_within_hours": 72,
                "price_per_weight": 2.0,
            },
            integrator=True,
        )
        resp = self.api_client.get(
            "%s%s/"
            % (
                self.shipping_provider_url,
                self.provider.id,
            ),
            authentication=self.get_anonymous_application_user_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["id"], self.provider.id)

    def test_list_application_providers_as_anonymous_application_user(self):
        self.provider_to_find = create_shipping_provider(
            method=shipper_choices.PER_WEIGHT_PROVIDER,
            options={
                "collection_within_hours": 48,
                "delivery_within_hours": 72,
                "price_per_weight": 2.0,
            },
            application=self.application,
        )
        # add another provider and make sure the app is different to make sure
        # permissions works well
        self.provider_other_app = create_shipping_provider(
            method=shipper_choices.PER_WEIGHT_PROVIDER,
            options={
                "collection_within_hours": 48,
                "delivery_within_hours": 72,
                "price_per_weight": 2.0,
            },
            application=self.create_application(name="app2", namespace="app2"),
        )
        self.provider_integrator = create_shipping_provider(
            method=shipper_choices.PER_WEIGHT_PROVIDER,
            options={
                "collection_within_hours": 48,
                "delivery_within_hours": 72,
                "price_per_weight": 2.0,
            },
            integrator=True,
        )

        self.assertNotEqual(self.provider_other_app.application, self.application)
        self.assertIsNotNone(self.provider_other_app.application)
        resp = self.api_client.get(
            self.shipping_provider_url,
            authentication=self.get_anonymous_application_user_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 2)
        found = []
        to_find = (self.provider_to_find.id, self.provider_integrator.id)
        for provider in data["objects"]:
            found.append(provider["id"])
        self.assertEqual(set(found), set(to_find))

    def test_get_other_application_provider_as_merchant_fails(self):
        self.provider = create_shipping_provider(
            method=shipper_choices.PER_WEIGHT_PROVIDER,
            options={
                "collection_within_hours": 48,
                "delivery_within_hours": 72,
                "price_per_weight": 2.0,
            },
            application=self.create_application(name="app2", namespace="app2"),
        )
        resp = self.api_client.get(
            "%s%s/" % (self.shipping_provider_url, self.provider.id),
            authentication=self.get_merchant_token(),
        )
        self.assertHttpUnauthorized(resp)

    def test_get_integrator_provider_as_merchant_works(self):
        self.provider = create_shipping_provider(
            method=shipper_choices.PER_WEIGHT_PROVIDER,
            options={
                "collection_within_hours": 48,
                "delivery_within_hours": 72,
                "price_per_weight": 2.0,
            },
            integrator=True,
        )
        resp = self.api_client.get(
            "%s%s/" % (self.shipping_provider_url, self.provider.id),
            authentication=self.get_merchant_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["id"], self.provider.id)


class CustomShippingProviderTest(CartTestSetupMixin, BaseResourceTestCase):
    def setUp(self):
        CartTestSetupMixin.set_up(self)

    def test_shipping_estimate_fields(self):
        # Given
        from shipping2.api import shipping_provider_resources as spr

        product_offer = self.product_offer
        resource = spr.CustomShippingProviderAssigmentResource(product_offer)
        from shipping2.models.shipping_assignments_models import (
            ShippingProviderAssignment,
        )
        from shipping2.models.shipping_provider_models import ShippingProvider

        zone = Zone.objects.create(
            application=self.product_offer.application,
            expression="starts('75')",
        )
        shipping_provider = ShippingProvider.objects.create(
            method="per_item_count",
            merchant=self.merchant,
            zone=zone,
            options={
                "collection_within_hours": 48,
                "free_above_item_count": 3,
                "first_item_amount": 2,
                "delivery_within_hours": 48,
                "additional_item_amount": 1,
                "free_above_amount": 250,
            },
        )
        assignment = ShippingProviderAssignment.objects.create(
            merchant=self.merchant,
            application=self.merchant.application,
            zone=zone,
            provider=shipping_provider,
        )
        resource._patch_estimated_shipping_prices(assignment)
        bundle = Bundle(obj=assignment, request=RequestFactory().get("/"))
        data = resource.full_dehydrate(bundle)

        # When
        result = json.loads(resource.serialize(None, data, "application/json"))

        # Then
        self.assertIn("estimated_shipping_price_with_vat", result.keys())
        self.assertIn("estimated_shipping_price_without_vat", result.keys())
        self.assertTrue(
            result["estimated_shipping_price_without_vat"]
            <= result["estimated_shipping_price_with_vat"],
        )

    def test_dehydreate_estimated_price(self):
        # Given
        from shipping2.api import shipping_provider_resources as spr

        product_offer = self.product_offer
        resource = spr.CustomShippingProviderAssigmentResource(product_offer)

        # not really an assignment but it's suffisant for the test
        assignment = object()
        bundle = Bundle(obj=assignment)

        # When
        result = resource.dehydrate_estimated_price(bundle, "price_with_vat")

        # Then
        self.assertEqual(result, None)
