# -*- coding: utf-8 -*-


from apps.address.models import Country
from django.conf import settings
from django.core.exceptions import ValidationError
from django.test.utils import override_settings
from ims.api.exceptions import ResourceLockedException
from ims.api.resources import MPModelResource
from ims.tests import BaseResourceTestCase, register_resource
from mock import patch
from mp_utils.tests.decorators import enable_json_log_middleware
from tastypie.authentication import Authentication
from tastypie.authorization import Authorization
from tastypie.exceptions import Unauthorized
from tastypie.http import HttpForbidden, HttpResponse


class SampleResource(MPModelResource):
    class Meta:
        resource_name = "sample_resource"
        always_return_data = True
        queryset = Country.objects.all()
        authentication = Authentication()
        authorization = Authorization()
        list_allowed_methods = ["get", "post"]
        detail_allowed_methods = ["get", "put", "patch", "delete"]

    def is_authenticated(self, *args, **kwargs):
        return True

    def prepend_urls(self):
        return [
            self.make_url("302", "return_302", "return-302"),
            self.make_url("400", "return_400", "return-400"),
            self.make_url("401", "return_401", "return-401"),
            self.make_url("403", "return_403", "return-403"),
            # 404 route not defined on purpose
            self.make_url("409", "return_409", "return-409"),
        ]

    def return_302(self, request, *args, **kwargs):
        response = HttpResponse()
        response.status_code = 302
        return response

    def return_400(self, request, *args, **kwargs):
        raise ValidationError("an error")

    def return_401(self, request, *args, **kwargs):
        raise Unauthorized("an error")

    def return_403(self, request, *args, **kwargs):
        return HttpForbidden()

    def return_409(self, request, *args, **kwargs):
        raise ResourceLockedException(Country())


class Sample500Resource(MPModelResource):
    class Meta:
        resource_name = "sample_resource"
        always_return_data = True
        queryset = Country.objects.all()
        authentication = Authentication()
        authorization = Authorization()
        list_allowed_methods = ["get"]

    def is_authenticated(self, *args, **kwargs):
        return True

    def obj_get_list(self, *args, **kwargs):
        raise Exception("An error")


@patch("tastypie.resources.Resource.authorized_read_detail", lambda *_: True)
@enable_json_log_middleware
class ResponseheadersTestCase(BaseResourceTestCase):
    def assert_common_headers(self, response):
        self.assertEqual(response["X-Release-Version"], settings.RELEASE_NAME)
        self.assertEqual(response["Content-Language"], "en")
        self.assertIn("X-Request-ID", response)

    @register_resource(SampleResource)
    def test_200(self, resource_url):
        response = self.api_client.get("{}/".format(resource_url))

        self.assertHttpOK(response)
        self.assert_common_headers(response)
        self.assertEqual(response["Cache-Control"], "no-cache")
        self.assertEqual(
            response["Access-Control-Allow-Headers"],
            settings.ACCESS_CONTROL_ALLOW_HEADERS,
        )
        self.assertEqual(response["Access-Control-Allow-Origin"], "*")
        self.assertEqual(response["X-RateLimit-Concurrency"], "1")
        self.assertEqual(response["X-RateLimit-Concurrency-Limit"], "10")
        self.assertEqual(response["Content-Type"], "application/json")
        self.assertEqual(
            response["Vary"], "Accept, Accept-Language, Cookie, Accept-Encoding, origin"
        )

    @register_resource(SampleResource)
    def test_201(self, resource_url):
        response = self.api_client.post("{}/".format(resource_url), data={})

        self.assertHttpCreated(response)
        self.assert_common_headers(response)
        self.assertEqual(
            response["Access-Control-Allow-Headers"],
            settings.ACCESS_CONTROL_ALLOW_HEADERS,
        )
        self.assertEqual(response["Access-Control-Allow-Origin"], "*")
        self.assertEqual(response["X-RateLimit-Concurrency"], "1")
        self.assertEqual(response["X-RateLimit-Concurrency-Limit"], "10")
        self.assertEqual(response["Content-Type"], "application/json")
        self.assertEqual(
            response["Vary"], "Accept, Accept-Language, Cookie, Accept-Encoding, origin"
        )

    @register_resource(SampleResource)
    def test_202(self, resource_url):
        response = self.api_client.patch("{}/1/".format(resource_url), data={})

        self.assertHttpAccepted(response)
        self.assert_common_headers(response)
        self.assertEqual(
            response["Access-Control-Allow-Headers"],
            settings.ACCESS_CONTROL_ALLOW_HEADERS,
        )
        self.assertEqual(response["Access-Control-Allow-Origin"], "*")
        self.assertEqual(response["X-RateLimit-Concurrency"], "1")
        self.assertEqual(response["X-RateLimit-Concurrency-Limit"], "10")
        self.assertEqual(response["Content-Type"], "application/json")
        self.assertEqual(
            response["Vary"], "Accept, Accept-Language, Cookie, Accept-Encoding, origin"
        )

    @register_resource(SampleResource)
    def test_301(self, resource_url):
        response = self.api_client.get("{}".format(resource_url), data={})

        self.assertEqual(response.status_code, 301)
        self.assertEqual(response["Content-Type"], "text/html; charset=utf-8")
        self.assertNotIn("X-Release-Version", response)
        self.assertNotIn("Content-Language", response)
        self.assertNotIn("X-Request-ID", response)
        self.assertEqual(response["Vary"], "origin")

    @register_resource(SampleResource)
    def test_302(self, resource_url):
        response = self.api_client.get("{}/302/".format(resource_url), data={})

        self.assertEqual(response.status_code, 302)
        self.assert_common_headers(response)
        self.assertEqual(response["Content-Type"], "text/html; charset=utf-8")
        self.assertEqual(response["Vary"], "Accept, Accept-Language, origin")

        self.assertEqual(response["Access-Control-Allow-Origin"], "*")
        self.assertEqual(response["X-RateLimit-Concurrency"], "1")
        self.assertEqual(response["X-RateLimit-Concurrency-Limit"], "10")
        self.assertEqual(response["Vary"], "Accept, Accept-Language, origin")

    @register_resource(SampleResource)
    def test_400(self, resource_url):
        response = self.api_client.get("{}/400/".format(resource_url))
        self.assertHttpBadRequest(response)
        self.assert_common_headers(response)
        self.assertEqual(response["X-RateLimit-Concurrency"], "1")
        self.assertEqual(response["X-RateLimit-Concurrency-Limit"], "10")
        self.assertEqual(response["Content-Type"], "application/json")
        self.assertEqual(response["Access-Control-Allow-Origin"], "*")
        self.assertEqual(response["Vary"], "Accept-Language, origin")

    @register_resource(SampleResource)
    def test_401(self, resource_url):
        response = self.api_client.get("{}/401/".format(resource_url))
        self.assertHttpUnauthorized(response)
        self.assert_common_headers(response)
        self.assertEqual(response["X-RateLimit-Concurrency"], "1")
        self.assertEqual(response["X-RateLimit-Concurrency-Limit"], "10")
        self.assertEqual(response["Content-Type"], "application/json")
        self.assertEqual(response["Access-Control-Allow-Origin"], "*")
        self.assertEqual(response["Vary"], "Accept-Language, origin")

    @register_resource(SampleResource)
    def test_403(self, resource_url):
        response = self.api_client.get("{}/403/".format(resource_url))
        self.assertEqual(response.status_code, 403)
        self.assert_common_headers(response)
        self.assertEqual(response["X-RateLimit-Concurrency"], "1")
        self.assertEqual(response["X-RateLimit-Concurrency-Limit"], "10")
        self.assertEqual(response["Content-Type"], "text/html; charset=utf-8")
        self.assertEqual(response["Access-Control-Allow-Origin"], "*")
        self.assertEqual(response["Vary"], "Accept, Accept-Language, origin")

    @register_resource(SampleResource)
    def test_404(self, resource_url):
        # 404 route not defined on purpose
        response = self.api_client.get("{}/404/".format(resource_url))
        self.assertHttpNotFound(response)
        self.assert_common_headers(response)
        self.assertEqual(response["X-RateLimit-Concurrency"], "1")
        self.assertEqual(response["X-RateLimit-Concurrency-Limit"], "10")
        self.assertEqual(response["Content-Type"], "text/html; charset=utf-8")
        self.assertEqual(response["Access-Control-Allow-Origin"], "*")
        self.assertEqual(response["Vary"], "Accept, Accept-Language, origin")

    @register_resource(SampleResource)
    def test_405(self, resource_url):
        response = self.api_client.post("{}/1/".format(resource_url))
        self.assertHttpMethodNotAllowed(response)
        self.assert_common_headers(response)
        self.assertEqual(response["X-RateLimit-Concurrency"], "1")
        self.assertEqual(response["X-RateLimit-Concurrency-Limit"], "10")
        self.assertEqual(response["Content-Type"], "text/html; charset=utf-8")
        self.assertEqual(response["Access-Control-Allow-Origin"], "*")
        self.assertEqual(response["Vary"], "Accept-Language, origin")

    @register_resource(SampleResource)
    def test_409(self, resource_url):
        response = self.api_client.get("{}/409/".format(resource_url))
        self.assertHttpConflict(response)
        self.assert_common_headers(response)
        self.assertEqual(response["X-RateLimit-Concurrency"], "1")
        self.assertEqual(response["X-RateLimit-Concurrency-Limit"], "10")
        self.assertEqual(response["Content-Type"], "application/json")
        self.assertEqual(response["Access-Control-Allow-Origin"], "*")
        self.assertEqual(
            response["Access-Control-Allow-Headers"],
            settings.ACCESS_CONTROL_ALLOW_HEADERS,
        )
        self.assertEqual(response["Vary"], "Accept-Language, origin")

    @register_resource(Sample500Resource)
    @override_settings(TASTYPIE_FULL_DEBUG=False)
    @patch("django.test.client.Client.store_exc_info", lambda *_, **__: None)
    @patch("django.core.handlers.base.logger.error", lambda *_, **__: None)
    def test_500(self, resource_url):
        response = self.api_client.get("{}/".format(resource_url))
        self.assertHttpApplicationError(response)
        self.assert_common_headers(response)
        self.assertEqual(response["X-RateLimit-Concurrency"], "1")
        self.assertEqual(response["X-RateLimit-Concurrency-Limit"], "10")
        self.assertEqual(response["Content-Type"], "application/json")
        self.assertEqual(response["Access-Control-Allow-Origin"], "*")
        self.assertEqual(
            response["Access-Control-Allow-Headers"],
            settings.ACCESS_CONTROL_ALLOW_HEADERS,
        )
        self.assertEqual(response["Vary"], "Accept-Language, origin")
