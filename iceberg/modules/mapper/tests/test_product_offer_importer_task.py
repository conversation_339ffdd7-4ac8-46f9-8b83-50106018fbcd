from decimal import Decimal

from apps.attributes.exceptions import InvalidAttributeValue
from apps.attributes.models import (
    ProductAttribute,
    ProductAttributeGroup,
    ProductAttributeGroupAssignment,
)
from apps.ice_applications.app_conf_settings import MandatoryOfferFields
from apps.products.cache import VariationConcurrencyLocker
from apps.products.models import ProductOffer, ProductVariation
from apps.tax.models import TaxRateWorkflow
from apps.testing.factories import (
    LocalizedProductInfoFactory,
    MerchantFactory,
    ProductFactory,
    ProductOfferFactory,
    TaxRateAssignmentFactory,
    TaxRateFactory,
)
from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import ValidationError
from django.utils import timezone
from ims.lock import ConcurrencyLockTimeoutError
from ims.tests import BaseTestCase
from mock import patch
from reference.status import PRODUCT_STATUS_ACTIVE

from ..cache import OfferConcurrencyLocker
from ..factories import TransformationLogFactory
from ..tasks import ProductOfferImporter


class TestProductOfferImporter(BaseTestCase):
    def _create_attribute(self, application, entity_kls, value_type="decimal"):
        attr = ProductAttribute.objects.create(
            application=application,
            key="cust-attribute",
            value_type=value_type,
        )
        attribute_group = ProductAttributeGroup.objects.create(
            group_key="group_1",
            application=application,
            entity_type=ContentType.objects.get_for_model(entity_kls),
        )
        ProductAttributeGroupAssignment.objects.create(
            attribute=attr,
            group=attribute_group,
            is_required=False,
        )

    def test_offer_and_variation_are_activated_after_basic_flat_import(self):
        localized_info = LocalizedProductInfoFactory(
            product__status=PRODUCT_STATUS_ACTIVE
        )
        product = localized_info.product
        merchant = MerchantFactory(
            application=product.application,
            default_currency=product.application.default_currency,
            prefered_language=product.application.language,
        )
        offer_data = {
            "gtin": product.gtin,
            "sku": "sku_123",
            "currency": merchant.default_currency.code,
            "language": localized_info.language,
        }
        variation_data = {
            "gtin": product.gtin,
            "parent_sku": offer_data["sku"],
            "sku": "var_sku_123",
            "name": "Ma vâr",
            "price": "10.50",
            "stock": 20,
            "language": localized_info.language,
        }
        context = {
            "application_id": merchant.application_id,
            "merchant_id": merchant.id,
            "ttl": 1,
        }
        ProductOfferImporter().run_import(
            data=offer_data,
            context=context,
        )
        ProductOfferImporter().run_import(
            data=variation_data,
            context=context,
        )
        variation = merchant.product_variations.get()
        self.assertEqual(variation.product_offer.status, "active")
        self.assertEqual(variation.status, "active")

    def test_offer_and_var_have_last_crawled_set_after_creation_by_basic_flat_import(
        self,
    ):  # noqa
        localized_info = LocalizedProductInfoFactory(
            product__status=PRODUCT_STATUS_ACTIVE
        )
        product = localized_info.product
        merchant = MerchantFactory(
            application=product.application,
            default_currency=product.application.default_currency,
            prefered_language=product.application.language,
        )
        offer_data = {
            "gtin": product.gtin,
            "sku": "sku_123",
            "currency": merchant.default_currency.code,
            "language": localized_info.language,
        }
        variation_data = {
            "gtin": product.gtin,
            "parent_sku": offer_data["sku"],
            "sku": "var_sku_123",
            "name": "Ma vâr",
            "price": "10.50",
            "stock": 20,
            "language": localized_info.language,
        }
        context = {
            "application_id": merchant.application_id,
            "merchant_id": merchant.id,
            "ttl": 1,
        }

        before_offer_import = timezone.now()
        ProductOfferImporter().run_import(
            data=offer_data,
            context=context,
        )
        offer = merchant.product_offers.get()
        self.assertGreater(offer.last_crawled, before_offer_import)
        offer_last_crawled = offer.last_crawled
        before_variation_import = timezone.now()
        ProductOfferImporter().run_import(
            data=variation_data,
            context=context,
        )
        variation = merchant.product_variations.get()
        self.assertGreater(variation.last_crawled, before_variation_import)
        # offer last_crawled must not change on variation import
        self.assertEqual(variation.product_offer.last_crawled, offer_last_crawled)

    def test_offer_and_var_have_last_crawled_set_after_creation_by_basic_nested_import(
        self,
    ):  # noqa
        localized_info = LocalizedProductInfoFactory(
            product__status=PRODUCT_STATUS_ACTIVE
        )
        product = localized_info.product
        merchant = MerchantFactory(
            application=product.application,
            default_currency=product.application.default_currency,
            prefered_language=product.application.language,
        )
        offer_data = {
            "gtin": product.gtin,
            "sku": "sku_123",
            "currency": merchant.default_currency.code,
            "variations": [
                {"sku": "var_sku_123", "name": "Ma vâr", "price": "10.50", "stock": 20}
            ],
            "language": localized_info.language,
        }
        context = {
            "application_id": merchant.application_id,
            "merchant_id": merchant.id,
            "ttl": 1,
        }

        before_offer_import = timezone.now()
        ProductOfferImporter().run_import(
            data=offer_data,
            context=context,
        )
        offer = merchant.product_offers.get()
        variation = merchant.product_variations.get()
        self.assertGreater(offer.last_crawled, before_offer_import)
        self.assertGreater(variation.last_crawled, before_offer_import)

    def test_last_crawled_not_updated_when_no_attribute_updated(
        self,
    ):
        localized_info = LocalizedProductInfoFactory(
            product__status=PRODUCT_STATUS_ACTIVE
        )
        product = localized_info.product
        merchant = MerchantFactory(
            application=product.application,
            default_currency=product.application.default_currency,
            prefered_language=product.application.language,
        )
        offer_data = {
            "gtin": product.gtin,
            "sku": "sku_123",
            "currency": merchant.default_currency.code,
            "language": localized_info.language,
            "variations": [
                {"sku": "var_sku_123", "name": "Ma vâr", "price": "10.50", "stock": 20}
            ],
        }
        context = {
            "application_id": merchant.application_id,
            "merchant_id": merchant.id,
            "ttl": 1,
        }
        # creation
        ProductOfferImporter().run_import(
            data=offer_data.copy(),
            context=context,
        )
        offer = merchant.product_offers.get()
        variation = merchant.product_variations.get()
        last_crawled_offer = offer.last_crawled
        last_crawled_variation = variation.last_crawled

        # update
        result = ProductOfferImporter().run_import(
            data=offer_data.copy(),
            context=context,
        )
        self.assertEqual(result.updated_attributes, [])
        offer.refresh_from_db()
        variation.refresh_from_db()
        self.assertEqual(offer.last_crawled, last_crawled_offer)
        self.assertEqual(variation.last_crawled, last_crawled_variation)

    def test_offer_and_variation_have_last_crawled_set_at_creation(
        self,
    ):
        product = ProductFactory(status="active")
        product.localized_infos.create(
            status="visible", language=product.application.language
        )
        merchant = MerchantFactory(
            application=product.application,
            default_currency=product.application.default_currency,
            prefered_language=product.application.language,
        )
        offer_and_var_data = {
            "gtin": product.gtin,
            "sku": "sku_123",
            "currency": merchant.default_currency.code,
            "language": merchant.prefered_language,
            "variations": [
                {"sku": "var_sku_123", "name": "Ma vâr", "price": "10.50", "stock": 20}
            ],
        }
        context = {
            "application_id": merchant.application_id,
            "merchant_id": merchant.id,
            "ttl": 1,
        }
        # creation
        before_import = timezone.now()
        ProductOfferImporter().run_import(
            data=offer_and_var_data.copy(),
            context=context,
        )
        offer = merchant.product_offers.get()
        variation = merchant.product_variations.get()
        self.assertGreater(offer.last_crawled, before_import)
        self.assertGreater(variation.last_crawled, before_import)

    def test_offer_have_last_crawled_set_at_creation(
        self,
    ):
        product = ProductFactory(status="active")
        product.localized_infos.create(
            status="visible", language=product.application.language
        )
        merchant = MerchantFactory(
            application=product.application,
            default_currency=product.application.default_currency,
            prefered_language=product.application.language,
        )
        offer_data = {
            "gtin": product.gtin,
            "sku": "sku_123",
            "currency": merchant.default_currency.code,
            "language": merchant.prefered_language,
        }
        context = {
            "application_id": merchant.application_id,
            "merchant_id": merchant.id,
            "ttl": 1,
        }
        # creation
        before_import = timezone.now()
        ProductOfferImporter().run_import(
            data=offer_data.copy(),
            context=context,
        )
        offer = merchant.product_offers.get()
        self.assertGreater(offer.last_crawled, before_import)

    def test_import_product_offer_with_invalid_offer_cust_attr_value(self):
        localized_info = LocalizedProductInfoFactory(
            product__status=PRODUCT_STATUS_ACTIVE
        )
        product = localized_info.product
        merchant = MerchantFactory(
            application=product.application,
            default_currency=product.application.default_currency,
            prefered_language=product.application.language,
        )
        self._create_attribute(product.application, ProductOffer)

        offer_data = {
            "gtin": product.gtin,
            "sku": "sku_123",
            "currency": merchant.default_currency.code,
            "language": localized_info.language,
            "cust-attribute": "invalid",
        }
        context = {
            "application_id": product.application_id,
            "merchant_id": merchant.id,
            "ttl": 1,
        }
        with self.assertRaises(InvalidAttributeValue):
            ProductOfferImporter().run_import(
                data=offer_data,
                context=context,
            )

    def test_import_product_offer_with_invalid_variation_cust_attr_value(self):
        localized_info = LocalizedProductInfoFactory(
            product__status=PRODUCT_STATUS_ACTIVE
        )
        product = localized_info.product
        offer = ProductOfferFactory.create_for_application(
            product.application,
            product=product,
        )
        self._create_attribute(product.application, ProductVariation)

        variation_data = {
            "gtin": product.gtin,
            "parent_sku": offer.sku,
            "sku": "var_sku_123",
            "name": "Ma vâr",
            "price": "10.50",
            "stock": 20,
            "language": localized_info.language,
            "cust-attribute": "invalid",
        }
        context = {
            "application_id": product.application_id,
            "merchant_id": offer.merchant_id,
            "ttl": 1,
        }
        with self.assertRaises(InvalidAttributeValue):
            ProductOfferImporter().run_import(
                data=variation_data,
                context=context,
            )

    def test_offer_import_on_locked_sku_raises(self):
        merchant = MerchantFactory()
        log = TransformationLogFactory(
            application=merchant.application,
            merchant=merchant,
        )
        importer = ProductOfferImporter()
        data = {
            "sku": "8862",
            "name": "Pasteque 62",
        }
        context = {
            "can_update": True,
            "izberg_entity": "offer",
            "application_id": merchant.application.id,
            "language": merchant.prefered_language,
            "ttl": 3,
            "log_id": log.id,
            "timestamp": timezone.now(),
            "merchant_id": merchant.id,
            "can_create": True,
        }
        with OfferConcurrencyLocker(merchant.id, data["sku"]):
            with self.assertRaises(ConcurrencyLockTimeoutError):
                importer.run_import(data, context)

    def test_offer_import_on_locked_sku_raises_for_flat_variation(self):
        merchant = MerchantFactory()
        log = TransformationLogFactory(
            application=merchant.application,
            merchant=merchant,
        )
        importer = ProductOfferImporter()
        data = {
            "parent_sku": "P-8862",
            "sku": "8862",
            "name": "Pasteque 62",
        }
        context = {
            "can_update": True,
            "izberg_entity": "offer",
            "application_id": merchant.application.id,
            "language": merchant.prefered_language,
            "ttl": 3,
            "log_id": log.id,
            "timestamp": timezone.now(),
            "merchant_id": merchant.id,
            "can_create": True,
        }
        with VariationConcurrencyLocker(merchant.id, data["sku"]):
            with self.assertRaises(ConcurrencyLockTimeoutError):
                importer.run_import(data, context)

    @patch("mapper.gateways.async_output.AsyncIterator.send")
    def test_flat_variation_import_on_locked_parent_sku_is_delayed(self, mocked_send):
        offer = ProductOfferFactory()
        merchant = offer.merchant
        log = TransformationLogFactory(
            application=merchant.application,
            merchant=merchant,
        )
        importer = ProductOfferImporter()
        data = {
            "parent_sku": offer.sku,
            "sku": "8862",
            "name": "Pasteque 62",
        }
        context = {
            "can_update": True,
            "izberg_entity": "offer",
            "application_id": merchant.application.id,
            "language": merchant.prefered_language,
            "ttl": 5,
            "log_id": log.id,
            "timestamp": timezone.now(),
            "merchant_id": merchant.id,
            "can_create": True,
        }

        with OfferConcurrencyLocker(merchant.id, data["parent_sku"]):
            import_result = importer.run_import(data, context)

        self.assertTrue(import_result.delayed)
        self.assertFalse(import_result.succeeded)
        self.assertEqual(
            import_result.error_code,
            "MAPPER.PRODUCT_OFFER_UPDATE_IN_PROGRESS",
        )
        self.assertEqual(import_result.ttl, 4)
        mocked_send.assert_called_with(
            [
                (
                    "valid",
                    {
                        "parent_sku": offer.sku,
                        "sku": "8862",
                        "name": "Pasteque 62",
                    },
                )
            ],
            ttl=4,
        )

    @patch("mapper.gateways.async_output.AsyncIterator.send")
    def test_flat_variation_import_on_locked_parent_sku_is_delayed_ttl_1_stays_at_1(
        self, mocked_send
    ):
        offer = ProductOfferFactory()
        merchant = offer.merchant
        log = TransformationLogFactory(
            application=merchant.application,
            merchant=merchant,
        )
        importer = ProductOfferImporter()
        data = {
            "parent_sku": offer.sku,
            "sku": "8862",
            "name": "Pasteque 62",
        }
        context = {
            "can_update": True,
            "izberg_entity": "offer",
            "application_id": merchant.application.id,
            "language": merchant.prefered_language,
            "ttl": 1,
            "log_id": log.id,
            "timestamp": timezone.now(),
            "merchant_id": merchant.id,
            "can_create": True,
        }

        with OfferConcurrencyLocker(merchant.id, data["parent_sku"]):
            import_result = importer.run_import(data, context)

        self.assertTrue(import_result.delayed)
        self.assertFalse(import_result.succeeded)
        self.assertEqual(
            import_result.error_code,
            "MAPPER.PRODUCT_OFFER_UPDATE_IN_PROGRESS",
        )
        self.assertEqual(import_result.ttl, 1)
        mocked_send.assert_called_with(
            [
                (
                    "valid",
                    {
                        "parent_sku": offer.sku,
                        "sku": "8862",
                        "name": "Pasteque 62",
                    },
                )
            ],
            ttl=1,
        )

    def test_offer_import_on_locked_sku_raises_for_nested_variation(self):
        localized_product = LocalizedProductInfoFactory(
            product__status=PRODUCT_STATUS_ACTIVE
        )
        product = localized_product.product
        merchant = MerchantFactory(
            application=product.application,
            default_currency=product.application.default_currency,
        )
        log = TransformationLogFactory(
            application=merchant.application,
            merchant=merchant,
        )
        importer = ProductOfferImporter()
        data = {
            "gtin": product.gtin,
            "sku": "8862",
            "name": "Pasteque 62",
            "variations": [
                {"sku": "8862-v", "name": "Pasteque 62 verte"},
            ],
        }
        context = {
            "can_update": True,
            "izberg_entity": "offer",
            "application_id": merchant.application.id,
            "language": merchant.prefered_language,
            "ttl": 3,
            "log_id": log.id,
            "timestamp": timezone.now(),
            "merchant_id": merchant.id,
            "can_create": True,
        }
        with VariationConcurrencyLocker(merchant.id, data["variations"][0]["sku"]):
            with self.assertRaises(ConcurrencyLockTimeoutError):
                importer.run_import(data, context)

    def test_import_offer_with_tax_origin_fields(self):
        localized_product = LocalizedProductInfoFactory(
            product__status=PRODUCT_STATUS_ACTIVE
        )
        product = localized_product.product
        merchant = MerchantFactory.create_for_application(product.application)

        offer_data = {
            "gtin": product.gtin,
            "sku": "sku_123",
            "currency": merchant.default_currency.code,
            "language": localized_product.language,
            "shipped_from_eu_vat_zone": True,
            "shipped_from_country": "FR",
            "shipped_from_region": "bla",
        }
        context = {
            "application_id": merchant.application_id,
            "merchant_id": merchant.id,
            "ttl": 1,
        }
        ret = ProductOfferImporter().run_import(
            data=offer_data,
            context=context,
        )

        self.assertTrue(ret.succeeded)
        offer = product.product_offers.get()
        self.assertTrue(offer.shipped_from_eu_vat_zone)
        self.assertEqual(offer.shipped_from_country.code, "FR")
        self.assertEqual(offer.shipped_from_region, "bla")

    def test_offer_price_and_stock_are_ignored_in_favor_of_nested_variations(
        self,
    ):
        product = ProductFactory(status="active")
        product.localized_infos.create(
            status="visible", language=product.application.language
        )
        merchant = MerchantFactory(
            application=product.application,
            default_currency=product.application.default_currency,
            prefered_language=product.application.language,
        )
        offer_and_var_data = {
            "gtin": product.gtin,
            "sku": "sku_123",
            "currency": merchant.default_currency.code,
            "language": merchant.prefered_language,
            "stock": 10,  # should be ignored
            "price": "15.00",  # should be ignored
            "variations": [
                {"sku": "var_sku_123", "name": "Ma vâr", "price": "10.50", "stock": 20}
            ],
        }
        context = {
            "application_id": merchant.application_id,
            "merchant_id": merchant.id,
            "ttl": 1,
        }
        ProductOfferImporter().run_import(
            data=offer_and_var_data.copy(),
            context=context,
        )
        variation = merchant.product_variations.get()
        self.assertEqual(variation.stock, 20)
        self.assertEqual(variation.price, Decimal("10.50"))
        self.assertTrue(variation.product_offer.is_abstract)
        self.assertEqual(variation.product_offer.stock, 20)
        self.assertEqual(variation.product_offer.availability, "in_stock")
        self.assertEqual(variation.product_offer.price, Decimal("10.50"))

    def test_importing_existing_offer_with_nothing_updated_is_not_saved(self):
        localized_info = LocalizedProductInfoFactory(
            product__status=PRODUCT_STATUS_ACTIVE
        )
        product = localized_info.product
        offer = ProductOfferFactory.create_for_application(
            product.application,
            product=product,
            status="active",
            availability="in_stock",
            stock=20,
        )

        offer_data = {
            "gtin": product.gtin,
            "sku": offer.sku,
            "price": offer.price,
            "stock": offer.stock,
            "language": localized_info.language,
        }
        context = {
            "application_id": product.application_id,
            "merchant_id": offer.merchant_id,
            "ttl": 1,
        }
        with self.withAssertNonSelectNumQueriesEqual(0):
            event_report = ProductOfferImporter().run_import(
                data=offer_data,
                context=context,
            )
            self.assertEqual(event_report.updated_attributes, [])

    def test_activate_existing_draft_offer_logs_status_as_updated_attribute(self):
        localized_info = LocalizedProductInfoFactory(
            product__status=PRODUCT_STATUS_ACTIVE
        )
        product = localized_info.product
        offer = ProductOfferFactory.create_for_application(
            product.application,
            product=product,
            status="draft",
            availability="in_stock",
            stock=20,
        )

        offer_data = {
            "gtin": product.gtin,
            "sku": offer.sku,
            "price": offer.price,
            "stock": offer.stock,
            "language": localized_info.language,
        }
        context = {
            "application_id": product.application_id,
            "merchant_id": offer.merchant_id,
            "ttl": 1,
        }
        with self.withAssertNonSelectNumQueriesEqual(2):
            event_report = ProductOfferImporter().run_import(
                data=offer_data,
                context=context,
            )
            self.assertEqual(event_report.obj.status, "active")
            self.assertEqual(event_report.previous_status, "draft")
            self.assertEqual(event_report.updated_attributes, ["status"])


class TaxRatesHandlingCreationTestCase(BaseTestCase):
    def test_tax_rates_not_provided_no_default_not_mandatory(self):
        localized_product = LocalizedProductInfoFactory(
            product__status=PRODUCT_STATUS_ACTIVE
        )
        product = localized_product.product
        merchant = MerchantFactory.create_for_application(product.application)

        offer_data = {
            "gtin": product.gtin,
            "sku": "sku_123",
            "currency": merchant.default_currency.code,
            "language": localized_product.language,
        }
        context = {
            "application_id": merchant.application_id,
            "merchant_id": merchant.id,
            "ttl": 1,
        }
        ret = ProductOfferImporter().run_import(
            data=offer_data,
            context=context,
        )

        self.assertTrue(ret.succeeded)
        offer = product.product_offers.get()
        self.assertEqual(offer.tax_rates, [])

    def test_tax_rates_not_provided_no_default_mandatory(self):
        localized_product = LocalizedProductInfoFactory(
            product__status=PRODUCT_STATUS_ACTIVE
        )
        product = localized_product.product
        merchant = MerchantFactory.create_for_application(product.application)
        product.application.set_setting(MandatoryOfferFields, ["tax_rates"])
        offer_data = {
            "gtin": product.gtin,
            "sku": "sku_123",
            "currency": merchant.default_currency.code,
            "language": localized_product.language,
        }
        context = {
            "application_id": merchant.application_id,
            "merchant_id": merchant.id,
            "ttl": 1,
        }

        with self.assertRaises(ValidationError) as err:
            ProductOfferImporter().run_import(
                data=offer_data,
                context=context,
            )

        self.assertEqual(
            err.exception.message_dict,
            {
                "__all__": [
                    "Please provide a tax rate for at least one zone. You need to"
                    " activate at least one zone in your merchant tax config page"
                    ", then map the tax zone or set default value for the zone"
                ]
            },
        )
        self.assertFalse(product.product_offers.exists())

    def test_tax_rates_not_provided_with_default_mandatory(self):
        localized_product = LocalizedProductInfoFactory(
            product__status=PRODUCT_STATUS_ACTIVE
        )
        product = localized_product.product
        merchant = MerchantFactory.create_for_application(product.application)
        product.application.set_setting(MandatoryOfferFields, ["tax_rates"])
        ass = TaxRateAssignmentFactory(application=merchant.application)
        merchant.tax_settings.enabled_tax_zones = [ass.tax_zone.zone_key]
        merchant.tax_settings.default_offer_tax_rates = [
            {"zone_key": ass.tax_zone.zone_key, "rate_key": ass.tax_rate.rate_key}
        ]
        merchant.tax_settings.save()
        offer_data = {
            "gtin": product.gtin,
            "sku": "sku_123",
            "currency": merchant.default_currency.code,
            "language": localized_product.language,
        }
        context = {
            "application_id": merchant.application_id,
            "merchant_id": merchant.id,
            "ttl": 1,
        }
        ret = ProductOfferImporter().run_import(
            data=offer_data,
            context=context,
        )

        self.assertTrue(ret.succeeded)
        offer = product.product_offers.get()
        self.assertEqual(
            offer.tax_rates,
            [{"zone_key": ass.tax_zone.zone_key, "rate_key": ass.tax_rate.rate_key}],
        )

    def test_tax_rates_partially_provided_with_default(self):
        localized_product = LocalizedProductInfoFactory(
            product__status=PRODUCT_STATUS_ACTIVE
        )
        product = localized_product.product
        merchant = MerchantFactory.create_for_application(product.application)
        ass = TaxRateAssignmentFactory(application=product.application)
        ass2 = TaxRateAssignmentFactory(application=product.application)
        rate = TaxRateFactory(
            application=product.application,
            status=TaxRateWorkflow.ACTIVE,
            country=ass.tax_zone.country,
        )
        merchant.tax_settings.enabled_tax_zones = [
            ass.tax_zone.zone_key,
            ass2.tax_zone.zone_key,
        ]
        merchant.tax_settings.default_offer_tax_rates = [
            {"zone_key": ass.tax_zone.zone_key, "rate_key": ass.tax_rate.rate_key},
            {"zone_key": ass2.tax_zone.zone_key, "rate_key": ass2.tax_rate.rate_key},
        ]
        merchant.tax_settings.save()
        offer_data = {
            "gtin": product.gtin,
            "sku": "sku_123",
            "currency": merchant.default_currency.code,
            "language": localized_product.language,
            f"tax_rate_for_zone_{ass.tax_zone.zone_key}": rate.rate_key,
            "shipped_from_country": ass.tax_zone.country.code,
        }
        context = {
            "application_id": merchant.application_id,
            "merchant_id": merchant.id,
            "ttl": 1,
        }
        ret = ProductOfferImporter().run_import(
            data=offer_data,
            context=context,
        )

        self.assertTrue(ret.succeeded)
        offer = product.product_offers.get()
        self.assertEqual(
            offer.tax_rates,
            [
                {"zone_key": ass.tax_zone.zone_key, "rate_key": rate.rate_key},
                {
                    "zone_key": ass2.tax_zone.zone_key,
                    "rate_key": ass2.tax_rate.rate_key,
                },
            ],
        )

    def test_tax_rates_partially_provided_as_unset(self):
        localized_product = LocalizedProductInfoFactory(
            product__status=PRODUCT_STATUS_ACTIVE
        )
        product = localized_product.product
        merchant = MerchantFactory.create_for_application(product.application)
        ass = TaxRateAssignmentFactory(application=product.application)
        ass2 = TaxRateAssignmentFactory(application=product.application)
        merchant.tax_settings.enabled_tax_zones = [
            ass.tax_zone.zone_key,
            ass2.tax_zone.zone_key,
        ]
        merchant.tax_settings.default_offer_tax_rates = [
            {"zone_key": ass.tax_zone.zone_key, "rate_key": ass.tax_rate.rate_key},
            {"zone_key": ass2.tax_zone.zone_key, "rate_key": ass2.tax_rate.rate_key},
        ]
        merchant.tax_settings.save()
        offer_data = {
            "gtin": product.gtin,
            "sku": "sku_123",
            "currency": merchant.default_currency.code,
            "language": localized_product.language,
            f"tax_rate_for_zone_{ass.tax_zone.zone_key}": "_UNSET_",
            "shipped_from_country": ass.tax_zone.country.code,
        }
        context = {
            "application_id": merchant.application_id,
            "merchant_id": merchant.id,
            "ttl": 1,
        }
        ret = ProductOfferImporter().run_import(
            data=offer_data,
            context=context,
        )

        self.assertTrue(ret.succeeded)
        offer = product.product_offers.get()
        self.assertEqual(
            offer.tax_rates,
            [{"zone_key": ass2.tax_zone.zone_key, "rate_key": ass2.tax_rate.rate_key}],
        )

    def test_tax_rates_provided_as_none(self):
        localized_product = LocalizedProductInfoFactory(
            product__status=PRODUCT_STATUS_ACTIVE
        )
        product = localized_product.product
        merchant = MerchantFactory.create_for_application(product.application)
        ass = TaxRateAssignmentFactory(application=product.application)
        merchant.tax_settings.enabled_tax_zones = [
            ass.tax_zone.zone_key,
        ]
        merchant.tax_settings.default_offer_tax_rates = [
            {"zone_key": ass.tax_zone.zone_key, "rate_key": ass.tax_rate.rate_key},
        ]
        merchant.tax_settings.save()
        offer_data = {
            "gtin": product.gtin,
            "sku": "sku_123",
            "currency": merchant.default_currency.code,
            "language": localized_product.language,
            f"tax_rate_for_zone_{ass.tax_zone.zone_key}": None,
            "shipped_from_country": ass.tax_zone.country.code,
        }
        context = {
            "application_id": merchant.application_id,
            "merchant_id": merchant.id,
            "ttl": 1,
        }
        with self.assertRaises(ValidationError) as err:
            ProductOfferImporter().run_import(
                data=offer_data,
                context=context,
            )

        self.assertEqual(
            err.exception.message_dict,
            {
                f"tax_rate_for_zone_{ass.tax_zone.zone_key}": [
                    "Null or empty values are not allowed. You either have"
                    " a blank value in your source file, or a value is "
                    "not mapped correctly. Use _UNSET_ to blank the rate, "
                    "_DEFAULT_ to use the default value or unmap the column."
                ]
            },
        )
        self.assertFalse(product.product_offers.exists())


class TaxRatesHandlingUpdateTestCase(BaseTestCase):
    def test_tax_rates_not_provided_no_default_not_mandatory(self):
        localized_product = LocalizedProductInfoFactory(
            product__status=PRODUCT_STATUS_ACTIVE
        )
        product = localized_product.product
        offer = ProductOfferFactory.create_for_application(
            product.application,
            product=product,
        )

        offer_data = {
            "gtin": product.gtin,
            "sku": offer.sku,
            "currency": offer.merchant.default_currency.code,
            "language": localized_product.language,
        }
        context = {
            "application_id": offer.application_id,
            "merchant_id": offer.merchant_id,
            "ttl": 1,
        }
        ret = ProductOfferImporter().run_import(
            data=offer_data,
            context=context,
        )

        self.assertTrue(ret.succeeded)
        offer = product.product_offers.get()
        self.assertEqual(offer.tax_rates, [])

    def test_tax_rates_not_provided_no_default_mandatory(self):
        localized_product = LocalizedProductInfoFactory(
            product__status=PRODUCT_STATUS_ACTIVE
        )
        product = localized_product.product
        offer = ProductOfferFactory.create_for_application(
            product.application,
            product=product,
        )
        product.application.set_setting(MandatoryOfferFields, ["tax_rates"])
        offer_data = {
            "gtin": product.gtin,
            "sku": offer.sku,
            "currency": offer.merchant.default_currency.code,
            "language": localized_product.language,
        }
        context = {
            "application_id": offer.merchant.application_id,
            "merchant_id": offer.merchant_id,
            "ttl": 1,
        }

        with self.assertRaises(ValidationError) as err:
            ProductOfferImporter().run_import(
                data=offer_data,
                context=context,
            )

        self.assertEqual(
            err.exception.message_dict,
            {
                "__all__": [
                    "Please provide a tax rate for at least one zone. You need to"
                    " activate at least one zone in your merchant tax config page"
                    ", then map the tax zone or set default value for the zone"
                ]
            },
        )

    def test_tax_rates_not_provided_with_default_mandatory(self):
        localized_product = LocalizedProductInfoFactory(
            product__status=PRODUCT_STATUS_ACTIVE
        )
        product = localized_product.product
        offer = ProductOfferFactory.create_for_application(
            product.application,
            product=product,
        )
        product.application.set_setting(MandatoryOfferFields, ["tax_rates"])
        ass = TaxRateAssignmentFactory(application=offer.merchant.application)
        offer.merchant.tax_settings.enabled_tax_zones = [ass.tax_zone.zone_key]
        offer.merchant.tax_settings.default_offer_tax_rates = [
            {"zone_key": ass.tax_zone.zone_key, "rate_key": ass.tax_rate.rate_key}
        ]
        offer.merchant.tax_settings.save()
        offer_data = {
            "gtin": product.gtin,
            "sku": offer.sku,
            "currency": offer.merchant.default_currency.code,
            "language": localized_product.language,
        }
        context = {
            "application_id": offer.merchant.application_id,
            "merchant_id": offer.merchant_id,
            "ttl": 1,
        }
        with self.assertRaises(ValidationError) as err:
            ProductOfferImporter().run_import(
                data=offer_data,
                context=context,
            )

        self.assertEqual(
            err.exception.message_dict,
            {
                "__all__": [
                    "Please provide a tax rate for at least one zone. You need to"
                    " activate at least one zone in your merchant tax config page"
                    ", then map the tax zone or set default value for the zone"
                ]
            },
        )

    def test_tax_rates_partially_provided_with_default(self):
        localized_product = LocalizedProductInfoFactory(
            product__status=PRODUCT_STATUS_ACTIVE
        )
        product = localized_product.product
        offer = ProductOfferFactory.create_for_application(
            product.application,
            product=product,
        )
        ass = TaxRateAssignmentFactory(application=product.application)
        ass2 = TaxRateAssignmentFactory(application=product.application)
        rate = TaxRateFactory(
            application=product.application,
            status=TaxRateWorkflow.ACTIVE,
            country=ass.tax_zone.country,
        )
        offer.merchant.tax_settings.enabled_tax_zones = [
            ass.tax_zone.zone_key,
            ass2.tax_zone.zone_key,
        ]
        offer.merchant.tax_settings.default_offer_tax_rates = [
            {"zone_key": ass.tax_zone.zone_key, "rate_key": ass.tax_rate.rate_key},
            {"zone_key": ass2.tax_zone.zone_key, "rate_key": ass2.tax_rate.rate_key},
        ]
        offer.merchant.tax_settings.save()
        offer_data = {
            "gtin": product.gtin,
            "sku": offer.sku,
            "currency": offer.merchant.default_currency.code,
            "language": localized_product.language,
            f"tax_rate_for_zone_{ass.tax_zone.zone_key}": rate.rate_key,
            "shipped_from_country": ass.tax_zone.country.code,
        }
        context = {
            "application_id": offer.merchant.application_id,
            "merchant_id": offer.merchant_id,
            "ttl": 1,
        }
        ret = ProductOfferImporter().run_import(
            data=offer_data,
            context=context,
        )

        self.assertTrue(ret.succeeded)
        offer = product.product_offers.get()
        self.assertEqual(
            offer.tax_rates,
            [
                {"zone_key": ass.tax_zone.zone_key, "rate_key": rate.rate_key},
            ],
        )

    def test_tax_rates_partially_provided_as_unset(self):
        localized_product = LocalizedProductInfoFactory(
            product__status=PRODUCT_STATUS_ACTIVE
        )
        product = localized_product.product
        offer = ProductOfferFactory.create_for_application(
            product.application,
            product=product,
        )
        ass = TaxRateAssignmentFactory(application=product.application)
        ass2 = TaxRateAssignmentFactory(application=product.application)
        offer.merchant.tax_settings.enabled_tax_zones = [
            ass.tax_zone.zone_key,
            ass2.tax_zone.zone_key,
        ]
        offer.merchant.tax_settings.default_offer_tax_rates = [
            {"zone_key": ass.tax_zone.zone_key, "rate_key": ass.tax_rate.rate_key},
            {"zone_key": ass2.tax_zone.zone_key, "rate_key": ass2.tax_rate.rate_key},
        ]
        offer.merchant.tax_settings.save()
        offer_data = {
            "gtin": product.gtin,
            "sku": offer.sku,
            "currency": offer.merchant.default_currency.code,
            "language": localized_product.language,
            f"tax_rate_for_zone_{ass.tax_zone.zone_key}": "_UNSET_",
            "shipped_from_country": ass.tax_zone.country.code,
        }
        context = {
            "application_id": offer.merchant.application_id,
            "merchant_id": offer.merchant.id,
            "ttl": 1,
        }
        ret = ProductOfferImporter().run_import(
            data=offer_data,
            context=context,
        )

        self.assertTrue(ret.succeeded)
        offer = product.product_offers.get()
        self.assertEqual(offer.tax_rates, [])

    def test_tax_rates_provided_as_none(self):
        localized_product = LocalizedProductInfoFactory(
            product__status=PRODUCT_STATUS_ACTIVE
        )
        product = localized_product.product
        offer = ProductOfferFactory.create_for_application(
            product.application,
            product=product,
        )
        ass = TaxRateAssignmentFactory(application=product.application)
        offer.merchant.tax_settings.enabled_tax_zones = [
            ass.tax_zone.zone_key,
        ]
        offer.merchant.tax_settings.default_offer_tax_rates = [
            {"zone_key": ass.tax_zone.zone_key, "rate_key": ass.tax_rate.rate_key},
        ]
        offer.merchant.tax_settings.save()
        offer_data = {
            "gtin": product.gtin,
            "sku": offer.sku,
            "currency": offer.merchant.default_currency.code,
            "language": localized_product.language,
            f"tax_rate_for_zone_{ass.tax_zone.zone_key}": None,
            "shipped_from_country": ass.tax_zone.country.code,
        }
        context = {
            "application_id": offer.merchant.application_id,
            "merchant_id": offer.merchant.id,
            "ttl": 1,
        }
        with self.assertRaises(ValidationError) as err:
            ProductOfferImporter().run_import(
                data=offer_data,
                context=context,
            )

        self.assertEqual(
            err.exception.message_dict,
            {
                f"tax_rate_for_zone_{ass.tax_zone.zone_key}": [
                    "Null or empty values are not allowed. You either have"
                    " a blank value in your source file, or a value is "
                    "not mapped correctly. Use _UNSET_ to blank the rate, "
                    "_DEFAULT_ to use the default value or unmap the column."
                ]
            },
        )

    def test_unset_already_filled_rate(self):
        localized_product = LocalizedProductInfoFactory(
            product__status=PRODUCT_STATUS_ACTIVE
        )
        product = localized_product.product
        ass = TaxRateAssignmentFactory(application=product.application)
        merchant = MerchantFactory.create_for_application(product.application)
        merchant.tax_settings.enabled_tax_zones = [
            ass.tax_zone.zone_key,
        ]
        merchant.tax_settings.save()
        offer = ProductOfferFactory.create_for_merchant(
            merchant,
            product=product,
            tax_rates=[
                {
                    "zone_key": ass.tax_zone.zone_key,
                    "rate_key": ass.tax_rate.rate_key,
                }
            ],
        )

        offer_data = {
            "gtin": product.gtin,
            "sku": offer.sku,
            "currency": offer.merchant.default_currency.code,
            "language": localized_product.language,
            f"tax_rate_for_zone_{ass.tax_zone.zone_key}": "_UNSET_",
            "shipped_from_country": ass.tax_zone.country.code,
        }
        context = {
            "application_id": merchant.application_id,
            "merchant_id": merchant.id,
            "ttl": 1,
        }
        ret = ProductOfferImporter().run_import(
            data=offer_data,
            context=context,
        )

        self.assertTrue(ret.succeeded)
        offer = product.product_offers.get()
        self.assertEqual(offer.tax_rates, [])

    def test_default_set_rate_to_default(self):
        localized_product = LocalizedProductInfoFactory(
            product__status=PRODUCT_STATUS_ACTIVE
        )
        product = localized_product.product
        ass = TaxRateAssignmentFactory(application=product.application)
        merchant = MerchantFactory.create_for_application(product.application)
        merchant.tax_settings.enabled_tax_zones = [
            ass.tax_zone.zone_key,
        ]
        merchant.tax_settings.default_offer_tax_rates = [
            {
                "zone_key": ass.tax_zone.zone_key,
                "rate_key": ass.tax_rate.rate_key,
            }
        ]
        merchant.tax_settings.save()
        offer = ProductOfferFactory.create_for_merchant(
            merchant, product=product, tax_rates=[]
        )

        offer_data = {
            "gtin": product.gtin,
            "sku": offer.sku,
            "currency": offer.merchant.default_currency.code,
            "language": localized_product.language,
            f"tax_rate_for_zone_{ass.tax_zone.zone_key}": "_DEFAULT_",
            "shipped_from_country": ass.tax_zone.country.code,
        }
        context = {
            "application_id": merchant.application_id,
            "merchant_id": merchant.id,
            "ttl": 1,
        }
        ret = ProductOfferImporter().run_import(
            data=offer_data,
            context=context,
        )

        self.assertTrue(ret.succeeded)
        offer = product.product_offers.get()
        self.assertEqual(
            offer.tax_rates,
            [
                {
                    "zone_key": ass.tax_zone.zone_key,
                    "rate_key": ass.tax_rate.rate_key,
                }
            ],
        )

    def test_empty_default_unset_already_filled_rate(self):
        localized_product = LocalizedProductInfoFactory(
            product__status=PRODUCT_STATUS_ACTIVE
        )
        product = localized_product.product
        ass = TaxRateAssignmentFactory(application=product.application)
        merchant = MerchantFactory.create_for_application(product.application)
        merchant.tax_settings.enabled_tax_zones = [
            ass.tax_zone.zone_key,
        ]
        merchant.tax_settings.default_offer_tax_rates = []
        merchant.tax_settings.save()
        offer = ProductOfferFactory.create_for_merchant(
            merchant,
            product=product,
            tax_rates=[
                {
                    "zone_key": ass.tax_zone.zone_key,
                    "rate_key": ass.tax_rate.rate_key,
                }
            ],
        )

        offer_data = {
            "gtin": product.gtin,
            "sku": offer.sku,
            "currency": offer.merchant.default_currency.code,
            "language": localized_product.language,
            f"tax_rate_for_zone_{ass.tax_zone.zone_key}": "_DEFAULT_",
            "shipped_from_country": ass.tax_zone.country.code,
        }
        context = {
            "application_id": merchant.application_id,
            "merchant_id": merchant.id,
            "ttl": 1,
        }
        ret = ProductOfferImporter().run_import(
            data=offer_data,
            context=context,
        )

        self.assertTrue(ret.succeeded)
        offer = product.product_offers.get()
        self.assertEqual(offer.tax_rates, [])

    def test_tax_rates_provided_multiple_import_update_offer(self):
        localized_product = LocalizedProductInfoFactory(
            product__status=PRODUCT_STATUS_ACTIVE
        )
        product = localized_product.product
        offer = ProductOfferFactory.create_for_application(
            product.application,
            product=product,
        )
        ass = TaxRateAssignmentFactory(
            application=product.application,
            tax_zone__zone_key="zone_www",
            tax_rate__rate_key="vat_www_exempt",
        )
        ass2 = TaxRateAssignmentFactory(
            application=product.application,
            tax_zone__zone_key="zone_fr",
            tax_rate__rate_key="vat_fr_standard",
        )

        offer.merchant.tax_settings.enabled_tax_zones = [
            ass.tax_zone.zone_key,
            ass2.tax_zone.zone_key,
        ]
        offer.merchant.tax_settings.default_offer_tax_rates = [
            {"zone_key": ass.tax_zone.zone_key, "rate_key": ass.tax_rate.rate_key},
            {"zone_key": ass2.tax_zone.zone_key, "rate_key": ass2.tax_rate.rate_key},
        ]
        offer.merchant.tax_settings.save()
        offer_data = {
            "gtin": product.gtin,
            "sku": offer.sku,
            "currency": offer.merchant.default_currency.code,
            "language": localized_product.language,
            f"tax_rate_for_zone_{ass.tax_zone.zone_key}": ass.tax_rate.rate_key,
            "shipped_from_country": ass.tax_zone.country.code,
        }
        context = {
            "application_id": offer.merchant.application_id,
            "merchant_id": offer.merchant_id,
            "ttl": 1,
        }

        ret = ProductOfferImporter().run_import(
            data=offer_data,
            context=context,
        )
        self.assertTrue(ret.succeeded)
        offer = product.product_offers.get()

        self.assertEqual(
            offer.tax_rates,
            [
                {
                    "zone_key": ass.tax_zone.zone_key,
                    "rate_key": ass.tax_rate.rate_key,
                }
            ],
        )

        offer_data = {
            "gtin": product.gtin,
            "sku": offer.sku,
            "language": localized_product.language,
            f"tax_rate_for_zone_{ass2.tax_zone.zone_key}": ass2.tax_rate.rate_key,
        }
        ret = ProductOfferImporter().run_import(
            data=offer_data,
            context=context,
        )
        self.assertTrue(ret.succeeded)
        offer = product.product_offers.get()
        self.assertEqual(
            offer.tax_rates,
            [
                {
                    "zone_key": ass.tax_zone.zone_key,
                    "rate_key": ass.tax_rate.rate_key,
                },
                {
                    "zone_key": ass2.tax_zone.zone_key,
                    "rate_key": ass2.tax_rate.rate_key,
                },
            ],
        )
