# -*- coding: utf-8 -*-

from apps.testing.factories import ProductOfferFactory, ProductVariationFactory
from django.utils import timezone
from ims.tests import BaseTestCase
from mapper.models import Configuration
from mapper.utils import InMemoryTransformationLog
from mock import patch

from ..factories import (
    ConfigurationFactory,
    MerchantTransformationLogFactory,
    TransformationLogFactory,
)


class LastCrawledHandlerPendingSetsTestCase(BaseTestCase):
    def test_sku_value_of_valid_offer_feed_item_is_added_to_pending_set(self):
        log = MerchantTransformationLogFactory(configuration__izberg_entity="offer")
        conf = log.configuration
        item = {"sku": "SKÜ-100", "stock": 0}
        self.assertEqual(conf.last_crawled_handler._pending_offer_skus, {})
        conf._handle_valid_item(
            result_item=item,
            skip_not_modified=False,
            data_validation=False,
            log=log,
            nb_items=1,
            matcher=conf.matcher,
        )
        self.assertEqual(
            conf.last_crawled_handler._pending_offer_skus,
            {(log.merchant_id, None): {item["sku"]}},
        )

    def test_sku_value_of_valid_product_and_offer_feed_item_is_added_to_pending_set(
        self,
    ):
        log = MerchantTransformationLogFactory(
            configuration__izberg_entity="product_and_offer"
        )
        conf = log.configuration
        item = {"sku": "SKÜ-100", "stock": 0}
        self.assertEqual(conf.last_crawled_handler._pending_offer_skus, {})
        conf._handle_valid_item(
            result_item=item,
            skip_not_modified=False,
            data_validation=False,
            log=log,
            nb_items=1,
            matcher=conf.matcher,
        )
        self.assertEqual(
            conf.last_crawled_handler._pending_offer_skus,
            {(log.merchant_id, None): {item["sku"]}},
        )

    def test_parent_sku_and_sku_value_of_valid_offer_feed_item_are_added_to_pending_sets(
        self,
    ):
        log = MerchantTransformationLogFactory(configuration__izberg_entity="offer")
        conf = log.configuration
        item = {"parent_sku": "SKÜ-100", "sku": "SKÜ-100-M", "stock": 0}
        self.assertEqual(conf.last_crawled_handler._pending_offer_skus, {})
        self.assertEqual(conf.last_crawled_handler._pending_variation_skus, {})
        conf._handle_valid_item(
            result_item=item,
            skip_not_modified=False,
            data_validation=False,
            log=log,
            nb_items=1,
            matcher=conf.matcher,
        )
        self.assertEqual(
            conf.last_crawled_handler._pending_offer_skus,
            {(log.merchant_id, None): {item["parent_sku"]}},
        )
        self.assertEqual(
            conf.last_crawled_handler._pending_variation_skus,
            {(log.merchant_id, None): {item["sku"]}},
        )

    def test_parent_sku_and_sku_value_of_valid_product_and_offer_feed_item_are_added_to_pending_sets(
        self,
    ):
        log = MerchantTransformationLogFactory(
            configuration__izberg_entity="product_and_offer"
        )
        conf = log.configuration
        item = {"parent_sku": "SKÜ-100", "sku": "SKÜ-100-M", "stock": 0}
        self.assertEqual(conf.last_crawled_handler._pending_offer_skus, {})
        self.assertEqual(conf.last_crawled_handler._pending_variation_skus, {})
        conf._handle_valid_item(
            result_item=item,
            skip_not_modified=False,
            data_validation=False,
            log=log,
            nb_items=1,
            matcher=conf.matcher,
        )
        self.assertEqual(
            conf.last_crawled_handler._pending_offer_skus,
            {(log.merchant_id, None): {item["parent_sku"]}},
        )
        self.assertEqual(
            conf.last_crawled_handler._pending_variation_skus,
            {(log.merchant_id, None): {item["sku"]}},
        )

    def test_parent_sku_and_nested_sku_values_of_valid_offer_feed_item_are_added_to_pending_sets(
        self,
    ):
        log = MerchantTransformationLogFactory(configuration__izberg_entity="offer")
        conf = log.configuration
        item = {
            "sku": "SKÜ-100",
            "variations": [
                {"sku": "SKÜ-100-M", "stock": 10},
                {"sku": "SKÜ-100-L", "stock": 20},
            ],
        }
        self.assertEqual(conf.last_crawled_handler._pending_offer_skus, {})
        self.assertEqual(conf.last_crawled_handler._pending_variation_skus, {})
        conf._handle_valid_item(
            result_item=item,
            skip_not_modified=False,
            data_validation=False,
            log=log,
            nb_items=1,
            matcher=conf.matcher,
        )
        self.assertEqual(
            conf.last_crawled_handler._pending_offer_skus,
            {(log.merchant_id, None): {item["sku"]}},
        )
        self.assertEqual(
            conf.last_crawled_handler._pending_variation_skus,
            {
                (log.merchant_id, None): {
                    item["variations"][0]["sku"],
                    item["variations"][1]["sku"],
                }
            },
        )

    def test_parent_sku_and_nested_sku_values_of_valid_product_and_offer_feed_item_are_added_to_pending_sets(
        self,
    ):
        log = MerchantTransformationLogFactory(
            configuration__izberg_entity="product_and_offer"
        )
        conf = log.configuration
        item = {
            "sku": "SKÜ-100",
            "variations": [
                {"sku": "SKÜ-100-M", "stock": 10},
                {"sku": "SKÜ-100-L", "stock": 20},
            ],
        }
        self.assertEqual(conf.last_crawled_handler._pending_offer_skus, {})
        self.assertEqual(conf.last_crawled_handler._pending_variation_skus, {})
        conf._handle_valid_item(
            result_item=item,
            skip_not_modified=False,
            data_validation=False,
            log=log,
            nb_items=1,
            matcher=conf.matcher,
        )
        self.assertEqual(
            conf.last_crawled_handler._pending_offer_skus,
            {(log.merchant_id, None): {item["sku"]}},
        )
        self.assertEqual(
            conf.last_crawled_handler._pending_variation_skus,
            {
                (log.merchant_id, None): {
                    item["variations"][0]["sku"],
                    item["variations"][1]["sku"],
                }
            },
        )

    def test_sku_value_of_valid_merchant_feed_item_is_not_added_to_pending_set(self):
        log = TransformationLogFactory(configuration__izberg_entity="merchant")
        conf = log.configuration
        item = {"sku": "SKÜ-100", "stock": 0}
        self.assertEqual(conf.last_crawled_handler._pending_offer_skus, {})
        self.assertEqual(conf.last_crawled_handler._pending_variation_skus, {})
        conf._handle_valid_item(
            result_item=item,
            skip_not_modified=False,
            data_validation=False,
            log=log,
            nb_items=1,
            matcher=conf.matcher,
        )
        self.assertEqual(conf.last_crawled_handler._pending_offer_skus, {})
        self.assertEqual(conf.last_crawled_handler._pending_variation_skus, {})

    def test_sku_and_merchant_id_is_added_to_pending_set(self):
        log = TransformationLogFactory(configuration__izberg_entity="offer")
        conf = log.configuration
        item = {"sku": "SKÜ-100", "stock": 0, "merchant_id": 999}
        self.assertEqual(conf.last_crawled_handler._pending_offer_skus, {})
        conf._handle_valid_item(
            result_item=item,
            skip_not_modified=False,
            data_validation=False,
            log=log,
            nb_items=1,
            matcher=conf.matcher,
        )
        self.assertEqual(
            conf.last_crawled_handler._pending_offer_skus, {(999, None): {item["sku"]}}
        )

    def test_no_merchant_case(self):
        log = TransformationLogFactory(configuration__izberg_entity="offer")
        conf = log.configuration
        item = {"sku": "SKÜ-100", "stock": 0}
        self.assertEqual(conf.last_crawled_handler._pending_offer_skus, {})
        conf._handle_valid_item(
            result_item=item,
            skip_not_modified=False,
            data_validation=False,
            log=log,
            nb_items=1,
            matcher=conf.matcher,
        )
        self.assertEqual(conf.last_crawled_handler._pending_offer_skus, {})

    @patch(
        "mapper.actions.configuration.last_crawled_handler."
        "LastCrawledHandler.store_references"
    )
    def test_with_in_memory_log(self, mocked):
        config = ConfigurationFactory.build()
        log = InMemoryTransformationLog(configuration=config)
        item = {"sku": "SKÜ-100", "stock": 0}

        log.configuration._handle_valid_item(
            result_item=item,
            skip_not_modified=False,
            data_validation=False,
            log=log,
            nb_items=1,
            matcher=config.matcher,
        )

        mocked.assert_not_called()

    def test_parent_sku_and_merchant_id_is_added_to_pending_sets(self):
        log = TransformationLogFactory(configuration__izberg_entity="offer")
        conf = log.configuration
        item = {
            "parent_sku": "SKÜ-100",
            "sku": "SKÜ-100-M",
            "stock": 0,
            "merchant_id": 999,
        }
        self.assertEqual(conf.last_crawled_handler._pending_offer_skus, {})
        self.assertEqual(conf.last_crawled_handler._pending_variation_skus, {})
        conf._handle_valid_item(
            result_item=item,
            skip_not_modified=False,
            data_validation=False,
            log=log,
            nb_items=1,
            matcher=conf.matcher,
        )
        self.assertEqual(
            conf.last_crawled_handler._pending_offer_skus,
            {(999, None): {item["parent_sku"]}},
        )
        self.assertEqual(
            conf.last_crawled_handler._pending_variation_skus,
            {(999, None): {item["sku"]}},
        )

    def test_sku_and_merchant_external_id_is_added_to_pending_set(self):
        log = TransformationLogFactory(configuration__izberg_entity="offer")
        conf = log.configuration
        item = {"sku": "SKÜ-100", "stock": 0, "merchant_external_id": "999"}
        self.assertEqual(conf.last_crawled_handler._pending_offer_skus, {})
        conf._handle_valid_item(
            result_item=item,
            skip_not_modified=False,
            data_validation=False,
            log=log,
            nb_items=1,
            matcher=conf.matcher,
        )
        self.assertEqual(
            conf.last_crawled_handler._pending_offer_skus,
            {(None, "999"): {item["sku"]}},
        )

    def test_parent_sku_and_merchant_external_id_is_added_to_pending_sets(self):
        log = TransformationLogFactory(configuration__izberg_entity="offer")
        conf = log.configuration
        item = {
            "parent_sku": "SKÜ-100",
            "sku": "SKÜ-100-M",
            "stock": 0,
            "merchant_external_id": "999",
        }
        self.assertEqual(conf.last_crawled_handler._pending_offer_skus, {})
        self.assertEqual(conf.last_crawled_handler._pending_variation_skus, {})
        conf._handle_valid_item(
            result_item=item,
            skip_not_modified=False,
            data_validation=False,
            log=log,
            nb_items=1,
            matcher=conf.matcher,
        )
        self.assertEqual(
            conf.last_crawled_handler._pending_offer_skus,
            {(None, "999"): {item["parent_sku"]}},
        )
        self.assertEqual(
            conf.last_crawled_handler._pending_variation_skus,
            {(None, "999"): {item["sku"]}},
        )

    @patch("mapper.models.transformation_models.TransformationLog.add_error")
    def test_sku_added_on_error_line(self, mocked):
        log = TransformationLogFactory(configuration__izberg_entity="offer")
        conf = log.configuration
        item = {
            "source": {
                "sku": "SKÜ-100",
                "stock": 0,
                "merchant_id": 999,
            },
            "trigger_description": "trigger_description",
            "trigger_resource_uri": "https://triger.resource.uri",
            "traceback": "traceback",
        }
        conf._handle_invalid_item_by_status("error", item, 1, log)
        self.assertEqual(
            conf.last_crawled_handler._pending_offer_skus,
            {(999, None): {item["source"]["sku"]}},
        )

    @patch("mapper.models.transformation_models.TransformationLog.add_warning")
    def test_sku_added_on_warning_line(self, mocked):
        log = TransformationLogFactory(configuration__izberg_entity="offer")
        conf = log.configuration
        item = {
            "sku": "SKÜ-100",
            "stock": 0,
            "merchant_id": 999,
        }
        conf._handle_invalid_item_by_status("warning", item, 1, log)
        self.assertEqual(
            conf.last_crawled_handler._pending_offer_skus, {(999, None): {item["sku"]}}
        )

    @patch("mapper.models.transformation_models.TransformationLog.add_skipped")
    def test_sku_added_on_skipped_line(self, mocked):
        log = TransformationLogFactory(configuration__izberg_entity="offer")
        conf = log.configuration
        item = {
            "source": {
                "sku": "SKÜ-100",
                "stock": 0,
                "merchant_id": 999,
            },
            "trigger_description": "trigger_description",
            "trigger_resource_uri": "https://triger.resource.uri",
        }
        conf._handle_invalid_item_by_status("skipped", item, 1, log)
        self.assertEqual(
            conf.last_crawled_handler._pending_offer_skus,
            {(999, None): {item["source"]["sku"]}},
        )


class LastCrawledHandlerBatchCommitTestCase(BaseTestCase):
    def test_config_batch_update_pending_with_empty_sets_and_batch_1_returns_null_dict(
        self,
    ):
        conf = Configuration(application_id=123)
        self.assertEqual(
            conf.last_crawled_handler.batch_commit(1), {"offers": [], "variations": []}
        )

    def test_config_batch_update_pending_with_1_offer_with_merchant_id_result(self):
        offer = ProductOfferFactory(sku="OFFER-SKU")
        conf = Configuration(
            merchant=offer.merchant, application=offer.merchant.application
        )

        conf.last_crawled_handler.store_references({"sku": offer.sku, "stock": 100})
        before_action = timezone.now()
        expected_result = {
            "offers": [
                {
                    "targeted": {offer.sku},
                    "updated": 1,
                    "merchant_filter": {"merchant_id": offer.merchant_id},
                }
            ],
            "variations": [],
        }
        self.assertEqual(
            conf.last_crawled_handler.batch_commit(min_batch_size=1), expected_result
        )
        offer.refresh_from_db()
        self.assertGreater(offer.last_crawled, before_action)

    def test_config_batch_update_pending_with_1_offer_with_merchant_external_id_result(
        self,
    ):
        offer = ProductOfferFactory(
            merchant__external_id="MERCH_EXT_ID", sku="OFFER-SKU"
        )
        conf = Configuration(application=offer.merchant.application)
        conf.last_crawled_handler.store_references(
            {
                "sku": offer.sku,
                "stock": 100,
                "merchant_external_id": offer.merchant.external_id,
            }
        )
        before_action = timezone.now()
        expected_result = {
            "offers": [
                {
                    "targeted": {offer.sku},
                    "updated": 1,
                    "merchant_filter": {
                        "merchant__external_id": offer.merchant.external_id
                    },
                }
            ],
            "variations": [],
        }
        self.assertEqual(
            conf.last_crawled_handler.batch_commit(min_batch_size=1), expected_result
        )
        offer.refresh_from_db()
        self.assertGreater(offer.last_crawled, before_action)

    def test_config_batch_update_pending_with_2_offers_of_2_merchant_external_id_result(
        self,
    ):
        offer1 = ProductOfferFactory(
            merchant__external_id="MERCH_EXT_ID_1",
        )
        application = offer1.merchant.application
        offer2 = ProductOfferFactory.create_for_application(
            application,
            merchant__external_id="MERCH_EXT_ID_2",
            merchant__default_currency=application.default_currency,
        )
        conf = Configuration(application=application)
        conf.last_crawled_handler.store_references(
            {
                "sku": offer1.sku,
                "stock": 100,
                "merchant_external_id": offer1.merchant.external_id,
            }
        )
        conf.last_crawled_handler.store_references(
            {
                "sku": offer2.sku,
                "stock": 100,
                "merchant_external_id": offer2.merchant.external_id,
            }
        )
        before_action = timezone.now()
        expected_result_1 = {
            "targeted": {offer1.sku},
            "updated": 1,
            "merchant_filter": {"merchant__external_id": offer1.merchant.external_id},
        }
        expected_result_2 = {
            "targeted": {offer2.sku},
            "updated": 1,
            "merchant_filter": {"merchant__external_id": offer2.merchant.external_id},
        }
        result = conf.last_crawled_handler.batch_commit(min_batch_size=1)
        self.assertTrue(expected_result_1 in result["offers"])
        self.assertTrue(expected_result_2 in result["offers"])
        offer1.refresh_from_db()
        offer2.refresh_from_db()
        self.assertGreater(offer1.last_crawled, before_action)
        self.assertGreater(offer2.last_crawled, before_action)

    def test_config_batch_update_pending_with_2_offers_of_1_merchant_external_id_result(
        self,
    ):
        offer1 = ProductOfferFactory(
            merchant__external_id="MERCH_EXT_ID_1",
        )
        merchant = offer1.merchant
        application = offer1.merchant.application
        offer2 = ProductOfferFactory.create_for_merchant(merchant)
        conf = Configuration(application=application)
        conf.last_crawled_handler.store_references(
            {
                "sku": offer1.sku,
                "stock": 100,
                "merchant_external_id": merchant.external_id,
            }
        )
        conf.last_crawled_handler.store_references(
            {
                "sku": offer2.sku,
                "stock": 100,
                "merchant_external_id": merchant.external_id,
            }
        )
        expected_result = {
            "offers": [
                {
                    "targeted": {offer1.sku, offer2.sku},
                    "updated": 2,
                    "merchant_filter": {"merchant__external_id": merchant.external_id},
                }
            ],
            "variations": [],
        }
        self.assertEqual(
            conf.last_crawled_handler.batch_commit(min_batch_size=1), expected_result
        )

    def test_config_batch_update_pending_with_1_variation_with_merchant_id_result(self):
        variation = ProductVariationFactory(sku="VARIÄTION-SKU")
        conf = Configuration(
            merchant=variation.merchant, application=variation.merchant.application
        )
        conf.last_crawled_handler.store_references(
            {
                "sku": variation.sku,
                "parent_sku": variation.product_offer.sku,
                "stock": 100,
            }
        )
        before_action = timezone.now()
        expected_result = {
            "offers": [
                {
                    "targeted": {variation.product_offer.sku},
                    "updated": 1,
                    "merchant_filter": {"merchant_id": variation.merchant_id},
                }
            ],
            "variations": [
                {
                    "targeted": {variation.sku},
                    "updated": 1,
                    "merchant_filter": {"merchant_id": variation.merchant_id},
                }
            ],
        }
        self.assertEqual(
            conf.last_crawled_handler.batch_commit(min_batch_size=1), expected_result
        )
        variation.refresh_from_db()
        variation.product_offer.refresh_from_db()
        self.assertGreater(variation.last_crawled, before_action)
        self.assertGreater(variation.product_offer.last_crawled, before_action)

    def test_config_batch_update_pending_with_1_variation_with_merchant_external_id_result(
        self,
    ):
        variation = ProductVariationFactory(
            merchant__external_id="MERCH_EXT_ID", sku="VARIATION-SKU"
        )
        merchant = variation.merchant
        conf = Configuration(application=variation.merchant.application)
        conf.last_crawled_handler.store_references(
            {
                "sku": variation.sku,
                "parent_sku": variation.product_offer.sku,
                "stock": 100,
                "merchant_external_id": merchant.external_id,
            }
        )
        before_action = timezone.now()
        expected_result = {
            "offers": [
                {
                    "targeted": {variation.product_offer.sku},
                    "updated": 1,
                    "merchant_filter": {"merchant__external_id": merchant.external_id},
                }
            ],
            "variations": [
                {
                    "targeted": {variation.sku},
                    "updated": 1,
                    "merchant_filter": {"merchant__external_id": merchant.external_id},
                }
            ],
        }
        self.assertEqual(
            conf.last_crawled_handler.batch_commit(min_batch_size=1), expected_result
        )
        variation.refresh_from_db()
        variation.product_offer.refresh_from_db()
        self.assertGreater(variation.last_crawled, before_action)
        self.assertGreater(variation.product_offer.last_crawled, before_action)
