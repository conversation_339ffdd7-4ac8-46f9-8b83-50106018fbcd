# -*- coding: utf-8 -*-

import os
import socket
from pathlib import Path

import mapper
from apps.ice_applications.app_conf_settings import MapperUsesIzbergUserAgent
from django.conf import settings
from django.core.exceptions import ValidationError
from django.test.utils import override_settings
from ims.tests import BaseTestCase
from mapper import exceptions
from mapper.factories import ConfigurationFactory, FeedFileFactory
from mapper.loaders import FileLoader, FTPLoader, HTTPLoader, UploadedFileLoader
from mapper.loaders.base import AbstractLoader
from mock import MagicMock, mock_open, patch
from paramiko import Transport
from paramiko.sftp_client import SFTPClient
from paramiko.ssh_exception import AuthenticationException, SSHException

from .utils import MockedRequestsResponse, MockedUrlLib2Response, open_sample

MAPPER_APP_PATH = os.path.abspath(mapper.__path__[0])
FEEDS_DIR = os.path.join(MAPPER_APP_PATH, "fixtures/feeds")

URLOPEN_PATH = "urllib.request.OpenerDirector.open"


@override_settings(MAPPER_VALIDATE_FILE_LOADER_PATH=True)
class FileTest(BaseTestCase):
    def test_iterate_sample_json(self):
        filepath = str(Path(FEEDS_DIR) / "sample.json")
        file_loader = FileLoader(ConfigurationFactory(), filepath=filepath)
        for md5sum, stream in file_loader.iterate():
            self.assertTrue(hasattr(stream, "read"))

    def test_file_outside_of_uploaded_files_folder(self):
        filepath = str(Path(settings.PROJECT_ROOT) / "iceberg/settings.py")

        with self.assertRaises(ValidationError) as err:
            FileLoader(ConfigurationFactory(), filepath=filepath)

        self.assertEqual(
            err.exception.message_dict,
            {"options": ["filepath is outside of sample feeds directory"]},
        )

    def test_get_extension_json(self):
        filepath = str(Path(FEEDS_DIR) / "sample.json")
        loader = FileLoader(ConfigurationFactory(), filepath=filepath)

        e = loader.get_extension()

        self.assertEqual(e, ".json")

    def test_get_extension_unknown(self):
        filepath = str(Path(FEEDS_DIR) / "sample")
        loader = FileLoader(ConfigurationFactory(), filepath=filepath)

        e = loader.get_extension()

        self.assertEqual(e, None)


class UploadedFileLoaderTest(BaseTestCase):
    def test_iterate_uploaded_file(self):
        """Load a  uploaded file (izberg-uploader based on 'sample.json')"""
        uploaded_file = FeedFileFactory()
        uploaded_file.save()
        file_loader = UploadedFileLoader(
            ConfigurationFactory(),
            pk=uploaded_file.pk,
        )

        for md5sum, stream in file_loader.iterate():
            assert hasattr(stream, "read")


@patch(URLOPEN_PATH)
class FtpLoaderTest(BaseTestCase):
    @property
    def sample(self):
        if not hasattr(self, "_sample"):
            self._sample = open_sample("sample.json")
        return self._sample

    def test_url_generation_handle_slash_in_pwd(self, mocked_request):
        loader = FTPLoader(
            ConfigurationFactory.build(),
            url="ftp://localhost/sample.json",
            login="us€/rn@me",
            password="p@$$/w0rd",
        )

        loader.get_content()

        self.assertEqual(
            mocked_request.call_args_list[0][0][0].get_full_url(),
            "ftp://us%E2%82%AC%2Frn%40me:p%40%24%24%2Fw0rd@localhost:21/sample.json",
        )

    def test_file_path_in_arguments(self, mocked_request):
        loader = FTPLoader(
            ConfigurationFactory.build(),
            url="ftp://localhost",
            file_path="myfile.txt",
        )

        loader.get_content()

        self.assertEqual(loader.remote_file_path, "myfile.txt")
        self.assertEqual(
            mocked_request.call_args_list[0][0][0].get_full_url(),
            "ftp://localhost:21/myfile.txt",
        )

    def test_file_path_in_arguments_and_url(self, mocked_request):
        with self.assertRaises(exceptions.FtpFilePathConflicts):
            FTPLoader(
                ConfigurationFactory.build(),
                url="ftp://localhost/myfile.txt",
                file_path="myfile.txt",
            )

    def test_credentials_in_url(self, mocked_request):
        mocked_request.return_value = MockedUrlLib2Response(file_object=self.sample)
        loader = FTPLoader(
            ConfigurationFactory(), url="*********************************/sample.json"
        )

        loader.get_content()

        self.assertEqual(
            mocked_request.call_args_list[0][0][0].get_full_url(),
            "*********************************:21/sample.json",
        )

    def test_credentials_as_arguments(self, mocked_request):
        mocked_request.return_value = MockedUrlLib2Response(file_object=self.sample)
        loader = FTPLoader(
            ConfigurationFactory(),
            url="ftp://localhost/sample.json",
            login="username",
            password="password",
        )

        loader.get_content()

        self.assertEqual(
            mocked_request.call_args_list[0][0][0].get_full_url(),
            "*********************************:21/sample.json",
        )

    def test_credentials_as_arguments_and_url(self, mocked_request):
        with self.assertRaises(exceptions.CredentialConflicts):
            FTPLoader(
                ConfigurationFactory(),
                url="*********************************:8020/sample.json",
                login="username",
                password="password",
            )

    def test_bad_protocol(self, mocked_request):
        with self.assertRaises(exceptions.InvalidProtocol):
            FTPLoader(
                ConfigurationFactory(),
                url="foobar://username:password@localhost/sample.json",
            )

    def test_no_credentials(self, mocked_request):
        mocked_request.return_value = MockedUrlLib2Response(
            file_object=open_sample("sample.json")
        )
        loader = FTPLoader(ConfigurationFactory(), url="ftp://localhost/sample.json")

        loader.get_content()

        self.assertEqual(
            mocked_request.call_args[0][0].full_url, "ftp://localhost:21/sample.json"
        )

    def test_login_and_password_encoding(self, mocked_request):
        loader = FTPLoader(
            ConfigurationFactory(),
            url="ftp://localhost:8020/sample.json",
            login="<EMAIL>",
            password="my=pass wôrd",
        )

        loader.get_content()

        self.assertEqual(
            mocked_request.call_args[0][0].full_url,
            (
                "ftp://toto%40example.com:my%3Dpass%20w%C3%B4rd@"
                "localhost:8020/sample.json"
            ),
        )

    def test_timeout_not_provided(self, mocked_request):
        loader = FTPLoader(
            ConfigurationFactory(),
            url="ftp://localhost:8020/sample.json",
        )

        loader.get_content()

        timeout = mocked_request.call_args[0][2]
        self.assertEqual(timeout, settings.MAPPER_DEFAULT_TIMEOUT)

    def test_timeout_provided(self, mocked_request):
        loader = FTPLoader(
            ConfigurationFactory(), url="ftp://localhost:8020/sample.json", timeout=42
        )

        loader.get_content()

        timeout = mocked_request.call_args[0][2]
        self.assertEqual(timeout, 42)

    def test_headers_not_provided_app_setting_true(self, mocked_request):
        loader = FTPLoader(
            ConfigurationFactory(),
            url="ftp://localhost:8020/sample.json",
        )
        loader.configuration.application.set_setting(MapperUsesIzbergUserAgent, True)

        loader.get_content()

        self.assertEqual(
            mocked_request.call_args[0][0].headers,
            {"User-agent": "Izberg-Marketplace/1.0"},
        )

    def test_headers_not_provided_app_setting_false(self, mocked_request):
        loader = FTPLoader(
            ConfigurationFactory(),
            url="ftp://localhost:8020/sample.json",
        )
        loader.configuration.application.set_setting(MapperUsesIzbergUserAgent, False)

        loader.get_content()

        self.assertEqual(mocked_request.call_args[0][0].headers, {})

    def test_headers_provided(self, mocked_request):
        loader = FTPLoader(
            ConfigurationFactory(),
            url="ftp://localhost:8020/sample.json",
            headers={"User-Agent": "hello"},
        )

        loader.get_content()

        self.assertEqual(
            mocked_request.call_args[0][0].headers, {"User-agent": "hello"}
        )

    def test_custom_port_provided_as_url(self, mocked_request):
        loader = FTPLoader(
            ConfigurationFactory(),
            url="ftp://localhost:4242/sample.json",
        )

        loader.get_content()

        self.assertEqual(
            mocked_request.call_args_list[0][0][0].get_full_url(),
            "ftp://localhost:4242/sample.json",
        )

    def test_custom_port_provided_as_arguments(self, mocked_request):
        loader = FTPLoader(
            ConfigurationFactory(),
            url="ftp://localhost/sample.json",
            port=4242,
        )

        loader.get_content()

        self.assertEqual(
            mocked_request.call_args_list[0][0][0].get_full_url(),
            "ftp://localhost:4242/sample.json",
        )

    def test_get_extension_json(self, mocked_request):
        loader = FTPLoader(
            ConfigurationFactory(),
            url="ftp://localhost/sample.json",
            port=4242,
        )

        e = loader.get_extension()

        self.assertEqual(e, ".json")

    def test_get_extension_unknown(self, mocked_request):
        loader = FTPLoader(
            ConfigurationFactory(),
            url="ftp://localhost/sample",
            port=4242,
        )

        e = loader.get_extension()

        self.assertEqual(e, None)

    def test_custom_port_provided_as_arguments_and_url(self, mocked_request):
        with self.assertRaises(exceptions.PortConflicts):
            FTPLoader(
                ConfigurationFactory(),
                url="ftp://localhost:4242/sample.json",
                port=4242,
            )


@patch("paramiko.Transport")
@patch("paramiko.SFTPClient.from_transport")
@patch("codecs.open", mock_open(read_data="hello world"))
@patch("os.makedirs", lambda *_, **__: None)
@patch("os.remove", lambda *_, **__: None)
class SftpLoaderTestCase(BaseTestCase):
    def get_transport_mock(self):
        class MockTransport(Transport):
            connect_args = []
            close_args = []
            connect_call_count = 0
            close_call_count = 0

            def connect(self, *args):
                self.connect_call_count += 1
                self.connect_args.append(args)

            def close(self, *args):
                self.close_call_count += 1
                self.close_args.append(args)

        return MockTransport(MagicMock())

    def get_sftp_client(self):
        class MockSFTPClient(SFTPClient):
            get_args = []
            close_args = []
            get_call_count = 1
            close_call_count = 0

            def _send_version(self):
                return None

            def get(self, *args):
                self.get_call_count += 1
                self.get_args.append(args)

            def close(self, *args):
                self.close_call_count += 1
                self.close_args.append(args)

        return MockSFTPClient(MagicMock())

    def test_iterate_on_file(self, mock_from_transport, mock_transport):
        mock_transport.return_value = self.get_transport_mock()
        mock_from_transport.return_value = self.get_sftp_client()
        loader = FTPLoader(ConfigurationFactory(), url="sftp://localhost/myfile.txt")

        for md5sum, stream in loader.iterate():
            self.assertEqual(stream.read(), "hello world")
        self.assertEqual(mock_transport.return_value.close_call_count, 1)
        self.assertEqual(mock_from_transport.return_value.close_call_count, 1)

    def test_credentials_in_url(self, mock_from_transport, mock_transport):
        mock_transport.return_value = self.get_transport_mock()
        loader = FTPLoader(
            ConfigurationFactory(), url="sftp://user:pwd@localhost/myfile.txt"
        )

        loader.get_content()

        self.assertEqual(loader.login, "user")
        self.assertEqual(loader.password, "pwd")
        self.assertEqual(
            mock_transport.return_value.connect_args, [(None, "user", "pwd")]
        )

    def test_credentials_as_params(self, mock_from_transport, mock_transport):
        mock_transport.return_value = self.get_transport_mock()
        loader = FTPLoader(
            ConfigurationFactory(),
            url="sftp://localhost/myfile.txt",
            login="john",
            password="doe",
        )

        loader.get_content()

        self.assertEqual(loader.login, "john")
        self.assertEqual(loader.password, "doe")
        self.assertEqual(
            mock_transport.return_value.connect_args, [(None, "john", "doe")]
        )

    def test_credentials_not_provided(self, mock_from_transport, mock_transport):
        class MockTransport(Transport):
            connect_args = []

            def connect(self, *args):
                self.connect_args.append(args)

        mock_transport.return_value = MockTransport(MagicMock())
        loader = FTPLoader(ConfigurationFactory(), url="sftp://localhost/myfile.txt")

        loader.get_content()

        self.assertIsNone(loader.login)
        self.assertIsNone(loader.password)
        self.assertEqual(mock_transport.return_value.connect_args, [(None, None, None)])

    def test_credentials_as_params_and_url(self, mock_from_transport, mock_transport):
        with self.assertRaises(exceptions.CredentialConflicts):
            FTPLoader(
                ConfigurationFactory(),
                url="sftp://toto:tata@localhost/myfile.txt",
                login="toto",
                password="tata",
            )

    def test_port_in_url(self, mock_from_transport, mock_transport):
        loader = FTPLoader(
            ConfigurationFactory(), url="sftp://localhost:4242/myfile.txt"
        )

        loader.get_content()

        self.assertEqual(loader.port, 4242)
        mock_transport.assert_called_once_with(("localhost", 4242))

    def test_port_as_param(self, mock_from_transport, mock_transport):
        loader = FTPLoader(
            ConfigurationFactory(),
            url="sftp://localhost/myfile.txt",
            port=4242,
        )

        loader.get_content()

        self.assertEqual(loader.port, 4242)
        mock_transport.assert_called_once_with(("localhost", 4242))

    def test_port_as_params_and_url(self, mock_from_transport, mock_transport):
        with self.assertRaises(exceptions.PortConflicts):
            FTPLoader(
                ConfigurationFactory(),
                url="sftp://localhost:4242/myfile.txt",
                port=4242,
            )

    def test_port_not_provided(self, mock_from_transport, mock_transport):
        loader = FTPLoader(
            ConfigurationFactory(),
            url="sftp://localhost/myfile.txt",
        )

        loader.get_content()

        mock_transport.assert_called_once_with(("localhost", 22))

    def test_invalid_protocol(self, mock_from_transport, mock_transport):
        with self.assertRaises(exceptions.InvalidProtocol):
            FTPLoader(ConfigurationFactory(), url="http://localhost:4242/myfile.txt")

    def test_missing_protocol(self, mock_from_transport, mock_transport):
        with self.assertRaises(exceptions.InvalidProtocol):
            FTPLoader(ConfigurationFactory(), url="localhost/myfile.txt")

    def test_file_path_in_url(self, mock_from_transport, mock_transport):
        mock_from_transport.return_value = self.get_sftp_client()
        loader = FTPLoader(ConfigurationFactory(), url="sftp://localhost/myfile.txt")

        loader.get_content()

        self.assertEqual(loader.remote_file_path, "myfile.txt")
        self.assertEqual(mock_from_transport.return_value.get_args[0][0], "myfile.txt")

    def test_file_path_as_params(self, mock_from_transport, mock_transport):
        mock_from_transport.return_value = self.get_sftp_client()
        loader = FTPLoader(
            ConfigurationFactory(), url="sftp://localhost", file_path="myfile.txt"
        )

        loader.get_content()

        self.assertEqual(loader.remote_file_path, "myfile.txt")
        self.assertEqual(mock_from_transport.return_value.get_args[0][0], "myfile.txt")

    def test_file_path_not_provided(self, mock_from_transport, mock_transport):
        with self.assertRaises(exceptions.MissingFtpFilePath):
            FTPLoader(ConfigurationFactory(), url="sftp://localhost")

    def test_file_path_as_params_and_url(self, mock_from_transport, mock_transport):
        with self.assertRaises(exceptions.FtpFilePathConflicts):
            FTPLoader(
                ConfigurationFactory(),
                url="sftp://toto:tata@localhost/myfile.txt",
                file_path="myfile.txt",
            )

    def test_timeout_provided(self, mock_from_transport, mock_transport):
        mock_transport.return_value = self.get_transport_mock()
        loader = FTPLoader(
            ConfigurationFactory(),
            url="sftp://localhost/lol.txt",
            timeout=20,
        )

        loader.get_content()

        self.assertEqual(loader.timeout, 20)
        self.assertEqual(mock_transport.return_value.banner_timeout, 20)

    def test_timeout_not_provided(self, mock_from_transport, mock_transport):
        mock_transport.return_value = self.get_transport_mock()
        loader = FTPLoader(
            ConfigurationFactory(),
            url="sftp://localhost/lol.txt",
        )

        loader.get_content()

        self.assertEqual(loader.timeout, None)
        self.assertEqual(mock_transport.return_value.banner_timeout, 15)

    def test_file_not_found(self, mock_from_transport, mock_transport):
        class MockSFTPClient(SFTPClient):
            def _send_version(self):
                return None

        mock_from_transport.return_value = MockSFTPClient(MagicMock())
        loader = FTPLoader(
            ConfigurationFactory(),
            url="sftp://toto:tata@localhost/myfile.txt",
        )

        with self.assertRaises(exceptions.FeedDownloadError) as err:
            loader.get_content()

        self.assertEqual(err.exception.args[0], "File not found on the server")

    def test_domain_name_invalid(self, mock_from_transport, mock_transport):
        mock_transport.side_effect = socket.gaierror()
        loader = FTPLoader(
            ConfigurationFactory(),
            url="sftp://toto:tata@localhost/myfile.txt",
        )

        with self.assertRaises(exceptions.FeedDownloadError) as err:
            loader.get_content()

        self.assertEqual(err.exception.args[0], "Domain name is invalid")

    def test_server_not_responding_timeout(self, mock_from_transport, mock_transport):
        mock_transport.side_effect = SSHException(
            "Unable to connect to localhost: [Errno 60] Operation timed out"
        )
        loader = FTPLoader(
            ConfigurationFactory(),
            url="sftp://toto:tata@localhost/myfile.txt",
        )

        with self.assertRaises(exceptions.FeedDownloadError) as err:
            loader.get_content()

        self.assertEqual(
            err.exception.args[0], "Server is not responding on provided url and port"
        )

    def test_server_not_responding_other_reason(
        self, mock_from_transport, mock_transport
    ):
        mock_transport.side_effect = SSHException(
            "An error occured: [Errno 42] Unknown reason"
        )
        loader = FTPLoader(
            ConfigurationFactory(),
            url="sftp://toto:tata@localhost/myfile.txt",
        )

        with self.assertRaises(exceptions.FeedDownloadError) as err:
            loader.get_content()

        self.assertEqual(
            err.exception.args[0], "An error occured: [Errno 42] Unknown reason"
        )

    def test_auth_failed(self, mock_from_transport, mock_transport):
        def connect(*args):
            raise AuthenticationException()

        mock_transport.return_value = self.get_transport_mock()
        mock_transport.return_value.connect = connect
        loader = FTPLoader(
            ConfigurationFactory(),
            url="sftp://toto:tata@localhost/myfile.txt",
        )

        with self.assertRaises(exceptions.FeedDownloadError) as err:
            loader.get_content()

        self.assertEqual(
            str(err.exception.args[0]), "Authentication on the server failed"
        )
        self.assertEqual(mock_transport.return_value.close_call_count, 1)


@patch("requests.get")
class HttpLoaderTest(BaseTestCase):
    @property
    def sample(self):
        if not hasattr(self, "_sample"):
            self._sample = open_sample("sample.json")
        return self._sample

    def test_simple(self, mocked_requests):
        mocked_requests.return_value = MockedRequestsResponse(
            file_object=self.sample, headers={"content-type": "application/json"}
        )
        http_loader = HTTPLoader(
            ConfigurationFactory(), url="http://localhost/sample.json"
        )
        # checking that we can iterate :
        # and that each stream has a read method
        for md5sum, stream in http_loader.iterate():
            self.assertTrue(hasattr(stream, "read"))

    def test_iterate_credentials_as_arguments(self, mocked_requests):
        mocked_requests.return_value = MockedRequestsResponse(
            file_object=self.sample, headers={"content-type": "application/json"}
        )
        http_loader = HTTPLoader(
            ConfigurationFactory(),
            url="http://localhost:8080/sample.json",
            login="username",
            password="password",
        )
        # checking that we can iterate :
        # and that each stream has a read method
        for md5sum, stream in http_loader.iterate():
            self.assertTrue(hasattr(stream, "read"))

    def test_http_loaders_keep_empty_get_params_in_base_url(self, _):
        http_loader = HTTPLoader(
            ConfigurationFactory(),
            url=(
                "http://localhost:8080/sample.json?param1=foo&empty_param="
                "&param2=bar"
            ),
        )

        # cleaned on init
        cleaned_url = http_loader.url

        self.assertIn("empty_param=", cleaned_url)
        self.assertIn("param1=foo", cleaned_url)
        self.assertIn("param2=bar", cleaned_url)

    def test_timeout_not_provided(self, mocked_request):
        loader = HTTPLoader(
            ConfigurationFactory(),
            url="http://localhost:8080/sample.json",
        )

        loader.get_content()

        timeout = mocked_request.call_args.kwargs["timeout"]
        self.assertEqual(timeout, settings.MAPPER_DEFAULT_TIMEOUT)

    def test_timeout_provided(self, mocked_request):
        loader = HTTPLoader(
            ConfigurationFactory(), url="http://localhost:8080/sample.json", timeout=42
        )

        loader.get_content()

        timeout = mocked_request.call_args.kwargs["timeout"]
        self.assertEqual(timeout, 42)

    def test_headers_not_provided_app_setting_true(self, mocked_request):
        loader = HTTPLoader(
            ConfigurationFactory(),
            url="http://localhost:8080/sample.json",
        )

        loader.configuration.application.set_setting(MapperUsesIzbergUserAgent, True)

        loader.get_content()

        self.assertEqual(
            mocked_request.call_args.kwargs["headers"],
            {"User-Agent": "Izberg-Marketplace/1.0"},
        )

    def test_headers_not_provided_app_setting_false(self, mocked_request):
        loader = HTTPLoader(
            ConfigurationFactory(),
            url="http://localhost:8080/sample.json",
        )
        loader.configuration.application.set_setting(MapperUsesIzbergUserAgent, False)

        loader.get_content()

        self.assertNotIn("headers", mocked_request.call_args.kwargs)

    def test_headers_provided(self, mocked_request):
        loader = HTTPLoader(
            ConfigurationFactory(),
            url="http://localhost:8080/sample.json",
            headers={"User-Agent": "hello"},
        )

        loader.get_content()

        self.assertEqual(
            mocked_request.call_args.kwargs["headers"], {"User-Agent": "hello"}
        )

    def test_unsupported_encoding_detected(self, mocked_request):
        mocked_request.return_value = MockedRequestsResponse(
            content=open_sample("viscii_encoding.csv", encoding="windows-1251").read(),
            headers={"content-type": "text/csv"},
        )
        loader = HTTPLoader(
            ConfigurationFactory(),
            url="http://localhost:8080/viscii_encoding.csv",
        )
        loader.encoding = "VISCII"
        with self.assertRaises(exceptions.UnsupportedEncoding):
            loader.get_content()


class SimpleObject:
    encoding = None


class TestUrlCleaner(BaseTestCase):
    def test_simple_url(self):
        loader = AbstractLoader(SimpleObject)
        url = "http://sample.com/totoé"
        self.assertEqual(loader.clean_url(url), url)

    def test_url_with_spaces_end(self):
        loader = AbstractLoader(SimpleObject)
        url = "http://sample.com/totoé "
        self.assertEqual(loader.clean_url(url), "http://sample.com/totoé")

    def test_url_with_encoded_spaces_end(self):
        loader = AbstractLoader(SimpleObject)
        url = "http://sample.com/totoé%20"
        self.assertEqual(loader.clean_url(url), "http://sample.com/totoé")

    def test_url_with_tabs_end(self):
        loader = AbstractLoader(SimpleObject)
        url = "http://sample.com/totoé\t"
        self.assertEqual(loader.clean_url(url), "http://sample.com/totoé")

    def test_url_with_spaces_start(self):
        loader = AbstractLoader(SimpleObject)
        url = " http://sample.com/totoé"
        self.assertEqual(loader.clean_url(url), "http://sample.com/totoé")

    def test_url_with_encoded_encoded_spaces_start(self):
        loader = AbstractLoader(SimpleObject)
        url = "%20http://sample.com/totoé"
        self.assertEqual(loader.clean_url(url), "http://sample.com/totoé")

    def test_url_with_tabs_start(self):
        loader = AbstractLoader(SimpleObject)
        url = "\thttp://sample.com/totoé"
        self.assertEqual(loader.clean_url(url), "http://sample.com/totoé")

    def test_url_with_multiple_spaces(self):
        loader = AbstractLoader(SimpleObject)
        url = " http://sample.com/totoé  "
        self.assertEqual(loader.clean_url(url), "http://sample.com/totoé")

    def test_url_with_multiple_tabs(self):
        loader = AbstractLoader(SimpleObject)
        url = "\t\thttp://sample.com/totoé\t"
        self.assertEqual(loader.clean_url(url), "http://sample.com/totoé")

    def test_url_with_tabs_and_spaces(self):
        loader = AbstractLoader(SimpleObject)
        url = "\t\thttp://sample.com/totoé\t "
        self.assertEqual(loader.clean_url(url), "http://sample.com/totoé")

    def test_url_with_spaces_middle(self):
        loader = AbstractLoader(SimpleObject)
        url = "http://sample.com/toto é"
        self.assertEqual(loader.clean_url(url), "http://sample.com/toto%20é")

    def test_url_with_tab_middle(self):
        loader = AbstractLoader(SimpleObject)
        url = "http://sample.com/toto\té"
        self.assertEqual(loader.clean_url(url), url)

    def test_url_with_encoded_space_middle(self):
        loader = AbstractLoader(SimpleObject)
        url = "http://sample.com/toto%20é"
        self.assertEqual(loader.clean_url(url), url)

    def test_url_with_zero_end(self):
        loader = AbstractLoader(SimpleObject)
        url = "http://sample.com/toto=0"
        self.assertEqual(loader.clean_url(url), url)

    def test_url_with_two_end(self):
        loader = AbstractLoader(SimpleObject)
        url = "http://sample.com/toto=2"
        self.assertEqual(loader.clean_url(url), url)

    def test_url_with_percent_end(self):
        loader = AbstractLoader(SimpleObject)
        url = "http://sample.com/toto%"
        self.assertEqual(loader.clean_url(url), url)


class AbstractLoaderTestCase(BaseTestCase):
    def setUp(self):
        self.loader = AbstractLoader(ConfigurationFactory(encoding=None))
        self.UNICODE_SAMPLE_NO_ACCENT = (
            "Lorem ipsum dolor sit amet, consectetur adipiscing elit."
        )
        self.UNICODE_SAMPLE_WITH_ACCENTS = (
            "Lôrêm ipsum dolor sit amêt, çonsectetur adipiscing élit. 100€"
        )

    def test_get_encoding_detects_ascii_characters_as_utf8(self):
        detected_encoding = self.loader.get_encoding(
            sample=self.UNICODE_SAMPLE_NO_ACCENT.encode("ascii")
        )
        self.assertEqual(detected_encoding, "UTF-8")

    def test_get_encoding_detects_utf8_characters_as_utf8(self):
        detected_encoding = self.loader.get_encoding(
            sample=self.UNICODE_SAMPLE_WITH_ACCENTS.encode("utf-8")
        )
        self.assertEqual(detected_encoding, "UTF-8")

    def test_get_encoding_detects_windows_1252_characters_as_utf8(self):
        detected_encoding = self.loader.get_encoding(
            sample=self.UNICODE_SAMPLE_WITH_ACCENTS.encode("windows-1252")
        )
        self.assertEqual(detected_encoding, "WINDOWS-1252")
