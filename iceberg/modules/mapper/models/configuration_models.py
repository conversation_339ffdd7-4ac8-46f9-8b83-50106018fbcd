# -*- coding: utf-8 -*-

import logging
import random
from copy import deepcopy
from decimal import Decimal
from hashlib import md5
from tempfile import TemporaryFile

import jsonschema
import simplejson as json
import yaml
from apps.categories.models import ApplicationCategory
from apps.ice_applications.app_conf_settings import (
    AllowMerchantProducts,
    IsClosedCatalog,
    MapperPreDownloadFeed,
    MapperSwitchOff,
)
from celery import shared_task
from constance import config as live_config
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.conf.global_settings import LANGUAGES
from django.core.exceptions import ValidationError
from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import models
from django.db.models.fields.files import FileField
from django.db.models.query import QuerySet
from django.utils import timezone
from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _
from django.utils.translation import override
from django_xworkflows import models as xwf_models
from ims.api.mixins import ApiUtilsMixin
from ims.json import <PERSON>zbergJSONDecoder, IzbergJSONEncoder
from ims.models.mixin import ChangedFieldLookupMixin, SanitizeCharFieldMixin
from ims.workflow import IzbergWorkflow
from jsonfield import JSONField
from lib.api.file_path import FilePathGenerator
from lib.fields import NoChoiceMigrationCharfield
from lib.models import CreatorModelMixin, IzbergQueryset, PostgresIndex
from mapper import exceptions, loaders, mixins
from mapper import settings as mapper_settings
from mapper import utils
from mapper.actions.configuration.last_crawled_handler import LastCrawledHandler
from mapper.actions.configuration.manager import ConfigurationActionsManager
from mapper.actions.configuration_export import ConfigurationExportActionsManager
from mapper.cache import cache, get_legacy_cache
from mapper.entities import IZBERG_ENTITIES, get_entity_schema_for_application
from mapper.mapper_utils.field_utils import extract_keys, recursively_merge_lists
from mapper.mapper_utils.json_utils import json_serializable
from mapper.schemas.configuration_schema import EXPORTED_CONFIGURATION_SCHEMA
from mapper.utils import split_on_non_escaped_delimiters
from redis import OutOfMemoryError
from requests.exceptions import BaseHTTPError
from six import ensure_binary
from uploader.models import UploaderMixin

from ..decorators import handle_errors
from ..error_codes import PRODUCT_NOT_FOUND, UNKNOWN_ERROR
from ..exceptions import AlreadyProcessingException, ExceedMaxColumns
from ..parsers import ParserOptionsDetector
from . import actions
from .mapper_models import IsoMapper, Mapper
from .matcher_models import IsoMatcher, Matcher
from .parser_models import Parser
from .transformation_models import TransformationLog, TransformationLogWorkflow
from .utils import aggregate_dict_stream_on_key, iterate_on_stream

logger = logging.getLogger(__name__)

# Parsing Interval
PERIOD_CHOICES = (("weeks", _("Weeks")), ("days", _("Days")), ("hours", _("Hours")))

DEFAULT_PREVIEW_COUNT = 20

ITEM_CACHE_DURATION = 60 * 60 * 24 * 7


class ConfigurationQueryset(QuerySet):
    def exclude_deleted(self):
        return self.exclude(status="deleted")

    def filter_active(self):
        """Return active configurations.

        Means all this conditions are met:
        - configuration status is ACTIVE
        - application status is INITIAL or ACTIVE
        - merchant (if any) status is INACTIVE (initial status), PENDING or
        ACTIVE
        """
        from apps.ice_applications.models import ApplicationWorkflow
        from apps.stores.models import Merchant

        return self.filter(
            status=ConfigurationWorkflow.ACTIVE,
            application__status__in=[
                ApplicationWorkflow.INITIAL,
                ApplicationWorkflow.ACTIVE,
            ],
        ).filter(
            models.Q(merchant__isnull=True)
            | models.Q(
                merchant__status__in=[
                    Merchant.INACTIVE,
                    Merchant.ACTIVE,
                    Merchant.PENDING,
                ]
            )
        )

    def filter_to_import_now(self):
        return (
            self.filter_active()
            .filter(
                next_automatic_run_date__isnull=False,
                next_automatic_run_date__lt=timezone.now(),
            )
            .order_by("next_automatic_run_date", "last_automatic_run_date")
        )


class ConfigurationUploadQueryset(IzbergQueryset):
    pass


class ConfigurationUpload(
    UploaderMixin,
    ApiUtilsMixin,
    SanitizeCharFieldMixin,
    ChangedFieldLookupMixin,
    models.Model,
):
    api_resource_path = "mapper.api.configuration_resource.ConfigurationUploadResource"
    objects = ConfigurationUploadQueryset.as_manager()

    application = models.ForeignKey(
        "ice_applications.Application",
        verbose_name=_("Application"),
        null=True,
        related_name="+",
        on_delete=models.CASCADE,
        db_index=False,
    )
    merchant = models.ForeignKey(
        "stores.Merchant",
        verbose_name=_("Merchant"),
        null=True,
        blank=True,
        related_name="+",
        on_delete=models.CASCADE,
        db_index=False,
    )
    uploader_fields = {
        "file": "configuration_file",
        "user": "uploaded_by",
        "uploaded_date": "created_on",
        "filename": "filename",
    }

    configuration_file = FileField(
        _("Mapper configuration"), upload_to=FilePathGenerator(), max_length=1024
    )
    configuration_file_bkp = FileField(
        null=True,
        default=None,
        blank=True,
        max_length=1024,
    )
    uploaded_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        verbose_name=_("Uploaded by"),
        null=True,
        blank=True,
        related_name="uploaded_mapper_configurations",
        on_delete=models.CASCADE,
        db_index=False,
    )
    created_on = models.DateTimeField(
        _("Created"),
        auto_now_add=True,
        null=True,
    )
    filename = models.CharField(
        _("File name"),
        max_length=256,
        blank=True,
        null=True,
    )
    CREATABLE_FIELDS = [
        "application",
        "merchant",
        "configuration_file",
        "filename",
        "uploaded_by",
    ]
    EDITABLE_FIELDS = []

    class Meta:
        indexes = [
            PostgresIndex(
                fields=["created_on"],
                name="mapper_configurationupload_created_on_idx",
            ),
        ]

    def full_clean(self, exclude=None, validate_unique=True):
        raw_content = self.configuration_file.read()
        try:
            mapper_conf = json.loads(raw_content, cls=IzbergJSONDecoder)
            jsonschema.validate(mapper_conf, EXPORTED_CONFIGURATION_SCHEMA)
            super(ConfigurationUpload, self).full_clean(exclude, validate_unique)
        except ValueError:
            content = {"configuration_file": _("not a valid JSON file")}
            raise ValidationError(content)
        except jsonschema.exceptions.ValidationError as e:
            content = {"configuration_file": e.args[0]}
            raise ValidationError(content)

    def save(self, *args, **kwargs):
        self.full_clean()
        super(ConfigurationUpload, self).save(*args, **kwargs)


class ConfigurationWorkflow(IzbergWorkflow):
    log_model = ""  # Disable transition log
    ACTIVE, STOPPED, TOO_MANY_ERRORS, DELETED = (
        "active",
        "stopped",
        "too_many_errors",
        "deleted",
    )
    states = (
        (ACTIVE, _("Active")),
        (STOPPED, _("Stopped")),
        (TOO_MANY_ERRORS, _("Too_many_errors")),
        (DELETED, _("Deleted")),
    )
    transitions = (
        ("stop_action", ACTIVE, STOPPED),
        ("errors_action", ACTIVE, TOO_MANY_ERRORS),
        ("delete_action", (ACTIVE, STOPPED, TOO_MANY_ERRORS, DELETED), DELETED),
        ("active_action", (STOPPED, TOO_MANY_ERRORS), ACTIVE),
    )
    initial_state = ACTIVE


class Configuration(
    ApiUtilsMixin,
    mixins.HasOptionForm,
    mixins.ModelSourcing,
    xwf_models.WorkflowEnabled,
    CreatorModelMixin,
):
    api_resource_path = "mapper.api.configuration_resource.ConfigurationResource"

    objects = ConfigurationQueryset.as_manager()

    name = models.CharField(
        _("Name"), max_length=255, help_text=_("A name to help you manage your feeds")
    )
    status = xwf_models.StateField(
        ConfigurationWorkflow,
        verbose_name=_("Status"),
    )
    created_on = models.DateTimeField(
        _("Created on"),
        auto_now_add=True,
        null=True,
        blank=True,
    )
    last_modified = models.DateTimeField(
        _("Last modified"),
        auto_now=True,
        null=True,
        blank=True,
    )

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        verbose_name=_("User"),
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        db_index=False,
    )

    merchant = models.ForeignKey(
        "stores.Merchant",
        verbose_name=_("Merchant"),
        null=True,
        blank=True,
        related_name="mapper_configurations",
        on_delete=models.CASCADE,
        db_index=False,
    )

    application = models.ForeignKey(
        "ice_applications.Application",
        verbose_name=_("Application"),
        related_name="mapper_configurations",
        on_delete=models.CASCADE,
        db_index=False,
    )

    # Feed Parsing Frequency
    every = models.PositiveIntegerField(_("Every"), null=False, default=0)
    period = models.CharField(
        _("Period"), max_length=24, choices=PERIOD_CHOICES, default="days"
    )
    last_run_date = models.DateTimeField(
        _("Last run date"),
        help_text=_("Date this config was ran by the scheduler or manually."),
        null=True,
        blank=True,
    )
    last_automatic_run_date = models.DateTimeField(
        _("Last automatic run date"),
        help_text=_("Date last run was trigged by the scheduler"),
        null=True,
        blank=True,
    )
    next_automatic_run_date = models.DateTimeField(
        _("Next automatic run date"),
        help_text=_("Next date for the scheduled run"),
        null=True,
        blank=True,
    )
    report_email_addresses = models.CharField(
        _("Report email addresses"),
        default=None,
        max_length=255,
        null=True,
        blank=True,
        help_text=_("A list of emails that will receive import repport"),
    )

    abortion_threshold_override = models.DecimalField(
        _("Abortion rate"),
        null=True,
        blank=True,
        decimal_places=2,
        max_digits=5,
        help_text=_("Abort if failure rate exceeds the specified rate (in percent)"),
        validators=[MinValueValidator(Decimal(0)), MaxValueValidator(Decimal(100))],
    )

    # Feed Information
    izberg_entity = models.CharField(
        _("Entity"),
        max_length=255,
        choices=IZBERG_ENTITIES,
        default=IZBERG_ENTITIES[0][0],
    )
    ENCODING_CHOICES = (
        (None, _("Default encoding")),
        ("UTF-8", "UTF-8"),
        ("UTF-8-SIG", "UTF-8 Sig"),
        ("ISO-8859-1", "ISO-8859-1"),
        ("ISO-8859-2", "ISO-8859-2"),
        ("US-ASCII", "US-ASCII"),
        ("UTF-16", "UTF-16"),
        ("UTF-16BE", "UTF-16BE"),
        ("UTF-16LE", "UTF-16LE"),
        ("Big5", "Big5"),
        ("Big5-HKSCS", "Big5-HKSCS"),
        ("windows-1250", "Windows 1250"),
        ("windows-1251", "Windows 1251"),
        ("windows-1252", "Windows 1252"),
    )
    encoding = models.CharField(
        _("Encoding"),
        max_length=100,
        null=True,
        blank=True,
        choices=ENCODING_CHOICES,
        help_text=_("Provide an encoding if needed"),
    )
    language = NoChoiceMigrationCharfield(
        _("Language"),
        max_length=10,
        choices=LANGUAGES,
        help_text=_("Set content language"),
    )

    # Fetch Information
    method = models.CharField(_("Method"), max_length=255, choices=loaders.LOADERS)
    options = JSONField(_("Options"), default=dict, blank=True)

    # Data Download option
    save_content = models.BooleanField(
        _("Save content"),
        default=True,
        help_text=_("If True, content will be saved in a File for history versionning"),
    )
    create_parsing_report = models.BooleanField(
        _("Create parsing report"),
        default=True,
        help_text=_("If True, a parsing report will be generated"),
    )

    JSON = "json"
    YAML = "yaml"

    PARSING_REPORT_FORMAT_CHOICES = (
        (JSON, _("JSON")),
        (YAML, _("YAML")),
    )
    parsing_report_format = models.CharField(
        _("Parsing report format"),
        max_length=32,
        choices=PARSING_REPORT_FORMAT_CHOICES,
        default=YAML,
    )

    can_create = models.BooleanField(default=True)
    can_update = models.BooleanField(default=True)

    cache_version = models.PositiveIntegerField(
        _("Cache version"), null=False, default=0
    )

    # Process Information
    matcher = models.OneToOneField(
        Matcher,
        verbose_name=_("Matcher"),
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
    )
    mapper = models.OneToOneField(
        Mapper,
        verbose_name=_("Mapper"),
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
    )
    parser = models.OneToOneField(
        Parser,
        verbose_name=_("Parser"),
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
    )
    configuration_upload = models.ForeignKey(
        ConfigurationUpload,
        verbose_name=_("Uploaded configuration"),
        null=True,
        blank=True,
        related_name="mapper_configuration",
        on_delete=models.SET_NULL,
        db_index=False,
    )

    fields = JSONField(_("Fields"), default=[], blank=True)

    CREATABLE_FIELDS = ("application",)
    EDITABLE_FIELDS = (
        "merchant",
        "name",
        "every",
        "period",
        "report_email_addresses",
        "izberg_entity",
        "encoding",
        "language",
        "method",
        "options",
        "save_content",
        "cache_version",
        "fields",
        "create_parsing_report",
        "parsing_report_format",
        "can_update",
        "can_create",
        "abortion_threshold_override",
        "mapper",
        "matcher",
        "parser",
    )

    # configuration options
    OMIT_IMAGE_GET_PARAMS_OPTION = "omit_image_get_params"
    CATALOG_CLEANING_ACTION = "catalog_cleaning_action"
    CATALOG_CLEANING_ACTION_MAX_ERROR_RATE = "catalog_cleaning_max_error_rate"
    ERRORS_TO_SKIP = "errors_to_skip"
    MAX_PARSED_ITEMS_PER_TASK = "max_parsed_items_per_task"
    CONFIGURATION_OPTIONS = {
        OMIT_IMAGE_GET_PARAMS_OPTION: bool,
        CATALOG_CLEANING_ACTION: str,
        ERRORS_TO_SKIP: list,
        CATALOG_CLEANING_ACTION_MAX_ERROR_RATE: int,
        MAX_PARSED_ITEMS_PER_TASK: int,
    }

    LOADER_OPTIONS = {
        "pk": int,
        "url": str,
        "login": str,
        "password": str,
        "headers": dict,
        "preview_url": str,
        "timeout": (int, float),
        "pagination": dict,
        "filepath": str,
        "file_path": str,
        "port": int,
    }

    MERCHANT_ALLOWED_ENTITIES = ("product", "offer", "product_and_offer", "price")

    class Meta:
        indexes = [
            PostgresIndex(
                fields=["application_id"],
                name="mapper_configuration_application_id_idx",
            ),
            PostgresIndex(
                fields=["created_on"],
                name="mapper_configuration_created_on_idx",
            ),
            PostgresIndex(
                fields=["last_run_date"],
                name="mapper_configuration_last_run_date_idx",
            ),
            PostgresIndex(
                fields=["status"],
                name="mapper_configuration_status_idx",
            ),
        ]

    def __str__(self):
        return "{}[{}]".format(self.__class__.__name__, self.id)

    ###
    # Properties
    ###
    @property
    def username(self):
        username = None
        if self.user is not None:
            username = self.user.username
        return username

    @property
    def description(self):
        return _("Configuration {name} (id: {id})").format(name=self.name, id=self.id)

    @property
    def last_log(self):
        return self.transformation_log.latest("id")

    @property
    def _loader_class(self):
        return self.get_related_class(self.method)

    def get_entity_schema(self, force_recache=False, filter_keys=False):
        if force_recache or not hasattr(self, "_entity_schema"):
            entity_schema = get_entity_schema_for_application(
                entity_type=self.izberg_entity,
                application_id=self.application_id,
                merchant_id=self.merchant_id,
                can_create=self.can_create,
                can_update=self.can_update,
                filter_keys=filter_keys,
                force=force_recache,
                language=self.language,
            )
            self._entity_schema = entity_schema
        return self._entity_schema

    @property
    def entity_schema(self):
        return self.get_entity_schema()

    @cached_property
    def entity_transform(self):
        from mapper.entities.transform import EntityTransform

        return EntityTransform(
            entity_name=self.izberg_entity,
            application_id=self.application_id,
            merchant_id=self.merchant_id,
            can_create=self.can_create,
            can_update=self.can_update,
            language=self.language,
        )

    @property
    def model_actions(self):
        if not hasattr(self, "_model_actions"):
            self._model_actions = ConfigurationActionsManager(self)
        return self._model_actions

    @property
    def last_crawled_handler(self):
        if not hasattr(self, "_last_crawled_handler"):
            self._last_crawled_handler = LastCrawledHandler(self)
        return self._last_crawled_handler

    @property
    def cleaning_manager(self):
        from mapper.managers import CatalogCleaningManager

        if not hasattr(self, "_cleaning_manager"):
            self._cleaning_manager = CatalogCleaningManager(self)
        return self._cleaning_manager

    @property
    def abortion_threshold(self):
        if self.abortion_threshold_override is not None:
            return self.abortion_threshold_override
        else:
            # if no override, use constance live settings to determine
            # threshold value
            return Decimal(str(live_config.MAPPER_FAILURE_ABORTION_THRESHOLD))

    def get_active_analysis(self, force_create=False):
        from .analysis_models import Analysis

        if force_create:
            return Analysis.objects.create(self, application=self.application)
        return Analysis.objects.get_active_or_create(self, application=self.application)

    ###
    # Scheduler Functions
    ###
    def update_last_run_date(self, date=None, automatic_run=False):
        """
        Set last_automatic_run_date & last_run_date to given date or take
        current if not set.
        NB: Also update "next_automatic_run_date" if is a periodic conf
        """
        date = date or timezone.now()
        self.last_run_date = date
        if automatic_run:
            self.last_automatic_run_date = date
        # save will call 'update_next_update'
        # and update next_automatic_run_date
        self.save(
            update_fields=[
                "last_run_date",
                "next_automatic_run_date",
                "last_automatic_run_date",
            ]
        )

    def update_next_update(self):
        if self.every != 0:
            last_automatic_run_date = self.last_automatic_run_date or timezone.now()
            self.next_automatic_run_date = last_automatic_run_date + relativedelta(
                **{str(self.period): int(self.every)}
            )
            # randomize a bit so feeds dont have the exact same date
            self.next_automatic_run_date += relativedelta(
                minutes=random.randint(-15, 15)
            )
        else:
            self.next_automatic_run_date = None

    @property
    def frequency(self):
        return "%s %s" % (self.every, self.get_period_display())

    ###
    # Actions
    ###
    def get_target_values(self, field_name):
        """
        ``field_name`` is the *target* field's name

        """
        # FIXME : this is just a mock implementation :
        return [{"val": i, "desc": "%s%s" % (field_name, i)} for i in range(1, 51)]

    def get_default_parser_options(self):
        options = {}
        if self.method == loaders.METHOD_GOOGLE_SPREADSHEET_LINK:
            # no need to sniff dialect, google_sheets_link is excel dialect
            options["dialect"] = "excel"
        return options

    def get_field_ids(self, force=False):
        if force or not self.fields:
            log, it = self.start_processing(
                count=10,
                preview=True,
                use_cleaner=False,
                stop_after_actions=True,
                loader_datasize=mapper_settings.IZBERG_MAPPER_ANALYZE_CHUNKSIZE,
                skip_not_modified=False,
                use_filters=False,
                force=force,
            )

            items = list(it)
            result = extract_keys(*items)
            self.fields = result
            self.save()
        return list(map(lambda x: x["id"], self.fields))

    @classmethod
    def get_related_class(cls, method):  # used in mixins.HasOptionForm
        return loaders.get_class(method)

    def auto_detect_format(self, force=False, ignore_errors=False):
        """Try to find the right parser for method"""

        try:
            method = self.loader.guess_method()
            if not method:
                raise ValidationError(_("Cannot detect method"))
            if self.parser is None:
                self.parser = Parser.objects.create(
                    application=self.application,
                    merchant=self.merchant,
                    method=method,
                    options=self.get_default_parser_options(),
                )
                self.auto_detect_options(force=True, ignore_errors=True)
                self.save()
            elif self.parser.method != method:
                # Update current parser
                self.parser.method = method
                self.parser.options = self.get_default_parser_options()
                self.auto_detect_options(force=True, ignore_errors=True)
                self.parser.save()
        except BaseHTTPError as err:
            if not ignore_errors or settings.RAISE_SILENT_ERRORS:
                raise ValidationError(
                    _("An error occured while downloading the feed: {error}").format(
                        error=err
                    )
                )
            logger.warning("Can't detect parser format for Configuration {self.id}")
        except Exception:
            if not ignore_errors or settings.RAISE_SILENT_ERRORS:
                raise
            logger.warning(
                "Can't detect parser format for Configuration {}".format(self.id)
            )

    def auto_detect_options(self, force=False, ignore_errors=False):
        try:
            if self.parser is None:
                raise ValidationError(_("Missing Parser"))

            if self.parser.method:
                stream = next(
                    self.loader.iterate(
                        max_datasize=mapper_settings.IZBERG_MAPPER_ANALYZE_CHUNKSIZE,
                        force=force,
                    )
                )[1]
                try:
                    options = ParserOptionsDetector().detect(
                        self.parser.method,
                        stream,
                        self.get_entity_schema(force_recache=force),
                        self,
                    )
                except Exception:
                    raise ValidationError("Cannot find options for method")

                if options:
                    if self.parser.options:
                        self.parser.options.update(options)
                    else:
                        self.parser.options = options
                    self.parser.save()
        except Exception:
            if not ignore_errors or settings.RAISE_SILENT_ERRORS:
                raise
            logger.warning(
                "Cant detect parser options for Configuration [{}]".format(self.id)
            )

    def auto_map_attributes(self):
        """Auto-map attributes and return newly created mapper details."""
        if self.mapper is None:
            self.mapper = Mapper.objects.create(
                application=self.application, merchant=self.merchant
            )
            self.save()

        if self.izberg_entity is None or self.parser is None:
            return []

        return self.mapper.auto_map_attributes(self.get_field_ids())

    def initialize(
        self, auto=False, parser_method=None, parser_options=None, force=False
    ):
        """
        Call initialize for creating and configuring all needed related
        objects.

        @auto: Boolean. If True, will use parser guess method for method and
        options detection and will try to auto map fields
        """
        parser_options = parser_options or self.get_default_parser_options()

        if self.parser is None:
            if auto:
                self.auto_detect_format(force=force, ignore_errors=True)
                self.auto_detect_options(force=force, ignore_errors=True)
            else:
                self.parser = Parser.objects.create(
                    method=parser_method,
                    options=parser_options,
                    application=self.application,
                    merchant=self.merchant,
                )
        if self.matcher is None:
            self.matcher = Matcher.objects.create(
                application=self.application,
                merchant=self.merchant,
                izberg_entity=self.izberg_entity,
            )
        if self.mapper is None:
            self.mapper = Mapper.objects.create(
                application=self.application, merchant=self.merchant
            )

            if auto:
                self.auto_map_attributes()
        self.save()

    @property
    def is_processing(self):
        return cache.get(self.generate_process_import_cache_key()) is not None

    def set_processing(self):
        cache_key = self.generate_process_import_cache_key()
        try:
            cache.set(cache_key, True, 60 * 60 * 24)  # 1 day
        except OutOfMemoryError as e:
            logger.error(
                "Out of memory error while trying to set {} cache key: {}".format(
                    cache_key, e
                )
            )

    def set_finished_processing(self):
        cache.delete(self.generate_process_import_cache_key())

    def increment_cache_version(self):
        self.cache_version += 1
        self.save(update_fields=["cache_version"])

    def get_queue_name(self, priority) -> str:
        mid_or_global = (
            self.merchant_id
            if self.merchant_id and live_config.MAPPER_PER_MERCHANT_QUEUES
            else "global"
        )
        return "{priority}:{application_id}_{mid_or_global}_{suffix}".format(
            priority=priority,
            application_id=self.application_id,
            mid_or_global=mid_or_global,
            suffix=settings.MAPPER_QUEUE_SUFFIX,
        )

    def iterate_cleaners(self, items):
        work_items = [(True, item, {}) for item in items]
        for cleaner in self.cleaners.order_by("index"):
            work_items = cleaner.iterate(work_items)
        return work_items

    def get_cached_actions(self, use_filters=True):
        from . import Action

        if use_filters:
            if not hasattr(self, "_actions"):
                setattr(
                    self,
                    "_actions",
                    list(
                        self.actions.filter(
                            status=Action.ACTION_STATUS_ENABLED
                        ).order_by("index")
                    ),
                )
            return self._actions
        else:
            if not hasattr(self, "_actions_without_filters"):
                actions_without_filters = (
                    self.actions.filter(status=Action.ACTION_STATUS_ENABLED)
                    .exclude(method__in=actions.METHODS_THAT_CAN_SKIP)
                    .order_by("index")
                )
                setattr(self, "_actions_without_filters", list(actions_without_filters))
            return self._actions_without_filters

    def actions_transform(self, item, use_filters=True):
        for action in self.get_cached_actions(use_filters=use_filters):
            try:
                item = action.transform(item)
            except Exception as e:
                raise exceptions.ActionError(e, trigger_in=action)
        return item

    def _raise_for_mapper_off(self):
        if self.application.get_setting(MapperSwitchOff):
            raise ValidationError(
                {
                    "error": _(
                        "Cannot synchronously run import because mapper is "
                        "temporarily off"
                    )
                }
            )

    def _set_processing_or_raise(self):
        if self.is_processing:
            raise AlreadyProcessingException(self)
        self.set_processing()

    def run_import(
        self,
        count=None,
        gateway=None,
        async_task=True,
        skip_not_modified=True,
        trigger_type=TransformationLog.TRIGGER_TYPE_MANUAL,
    ):
        """
        Import the current configuration
        """
        gateway = gateway or self.get_default_gateway()
        if not async_task:
            self._raise_for_mapper_off()

        self._set_processing_or_raise()

        log = TransformationLog.objects.create(
            application=self.application,
            configuration=self,
            merchant=self.merchant,
            trigger_type=trigger_type,
        )
        logger.info(f"Importing configuration[{self.id}] - log[{log.id}]")
        kwargs = {
            "log_id": log.id,
            "configuration_id": self.id,
            "application_id": self.application_id,
            "merchant_id": self.merchant_id,
            "count": count,
            "gateway": gateway,
            "skip_not_modified": skip_not_modified,
        }
        if async_task and mapper_settings.IZBERG_MAPPER_ASYNC_RUN:
            countdown = (
                settings.MAPPER_OFF_POLL_INTERVAL
                if self.application.get_setting(MapperSwitchOff)
                else None
            )
            self.process_import.apply_async(
                kwargs=kwargs,
                countdown=countdown,
            )
        else:
            return self.process_import(**kwargs)

        return log

    def generate_process_import_cache_key(self):
        return "{base}:process_import".format(base=self.get_base_cache_key())

    def get_default_gateway(self):
        return self.get_entity_schema().get("default_gateway", "async")

    def _get_gateway(self, gateway, log, run_by):
        from mapper.gateways.async_output import AsyncIterator
        from mapper.gateways.file_output import FileOutputIterator
        from mapper.gateways.live_output import LiveOutputIterator

        iterators = {
            "file": FileOutputIterator,
            "core": AsyncIterator,  # deprecated
            "async": AsyncIterator,
            "live": LiveOutputIterator,
        }
        try:
            iterator = iterators[gateway]
        except KeyError:
            raise Exception('"{}" is an invalid gateway'.format(gateway))
        return iterator(configuration=self, log=log, run_by=run_by)

    @shared_task(queue=mapper_settings.PARSING_QUEUE_NAME, serializer="json")
    def process_import(
        configuration_id,  # noqa: N805
        count,
        application_id,
        stop_after_actions=False,
        use_cleaner=False,
        run_by=None,
        gateway=None,
        save_data=True,
        log_id=None,
        force=False,
        skip_not_modified=True,
        from_index=None,
        merchant_id=None,
    ):
        conf = Configuration.objects.get(id=configuration_id)
        log = TransformationLog.objects.get(id=log_id) if log_id else None
        if log and not gateway:
            raise Exception("A gateway must be supplied for import")
        accepted_statuses = [
            TransformationLogWorkflow.IN_QUEUE,
            TransformationLogWorkflow.PARSING,
        ]
        if log and log.status not in accepted_statuses:
            return {
                "action": "ignored",
                "reason": "invalid status",
                "status": log.status,
            }
        if conf.application.get_setting(MapperSwitchOff):
            next_task = conf.process_import.apply_async(
                kwargs={
                    "log_id": log_id,
                    "configuration_id": conf.id,
                    "application_id": conf.application_id,
                    "merchant_id": conf.merchant_id,
                    "stop_after_actions": stop_after_actions,
                    "use_cleaner": use_cleaner,
                    "run_by": run_by,
                    "gateway": gateway,
                    "save_data": save_data,
                    "skip_not_modified": skip_not_modified,
                    "from_index": from_index,
                    "count": count,
                },
                countdown=settings.MAPPER_OFF_POLL_INTERVAL,
            )
            return {
                "action": "postponed",
                "reason": "mapper off",
                "next_task": next_task.id,
            }
        download_content = (
            gateway == "async"
            and isinstance(log, TransformationLog)
            and conf.application.get_setting(MapperPreDownloadFeed)
            and not log.content_file
        )
        if download_content:
            # only download content and requeue for parsing
            with override(conf.language):
                with handle_errors(log):
                    log.download_content()

            if log.status == TransformationLogWorkflow.FAILED:
                return {
                    "action": "failed",
                    "reason": "content pre-download failed",
                }

            next_task = conf.process_import.apply_async(
                kwargs={
                    "log_id": log_id,
                    "configuration_id": conf.id,
                    "application_id": conf.application_id,
                    "merchant_id": conf.merchant_id,
                    "stop_after_actions": stop_after_actions,
                    "use_cleaner": use_cleaner,
                    "run_by": run_by,
                    "gateway": gateway,
                    "save_data": save_data,
                    "skip_not_modified": skip_not_modified,
                    "from_index": from_index,
                    "count": count,
                },
            )
            return {
                "action": "downloaded",
                "reason": "content pre-download enabled",
                "next_task": next_task.id,
            }
        with override(conf.language):
            with handle_errors(log):
                log, items = conf.start_processing(
                    count=count,
                    preview=False,
                    save_data=save_data,
                    stop_after_actions=stop_after_actions,
                    use_cleaner=use_cleaner,
                    log=log,
                    data_validation=True,
                    skip_not_modified=skip_not_modified,
                    force=force,
                    aggregate=True,
                    from_index=from_index,
                )
                if gateway:
                    conf._get_gateway(gateway, log, run_by).send(items)
                else:
                    list(items)  # loop on iterator
                    log.finish()
        if log:
            return {
                "action": "parsed",
                "valid_count": log.valid_count,
                "invalid_count": log.invalid_count,
                "not_modified_count": log.not_modified_count,
                "next_task": conf._get_next_task_id(),
            }
        else:
            return True

    def run_import_preview(self, count, force=False):
        log, items = self.start_processing(
            count=count,
            preview=False,
            save_data=False,
            stop_after_actions=False,
            use_cleaner=False,
            data_validation=True,
            loader_datasize=mapper_settings.IZBERG_MAPPER_PREVIEW_CHUNKSIZE,
            skip_not_modified=False,
            force=force,
            aggregate=False,
        )

        return log, [
            {"status": status, "item": item, "info": info}
            for status, item, info in items
        ]

    def _raise_invalid_option_key(self, expected_type, key):
        if isinstance(expected_type, tuple):
            expected_type = ", ".join((t.__name__ for t in expected_type))
        else:
            expected_type = expected_type.__name__
        raise ValidationError(
            {
                "options": _("Expected type '{type}' for option '{key}'").format(
                    type=expected_type, key=key
                )
            }
        )

    def _validate_header_options(self):
        if "headers" not in self.options:
            return
        for key, value in self.options["headers"].items():
            if not isinstance(value, str):
                self._raise_invalid_option_key(str, "headers." + key)

    def _validate_timeout(self):
        if "timeout" not in self.options:
            return
        elif self.options["timeout"] > settings.MAPPER_MAX_TIMEOUT:
            raise ValidationError(
                {
                    "options": _("Timeout must be lower than {max_timeout}").format(
                        max_timeout=settings.MAPPER_MAX_TIMEOUT
                    )
                }
            )
        elif self.options["timeout"] <= 0:
            raise ValidationError({"options": _("Timeout must be greater than 0")})

    def _validate_catalog_cleaning_max_error_rate(self):
        if self.CATALOG_CLEANING_ACTION_MAX_ERROR_RATE not in self.options:
            return
        value = self.options[self.CATALOG_CLEANING_ACTION_MAX_ERROR_RATE]
        if value > 100:
            raise ValidationError(
                {"options": _("Max error rate must be lower than 100")}
            )
        elif value < 0:
            raise ValidationError(
                {"options": _("Max error rate must be greater than 0")}
            )

    def _validate_errors_to_skip(self):
        skippable_errors = set([PRODUCT_NOT_FOUND])
        if self.ERRORS_TO_SKIP not in self.options:
            return
        non_skippable = set(self.options[self.ERRORS_TO_SKIP]) - skippable_errors
        if non_skippable:
            raise ValidationError(
                {
                    "options": _(
                        "Non skippable error(s): {non_skippable}. Skippable are:"
                        " {skippable_errors}."
                    ).format(
                        non_skippable=", ".join(non_skippable),
                        skippable_errors=", ".join(skippable_errors),
                    )
                }
            )

    def _validate_options(self):
        options = (self.CONFIGURATION_OPTIONS, self.LOADER_OPTIONS)
        for option_dict in options:
            for key, expected_type in option_dict.items():
                if key not in self.options:
                    continue
                if not isinstance(self.options[key], expected_type):
                    self._raise_invalid_option_key(expected_type, key)
        self.cleaning_manager.validate_cleaning_options()
        self._validate_header_options()
        self._validate_timeout()
        self._validate_errors_to_skip()
        self._validate_catalog_cleaning_max_error_rate()

    @property
    def loader_options(self):
        return {
            key: self.options[key] for key in self.options if key in self.LOADER_OPTIONS
        }

    @property
    def loader(self):
        if not hasattr(self, "_loader"):
            self._loader = self._loader_class(self, **self.loader_options)
        return self._loader

    def start_processing(
        self,
        count=None,
        preview=True,
        stop_after_actions=False,
        use_cleaner=True,
        save_data=True,
        log=None,
        data_validation=False,
        loader_datasize=None,
        skip_not_modified=False,
        use_filters=True,
        force=False,
        aggregate=True,
        from_index=None,
    ):
        """
        Will trigger a Configuration processing.

        @count: Int: Stop after x items
        @preview: Boolean: Add non mapped item in results
        @stop_after_actions: Boolean: Dont do matching phase
        @use_cleaner: Boolean: If false, will skip the cleaner phase
        @save_data: Boolean: Force use of InMemoryTransformationLog

        Return tuple
            - Log object
            - iteraror
        """
        if log is None:
            if preview or not save_data:
                log_klass = utils.InMemoryTransformationLog
            else:
                log_klass = TransformationLog

            log = log_klass(configuration=self)
            log.application = log.configuration.application
            log.merchant = log.configuration.merchant
            log.save()

        logger.info(
            "Process Configuration {id} with Count: {count}, "
            "Use Cleaner: {use_cleaner}, save_data: {save_data}".format(
                id=self.id, count=count, use_cleaner=use_cleaner, save_data=save_data
            )
        )
        if log.status != TransformationLogWorkflow.PARSING:
            log.parse()
        return log, self.iterate(
            log,
            count=count,
            stop_after_actions=stop_after_actions,
            use_cleaner=use_cleaner,
            data_validation=data_validation,
            loader_datasize=loader_datasize,
            skip_not_modified=skip_not_modified,
            use_filters=use_filters,
            force=force,
            preview=preview,
            aggregate=aggregate,
            from_index=from_index,
        )

    def pre_iterate(
        self,
        stop_after_actions=False,
        use_cleaner=True,
        loader_datasize=None,
        use_filters=True,
        force=False,
        original_stream=None,
        preview=True,
        aggregate=True,
        log=None,
    ):
        """
        pre_iterate contains 4 steps:
            - parser
            - cleaner
            - actions
            - mapping
        """
        current_fields = (self.fields or [])[:]
        mapper = self.mapper if not stop_after_actions else IsoMapper()
        entity_transform = self.entity_transform if not stop_after_actions else None
        cols_limit = live_config.MAPPER_MAX_COLUMNS

        if not self.parser:
            raise exceptions.ParserMustBeDefined
        if not mapper:
            raise exceptions.MapperMustBeDefined
        use_downloaded_content = (
            isinstance(log, TransformationLog)
            and log.content_file
            and self.application.get_setting(MapperPreDownloadFeed)
        )
        if use_downloaded_content:
            loader = log.content_loader
        else:
            loader = self.loader
        iterator = loader.iterate(max_datasize=loader_datasize, force=force)
        entity_schema = self.get_entity_schema(force_recache=force)
        for md5sum, stream in iterator:
            if original_stream:
                stream.seek(0)
                original_stream.write(stream.read().encode(self.loader.encoding))
                stream.seek(0)
            iterator = iterate_on_stream(
                md5sum,
                stream,
                self.parser,
                mapper,
                entity_transform,
                self,
                preview_mode=loader_datasize is not None,
                use_filters=use_filters,
                force=force,
                use_cleaners=use_cleaner,
            )

            aggregate_on_field = entity_schema.get("aggregate_on_field", False)
            if aggregate and aggregate_on_field:
                iterator = aggregate_dict_stream_on_key(
                    iterator, entity_schema["aggregate_on_field"]
                )
            for status, info, after_action_item in iterator:
                yield status, info

                # using after_action_item to update fields :
                if after_action_item is not None:
                    try:
                        after_action_item_fields = extract_keys(after_action_item)
                        current_fields = recursively_merge_lists(
                            current_fields, after_action_item_fields
                        )
                        self.fields = current_fields
                    except Exception as e:
                        logger.exception("In preiterate: {}".format(e))
                        # FIXME : Classical anti-pattern...
                        pass

                    if len(current_fields) > cols_limit:
                        raise ExceedMaxColumns(cols_limit)

        self.fields = current_fields
        self.save()

    def get_base_cache_key(self):
        return "mapper:conf:{id}:version:{version}".format(
            id=self.id, version=self.cache_version
        )

    def generate_item_cache_key(self, item):
        lookup_keys = self.entity_schema["lookup_keys"]
        lookup_values = [item.get(key) for key in lookup_keys]
        lookup_values_hash = md5(ensure_binary(json.dumps(lookup_values))).hexdigest()
        return "{base}:item_id:{lookup_values_hash}".format(
            base=self.get_base_cache_key(), lookup_values_hash=lookup_values_hash
        )

    def item_not_modified(self, item):
        legacy_cache = get_legacy_cache()
        try:
            cache_key = self.generate_item_cache_key(item)
            cached_content_hash = cache.get(cache_key)
            if not cached_content_hash and legacy_cache:
                # check again in legacy cache
                cached_content_hash = legacy_cache.get(cache_key)
            if isinstance(cached_content_hash, bytes):
                # cached value from python2, to drop 1 week after migration
                cached_content_hash = cached_content_hash.decode()
            content_hash = md5(
                ensure_binary(json.dumps(item, sort_keys=True, cls=IzbergJSONEncoder))
            ).hexdigest()
            if cached_content_hash != content_hash:
                try:
                    cache.set(cache_key, content_hash, ITEM_CACHE_DURATION)
                except OutOfMemoryError as e:
                    logger.error(
                        "Out of memory error while trying to set {} cache key: {}".format(
                            cache_key, e
                        )
                    )
                return False
            return True
        except Exception as e:
            if settings.RAISE_SILENT_ERRORS:
                raise
            logger.exception("in item_not_modified: {}".format(e))

    def _invalidate_item_cache(self, item):
        cache_key = self.generate_item_cache_key(item)
        return cache.delete(cache_key)

    def log_item_to_parsing_report(
        self, parsing_report, index, result_item, original_item, status
    ):
        if isinstance(result_item, tuple):
            # went through validation, getting the validation status
            validation_status, report_item, validation_report = result_item
            report_data = {
                "index": index,
                "status": validation_status,
                "validation_errors": validation_report.get("errors"),
                "validation_warning": validation_report.get("warnings"),
                "original_item": json_serializable(original_item),
                "processed_item": json_serializable(report_item),
            }
        else:
            if status == "error":
                original_item = original_item["source"]

            report_data = {
                "index": index,
                "status": status,
                "original_item": json_serializable(original_item),
            }
        if self.parsing_report_format == "yaml":
            yaml.safe_dump(
                [report_data],
                parsing_report,
                default_flow_style=False,
                encoding="utf-8",
            )
        elif self.parsing_report_format == "json":
            json.dump(report_data, parsing_report, indent=4)

    def _handle_valid_item(
        self, result_item, skip_not_modified, data_validation, log, nb_items, matcher
    ):
        from mapper.models import TransformationLogDetail  # noqa

        result_item = matcher.transform(result_item)
        status = "valid"
        result = result_item
        if result_item is not None:
            if not log.is_in_memory:
                self.last_crawled_handler.store_references(result_item)
            if skip_not_modified and self.item_not_modified(result_item):
                status = "not_modified"
                log.counters.increase_not_modified_count(quantity=nb_items)
            else:
                if data_validation:
                    result = self.entity_transform.validation(result_item, log)
                    transform_status = result[0]
                    invalid_item_cache = (
                        skip_not_modified
                        and transform_status == TransformationLogDetail.KIND_INVALID
                    )
                    if invalid_item_cache:
                        self._invalidate_item_cache(result_item)
                    warning_count = nb_items * len(result[2]["warnings"])
                    if warning_count:
                        log.counters.increase_warning_count(quantity=warning_count)
                else:
                    log.counters.increase_valid_count(quantity=nb_items)
        return status, result

    def _set_up_original_stream(self, log=None):
        if isinstance(log, TransformationLog) and bool(log.content_file):
            # content already downloaded
            return None
        try:
            return TemporaryFile() if self.save_content else None
        except OSError:
            logger.exception("In Mapper Iterate while setting up original stream:")
            raise

    def _set_up_parsing_report(self, log=None):
        if not self.create_parsing_report:
            return None
        temp_file = TemporaryFile()
        if isinstance(log, TransformationLog) and bool(log.parsing_report):
            with log.parsing_report.open() as existing_file:
                temp_file.write(existing_file.read())
        return temp_file

    def _handle_invalid_item_by_status(self, status, item, nb_items, log):
        if status == "skipped":
            source = item["source"]
            trigger_description = item["trigger_description"]
            trigger_resource_uri = item["trigger_resource_uri"]
            log.add_skipped(
                source=source,
                item_count=nb_items,
                trigger_description=trigger_description,
                trigger_resource_uri=trigger_resource_uri,
            )
        elif status == "warning":
            log.add_warning(_("Unknown error"), item, item_count=nb_items)
        elif status == "error":
            source = item["source"]
            trigger_description = item["trigger_description"]
            trigger_resource_uri = item["trigger_resource_uri"]
            traceback = item["traceback"]
            error_code = item.get("error_code", UNKNOWN_ERROR)
            log.add_error(
                error_code=error_code,
                item_count=nb_items,
                source=source,
                trigger_description=trigger_description,
                trigger_resource_uri=trigger_resource_uri,
                traceback=traceback,
            )
        if status == "warning":
            data = item
        else:
            data = item["source"]
        if not log.is_in_memory:
            self.last_crawled_handler.store_references(data)

    def iterate(
        self,
        log,
        count=None,
        stop_after_actions=False,
        use_cleaner=True,
        data_validation=False,
        loader_datasize=None,
        skip_not_modified=False,
        use_filters=True,
        force=False,
        preview=True,
        aggregate=True,
        from_index=None,
    ):
        """
        Run the 5 steps of the i
        """
        matcher = self.matcher if not stop_after_actions else IsoMatcher()
        matcher = matcher or IsoMatcher()
        if preview:
            original_stream = parsing_report = None
        else:
            original_stream = self._set_up_original_stream(log)
            parsing_report = self._set_up_parsing_report(log)

        items_generator = self.pre_iterate(
            stop_after_actions=stop_after_actions,
            use_cleaner=use_cleaner,
            loader_datasize=loader_datasize,
            use_filters=use_filters,
            force=force,
            original_stream=original_stream,
            preview=preview,
            aggregate=aggregate,
            log=log,
        )
        counter = 0  # Only on valid elem
        looper = 0  # For every iterator item
        requeue_from_index = None
        for index, (status, item) in enumerate(items_generator):
            if from_index is not None and index < from_index:
                # fast forward until reaching from_index
                continue
            nb_items = item.get("nb_items", 1)
            looper += nb_items
            result_item = item
            original_item = deepcopy(item)
            if status == "valid":
                counter += 1
                status, result_item = self._handle_valid_item(
                    result_item,
                    skip_not_modified,
                    data_validation,
                    log,
                    nb_items,
                    matcher,
                )
                if status == "valid":
                    yield result_item
            else:
                self._handle_invalid_item_by_status(status, item, nb_items, log)

            if parsing_report:
                try:
                    self.log_item_to_parsing_report(
                        parsing_report, index, result_item, original_item, status
                    )
                except Exception as err:
                    if settings.RAISE_SILENT_ERRORS:
                        raise
                    logger.exception("while logging item: {}".format(err))

            self._log_item_parsed_event(log, index, status, result_item, original_item)
            self.last_crawled_handler.batch_commit(min_batch_size=50)
            if looper % 5000 - 49 == 0:  # first save after 50 items
                log.counters.save_counts()
            if self._count_reached_or_aborted(counter, looper, count, log):
                break
            if log.allow_parsing_interruption and self._must_interrupt_parsing(index):
                requeue_from_index = index + 1
                break

        # update all pending
        self.last_crawled_handler.batch_commit(min_batch_size=1)
        log.on_parsing_done(
            original_stream=original_stream,
            parsing_report=parsing_report,
            switch_to_process=requeue_from_index is None,
        )
        if requeue_from_index is not None:
            self._requeue_from_index(log, requeue_from_index, count)

    def _log_item_parsed_event(self, log, index, status, result_item, original_item):
        if log.is_in_memory:
            return

        parsed_item = parsing_result = None
        if isinstance(result_item, tuple):
            parsed_item = result_item[1]
            parsing_result = result_item[2]
        elif status == "not_modified":
            parsed_item = result_item

        log.event_loggers.log_item_parsed(
            item_index=index,
            parsed_item=parsed_item,
            original_item=original_item,
            parsing_result=parsing_result,
            success=status != "error",
            status=status,
        )

    def _set_next_task_id(self, task_id):
        self._next_task_id = task_id

    def _get_next_task_id(self):
        return getattr(self, "_next_task_id", None)

    def _must_interrupt_parsing(self, index: int) -> bool:
        """
        Two reasons can provoke the interruption + requeuing:
        - if worker is being shutdown
        - if we reached the MAX_PARSED_ITEMS_PER_TASK option
        """
        from apps.tasking.shutdown import (  # imported here to allow mock
            get_shutdown_flag,
        )

        return bool(get_shutdown_flag()) or (
            self.MAX_PARSED_ITEMS_PER_TASK in self.options
            and (index + 1) % self.options[self.MAX_PARSED_ITEMS_PER_TASK] == 0
        )

    def _requeue_from_index(self, log: TransformationLog, from_index: int, count):
        next_task = self.process_import.apply_async(
            kwargs={
                "log_id": log.id,
                "configuration_id": self.id,
                "application_id": self.application_id,
                "merchant_id": self.merchant_id,
                "gateway": self.get_default_gateway(),
                "from_index": from_index,
                "count": count,
            },
        )
        self._set_next_task_id(next_task.id)

    def _count_reached_or_aborted(self, counter, looper, count, log):
        if count is not None and counter >= count:
            # we reached asked 'count'
            return True
        if not log.is_in_memory and (looper + 1) % 100 == 0:
            log.refresh_from_db()
            if log.status.is_aborted:
                # trans log has been aborted during parsing, stopping here
                return True
        return False

    def full_clean(self, *args, **kwargs):
        super().full_clean(*args, **kwargs)
        utils.check_method_parameter(self.method, loaders.LOADERS_LIST)
        utils.check_init_parameters(
            self._loader_class, self.loader_options, error_key="options"
        )
        self._validate_options()
        # check that parameters passed to loader class are valid :
        loader_form_klass = self.option_form_class(self.method)
        loader_form = loader_form_klass(self.loader_options)
        if not loader_form.is_valid():
            raise ValidationError(loader_form.errors)

        # Check that the merchant belongs to the application
        merchant_does_not_belong_to_app = (
            self.merchant and self.merchant.application_id != self.application.id
        )
        if merchant_does_not_belong_to_app:
            raise ValidationError(_("Merchant does not belong to application"))

        if self.merchant_id:
            self._validate_entity_for_merchant()

        if "url" in self.options:
            self.options["url"] = self.options["url"].strip(" ").strip("\t")

    def _validate_entity_for_merchant(self):
        if self.izberg_entity not in self.MERCHANT_ALLOWED_ENTITIES:
            self.set_finished_processing()
            raise ValidationError(
                {"izberg_entity": _("Invalid value for merchant configuration")}
            )

        if self.izberg_entity == "product":
            self._raise_for_forbidden_merchant_products()

    def _raise_for_forbidden_merchant_products(self):
        if not self.application.get_setting(IsClosedCatalog):
            # no restriction on open catalog
            return
        if not self.application.get_setting(AllowMerchantProducts):
            self.set_finished_processing()
            raise ValidationError(_("Merchants are not allowed to import products"))

        if self.merchant.product_submission == self.merchant.NOT_ALLOWED:
            self.set_finished_processing()
            raise ValidationError(_("This merchant is not allowed to import products"))

    def save(self, *args, **kwargs):
        self.full_clean()
        self.update_next_update()
        super(Configuration, self).save(*args, **kwargs)

    def _get_category_ids_from_internal_ids(self) -> list:
        disabled_category_matching = (
            self.matcher.disable_matching
            and "application_categories" in self.matcher.disable_matching
        )
        if disabled_category_matching:
            # get cat ids directly from values detected in analysis
            cat_iterator = self.analyses.last_valid().get_analyzed_values(
                self.mapper.category_mapping.feed_attribute_id, flat=True
            )
        else:
            # get cat ids from matched values
            cat_iterator = self.matcher.get_details_values("application_categories")

        cat_ids = []
        for cat_id in cat_iterator:
            try:
                cat_ids.append(int(cat_id))
            except Exception:
                pass

        # filter on existing categories to ignore bad values
        return list(
            ApplicationCategory.objects.filter(
                application=self.application,
                status=ApplicationCategory.STATUS_ACTIVE,
                id__in=cat_ids,
            ).values_list("id", flat=True)
        )

    def _get_category_external_ids_from_last_analysis(self) -> set:
        external_ids = set()
        raw_value_iterator = self.analyses.last_valid().get_analyzed_values(
            self.mapper.category_mapping.feed_attribute_id, flat=True
        )
        for raw_value in raw_value_iterator:
            # can be comma-separated ids, splitting value
            external_ids = external_ids.union(
                set(split_on_non_escaped_delimiters(raw_value))
            )
        return external_ids

    def _get_category_ids_from_external_ids(self) -> list:
        return list(
            ApplicationCategory.objects.filter(
                application=self.application,
                status=ApplicationCategory.STATUS_ACTIVE,
                external_id__in=self._get_category_external_ids_from_last_analysis(),
            ).values_list("id", flat=True)
        )

    def get_matched_category_ids(self) -> list:
        """resolve category IDs based on config mapping/matching"""
        category_mapping = self.mapper.category_mapping
        if not category_mapping:
            return []
        mapping_type_to_func = {
            "application_categories": self._get_category_ids_from_internal_ids,
            "application_category_external_ids": self._get_category_ids_from_external_ids,
        }
        return mapping_type_to_func[category_mapping.entity_attribute_id]()

    def must_skip_error(self, error_code):
        return error_code in self.options.get(self.ERRORS_TO_SKIP, [])


class ConfigurationExport(
    SanitizeCharFieldMixin, ChangedFieldLookupMixin, models.Model
):
    objects = IzbergQueryset.as_manager()

    application = models.ForeignKey(
        "ice_applications.Application",
        verbose_name=_("Application"),
        null=True,
        blank=True,
        related_name="+",
        on_delete=models.CASCADE,
        db_index=False,
    )
    merchant = models.ForeignKey(
        "stores.Merchant",
        verbose_name=_("Merchant"),
        null=True,
        blank=True,
        related_name="+",
        on_delete=models.CASCADE,
        db_index=False,
    )
    configuration_file = models.FileField(
        _("Mapper configuration export"),
        upload_to=FilePathGenerator(),
        max_length=1024,
    )
    configuration_file_bkp = models.FileField(
        null=True,
        default=None,
        blank=True,
        max_length=1024,
    )

    mapper_configuration = models.ForeignKey(
        Configuration,
        verbose_name=_("Mapper configuration"),
        null=False,
        blank=False,
        related_name="exported_configurations",
        on_delete=models.CASCADE,
        db_index=False,
    )

    exported_on = models.DateField(
        _("Exported on"),
        auto_now_add=True,
        null=True,
    )

    filename = models.CharField(
        _("File name"),
        max_length=256,
        blank=True,
        null=True,
    )

    exported_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        verbose_name=_("Exported by"),
        null=True,
        blank=True,
        related_name="exported_mapper_configuration",
        on_delete=models.CASCADE,
        db_index=False,
    )

    @property
    def actions(self):
        if not hasattr(self, "_actions"):
            self._actions = ConfigurationExportActionsManager(self)
        return self._actions
