# -*- coding: utf-8 -*-


from django.utils.translation import gettext_lazy as _
from mapper.utils import LANGUAGE_CHOICES

from ..schema import Schema

MERCHANT_ENTITY_SCHEMA = Schema(
    {
        "entity_name": "merchant",
        "entity_verbose_name": _("Merchants"),
        "importer": "mapper.tasks.import_tasks.MerchantImporter",
        "fieldGroups": [
            {
                "key": "images",
                "order": 0,
                "name": _("Images"),
                "display_type": "images",
            },
            {
                "key": "general",
                "order": 1,
                "name": _("General"),
                "display_type": "large",
            },
            {
                "key": "bank_account",
                "order": 2,
                "name": _("Bank Account"),
                "display_type": "large",
            },
            {
                "key": "company",
                "order": 2,
                "name": _("Company"),
                "display_type": "regular",
            },
            {
                "key": "billing_address",
                "order": 2,
                "name": _("Billing Address"),
                "display_type": "regular",
            },
            {
                "key": "return_address",
                "order": 2,
                "name": _("Return Address"),
                "display_type": "regular",
            },
            {
                "key": "merchant_group",
                "order": 2,
                "name": _("Merchant Groups"),
                "display_type": "regular",
            },
            {
                "key": "details",
                "order": 2,
                "name": _("Details"),
                "display_type": "regular",
            },
            {
                "key": "identifiers",
                "order": 3,
                "name": _("Identifiers"),
                "display_type": "regular",
            },
            {
                "key": "offer",
                "order": 3,
                "name": _("Offer Information"),
                "display_type": "regular",
            },
            {
                "key": "backoffice_access",
                "order": 3,
                "name": _("Backoffice Access"),
                "display_type": "regular",
            },
        ],
        "fields": [
            {
                "id": "name",
                "alternative_names": ["merchant_name"],
                "field_type": "string",
                "faker": "last_name",
                "name": _("Name"),
                "help_text": _("Merchant name"),
                "required_for_create": True,
                "required_for_update": False,
                "blank": True,
                "field_group": "general",
                "max_length": 128,
            },
            {
                "id": "external_id",
                "name": _("External id"),
                "help_text": _("Unique identifier of the merchant"),
                "field_type": "string",
                "required_for_create": True,
                "required_for_update": True,
                "max_length": 200,
            },
            {
                "id": "billing_address_contact_email",
                "alternative_names": ["address_contact_email"],
                "field_type": "string",
                "faker": "email",
                "name": _("Billing address contact email"),
                "help_text": _("Billing address contact email"),
                "field_group": "billing_address",
                "max_length": 255,
            },
            {
                "id": "billing_address_contact_first_name",
                "alternative_names": ["address_contact_first_name"],
                "field_type": "string",
                "faker": "first_name",
                "name": _("Billing address contact first name"),
                "help_text": _("Billing address contact first name"),
                "field_group": "billing_address",
                "max_length": 128,
            },
            {
                "id": "billing_address_contact_last_name",
                "alternative_names": ["address_contact_last_name"],
                "field_type": "string",
                "faker": "last_name",
                "name": _("Billing address contact last name"),
                "help_text": _("Billing address contact last name"),
                "field_group": "billing_address",
                "max_length": 128,
            },
            {
                "id": "billing_address_phone",
                "alternative_names": ["address_phone"],
                "field_type": "string",
                "faker": "phone_number",
                "name": _("Billing address phone"),
                "help_text": _("Billing address phone"),
                "field_group": "billing_address",
                "max_length": 255,
            },
            {
                "id": "billing_address_zipcode",
                "alternative_names": ["address_zipcode"],
                "field_type": "string",
                "faker": "zipcode",
                "name": _("Billing address zipcode"),
                "help_text": _("Billing address zipcode"),
                "field_group": "billing_address",
                "max_length": 20,
            },
            {
                "id": "billing_address_city",
                "alternative_names": ["address_city"],
                "field_type": "string",
                "faker": "city",
                "name": _("Billing address city"),
                "help_text": _("Billing address city"),
                "field_group": "billing_address",
                "max_length": 50,
            },
            {
                "id": "billing_address_address",
                "alternative_names": ["address_address"],
                "field_type": "string",
                "faker": "address",
                "name": _("Billing address"),
                "help_text": _(" Billing address"),
                "field_group": "billing_address",
                "max_length": 355,
            },
            {
                "id": "billing_address_address2",
                "alternative_names": ["address_address2"],
                "field_type": "string",
                "name": _("Billing address addtional info"),
                "help_text": _("Billing address addtional info"),
                "field_group": "billing_address",
                "max_length": 355,
            },
            {
                "id": "billing_address_country",
                "alternative_names": ["address_country"],
                "field_type": "string",
                "faker": "country",
                "name": _("Billing address country"),
                "help_text": _("Billing address country"),
                "field_group": "billing_address",
                "max_length": 3,
            },
            {
                "id": "return_address_contact_email",
                "alternative_names": ["address_contact_email"],
                "field_type": "string",
                "faker": "email",
                "name": _("Return address contact email"),
                "help_text": _("Return address contact email"),
                "field_group": "return_address",
                "max_length": 255,
            },
            {
                "id": "return_address_contact_first_name",
                "alternative_names": ["address_contact_first_name"],
                "field_type": "string",
                "faker": "first_name",
                "name": _("Return address contact first name"),
                "help_text": _("Return address contact first name"),
                "field_group": "return_address",
                "max_length": 128,
            },
            {
                "id": "return_address_contact_last_name",
                "alternative_names": ["address_contact_last_name"],
                "field_type": "string",
                "faker": "last_name",
                "name": _("Return address contact last name"),
                "help_text": _("Return address contact last name"),
                "field_group": "return_address",
                "max_length": 128,
            },
            {
                "id": "return_address_phone",
                "alternative_names": ["address_phone"],
                "field_type": "string",
                "faker": "phone_number",
                "name": _("Return address phone"),
                "help_text": _("Return address phone"),
                "field_group": "return_address",
                "max_length": 255,
            },
            {
                "id": "return_address_zipcode",
                "alternative_names": ["address_zipcode"],
                "field_type": "string",
                "faker": "zipcode",
                "name": _("Return address zipcode"),
                "help_text": _("Return address zipcode"),
                "field_group": "return_address",
                "max_length": 20,
            },
            {
                "id": "return_address_city",
                "alternative_names": ["address_city"],
                "field_type": "string",
                "faker": "city",
                "name": _("Return address city"),
                "help_text": _("Return address city"),
                "field_group": "return_address",
                "max_length": 50,
            },
            {
                "id": "return_address_address",
                "alternative_names": ["address_address"],
                "field_type": "string",
                "faker": "address",
                "name": _("Return address"),
                "help_text": _(" Return address"),
                "field_group": "return_address",
                "max_length": 355,
            },
            {
                "id": "return_address_address2",
                "alternative_names": ["address_address2"],
                "field_type": "string",
                "name": _("Return address addtional info"),
                "help_text": _("Return address addtional info"),
                "field_group": "return_address",
                "max_length": 355,
            },
            {
                "id": "return_address_country",
                "alternative_names": ["address_country"],
                "field_type": "string",
                "faker": "country",
                "name": _("Return address country"),
                "help_text": _("Return address country"),
                "field_group": "return_address",
                "max_length": 3,
            },
            {
                "id": "prefered_language",
                "alternative_names": ["default_language"],
                "name": _("Merchant language"),
                "field_type": "string",
                "help_text": _("Prefered language of the merchant"),
                "field_group": "general",
                "choices": {"choice_type": "internal", "data": LANGUAGE_CHOICES},
                "max_length": 7,
            },
            {
                "id": "default_currency",
                "field_type": "string",
                "faker": "currency_code",
                "name": _("Default Currency"),
                "help_text": _("Default currency of the merchant"),
                "max_length": 3,
            },
            {
                "id": "bank_account_owner_name",
                "field_type": "string",
                "faker": "last_name",
                "name": _("Bank account owner"),
                "help_text": _("Merchant bank account owner name"),
                "field_group": "bank_account",
                "max_length": 255,
            },
            {
                "id": "bank_account_IBAN",
                "alternative_names": ["IBAN"],
                "field_type": "string",
                "faker": "iban",
                "name": _("Merchant IBAN"),
                "help_text": _("Merchant IBAN account number"),
                "field_group": "bank_account",
                "max_length": 55,
            },
            {
                "id": "bank_account_BIC",
                "alternative_names": ["BIC"],
                "field_type": "string",
                "faker": "bic",
                "name": _("Merchant BIC"),
                "help_text": _("Merchant BIC account number"),
                "field_group": "bank_account",
                "max_length": 255,
            },
            {
                "id": "company_siret",
                "alternative_names": ["siret_number"],
                "field_type": "string",
                "faker": "siret_number",
                "name": _("SIRET number"),
                "help_text": _("French Siret number"),
                "max_length": 100,
            },
            {
                "id": "company_code_naf",
                "alternative_names": ["naf_code"],
                "field_type": "string",
                "faker": "naf_code",
                "name": _("NAF code"),
                "help_text": _("French NAF code"),
                "field_group": "company",
                "max_length": 100,
            },
            {
                "id": "company_legal_form",
                "field_type": "string",
                "faker": "sentence",
                "name": _("Legal form"),
                "help_text": _("Example: SARL, SAS..."),
                "field_group": "company",
                "max_length": 100,
            },
            {
                "id": "company_registration_number",
                "field_type": "string",
                "faker": "sentence",
                "name": _("Registration number"),
                "help_text": _("Example: RCS PARIS *********"),
                "field_group": "company",
                "max_length": 100,
            },
            {
                "id": "company_share_capital",
                "field_type": "string",
                "faker": "sentence",
                "name": _("Share capital"),
                "help_text": _("Example: 123 0000 000 €"),
                "field_group": "company",
                "max_length": 100,
            },
            {
                "id": "company_extra_legal_infos",
                "field_type": "string",
                "faker": "sentence",
                "name": _("Extra legal info"),
                "field_group": "company",
            },
            {
                "id": "company_country",
                "alternative_names": ["region"],
                "name": _("Company registered country"),
                "help_text": _(
                    "Country code of the country where the company is registered"
                ),
                "field_type": "string",
                "field_group": "company",
                "max_length": 3,
            },
            {
                "id": "company_vat_number",
                "alternative_names": ["vat_number"],
                "field_type": "string",
                "faker": "vat_number",
                "name": _("Company VAT number"),
                "help_text": _("VAT number of the company"),
                "field_group": "company",
                "max_length": 100,
            },
            {
                "id": "url",
                "name": _("Merchant url"),
                "alternative_names": ["url", "link", "merchant_website_url"],
                "help_text": _("Merchant url"),
                "field_type": "string",
                "faker": "url",
                "field_group": "general",
                "max_length": 200,
            },
            {
                "id": "description",
                "name": _("Merchant description"),
                "help_text": _("Description of the merchant"),
                "field_type": "text",
                "faker": "sentence",
                "field_group": "general",
            },
            {
                "id": "long_description",
                "name": _("Merchant long description"),
                "help_text": _("Long description of the merchant"),
                "field_type": "text",
                "faker": "sentence",
                "field_group": "general",
            },
            {
                "id": "applicable_tax_rate_on_commissions",
                "name": _("Applicable tax rate on commissions"),
                "help_text": _("Applicable tax rate on commissions"),
                "field_type": "decimal",
                "field_group": "general",
            },
            {
                "id": "profile_image",
                "name": _("Images"),
                "alternative_names": ["logo", "logos", "logo_url", "logos_url"],
                "abstract": True,
                "field_type": "nested",
                "allow_multiple": {"split_on": [",", " ", ";"]},
                "help_text": _("List of logos of the merchant"),
                "nested_entity": "image",  # IMAGE_ENTITY_SCHEMA
                "field_group": "images",
            },
            {
                "id": "international_group_keys",
                "name": _("International groups"),
                "help_text": _("International groups of the merchant"),
                "field_type": "text",
                "allow_multiple": {"split_on": [",", " ", ";"]},
                "field_group": "merchant_group",
            },
            {
                "id": "generic_group_keys",
                "name": _("Generic groups"),
                "help_text": _("Generic groups of the merchant"),
                "field_type": "text",
                "allow_multiple": {"split_on": [",", " ", ";"]},
                "field_group": "merchant_group",
            },
            {
                "id": "backoffice_access_email",
                "alternative_names": ["backoffice_access_email"],
                "field_type": "string",
                "faker": "email",
                "name": _("Backoffice access email"),
                "field_group": "backoffice_access",
            },
            {
                "id": "backoffice_access_first_name",
                "alternative_names": ["backoffice_access_first_name"],
                "field_type": "string",
                "faker": "first_name",
                "name": _("Backoffice access first name"),
                "field_group": "backoffice_access",
            },
            {
                "id": "backoffice_access_last_name",
                "alternative_names": ["backoffice_access_last_name"],
                "field_type": "string",
                "faker": "last_name",
                "name": _("Backoffice access last name"),
                "field_group": "backoffice_access",
            },
            {
                "id": "backoffice_access_password",
                "alternative_names": ["backoffice_access_password"],
                "field_type": "string",
                "name": _("Backoffice access password"),
                "field_group": "backoffice_access",
            },
            {
                "id": "status",
                "alternative_names": ["statut"],
                "field_type": "string",
                "name": _("Merchant status"),
                "help_text": _("The status of the merchant"),
                "choices": {
                    "choice_type": "internal",
                    "data": [
                        {
                            "name": _("Inactive"),
                            "id": "0",
                        },
                        {
                            "name": _("Pending"),
                            "id": "5",
                        },
                        {"name": _("Active"), "id": "10"},
                        {"name": _("Paused"), "id": "20"},
                        {"name": _("Stopped"), "id": "30"},
                        {"name": _("Deleted"), "id": "90"},
                    ],
                },
            },
        ],
        "validators": [
            {
                "type": "exist_together",
                "fields": [
                    "bank_account_owner_name",
                    "bank_account_IBAN",
                    "bank_account_BIC",
                ],
                "if_exists": ["bank_account_IBAN"],
            }
        ],
        "import_mode": [
            {"create_and_update": True, "create_only": True, "update_only": True}
        ],
        "lookup_keys": ["external_id"],
    }
)
