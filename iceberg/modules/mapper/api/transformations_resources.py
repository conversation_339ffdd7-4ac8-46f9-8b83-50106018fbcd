# -*- coding: utf-8 -*-
from dateutil.relativedelta import relativedelta
from django.db.models import Count
from django.utils import timezone
from ims.api.decorators import api_view
from ims.api.fields import LegacyForeignKey
from ims.api.resources import MPModelResource
from lib.api.resources.mixins import ExportableResourceMixin
from lib.paginations.paginator import NoTotalCountPaginator
from mapper.actions.admin_actions import generate_global_queue_stats
from mapper.error_codes import ERRORS
from tastypie import fields
from tastypie.resources import ALL, ALL_WITH_RELATIONS

from ..models import (
    TransformationLog,
    TransformationLogDetail,
    TransformationLogTransitionLog,
    TransformationLogWorkflow,
)
from .auths import (
    MapperTransformationLogTransitionLogAuthorization,
    TransformationLogDetailResourceAuth,
    TransformationLogResourceAuthorization,
)
from .base_resource import MapperBaseResource, MapperBaseWithAppAndMerchantResource
from .mixins import BaseCorsResource


class TransformationLogResource(MapperBaseWithAppAndMerchantResource):
    configuration = fields.ForeignKey(
        "mapper.api.resources.ConfigurationResource", "configuration"
    )

    status = fields.CharField("status", readonly=True)

    class Meta(MapperBaseWithAppAndMerchantResource.Meta):
        queryset = TransformationLog.objects.order_by("-id")
        resource_name = "mapper_transformation_log"
        authorization = TransformationLogResourceAuthorization()
        editable_fields = TransformationLog.EDITABLE_FIELDS
        creatable_fields = TransformationLog.CREATABLE_FIELDS
        excludes = [
            "content_file_bkp",
            "parsing_report_bkp",
            "error_report_bkp",
            "detailed_report_bkp",
            "created_products_report_bkp",
        ]
        list_allowed_methods = ["get"]
        detail_allowed_methods = ["get"]
        filtering = {
            "id": ALL,
            "configuration": ALL_WITH_RELATIONS,
            "trigger_type": ALL,
            "created_on": ALL,
            "last_modified": ALL,
            "start": ALL,
            "merchant": ALL_WITH_RELATIONS,
            "application": ALL_WITH_RELATIONS,
            "status": ALL,
            "priority": ALL,
        }
        abstract = False

    def prepend_urls(self):
        return [
            self.make_pk_url(
                "transition_logs",
                "mapper_transformation_log_transition_logs",
                "api_filtered_mapper_transition_log",
            ),
            self.make_pk_url(
                "error_count",
                "transformation_log_error_count",
                "mapper_transformation_log_error_count",
            ),
            self.make_pk_url("abort", "abort_view", "mapper_transformation_log_abort"),
            self.make_url(
                "admin/global_stats",
                "admin_global_stats_view",
                "mapper_admin_global_stats",
            ),
        ]

    def abort_view(self, request, **kwargs):
        return self.generic_transition_view(request, "abort", **kwargs)

    @api_view(method_allowed=["get"])
    def mapper_transformation_log_transition_logs(self, request, **kwargs):
        bundle = self.get_cached_object_or_raise(request, **kwargs)

        class FilteredTransformationLogTransitionLogResource(
            TransformationLogTransitionLogResource
        ):
            class Meta(TransformationLogTransitionLogResource.Meta):
                queryset = bundle.obj.transition_logs.all()

        return FilteredTransformationLogTransitionLogResource().dispatch(
            "list", request, **kwargs
        )

    @api_view(method_allowed=["get"])
    def transformation_log_error_count(self, request, **kwargs):
        bundle = self.get_cached_object_or_raise(request, **kwargs)
        err_counts = list(
            bundle.obj.details.values("error_code", "kind")
            .annotate(count=Count("error_code"))
            .order_by("-count")
        )
        for err_count in err_counts:
            err_count["help_text"] = ERRORS.get(err_count["error_code"])
        return self.create_response(request, err_counts)

    @api_view(method_allowed=["get"], only_for_izberg_staff=True)
    def admin_global_stats_view(self, request, **kwargs):
        return self.create_response(request, generate_global_queue_stats())

    def dehydrate(self, bundle):
        if bundle.obj.status not in TransformationLogWorkflow.FINAL_STATUSES:
            self._inject_live_counters(bundle)
        return bundle

    def _inject_live_counters(self, bundle):
        for counter in TransformationLog.COUNT_FIELDS:
            if counter not in bundle.data:
                continue
            bundle.data[counter] = bundle.obj.counters.get_cache_or_db_count(counter)


class TransformationLogTransitionLogResource(BaseCorsResource, MPModelResource):
    transformation_log = fields.ForeignKey(
        "mapper.api.resources.TransformationLogResource", "transformation_log"
    )

    user = LegacyForeignKey(
        "apps.user.api.resources.UserResource",
        "user",
        null=True,
        backbone_friendly=True,
    )
    timestamp = fields.DateTimeField(
        "timestamp",
        readonly=True,
        default="1970-01-01T00:00:00.000000+00:00",
    )

    class Meta:
        queryset = TransformationLogTransitionLog.objects.all()
        authorization = MapperTransformationLogTransitionLogAuthorization()
        always_return_data = True
        resource_name = "mapper_transformation_log_transition_log"
        editable_fields = []
        creatable_fields = []
        list_allowed_methods = ["get"]
        detail_allowed_methods = ["get"]
        filtering = {
            "id": ALL,
            "transformation_log": ALL_WITH_RELATIONS,
            "user": ALL_WITH_RELATIONS,
            "comment": ALL,
            "transition": ALL,
            "from_state": ALL,
            "to_state": ALL,
            "timestamp": ALL,
        }
        abstract = False


class TransformationLogDetailResource(ExportableResourceMixin, MapperBaseResource):
    transformation_log = fields.ForeignKey(
        TransformationLogResource, "transformation_log"
    )
    configuration = fields.ForeignKey(
        "mapper.api.resources.ConfigurationResource",
        attribute="transformation_log__configuration",
        readonly=True,
    )
    item = fields.DictField("item", null=True, default=dict)
    error_code_verbose = fields.CharField(readonly=True)

    class Meta(MapperBaseResource.Meta):
        paginator_class = NoTotalCountPaginator
        queryset = TransformationLogDetail.objects.exclude(
            kind=TransformationLogDetail.KIND_VALID
        ).order_by("-id")
        resource_name = "mapper_transformation_log_detail"
        editable_fields = TransformationLogDetail.EDITABLE_FIELDS
        creatable_fields = TransformationLogDetail.CREATABLE_FIELDS
        list_allowed_methods = ["get"]
        detail_allowed_methods = ["get"]
        filtering = {
            "transformation_log": ALL_WITH_RELATIONS,
            "created_on": ALL,
            "kind": ALL,
            "id": ALL,
            "error_code": ALL,
            "item_identifier_key": ALL,
            "item_identifier_value": ALL,
            "item_external_id": ALL,
        }
        ordering = ["id"]
        authorization = TransformationLogDetailResourceAuth()

        excludes = ["traceback"]
        abstract = False

    def prepend_urls(self):
        return [
            self.get_api_export_url(urlname="mapper_transformation_log_detail_export"),
        ]

    def get_sender_email(self, request, objects):
        if getattr(request, "application", None) is not None:
            application = request.application
        else:
            application = objects[0].transformation_log.application
        return application.email_contact_with_name

    def hydrate(self, bundle):
        if "item" in bundle.data:
            bundle.obj.item = bundle.data["item"]
        return bundle

    def dehydrate_error_code_verbose(self, bundle):
        return bundle.obj.get_error_code_display()

    def build_filters(self, filters=None, *args, **kwargs):
        """
        Override filters to always limit on date for perf reason:
        - if filtered on transformation_log, limit to its start/end dates
          (with 1 hour safety span on each side)
        - else last 15 days
        """
        filters = super(TransformationLogDetailResource, self).build_filters(
            filters=filters, *args, **kwargs
        )
        if filters.get("transformation_log__exact"):
            try:
                tl = TransformationLog.objects.only("start", "end").get(
                    id=int(filters["transformation_log__exact"])
                )
            except (ValueError, TransformationLog.DoesNotExist):
                pass
            else:
                start, end = tl.start, tl.end
                if start and not filters.get("created_on__gt"):
                    filters["created_on__gt"] = start - relativedelta(hours=1)
                if end and not filters.get("created_on__lt"):
                    filters["created_on__lt"] = end + relativedelta(hours=1)
        if not filters.get("created_on__gt"):
            filters["created_on__gt"] = timezone.now() - relativedelta(days=15)
        return filters

    def _inject_estimated_total_count(self, request):
        if "meta.total_count" in request.GET:
            # already overridden by front, no need to inject it
            return request
        request.GET = request.GET.copy()
        ignored_params = {
            "limit",
            "offset",
            "order_by",
            "only",
            "transformation_log",
            "transformation_log__exact",
        }
        if set(request.GET.keys()) - ignored_params:
            # presence of specific filters, can't inject estimation >> no count
            return request
        trans_log_id = request.GET.get("transformation_log") or request.GET.get(
            "transformation_log__exact"
        )
        if not trans_log_id:
            # no transformation_log filter, can't inject estimation >> no count
            return request
        try:
            tl = TransformationLog.objects.get(id=int(trans_log_id))
        except (ValueError, TypeError, TransformationLog.DoesNotExist):
            pass
        else:
            request.GET["meta.total_count"] = tl.counters.estimate_details_total_count()
        return request

    def dispatch_list(self, request, **kwargs):
        request = self._inject_estimated_total_count(request)
        return super(TransformationLogDetailResource, self).dispatch_list(
            request, **kwargs
        )
