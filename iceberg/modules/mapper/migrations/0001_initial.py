# Generated by Django 3.2.20 on 2023-08-24 09:28

from decimal import Decimal
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import django_xworkflows.models
import ims.api.mixins
import ims.fields.foreign_key
import ims.models.mixin
import jsonfield.fields
import lib.api.file_path
import lib.fields.no_choice_migration_charfield
import mapper.mixins
import mapper.models.transformation_models
import mp_utils.model_mixins.exportable_model_mixin
import uploader.models.mixins


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Action",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_on",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created on"),
                ),
                (
                    "method",
                    models.CharField(
                        choices=[
                            ("cast", "Cast"),
                            ("delete", "Delete"),
                            ("rename", "Rename"),
                            ("compute", "Compute"),
                            ("modify", "Modify"),
                            ("filter", "Filter"),
                            ("exclude", "Exclude"),
                            ("convert_html_entities", "Convert HTML entities"),
                            ("html_to_text", "Convert HTML to text"),
                            ("zip", "Zip two columns (key/value)"),
                        ],
                        max_length=255,
                        verbose_name="Method",
                    ),
                ),
                (
                    "label",
                    models.CharField(
                        blank=True,
                        help_text="Label field to tag/categorize action",
                        max_length=255,
                        null=True,
                        verbose_name="Label",
                    ),
                ),
                (
                    "options",
                    jsonfield.fields.JSONField(default=dict, verbose_name="Options"),
                ),
                ("index", models.IntegerField(default=0)),
                (
                    "status",
                    models.CharField(
                        choices=[("enabled", "Enabled"), ("disabled", "Disabled")],
                        default="enabled",
                        max_length=20,
                    ),
                ),
            ],
            bases=(
                mapper.mixins.HasOptionForm,
                ims.models.mixin.SanitizeCharFieldMixin,
                ims.models.mixin.ChangedFieldLookupMixin,
                mapper.mixins.ModelSourcing,
                models.Model,
            ),
        ),
        migrations.CreateModel(
            name="Analysis",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "status",
                    django_xworkflows.models.StateField(
                        max_length=16,
                        verbose_name="Status",
                        workflow=django_xworkflows.models._SerializedWorkflow(
                            initial_state="new",
                            name="AnalysisWorkflow",
                            states=[
                                "new",
                                "in_queue",
                                "processing",
                                "analyzing",
                                "stopped",
                                "done",
                                "expired",
                                "failed",
                            ],
                        ),
                    ),
                ),
                (
                    "created_on",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created on"),
                ),
                (
                    "expires_on",
                    models.DateTimeField(blank=True, verbose_name="Expires on"),
                ),
                (
                    "started_on",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Started on"
                    ),
                ),
                (
                    "ended_on",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Ended on"
                    ),
                ),
                (
                    "max_item",
                    models.PositiveIntegerField(
                        blank=True,
                        default=None,
                        null=True,
                        verbose_name="Maximum number of items to analyse",
                    ),
                ),
                (
                    "_items_count",
                    models.IntegerField(
                        default=0, verbose_name="Number of items in the feed"
                    ),
                ),
                (
                    "_processed_items",
                    models.IntegerField(
                        default=0, verbose_name="Number of processed items"
                    ),
                ),
                (
                    "_field_count",
                    models.IntegerField(
                        default=0, verbose_name="Number of fields found"
                    ),
                ),
                (
                    "alert_on_done",
                    models.BooleanField(
                        default=False,
                        verbose_name="Does the user want to be alerted upon analysis completion",
                    ),
                ),
                (
                    "alert_email",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=255,
                        verbose_name="Override the main user mail if completed",
                    ),
                ),
            ],
            bases=(
                django_xworkflows.models.BaseWorkflowEnabled,
                ims.api.mixins.ApiUtilsMixin,
                models.Model,
            ),
        ),
        migrations.CreateModel(
            name="AnalysisTransitionLog",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "transition",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="transition"
                    ),
                ),
                (
                    "from_state",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="from state"
                    ),
                ),
                (
                    "to_state",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="to state"
                    ),
                ),
                (
                    "timestamp",
                    models.DateTimeField(
                        db_index=True,
                        default=django.utils.timezone.now,
                        verbose_name="performed at",
                    ),
                ),
                ("comment", models.TextField(blank=True, null=True)),
            ],
            options={
                "verbose_name": "XWorkflow transition log",
                "verbose_name_plural": "XWorkflow transition logs",
                "ordering": ("-timestamp", "transition"),
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Cleaner",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_on",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created on"),
                ),
                (
                    "method",
                    models.CharField(
                        choices=[("unique", "Unique")],
                        max_length=255,
                        verbose_name="Method",
                    ),
                ),
                (
                    "options",
                    jsonfield.fields.JSONField(default=dict, verbose_name="Options"),
                ),
                ("index", models.IntegerField(default=0)),
            ],
            bases=(
                ims.api.mixins.ApiUtilsMixin,
                mapper.mixins.HasOptionForm,
                ims.models.mixin.SanitizeCharFieldMixin,
                ims.models.mixin.ChangedFieldLookupMixin,
                mapper.mixins.ModelSourcing,
                models.Model,
            ),
        ),
        migrations.CreateModel(
            name="Configuration",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "creator_uri",
                    models.CharField(blank=True, max_length=256, null=True),
                ),
                (
                    "creator_name",
                    models.CharField(blank=True, max_length=128, null=True),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="A name to help you manage your feeds",
                        max_length=255,
                        verbose_name="Name",
                    ),
                ),
                (
                    "status",
                    django_xworkflows.models.StateField(
                        max_length=16,
                        verbose_name="Status",
                        workflow=django_xworkflows.models._SerializedWorkflow(
                            initial_state="active",
                            name="ConfigurationWorkflow",
                            states=["active", "stopped", "too_many_errors", "deleted"],
                        ),
                    ),
                ),
                (
                    "created_on",
                    models.DateTimeField(
                        auto_now_add=True, null=True, verbose_name="Created on"
                    ),
                ),
                (
                    "last_modified",
                    models.DateTimeField(
                        auto_now=True, null=True, verbose_name="Last modified"
                    ),
                ),
                ("every", models.PositiveIntegerField(default=0, verbose_name="Every")),
                (
                    "period",
                    models.CharField(
                        choices=[
                            ("weeks", "Weeks"),
                            ("days", "Days"),
                            ("hours", "Hours"),
                        ],
                        default="days",
                        max_length=24,
                        verbose_name="Period",
                    ),
                ),
                (
                    "last_run_date",
                    models.DateTimeField(
                        blank=True,
                        help_text="Date this config was ran by the scheduler or manually.",
                        null=True,
                        verbose_name="Last run date",
                    ),
                ),
                (
                    "last_automatic_run_date",
                    models.DateTimeField(
                        blank=True,
                        help_text="Date last run was trigged by the scheduler",
                        null=True,
                        verbose_name="Last automatic run date",
                    ),
                ),
                (
                    "next_automatic_run_date",
                    models.DateTimeField(
                        blank=True,
                        help_text="Next date for the scheduled run",
                        null=True,
                        verbose_name="Next automatic run date",
                    ),
                ),
                (
                    "report_email_addresses",
                    models.CharField(
                        blank=True,
                        default=None,
                        help_text="A list of emails that will receive import repport",
                        max_length=255,
                        null=True,
                        verbose_name="Report email addresses",
                    ),
                ),
                (
                    "abortion_threshold_override",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Abort if failure rate exceeds the specified rate (in percent)",
                        max_digits=5,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0")),
                            django.core.validators.MaxValueValidator(Decimal("100")),
                        ],
                        verbose_name="Abortion rate",
                    ),
                ),
                (
                    "izberg_entity",
                    models.CharField(
                        choices=[
                            ("product_and_offer", "Products and offers"),
                            ("product", "Products"),
                            ("offer", "Product offers"),
                            ("app_category", "Application categories"),
                            ("merchant", "Merchants"),
                            ("product_attribute", "Product-related attribute"),
                            ("merchant_attribute", "Merchant attribute"),
                            ("item_commission", "Item Commission"),
                            ("merchant_commission", "Merchant order commission"),
                            ("price", "Price"),
                        ],
                        default="product_and_offer",
                        max_length=255,
                        verbose_name="Entity",
                    ),
                ),
                (
                    "encoding",
                    models.CharField(
                        blank=True,
                        choices=[
                            (None, "Default encoding"),
                            ("UTF-8", "UTF-8"),
                            ("UTF-8-SIG", "UTF-8 Sig"),
                            ("ISO-8859-1", "ISO-8859-1"),
                            ("ISO-8859-2", "ISO-8859-2"),
                            ("US-ASCII", "US-ASCII"),
                            ("UTF-16", "UTF-16"),
                            ("UTF-16BE", "UTF-16BE"),
                            ("UTF-16LE", "UTF-16LE"),
                            ("Big5", "Big5"),
                            ("Big5-HKSCS", "Big5-HKSCS"),
                            ("windows-1250", "Windows 1250"),
                            ("windows-1251", "Windows 1251"),
                            ("windows-1252", "Windows 1252"),
                        ],
                        help_text="Provide an encoding if needed",
                        max_length=100,
                        null=True,
                        verbose_name="Encoding",
                    ),
                ),
                (
                    "language",
                    lib.fields.no_choice_migration_charfield.NoChoiceMigrationCharfield(
                        help_text="Set content language",
                        max_length=10,
                        verbose_name="Language",
                    ),
                ),
                (
                    "method",
                    models.CharField(
                        choices=[
                            ("file", "File"),
                            ("ftp", "(S)FTP"),
                            ("http", "HTTP(S)"),
                            ("uploaded_file", "Uploaded file"),
                            ("google_sheets_link", "Google Sheets"),
                        ],
                        max_length=255,
                        verbose_name="Method",
                    ),
                ),
                (
                    "options",
                    jsonfield.fields.JSONField(
                        blank=True, default=dict, verbose_name="Options"
                    ),
                ),
                (
                    "save_content",
                    models.BooleanField(
                        default=True,
                        help_text="If True, content will be saved in a File for history versionning",
                        verbose_name="Save content",
                    ),
                ),
                (
                    "create_parsing_report",
                    models.BooleanField(
                        default=True,
                        help_text="If True, a parsing report will be generated",
                        verbose_name="Create parsing report",
                    ),
                ),
                (
                    "parsing_report_format",
                    models.CharField(
                        choices=[("json", "JSON"), ("yaml", "YAML")],
                        default="yaml",
                        max_length=32,
                        verbose_name="Parsing report format",
                    ),
                ),
                ("can_create", models.BooleanField(default=True)),
                ("can_update", models.BooleanField(default=True)),
                (
                    "cache_version",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Cache version"
                    ),
                ),
                (
                    "fields",
                    jsonfield.fields.JSONField(
                        blank=True, default=[], verbose_name="Fields"
                    ),
                ),
            ],
            bases=(
                ims.api.mixins.ApiUtilsMixin,
                mapper.mixins.HasOptionForm,
                mapper.mixins.ModelSourcing,
                django_xworkflows.models.BaseWorkflowEnabled,
                models.Model,
            ),
        ),
        migrations.CreateModel(
            name="ConfigurationExport",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "configuration_file",
                    models.FileField(
                        max_length=1024,
                        upload_to=lib.api.file_path.FilePathGenerator(),
                        verbose_name="Mapper configuration export",
                    ),
                ),
                (
                    "configuration_file_bkp",
                    models.FileField(
                        blank=True,
                        default=None,
                        max_length=1024,
                        null=True,
                        upload_to="",
                    ),
                ),
                (
                    "exported_on",
                    models.DateField(
                        auto_now_add=True, null=True, verbose_name="Exported on"
                    ),
                ),
                (
                    "filename",
                    models.CharField(
                        blank=True, max_length=256, null=True, verbose_name="File name"
                    ),
                ),
            ],
            bases=(
                ims.models.mixin.SanitizeCharFieldMixin,
                ims.models.mixin.ChangedFieldLookupMixin,
                models.Model,
            ),
        ),
        migrations.CreateModel(
            name="ConfigurationUpload",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "configuration_file",
                    models.FileField(
                        max_length=1024,
                        upload_to=lib.api.file_path.FilePathGenerator(),
                        verbose_name="Mapper configuration",
                    ),
                ),
                (
                    "configuration_file_bkp",
                    models.FileField(
                        blank=True,
                        default=None,
                        max_length=1024,
                        null=True,
                        upload_to="",
                    ),
                ),
                (
                    "created_on",
                    models.DateTimeField(
                        auto_now_add=True, null=True, verbose_name="Created"
                    ),
                ),
                (
                    "filename",
                    models.CharField(
                        blank=True, max_length=256, null=True, verbose_name="File name"
                    ),
                ),
            ],
            bases=(
                uploader.models.mixins.UploaderMixin,
                ims.api.mixins.ApiUtilsMixin,
                ims.models.mixin.SanitizeCharFieldMixin,
                ims.models.mixin.ChangedFieldLookupMixin,
                models.Model,
            ),
        ),
        migrations.CreateModel(
            name="FeedFile",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "uploader_file",
                    models.FileField(
                        max_length=1024, upload_to=lib.api.file_path.FilePathGenerator()
                    ),
                ),
                (
                    "uploader_file_bkp",
                    models.FileField(
                        blank=True,
                        default=None,
                        max_length=1024,
                        null=True,
                        upload_to="",
                    ),
                ),
                ("uploader_uploaded_date", models.DateTimeField(auto_now_add=True)),
                ("uploader_filename", models.CharField(max_length=256)),
            ],
            options={
                "abstract": False,
            },
            bases=(uploader.models.mixins.UploaderMixin, models.Model),
        ),
        migrations.CreateModel(
            name="FeedTemplate",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "creator_uri",
                    models.CharField(blank=True, max_length=256, null=True),
                ),
                (
                    "creator_name",
                    models.CharField(blank=True, max_length=128, null=True),
                ),
                (
                    "template",
                    models.FileField(
                        blank=True,
                        help_text="Can be generated from a mapper config",
                        max_length=1024,
                        null=True,
                        upload_to=lib.api.file_path.FilePathGenerator(),
                        verbose_name="Template file",
                    ),
                ),
                (
                    "template_bkp",
                    models.FileField(
                        blank=True,
                        default=None,
                        max_length=1024,
                        null=True,
                        upload_to="",
                    ),
                ),
                (
                    "status",
                    django_xworkflows.models.StateField(
                        help_text="Manage template availability in application's catalog",
                        max_length=16,
                        verbose_name="Publication status",
                        workflow=django_xworkflows.models._SerializedWorkflow(
                            initial_state="draft",
                            name="FeedTemplateStatusWorkflow",
                            states=["draft", "visible", "deleted"],
                        ),
                    ),
                ),
                (
                    "visibility",
                    models.CharField(
                        choices=[
                            ("staff", "staff"),
                            ("application", "application"),
                            ("merchant", "merchant"),
                        ],
                        default="merchant",
                        help_text="Manage targeted audience according to role level",
                        max_length=64,
                        verbose_name="Audience",
                    ),
                ),
                (
                    "name",
                    models.CharField(max_length=255, verbose_name="Template name"),
                ),
                (
                    "entity_type",
                    models.CharField(
                        choices=[
                            ("product_and_offer", "Products and offers"),
                            ("product", "Products"),
                            ("offer", "Product offers"),
                            ("app_category", "Application categories"),
                            ("merchant", "Merchants"),
                            ("product_attribute", "Product-related attribute"),
                            ("merchant_attribute", "Merchant attribute"),
                            ("item_commission", "Item Commission"),
                            ("merchant_commission", "Merchant order commission"),
                            ("price", "Price"),
                        ],
                        default="offer",
                        max_length=255,
                        verbose_name="Entity type",
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="Template description"),
                ),
                (
                    "created_on",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="Creation date"
                    ),
                ),
                (
                    "updated_on",
                    models.DateTimeField(
                        auto_now=True, verbose_name="Last modification date"
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(
                uploader.models.mixins.UploaderMixin,
                ims.api.mixins.ApiUtilsMixin,
                django_xworkflows.models.BaseWorkflowEnabled,
                mapper.mixins.ModelSourcing,
                models.Model,
            ),
        ),
        migrations.CreateModel(
            name="FeedTemplateTransitionLog",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "transition",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="transition"
                    ),
                ),
                (
                    "from_state",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="from state"
                    ),
                ),
                (
                    "to_state",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="to state"
                    ),
                ),
                (
                    "timestamp",
                    models.DateTimeField(
                        db_index=True,
                        default=django.utils.timezone.now,
                        verbose_name="performed at",
                    ),
                ),
                (
                    "content_id",
                    models.PositiveIntegerField(
                        blank=True, db_index=True, null=True, verbose_name="Content id"
                    ),
                ),
                ("comment", models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                "verbose_name": "XWorkflow transition log",
                "verbose_name_plural": "XWorkflow transition logs",
                "ordering": ("-timestamp", "transition"),
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Mapper",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_on",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created on"),
                ),
                (
                    "disable_mapping",
                    jsonfield.fields.JSONField(
                        blank=True,
                        default=[],
                        verbose_name="Keys that don't need to be mapped",
                    ),
                ),
            ],
            bases=(
                ims.api.mixins.ApiUtilsMixin,
                mapper.mixins.ModelSourcing,
                ims.models.mixin.SanitizeCharFieldMixin,
                ims.models.mixin.ChangedFieldLookupMixin,
                models.Model,
            ),
        ),
        migrations.CreateModel(
            name="MapperDetail",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[("active", "Active"), ("inactive", "Inactive")],
                        default="active",
                        max_length=10,
                        verbose_name="Status",
                    ),
                ),
                ("created_on", models.DateTimeField(auto_now_add=True, null=True)),
                (
                    "feed_path",
                    models.CharField(
                        blank=True,
                        help_text="For nested mapping, use path",
                        max_length=50,
                        null=True,
                        verbose_name="Feed path",
                    ),
                ),
                ("feed_attribute_id", models.CharField(max_length=128)),
                (
                    "entity_path",
                    models.CharField(
                        blank=True,
                        help_text="For nested mapping, use path",
                        max_length=50,
                        null=True,
                        verbose_name="Entity path",
                    ),
                ),
                ("entity_attribute_id", models.CharField(max_length=255)),
            ],
            bases=(
                ims.api.mixins.ApiUtilsMixin,
                mapper.mixins.ModelSourcing,
                ims.models.mixin.SanitizeCharFieldMixin,
                ims.models.mixin.ChangedFieldLookupMixin,
                models.Model,
            ),
        ),
        migrations.CreateModel(
            name="Matcher",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="Name"
                    ),
                ),
                (
                    "created_on",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created on"),
                ),
                (
                    "izberg_entity",
                    models.CharField(
                        choices=[
                            ("product_and_offer", "Products and offers"),
                            ("product", "Products"),
                            ("offer", "Product offers"),
                            ("app_category", "Application categories"),
                            ("merchant", "Merchants"),
                            ("product_attribute", "Product-related attribute"),
                            ("merchant_attribute", "Merchant attribute"),
                            ("item_commission", "Item Commission"),
                            ("merchant_commission", "Merchant order commission"),
                            ("price", "Price"),
                        ],
                        max_length=20,
                        verbose_name="Entity",
                    ),
                ),
                (
                    "disable_matching",
                    jsonfield.fields.JSONField(
                        blank=True,
                        default=None,
                        null=True,
                        verbose_name="Keys that don't need to be matched",
                    ),
                ),
            ],
            bases=(
                ims.api.mixins.ApiUtilsMixin,
                mapper.mixins.ModelSourcing,
                ims.models.mixin.SanitizeCharFieldMixin,
                ims.models.mixin.ChangedFieldLookupMixin,
                models.Model,
            ),
        ),
        migrations.CreateModel(
            name="MatcherDetail",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "detected_on",
                    models.DateTimeField(default=django.utils.timezone.now),
                ),
                ("entity_attribute_id", models.CharField(max_length=50)),
                (
                    "entity_label",
                    models.CharField(
                        blank=True,
                        help_text="A helper label to be shown in the mapper UI",
                        max_length=255,
                        null=True,
                        verbose_name="Entity label",
                    ),
                ),
                (
                    "entity_value",
                    models.CharField(
                        blank=True,
                        help_text="Matched value from IZBERG",
                        max_length=255,
                        null=True,
                        verbose_name="Entity value",
                    ),
                ),
                (
                    "feed_value",
                    models.CharField(
                        help_text="Detected value in the feed",
                        max_length=255,
                        verbose_name="Feed value",
                    ),
                ),
                (
                    "auto_matched",
                    models.BooleanField(
                        default=False,
                        help_text="Has the value been auto-matched ?",
                        verbose_name="Auto-matched",
                    ),
                ),
            ],
            bases=(
                ims.api.mixins.ApiUtilsMixin,
                mapper.mixins.ModelSourcing,
                ims.models.mixin.SanitizeCharFieldMixin,
                ims.models.mixin.ChangedFieldLookupMixin,
                models.Model,
            ),
        ),
        migrations.CreateModel(
            name="Parser",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "method",
                    models.CharField(
                        choices=[("csv", "CSV"), ("json", "JSON"), ("xml", "XML")],
                        max_length=255,
                        verbose_name="Method",
                    ),
                ),
                (
                    "created_on",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created on"),
                ),
                (
                    "options",
                    jsonfield.fields.JSONField(
                        blank=True, default=dict, verbose_name="Options"
                    ),
                ),
            ],
            bases=(
                ims.api.mixins.ApiUtilsMixin,
                mapper.mixins.HasOptionForm,
                mapper.mixins.ModelSourcing,
                ims.models.mixin.SanitizeCharFieldMixin,
                ims.models.mixin.ChangedFieldLookupMixin,
                models.Model,
            ),
        ),
        migrations.CreateModel(
            name="TransformationLog",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "content_file",
                    models.FileField(
                        blank=True,
                        help_text="Use to keep a versionned snapshot of the content used during import",
                        max_length=1024,
                        null=True,
                        upload_to=mapper.models.transformation_models.TransformationLogFilePathGenerator(
                            "history_files"
                        ),
                        verbose_name="Content file",
                    ),
                ),
                (
                    "content_file_bkp",
                    models.FileField(
                        blank=True,
                        default=None,
                        max_length=1024,
                        null=True,
                        upload_to="",
                    ),
                ),
                (
                    "parsing_report",
                    models.FileField(
                        blank=True,
                        help_text="Use to keep a parsing report",
                        max_length=1024,
                        null=True,
                        upload_to=mapper.models.transformation_models.TransformationLogFilePathGenerator(
                            "parsing_reports"
                        ),
                        verbose_name="Parsing report",
                    ),
                ),
                (
                    "parsing_report_bkp",
                    models.FileField(
                        blank=True,
                        default=None,
                        max_length=1024,
                        null=True,
                        upload_to="",
                    ),
                ),
                (
                    "error_report",
                    models.FileField(
                        blank=True,
                        help_text="Use to keep an error report",
                        max_length=1024,
                        null=True,
                        upload_to=mapper.models.transformation_models.TransformationLogFilePathGenerator(
                            "error_reports"
                        ),
                        verbose_name="Error report",
                    ),
                ),
                (
                    "error_report_bkp",
                    models.FileField(
                        blank=True,
                        default=None,
                        max_length=1024,
                        null=True,
                        upload_to="",
                    ),
                ),
                (
                    "detailed_report",
                    models.FileField(
                        blank=True,
                        help_text="Use to keep a detailed report",
                        max_length=1024,
                        null=True,
                        upload_to=mapper.models.transformation_models.TransformationLogFilePathGenerator(
                            "detailed_reports"
                        ),
                        verbose_name="Detailed report",
                    ),
                ),
                (
                    "detailed_report_bkp",
                    models.FileField(
                        blank=True,
                        default=None,
                        max_length=1024,
                        null=True,
                        upload_to="",
                    ),
                ),
                (
                    "created_products_report",
                    models.FileField(
                        blank=True,
                        help_text="Use to keep a report of created products",
                        max_length=1024,
                        null=True,
                        upload_to=mapper.models.transformation_models.TransformationLogFilePathGenerator(
                            "created_products_reports"
                        ),
                        verbose_name="Created report",
                    ),
                ),
                (
                    "created_products_report_bkp",
                    models.FileField(
                        blank=True,
                        default=None,
                        max_length=1024,
                        null=True,
                        upload_to="",
                    ),
                ),
                (
                    "trigger_type",
                    models.CharField(
                        choices=[
                            ("manual", "Manual trigger"),
                            ("schedule", "Scheduled trigger"),
                        ],
                        default="manual",
                        max_length=100,
                        verbose_name="Trigger type",
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        blank=True,
                        choices=[("normal", "Normal"), ("high", "High")],
                        max_length=32,
                        null=True,
                        verbose_name="Priority",
                    ),
                ),
                (
                    "created_on",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created on"),
                ),
                (
                    "last_modified",
                    models.DateTimeField(
                        auto_now=True, null=True, verbose_name="Last modified"
                    ),
                ),
                (
                    "start",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Start date"
                    ),
                ),
                (
                    "start_process",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Start of process date"
                    ),
                ),
                (
                    "end",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="End date"
                    ),
                ),
                (
                    "valid_count",
                    models.IntegerField(
                        default=0,
                        help_text="Number of valid items during import",
                        verbose_name="Valid count",
                    ),
                ),
                (
                    "invalid_count",
                    models.IntegerField(
                        default=0,
                        help_text="Number of invalid items during import",
                        verbose_name="Invalid count",
                    ),
                ),
                (
                    "not_modified_count",
                    models.IntegerField(default=0, verbose_name="Not modified count"),
                ),
                (
                    "import_count",
                    models.IntegerField(
                        default=0,
                        help_text="Number of items imported in IZBERG",
                        verbose_name="Imported count",
                    ),
                ),
                (
                    "skipped_count",
                    models.IntegerField(default=0, verbose_name="Skipped count"),
                ),
                (
                    "warning_count",
                    models.IntegerField(default=0, verbose_name="Warnings count"),
                ),
                (
                    "error_count",
                    models.IntegerField(default=0, verbose_name="Errors count"),
                ),
                (
                    "status",
                    django_xworkflows.models.StateField(
                        max_length=16,
                        workflow=django_xworkflows.models._SerializedWorkflow(
                            initial_state="in_queue",
                            name="TransformationLogWorkflow",
                            states=[
                                "in_queue",
                                "parsing",
                                "processing",
                                "done",
                                "aborted",
                                "failed",
                            ],
                        ),
                    ),
                ),
                (
                    "cleaning_task_uri",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="Triggered cleaning task URI",
                    ),
                ),
            ],
            bases=(
                django_xworkflows.models.BaseWorkflowEnabled,
                ims.api.mixins.ApiUtilsMixin,
                mapper.mixins.ModelSourcing,
                models.Model,
            ),
        ),
        migrations.CreateModel(
            name="TransformationLogDetail",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                (
                    "created_on",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created on"),
                ),
                (
                    "error_code",
                    lib.fields.no_choice_migration_charfield.NoChoiceMigrationCharfield(
                        blank=True, max_length=50, null=True, verbose_name="Error code"
                    ),
                ),
                (
                    "item_identifier_key",
                    models.CharField(
                        blank=True,
                        choices=[("gtin", "GTIN"), ("sku", "SKU")],
                        max_length=10,
                        null=True,
                        verbose_name="Item identifier key",
                    ),
                ),
                (
                    "item_identifier_value",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="Item identifier value",
                    ),
                ),
                (
                    "item_external_id",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="Item external ID",
                    ),
                ),
                (
                    "kind",
                    models.CharField(
                        choices=[
                            ("valid", "Valid"),
                            ("invalid", "Invalid"),
                            ("skipped", "Skipped"),
                            ("warning", "Warning"),
                            ("error", "Error"),
                        ],
                        max_length=10,
                        verbose_name="Kind",
                    ),
                ),
                (
                    "item",
                    jsonfield.fields.JSONField(
                        blank=True,
                        default=dict,
                        help_text="JSON representation of the item",
                        verbose_name="Item",
                    ),
                ),
                (
                    "trigger_description",
                    models.CharField(
                        blank=True,
                        help_text="Exception description forwarder",
                        max_length=255,
                        null=True,
                        verbose_name="Trigger description",
                    ),
                ),
                (
                    "trigger_resource_uri",
                    models.CharField(
                        blank=True,
                        help_text="URI of the exception trigger. Ex: mapper, cleaner, matcher uris",
                        max_length=255,
                        null=True,
                        verbose_name="Trigger resource uri",
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Log description",
                        null=True,
                        verbose_name="Description",
                    ),
                ),
                (
                    "traceback",
                    models.TextField(
                        blank=True,
                        help_text="Error traceback",
                        null=True,
                        verbose_name="Traceback",
                    ),
                ),
            ],
            bases=(
                ims.api.mixins.ApiUtilsMixin,
                mapper.mixins.ModelSourcing,
                mp_utils.model_mixins.exportable_model_mixin.ExportableModelMixin,
                models.Model,
            ),
        ),
        migrations.CreateModel(
            name="TransformationLogTransitionLog",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "transition",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="transition"
                    ),
                ),
                (
                    "from_state",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="from state"
                    ),
                ),
                (
                    "to_state",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="to state"
                    ),
                ),
                (
                    "timestamp",
                    models.DateTimeField(
                        db_index=True,
                        default=django.utils.timezone.now,
                        verbose_name="performed at",
                    ),
                ),
                ("comment", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "transformation_log",
                    ims.fields.foreign_key.IzbergForeignKey(
                        db_index=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transition_logs",
                        to="mapper.transformationlog",
                    ),
                ),
            ],
            options={
                "verbose_name": "XWorkflow transition log",
                "verbose_name_plural": "XWorkflow transition logs",
                "ordering": ("-timestamp", "transition"),
                "abstract": False,
            },
        ),
    ]
