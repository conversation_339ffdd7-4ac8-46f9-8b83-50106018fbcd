from unittest.mock import patch

from drf_izberg.tests.base import BaseAPITestCase


class ItemCommissionRuleViewsSchemaTestCase(BaseAPITestCase):
    API_VERSION = "v2"
    # This attribute controls the maximum length of diffs output by assert methods
    # that report diffs on failure.
    maxDiff = None

    # We have to mock <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> because /schema/ is a special endpoint
    # with his own override of permissions.
    # see IzbergSchemaMixin in drf_izberg.views.schema_mixins
    @patch("rest_framework.permissions.IsAuthenticated.has_permission", lambda *_: True)
    def test_schema(self):
        resp = self.api_client.get(
            f"/{self.API_VERSION}/order-item-commission-rule/schema/"
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)

        # The expected schema is slightly different from the actual Tastypie schema.
        # - Most listed fields have their attributes blank and default changed.
        #   DRF will use the value of the model by default OR the value set on the
        #   serializer. Tastypie seems to be different and are sometimes wrong.
        #   Example: id with blank: True -> Wrong
        # - We are adding the attribute "required" that defined if the field is required
        #   by our serializer to pass validation.
        # - in methods, for each 'action' listed, we also add the allowed_http_methods
        #
        # Therefore, we cannot directly use our schema.json, we need to change
        # those values. You must be careful when changing the value, do not simply use
        # what our DRF schema generator is giving, but check on the model and
        # serializer that the information is correct
        expected_data = {
            "allowed_detail_http_methods": ["get", "post", "put", "patch", "delete"],
            "allowed_list_http_methods": ["get", "post"],
            "default_format": "application/json",
            "default_limit": 20,
            "doc": "",
            "editable_for_statuses": [],
            "fields": {
                "application": {
                    "blank": False,
                    "creatable": True,
                    "default": "",
                    "editable": False,
                    "help_text": "",
                    "name": "Application",
                    "nullable": False,
                    "readonly": True,
                    "related_type": "to_one",
                    "required": False,
                    "type": "related",
                    "unique": False,
                },
                "behaviour_group": {
                    "blank": False,
                    "creatable": True,
                    "default": "default",
                    "editable": True,
                    "help_text": "",
                    "max_length": 100,
                    "name": "Behaviour group",
                    "nullable": False,
                    "readonly": False,
                    "required": False,
                    "type": "string",
                    "unique": False,
                },
                "behaviour_type": {
                    "blank": False,
                    "choices": [
                        {"value": "additive", "help_text": "Additive application rule"},
                        {
                            "value": "exclusive",
                            "help_text": "Exclusive application rule",
                        },
                    ],
                    "choices_to_hide": [],
                    "creatable": True,
                    "default": "exclusive",
                    "editable": True,
                    "help_text": "",
                    "max_length": 50,
                    "name": "Behaviour type",
                    "nullable": False,
                    "readonly": False,
                    "required": False,
                    "type": "string",
                    "unique": False,
                    "virtual_choices": [],
                },
                "commission_fixed_price_amount": {
                    "blank": False,
                    "creatable": True,
                    "default": "",
                    "editable": True,
                    "help_text": "Commission's amount to apply on order_item/merchant",
                    "name": "Commission price",
                    "nullable": True,
                    "readonly": False,
                    "required": False,
                    "type": "decimal",
                    "unique": False,
                },
                "commission_method": {
                    "blank": False,
                    "choices": [
                        {"value": "fixed_price", "help_text": "Fixed price"},
                        {"value": "rate", "help_text": "Rate"},
                    ],
                    "choices_to_hide": [],
                    "creatable": True,
                    "default": "",
                    "editable": True,
                    "help_text": "",
                    "max_length": 50,
                    "name": "Commission method",
                    "nullable": False,
                    "readonly": False,
                    "required": True,
                    "type": "string",
                    "unique": False,
                    "virtual_choices": [],
                },
                "commission_rate_price_base": {
                    "blank": False,
                    "choices": [
                        {
                            "value": "product_tax_free",
                            "help_text": "Product price - taxes excluded",
                        },
                        {
                            "value": "product_tax_included",
                            "help_text": "Product price - taxes included",
                        },
                        {
                            "value": "product_and_shipping_tax_free",
                            "help_text": "Product & shipping price - taxes excluded",
                        },
                        {
                            "value": "product_and_shipping_tax_included",
                            "help_text": "Product & shipping price - taxes included",
                        },
                    ],
                    "choices_to_hide": [],
                    "creatable": True,
                    "default": None,
                    "editable": True,
                    "help_text": "Defines whether the rate should be calculated on prices with taxes or not and using shipping amount or not.",
                    "max_length": 50,
                    "name": "Commission rate price base",
                    "nullable": True,
                    "readonly": False,
                    "required": False,
                    "type": "string",
                    "unique": False,
                    "virtual_choices": [],
                },
                "commission_rate_value": {
                    "blank": False,
                    "creatable": True,
                    "default": "",
                    "editable": True,
                    "help_text": "Percentage of commission to apply on order_item/merchant",
                    "name": "Commission rate",
                    "nullable": True,
                    "readonly": False,
                    "required": False,
                    "type": "decimal",
                    "unique": False,
                },
                "description": {
                    "blank": True,
                    "creatable": True,
                    "default": "",
                    "editable": True,
                    "help_text": "Only for marketplace management; tells what the commission is. If this commission is expressed using a given reference, you shall add the reference in the description so other marketplace admins will be able to review the reference too.",
                    "max_length": 400,
                    "name": "Rule description",
                    "nullable": True,
                    "readonly": False,
                    "required": False,
                    "type": "string",
                    "unique": False,
                },
                "destination_countries": {
                    "blank": False,
                    "creatable": True,
                    "default": "",
                    "editable": True,
                    "help_text": "",
                    "name": "Destination countries",
                    "nullable": True,
                    "readonly": False,
                    "related_type": "to_many",
                    "required": False,
                    "type": "related",
                    "unique": False,
                },
                "ending_date": {
                    "blank": False,
                    "creatable": True,
                    "default": "",
                    "editable": True,
                    "help_text": "Lets you define a limit date after which commission doesn't apply anymore",
                    "name": "Application end date",
                    "nullable": True,
                    "readonly": False,
                    "required": False,
                    "type": "datetime",
                    "unique": False,
                },
                "external_id": {
                    "blank": True,
                    "creatable": True,
                    "default": "",
                    "editable": True,
                    "help_text": "Use it to store external matching id",
                    "max_length": 255,
                    "name": "External id",
                    "nullable": True,
                    "readonly": False,
                    "required": False,
                    "type": "string",
                    "unique": False,
                },
                "id": {
                    "blank": False,
                    "creatable": False,
                    "default": "",
                    "editable": False,
                    "help_text": "",
                    "name": "Id",
                    "nullable": False,
                    "readonly": True,
                    "required": False,
                    "type": "integer",
                    "unique": True,
                },
                "max_threshold": {
                    "blank": False,
                    "creatable": True,
                    "default": "",
                    "editable": True,
                    "help_text": "Rule application limit : over this item price, it won't apply",
                    "name": "Maximum item price",
                    "nullable": True,
                    "readonly": False,
                    "required": False,
                    "type": "decimal",
                    "unique": False,
                },
                "merchant_groups": {
                    "blank": False,
                    "creatable": True,
                    "default": "",
                    "editable": True,
                    "help_text": "",
                    "name": "Merchant groups",
                    "nullable": True,
                    "readonly": False,
                    "related_type": "to_many",
                    "required": False,
                    "type": "related",
                    "unique": False,
                },
                "merchants": {
                    "blank": False,
                    "creatable": True,
                    "default": "",
                    "editable": True,
                    "help_text": "",
                    "name": "Merchants",
                    "nullable": True,
                    "readonly": False,
                    "related_type": "to_many",
                    "required": False,
                    "type": "related",
                    "unique": False,
                },
                "min_threshold": {
                    "blank": False,
                    "creatable": True,
                    "default": "",
                    "editable": True,
                    "help_text": "Rule application limit : under this item price, it won't apply",
                    "name": "Minimum item price",
                    "nullable": True,
                    "readonly": False,
                    "required": False,
                    "type": "decimal",
                    "unique": False,
                },
                "name": {
                    "blank": True,
                    "creatable": True,
                    "default": "",
                    "editable": True,
                    "help_text": "Rule name as it will be read on order's details",
                    "max_length": 255,
                    "name": "Rule name",
                    "nullable": True,
                    "readonly": False,
                    "required": False,
                    "type": "string",
                    "unique": False,
                },
                "origin_countries": {
                    "blank": False,
                    "creatable": True,
                    "default": "",
                    "editable": True,
                    "help_text": "",
                    "name": "Origin countries",
                    "nullable": True,
                    "readonly": False,
                    "related_type": "to_many",
                    "required": False,
                    "type": "related",
                    "unique": False,
                },
                "override_only": {
                    "blank": False,
                    "creatable": False,
                    "default": False,
                    "editable": False,
                    "help_text": "Define whether the rule is only applicable manually or going through Izberg commissions engine",
                    "name": "Override only",
                    "nullable": False,
                    "readonly": True,
                    "required": False,
                    "type": "boolean",
                    "unique": False,
                },
                "price_currency": {
                    "blank": False,
                    "creatable": True,
                    "default": "",
                    "editable": True,
                    "help_text": "Currency used for commission (only used on fixed price commissions)",
                    "name": "Commission price's currency",
                    "nullable": False,
                    "readonly": False,
                    "related_type": "to_one",
                    "required": False,
                    "type": "related",
                    "unique": False,
                },
                "priority": {
                    "blank": False,
                    "creatable": True,
                    "default": 1,
                    "editable": True,
                    "help_text": "",
                    "name": "Priority",
                    "nullable": False,
                    "readonly": False,
                    "required": False,
                    "type": "integer",
                    "unique": False,
                },
                "product_brands": {
                    "blank": False,
                    "creatable": True,
                    "default": "",
                    "editable": True,
                    "help_text": "",
                    "name": "Product brands",
                    "nullable": True,
                    "readonly": False,
                    "related_type": "to_many",
                    "required": False,
                    "type": "related",
                    "unique": False,
                },
                "product_categories": {
                    "blank": False,
                    "creatable": True,
                    "default": "",
                    "editable": True,
                    "help_text": "",
                    "name": "Product categories",
                    "nullable": True,
                    "readonly": False,
                    "related_type": "to_many",
                    "required": False,
                    "type": "related",
                    "unique": False,
                },
                "product_condition": {
                    "blank": False,
                    "choices": [
                        {"value": 0, "help_text": "New"},
                        {"value": 1, "help_text": "Second Hand"},
                    ],
                    "choices_to_hide": [],
                    "creatable": True,
                    "default": "",
                    "editable": True,
                    "help_text": "Product condition selector : 1 item is new - 0 item is refurbished.",
                    "name": "Product condition",
                    "nullable": True,
                    "readonly": False,
                    "required": False,
                    "type": "integer",
                    "unique": False,
                    "virtual_choices": [],
                },
                "product_families": {
                    "blank": False,
                    "creatable": True,
                    "default": "",
                    "editable": True,
                    "help_text": "",
                    "name": "Product families",
                    "nullable": True,
                    "readonly": False,
                    "related_type": "to_many",
                    "required": False,
                    "type": "related",
                    "unique": False,
                },
                "resource_uri": {
                    "blank": False,
                    "creatable": False,
                    "default": "",
                    "editable": False,
                    "help_text": "",
                    "max_length": None,
                    "name": "Resource uri",
                    "nullable": False,
                    "readonly": True,
                    "required": False,
                    "type": "string",
                    "unique": False,
                },
                "starting_date": {
                    "blank": False,
                    "creatable": True,
                    "default": "",
                    "editable": True,
                    "help_text": "Lets you apply the commission after a given date.",
                    "name": "Application start date",
                    "nullable": True,
                    "readonly": False,
                    "required": False,
                    "type": "datetime",
                    "unique": False,
                },
                "status": {
                    "blank": False,
                    "choices": [
                        {"value": "active", "help_text": "Active rule"},
                        {"value": "inactive", "help_text": "Inactive rule"},
                        {"value": "deleted", "help_text": "Removed rule"},
                    ],
                    "choices_to_hide": [],
                    "creatable": False,
                    "default": "active",
                    "editable": False,
                    "help_text": "",
                    "max_length": 16,
                    "name": "Status",
                    "nullable": False,
                    "readonly": True,
                    "required": False,
                    "type": "string",
                    "unique": False,
                    "virtual_choices": [],
                    "transitions": {
                        "activate": {"to_state": "active", "from_states": ["inactive"]},
                        "deactivate": {
                            "to_state": "inactive",
                            "from_states": ["active"],
                        },
                        "delete_action": {
                            "to_state": "deleted",
                            "from_states": ["active", "inactive"],
                        },
                    },
                },
            },
            "filtering": {
                "id": 1,
                "status": 1,
                "name": 1,
                "priority": 1,
                "external_id": 1,
                "starting_date": 1,
                "ending_date": 1,
                "commission_method": 1,
                "behaviour_type": 1,
                "min_threshold": 1,
                "max_threshold": 1,
                "product_families": 1,
                "product_categories": 1,
                "product_brands": 1,
                "origin_countries": 1,
                "destination_countries": 1,
                "merchants": 1,
                "application": 1,
            },
            "methods": [
                {
                    "regex": "/v1/order-item-commission-rule/:id/activate/",
                    "doc_text": "",
                    "doc_html": "",
                    "allowed_http_methods": ["post"],
                },
                {
                    "regex": "/v1/order-item-commission-rule/:id/deactivate/",
                    "doc_text": "",
                    "doc_html": "",
                    "allowed_http_methods": ["post"],
                },
            ],
            "ordering": [
                "application",
                "behaviour_type",
                "commission_method",
                "ending_date",
                "external_id",
                "max_threshold",
                "min_threshold",
                "name",
                "priority",
                "starting_date",
                "status",
            ],
        }

        self.assertEqual(data, expected_data)
