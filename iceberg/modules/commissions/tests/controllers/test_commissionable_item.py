from decimal import Decimal

from apps.tax.utils import quantize_amount
from apps.testing.factories import (
    OrderItemDiscountUseFactory,
    ProductOfferOrderItemFactory,
)
from commissions.structures import CommissionableItem
from ims.tests import BaseTestCase


class MerchantOrderCommissionableItemTastCase(BaseTestCase):
    def test_merchant_order_amounts_no_discount(self):
        merchant_order = ProductOfferOrderItemFactory().merchant_order
        commission_item = CommissionableItem.build_from_merchant_order(merchant_order)
        self.assertEqual(commission_item.product_tax_free, merchant_order.price)
        self.assertEqual(
            commission_item.product_tax_included, merchant_order.price_vat_included
        )
        self.assertEqual(
            commission_item.product_and_shipping_tax_free, merchant_order.amount
        )
        self.assertEqual(
            commission_item.product_and_shipping_tax_included,
            merchant_order.amount_vat_included,
        )
        self.assertEqual(commission_item.shipping_tax_free, merchant_order.shipping)
        self.assertEqual(
            commission_item.shipping_tax_included, merchant_order.shipping_vat_included
        )
        self.assertEqual(
            commission_item.app_discount_amount_on_shipping_vat_included,
            Decimal("0.00"),
        )
        self.assertEqual(
            commission_item.app_discount_amount_on_shipping_vat_excluded,
            Decimal("0.00"),
        )

    def test_commissionable_amounts_with_merchant_discount_are_after_discount(self):
        order_item = ProductOfferOrderItemFactory()
        merchant_order = order_item.merchant_order
        OrderItemDiscountUseFactory(
            order_item=order_item,
            application=order_item.application,
            merchant=merchant_order.merchant,
            discount__financed_by_application=0,
        )
        commission_item = CommissionableItem.build_from_merchant_order(merchant_order)
        self.assertEqual(commission_item.product_tax_free, merchant_order.price)
        self.assertEqual(
            commission_item.product_tax_included, merchant_order.price_vat_included
        )
        self.assertEqual(
            commission_item.product_and_shipping_tax_free, merchant_order.amount
        )
        self.assertEqual(
            commission_item.product_and_shipping_tax_included,
            merchant_order.amount_vat_included,
        )
        self.assertEqual(commission_item.shipping_tax_free, merchant_order.shipping)
        self.assertEqual(
            commission_item.shipping_tax_included, merchant_order.shipping_vat_included
        )
        self.assertEqual(
            commission_item.app_discount_amount_on_shipping_vat_included,
            Decimal("0.00"),
        )
        self.assertEqual(
            commission_item.app_discount_amount_on_shipping_vat_excluded,
            Decimal("0.00"),
        )

    def test_commissionable_amounts_with_app_discount_are_before_discount(self):
        order_item = ProductOfferOrderItemFactory()
        merchant_order = order_item.merchant_order
        discount_use = OrderItemDiscountUseFactory(
            order_item=order_item,
            application=order_item.application,
            merchant=merchant_order.merchant,
            discount__financed_by_application=100,
        )
        commission_item = CommissionableItem.build_from_merchant_order(merchant_order)

        self.assertEqual(
            commission_item.product_tax_free,
            merchant_order.price + discount_use.discount_amount,
        )
        self.assertEqual(
            commission_item.product_tax_included,
            merchant_order.price_vat_included
            + discount_use.discount_amount_vat_included,
        )
        self.assertEqual(
            commission_item.product_and_shipping_tax_free,
            merchant_order.amount + discount_use.discount_amount,
        )
        self.assertEqual(
            commission_item.product_and_shipping_tax_included,
            merchant_order.amount_vat_included
            + discount_use.discount_amount_vat_included,
        )
        self.assertEqual(commission_item.shipping_tax_free, merchant_order.shipping)
        self.assertEqual(
            commission_item.shipping_tax_included, merchant_order.shipping_vat_included
        )
        self.assertEqual(
            commission_item.app_discount_amount_on_shipping_vat_included,
            Decimal("0.00"),
        )
        self.assertEqual(
            commission_item.app_discount_amount_on_shipping_vat_excluded,
            Decimal("0.00"),
        )

    def test_commissionable_amounts_with_shared_discount_are_before_app_discount(self):
        order_item = ProductOfferOrderItemFactory()
        merchant_order = order_item.merchant_order
        discount_use = OrderItemDiscountUseFactory(
            order_item=order_item,
            application=order_item.application,
            merchant=merchant_order.merchant,
            discount__financed_by_application=60,
        )
        commission_item = CommissionableItem.build_from_merchant_order(merchant_order)

        self.assertEqual(
            commission_item.product_tax_free,
            merchant_order.price + discount_use.discount_amount_financed_by_application,
        )
        self.assertEqual(
            commission_item.product_tax_included,
            merchant_order.price_vat_included
            + quantize_amount(discount_use.app_discount_amount_vat_incl, 2),
        )
        self.assertEqual(
            commission_item.product_and_shipping_tax_free,
            merchant_order.amount
            + discount_use.discount_amount_financed_by_application,
        )
        self.assertEqual(
            commission_item.product_and_shipping_tax_included,
            merchant_order.amount_vat_included
            + quantize_amount(discount_use.app_discount_amount_vat_incl, 2),
        )
        self.assertEqual(commission_item.shipping_tax_free, merchant_order.shipping)
        self.assertEqual(
            commission_item.shipping_tax_included, merchant_order.shipping_vat_included
        )
        self.assertEqual(
            commission_item.app_discount_amount_on_shipping_vat_included,
            Decimal("0.00"),
        )
        self.assertEqual(
            commission_item.app_discount_amount_on_shipping_vat_excluded,
            Decimal("0.00"),
        )
