# -*- coding: utf-8 -*-
from logging import getLogger

from commissions.models import (
    CommissionLine,
    ItemCommissionRule,
    MerchantCommissionRule,
)
from commissions.structures import CommissionableItem, InvoiceToCommissionMarshaller

logger = getLogger(__name__)


class CommissionHandlingController:
    def __init__(self, commission_handler):
        self.handler = commission_handler

    def _delete_previous_lines(self, merchant_order):
        """Remove any commission line that was already computed for this
        merchant_order.

        :param merchant_order: Merchant order to inspect to detect stalled
        lines
        :type merchant_order: apps.orders.models.MerchantOrder
        """
        merchant_order.commission_lines.actives().delete()

    def get_order_commission_lines(self, merchant_order):
        """Gives the commission lines applied for the handler's application
        on each order-item. Result is a dict matching each order-item's ID with
        a list of applied commission lines.

        :param merchant_order: Order to get lines for.
        :type merchant_order: apps.orders.models.MerchantOrder

        :rtype: dict
        """
        logger.info("Getting commission lines for order %s", merchant_order.id)
        order_items = self.get_merchant_order_items(merchant_order)
        order = merchant_order.order
        logger.info("Handler is applied to %s", merchant_order.application)
        rules_dict = self.get_item_commission_rules_from_order(
            order_items, merchant_order, order, merchant_order.application_id
        )
        rules_dict["merchant_order"] = self.get_merchant_commission_rules(
            merchant_order, order
        )

        logger.info("Handler fetched the folowing rules : %s", rules_dict)

        commissionable_items = list(
            map(CommissionableItem.build_from_order_item, order_items)
        )
        self._delete_previous_lines(merchant_order)
        lines = self.get_commission_lines_table(
            commissionable_items,
            merchant_order.id,
            rules_dict,
            merchant_order.application,
        )
        logger.info("Handler returning the following commission lines : %s", lines)

        return lines

    def get_invoice_commission_lines(self, invoice):
        """Gives the commission lines applied for the handler's application
        on each invoice-line. Result is a dict matching each line's ID with
        a list of applied commission lines.

        :param invoice: Invoice on which getting commission.
        :type invoice: invoicing.models.CustomerInvoice

        :rtype: dict
        """
        logger.info("Getting commission lines for invoice %s", invoice.id)
        order_items = self.get_order_items_bound_to_invoice(invoice)
        logger.info("Handler is applied to %s", invoice.application_id)

        rules_dict = self.get_item_commission_rules_from_invoice(
            order_items,
            InvoiceToCommissionMarshaller(invoice),
            invoice,
            invoice.application_id,
        )

        logger.info("Handler fetched the folowing rules : %s", rules_dict)

        order_items_to_invoice_lines = self.get_invoice_items_mapping(invoice)
        commissionable_items = list(
            map(
                CommissionableItem.build_from_invoice_line,
                order_items_to_invoice_lines.values(),
            )
        )
        lines = self.get_commission_lines_table(
            commissionable_items,
            # Commissions are not applicable to merchant-order when applied
            # to an invoice
            None,
            rules_dict,
            invoice.application,
            order_items_to_invoice_lines,
        )
        logger.info("Handler returning the following commission lines : %s", lines)
        return lines

    ###
    # Utils
    ###

    @staticmethod
    def get_merchant_order_items(merchant_order):
        """
        Get order items on which commission applies, ie non deleted/canceled.

        This function is not implemented as a generator because we wanted to
        have only one query made to database for fetching order_items
        """
        # wrapped the queryset into a list to fetch all rules at once
        return list(merchant_order.order_items.exclude_cancelled_deleted())

    @staticmethod
    def get_invoice_items_mapping(invoice):
        """Get a mapping between order items on which commission applies,
        i.e. existing items. AND related invoice_line instance.
        """
        mapping = {}
        for invoice_line in list(
            invoice.lines.exclude_deleted()
            .exclude(line_type="shipping")
            .exclude(line_type="extra_fee")
        ):
            mapping[invoice_line.order_item_id] = invoice_line
        return mapping

    @staticmethod
    def get_order_items_bound_to_invoice(invoice):
        return list(
            map(
                lambda x: x.order_item,
                list(
                    invoice.lines.exclude_deleted()
                    .exclude(line_type="shipping")
                    .exclude(line_type="extra_fee")
                ),
            )
        )

    def get_item_commission_rules_from_order(
        self, order_items, merchant_order, order, application_id
    ):
        applied_rules_table = {}

        if not order_items:
            return applied_rules_table

        # cast this to a list, to avoid refetching rules on each order-item
        # iteration
        commission_rules = list(self.get_ordered_item_rules(application_id))
        item_commission_rules_override = order.get_populated_item_commission_override()

        for item in order_items:
            # Check for commission override first:
            if item in item_commission_rules_override:
                applied_rules_table[item.id] = item_commission_rules_override[item]
                continue

            # otherwise retrieve applicable rules
            rules_to_apply = self.find_applicable_rules(
                item, merchant_order, order, commission_rules
            )
            applied_rules_table[item.id] = rules_to_apply

        return applied_rules_table

    def get_item_commission_rules_from_invoice(
        self, order_items, invoice_as_merchant_order, invoice, application_id
    ):
        applied_rules_table = {}

        if not order_items:
            return applied_rules_table

        # cast this to a list, to avoid refetching rules on each order-item
        # iteration
        commission_rules = list(self.get_ordered_item_rules(application_id))
        for item in order_items:
            rules_to_apply = self.find_applicable_rules(
                item, invoice_as_merchant_order, invoice, commission_rules
            )
            applied_rules_table[item.id] = rules_to_apply

        return applied_rules_table

    def get_merchant_commission_rules(self, merchant_order, order):
        application_id = merchant_order.application_id
        commission_rules = self.get_ordered_merchant_rules(application_id)

        merchant_commission_rules_override = (
            order.get_populated_merchant_commission_override()
        )
        # Check for commission override first:
        if merchant_order in merchant_commission_rules_override:
            return merchant_commission_rules_override[merchant_order]

        # otherwise return applicable rules
        return self.find_applicable_rules(None, merchant_order, order, commission_rules)

    def get_order(self, order_item):
        return order_item.merchant_order.order

    def get_ordered_item_rules(self, application_id):
        return (
            ItemCommissionRule.objects.actives(application_id=application_id)
            .filter(override_only=False)
            .order_by("-priority", "id")
            .prefetch_related(
                "product_families",
                "product_categories",
                "product_brands",
                "origin_countries",
                "destination_countries",
                "merchant_groups",
                "merchants",
            )
        )

    def get_ordered_merchant_rules(self, application_id):
        return (
            MerchantCommissionRule.objects.actives(application=application_id)
            .filter(override_only=False)
            .order_by("-priority", "id")
        )

    def find_applicable_rules(
        self, order_item, merchant_order, order, commission_rules
    ):
        """Get a list of rules applicable to the order item / merchant order.

        :param obj: Order item or merchant order to examine
        :type order_item: apps.orders.models.OrderItem OR
                          apps.orders.models.MerchantOrder

        :param commission_rules: Ordered list of rules
        :type commission_rules: list

        :return: filtered list of rules
        :rtype: list
        """
        rules_to_apply = []
        behaviour_groups = {}
        for rule in commission_rules:
            # Commission with override are always skipped
            # They are applied manually by the Operator.
            if rule.override_only:
                continue

            if isinstance(rule, ItemCommissionRule) and not rule.is_applicable(
                order_item, merchant_order, order
            ):
                continue
            if isinstance(rule, MerchantCommissionRule) and not rule.is_applicable(
                merchant_order, order
            ):
                continue
            elif rule.is_additive:
                # Additive rule: just applying
                rules_to_apply.append(rule)

            elif rule.behaviour_group not in behaviour_groups:
                # Exclusive rule: Only 1 rule per group
                rules_to_apply.append(rule)
                behaviour_groups[rule.behaviour_group] = rule.behaviour_type

        if not rules_to_apply:
            target = order_item or merchant_order

            logger.warning(
                "Marketplace {} has no rule matching {} {}".format(
                    target.application_id, target.__class__.__name__, target.id
                )
            )
        return rules_to_apply

    def _compute_merchant_order_commission_lines(
        self, merchant_order_id, rules, application, default_currency=None
    ):
        lines = []
        for rule in rules:
            lines.append(
                CommissionLine.objects.create_for_merchant_order_and_rule(
                    merchant_order_id,
                    rule,
                    rule.price_currency or default_currency,
                    application,
                )
            )
        return lines

    def get_commission_lines_table(
        self,
        commissionable_items,
        merchant_order_id,
        rules_dict,
        application,
        invoice_lines_mapping=None,
    ):
        """Fetches or creates commssion lines for each order-item

        If a third party is involved in the transaction (example share the
        earnings with a given user), then the commission will be calculated
        using this application and it's applicable taxes if any.

        :param order_items: uses the order's items to compute the amount
        :type order_items: list

        :param rules_dict: dict of selected rules, per order item-ID
        :param rules_dict: dict<int,commissions.models.ItemCommissionRule>

        :param invoice_lines_mapping: if given - binds an order-item-id to
        an invoice_line
        :type invoice_lines_mapping:
            dict<int,invoicing.models.CustomerInvoiceLine>

        :return: Commissions prices per merchant_order (in dict)
        :rtype: CommissionPrice

        """
        if invoice_lines_mapping is None:
            invoice_lines_mapping = {}
        commissions = {}

        last_item_currency = (
            commissionable_items[-1].currency if len(commissionable_items) else None
        )

        for item in commissionable_items:
            commissions[item.id] = []
            for rule in rules_dict[item.id]:
                line = CommissionLine.objects.create_for_order_item_and_rule(
                    item.id,
                    rule,
                    item.quantity,
                    item.merchant_order_id,
                    item.currency,
                    application,
                    invoice_lines_mapping.get(item.id, None),
                )
                commissions[item.id].append(line)

        if "merchant_order" not in rules_dict:
            return commissions

        commissions["merchant_order"] = self._compute_merchant_order_commission_lines(
            merchant_order_id,
            rules_dict["merchant_order"],
            application,
            default_currency=last_item_currency,
        )
        return commissions

    def get_term_payment_order_commission_lines(self, merchant_order):
        logger.info("Getting commission lines for order {merchant_order.id}")
        order = merchant_order.order
        logger.info(f"Handler is applied to {merchant_order.application}")
        rules = self.get_merchant_commission_rules(merchant_order, order)

        logger.info(f"Handler fetched the folowing rules : {rules}")

        self._delete_previous_lines(merchant_order)
        lines = self._compute_merchant_order_commission_lines(
            merchant_order.id,
            rules,
            merchant_order.application,
            default_currency=merchant_order.currency,
        )
        logger.info(f"Handler returning the following commission lines : {lines}")
        return lines
