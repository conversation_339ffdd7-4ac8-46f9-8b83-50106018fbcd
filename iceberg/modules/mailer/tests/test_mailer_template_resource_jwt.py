# -*- coding: utf-8 -*-

from apps.testing.factories import (
    ActionFactory,
    ApplicationFactory,
    ApplicationTemplateFactory,
)
from ims.tests import BaseResourceTestCase
from ims.tests.jwt_tokens import GLO<PERSON>L_TOKENS
from mock import ANY


class MailerTemplateResourceJwtTestCase(BaseResourceTestCase):
    def test_read_list_as_operator(self):
        tpl = ApplicationTemplateFactory(application__id=1)
        ApplicationTemplateFactory(application__id=2)

        resp = self.api_client.get(
            "/v1/mailer_template/",
            authentication=("Bearer " + GLOBAL_TOKENS["APPLICATION_1_READ"]["token"]),
        )

        data = self.deserialize(resp)
        self.assertHttpOK(resp)
        self.assertEqual(data["meta"]["total_count"], 1)
        self.assertEqual(data["objects"][0]["id"], tpl.id)

    def test_read_detail_as_operator_valid(self):
        tpl = ApplicationTemplateFactory(application__id=1)

        resp = self.api_client.get(
            f"/v1/mailer_template/{tpl.id}/",
            authentication=("Bearer " + GLOBAL_TOKENS["APPLICATION_1_READ"]["token"]),
        )

        data = self.deserialize(resp)
        self.assertHttpOK(resp)
        self.assertEqual(data["id"], tpl.id)

    def test_read_detail_as_operator_invalid(self):
        ApplicationFactory(id=1)
        tpl = ApplicationTemplateFactory(application__id=2)

        resp = self.api_client.get(
            f"/v1/mailer_template/{tpl.id}/",
            authentication=("Bearer " + GLOBAL_TOKENS["APPLICATION_1_READ"]["token"]),
        )

        data = self.deserialize(resp)
        self.assertHttpUnauthorized(resp)
        self.assertEqual(
            data,
            {
                "error": "invalid_token",
                "error_description": "Invalid access token for this action",
                "error_code": "OAUTH.INVALID_TOKEN",
                "error_context": {
                    "action": "read_detail",
                    "scope": "read",
                    "application_id": 1,
                    "merchant_id": None,
                },
            },
        )

    def test_update_detail_as_operator_admin(self):
        tpl = ApplicationTemplateFactory(application__id=1)

        resp = self.api_client.patch(
            f"/v1/mailer_template/{tpl.id}/",
            data={"name": "hello"},
            authentication=("Bearer " + GLOBAL_TOKENS["APPLICATION_1_ADMIN"]["token"]),
        )

        self.assertHttpAccepted(resp)
        self.assertEqual(self.deserialize(resp)["name"], "hello")

    def test_create_detail_as_operator_admin(self):
        app = ApplicationFactory(id=1)
        action = ActionFactory()
        resp = self.api_client.post(
            "/v1/mailer_template/",
            data={
                "action": action.get_resource_uri(),
                "to": "<EMAIL>",
                "subject": "hello",
                "name": "Groot",
                "content_text": "Je s'appelle Groot",
                "language": "fr",
            },
            authentication=("Bearer " + GLOBAL_TOKENS["APPLICATION_1_ADMIN"]["token"]),
        )

        self.assertHttpCreated(resp)
        self.assertEqual(
            self.deserialize(resp),
            {
                "action": {
                    "id": action.id,
                    "pk": action.id,
                    "resource_uri": action.get_resource_uri(),
                },
                "application": {
                    "id": app.id,
                    "pk": app.id,
                    "resource_uri": app.get_resource_uri(),
                },
                "content_html": "",
                "content_text": "Je s'appelle Groot",
                "created_on": ANY,
                "creator": None,
                "creator_id": None,
                "from_email": "",
                "from_name": "",
                "id": ANY,
                "integrator": False,
                "language": "fr",
                "last_modified": ANY,
                "merchant": None,
                "name": "Groot",
                "resource_uri": ANY,
                "subject": "hello",
                "to": "<EMAIL>",
            },
        )

    def test_delete_detail_as_operator_admin(self):
        tpl = ApplicationTemplateFactory(application__id=1)

        resp = self.api_client.delete(
            f"/v1/mailer_template/{tpl.id}/",
            authentication=("Bearer " + GLOBAL_TOKENS["APPLICATION_1_ADMIN"]["token"]),
        )

        self.assertHttpNoContent(resp)
        with self.assertRaises(tpl.DoesNotExist):
            tpl.refresh_from_db()
