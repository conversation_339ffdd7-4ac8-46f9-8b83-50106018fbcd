# -*- coding: utf-8 -*-


import logging
from decimal import Decimal

from apps.orders.models import MerchantOrder
from apps.reviews.models import MerchantReview
from apps.user.models import InMemoryUser
from django.core.exceptions import ValidationError
from django.db import models
from django.utils.translation import gettext_lazy as _
from ims.api.decorators import api_view
from ims.api.exceptions import MandatoryUserAction
from ims.api.resources import MPModelResource
from reference.status.order import MERCHANT_ORDER_REVIEWABLE_STATUSES
from tastypie import fields
from tastypie.constants import ALL, ALL_WITH_RELATIONS
from tastypie.http import HttpAccepted

from .auth import ReviewResourceAuthorization

logger = logging.getLogger(__name__)


class MerchantReviewResource(MPModelResource):
    id = fields.CharField("id", readonly=True, unique=True)
    application = fields.ForeignKey(
        "apps.ice_applications.api.ApplicationResource",
        "merchant_order__application",
        readonly=True,
        full=False,
    )
    merchant = fields.ForeignKey(
        "apps.stores.api.resources.MerchantResource",
        "merchant_order__merchant",
        readonly=True,
        full=False,
    )
    customer = fields.ForeignKey(
        "apps.user.api.resources.UserResource",
        "merchant_order__user",
        readonly=True,
        full=False,
    )
    merchant_order = fields.ForeignKey(
        "apps.orders.api.resources.MerchantOrderResource",
        "merchant_order",
        full=False,
        use_in=["detail"],
    )

    merchant_name = fields.CharField(
        "merchant_order__merchant__name",
        readonly=True,
        help_text=_("Merchant commercial name"),
    )

    display_name = fields.CharField(
        "display_name",
        blank=True,
    )

    title = fields.CharField("title", blank=True, null=False)

    body = fields.CharField("body", blank=True, null=False)

    score = fields.IntegerField(
        "score",
        blank=False,
        null=False,
    )
    has_votes = fields.BooleanField("has_votes", readonly=True, default=False)
    up_votes = fields.IntegerField("up_votes", readonly=True, default=0)
    down_votes = fields.IntegerField("down_votes", readonly=True, default=0)

    class Meta:
        queryset = MerchantReview.objects.exclude_deleted()
        authorization = ReviewResourceAuthorization()
        resource_name = "merchant-review"
        always_return_data = True
        cacheable_methods = ["get_list"]
        protect_unknown_fields = True

        creatable_fields = [
            "merchant_order",
            "display_name",
            "title",
            "score",
            "body",
        ]
        editable_fields = [
            "body",
            "title",
            "score",
            "display_name",
        ]

        filtering = {
            "id": ALL,
            "customer": ALL_WITH_RELATIONS,
            "merchant": ALL_WITH_RELATIONS,
            "application": ALL_WITH_RELATIONS,
            "merchant_order": ALL_WITH_RELATIONS,
            "created_on": ALL,
            "last_modified": ALL,
            "status": ALL,
            "score": ALL,
            "total_votes": ALL,
            "delta_votes": ALL,
            "has_votes": ALL,
        }
        fields_editable_for_statuses = MerchantReview.EDITABLE_FOR_STATUSES

    def save(self, bundle, skip_errors=False):
        if bundle.obj:
            bundle.obj.full_clean()
        return super(MerchantReviewResource, self).save(bundle, skip_errors)

    def prepend_urls(self):
        return [
            self.make_url(
                "can-review", "can_review", "mp_api_merchant_review_can_review"
            ),
            self.make_url(
                "average-score", "average_score", "mp_api_merchant_review_average_score"
            ),
            self.make_uuid_pk_url(
                "approve", "approve_view", "mp_api_merchant_review_approve"
            ),
            self.make_uuid_pk_url(
                "reject", "reject_view", "mp_api_merchant_review_reject"
            ),
            self.make_uuid_pk_url(
                "can-delete", "can_delete_view", "mp_api_merchant_review_delete"
            ),
            self.make_uuid_pk_url(
                "vote-up", "vote_up_view", "mp_api_merchant_review_vote_up"
            ),
            self.make_uuid_pk_url(
                "vote-down", "vote_down_view", "mp_api_merchant_review_vote_down"
            ),
            self.make_uuid_pk_url(
                "cancel-vote", "cancel_vote_view", "mp_api_merchant_review_cancel_vote"
            ),
        ]

    @api_view(
        method_allowed=["get"], check_permissions=True, requested_action="read_list"
    )
    def can_review(self, request, **kwargs):
        """
        [Doc]
        Check if user can submit a review on merchant `merchant_id`

        @Allowed methods: GET
        @Args:
            - merchant_id: Merchant to check
            - [customer_id]:
                Customer to check (if missing, customer is taken from bearer)

        @Return: {'can_review': bool}
        """
        merchant_id = request.GET.get("merchant_id")
        if merchant_id is None:
            raise ValidationError(
                {
                    "merchant_id": _("Missing required get parameter 'merchant_id'"),
                }
            )
        customer_id = request.GET.get("customer_id", request.user.id)

        already_reviewed = (
            MerchantReview.objects.approved()
            .filter(
                merchant_order__merchant=merchant_id,
                merchant_order__user_id=customer_id,
            )
            .values_list("merchant_order__id", flat=True)
        )

        not_reviewed_orders = MerchantOrder.objects.filter(
            merchant_id=merchant_id,
            user_id=customer_id,
            status__in=MERCHANT_ORDER_REVIEWABLE_STATUSES,
        ).exclude(id__in=already_reviewed)
        return self.create_response(
            request, {"can_review": not_reviewed_orders.exists()}
        )

    @api_view(
        method_allowed=["get"], check_permissions=True, requested_action="read_list"
    )
    def average_score(self, request, **kwargs):
        """
        [Doc]
        Return review statistics about merchant

        @Allowed methods: GET
        @Args:
        - merchant_id: Merchant to get rate for

        @Return: {
            "average": <average_score>
            "scores": {
                "1": <count of scores == 1>
                "2": <count of scores == 2>
                "3": <count of scores == 3>
                "4": <count of scores == 4>
                "5": <count of scores == 5>
            }
        }
        """
        if "merchant_id" not in request.GET:
            raise ValidationError(
                {"merchant_id": _("Missing required get parameter 'merchant_id'")}
            )
        merchant_id = request.GET["merchant_id"]

        query = MerchantReview.objects.approved().filter(
            merchant_order__merchant=merchant_id
        )
        average = query.aggregate(
            average=models.Avg("score"),
        )
        res = {
            "average": (
                Decimal(average["average"]).quantize(Decimal("0.01"))
                if average["average"] is not None
                else None
            ),
            "scores_count": {
                "1": query.filter(score=1).count(),
                "2": query.filter(score=2).count(),
                "3": query.filter(score=3).count(),
                "4": query.filter(score=4).count(),
                "5": query.filter(score=5).count(),
            },
        }
        return self.create_response(request, res)

    @api_view(
        method_allowed=["get", "post"],
        check_permissions=True,
        requested_action="moderate",
    )
    def approve_view(self, request, **kwargs):
        """[Doc]
        Approve moderated review

        @Allowed methods: GET, POST
        """
        return self.generic_transition_view(request, "approve", **kwargs)

    @api_view(
        method_allowed=["get", "post"],
        check_permissions=True,
        requested_action="moderate",
    )
    def reject_view(self, request, **kwargs):
        """[Doc]
        Reject review

        @Allowed methods: GET, POST
        """
        return self.generic_transition_view(request, "reject", **kwargs)

    @api_view(
        method_allowed=["get"], check_permissions=True, requested_action="delete_detail"
    )
    def can_delete_view(self, request, **kwargs):
        """[Doc]
        Can delete review

        @Allowed methods: GET
        """
        return self.generic_can_delete_view(request, **kwargs)

    @api_view(
        method_allowed=["get", "post"], check_permissions=True, requested_action="vote"
    )
    def vote_up_view(self, request, **kwargs):
        """[Doc]
        Vote for review

        @Allowed methods: POST
        """
        if isinstance(request.user, InMemoryUser) and "by_user" not in request.GET:
            raise MandatoryUserAction()
        return self.generic_action_view(
            request,
            "vote_up",
            requested_auth_action="vote",
            response_class=HttpAccepted,  # keep legacy behaviour
            allow_by_user_in_request=True,
            **kwargs,
        )

    @api_view(method_allowed=["post"], check_permissions=True, requested_action="vote")
    def vote_down_view(self, request, **kwargs):
        """[Doc]
        Vote for review

        @Allowed methods: POST
        """
        if isinstance(request.user, InMemoryUser) and "by_user" not in request.GET:
            raise MandatoryUserAction()
        return self.generic_action_view(
            request,
            "vote_down",
            requested_auth_action="vote",
            response_class=HttpAccepted,  # keep legacy behaviour
            allow_by_user_in_request=True,
            **kwargs,
        )

    @api_view(method_allowed=["post"], check_permissions=True, requested_action="vote")
    def cancel_vote_view(self, request, **kwargs):
        """[Doc]
        Cancel last vote for review

        @Allowed methods: POST
        """
        if isinstance(request.user, InMemoryUser) and "by_user" not in request.GET:
            raise MandatoryUserAction()
        return self.generic_action_view(
            request,
            "cancel_vote",
            requested_auth_action="vote",
            response_class=HttpAccepted,  # keep legacy behaviour
            allow_by_user_in_request=True,
            **kwargs,
        )

    def obj_delete(self, bundle, **kwargs):
        return self.generic_soft_obj_delete(bundle, **kwargs)
