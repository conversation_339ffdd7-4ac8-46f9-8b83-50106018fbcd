[{"model": "address.state", "pk": 1, "fields": {"country": 36, "name": "British Columbia", "iso": "BC"}}, {"model": "address.state", "pk": 2, "fields": {"country": 36, "name": "Newfoundland and Labrador", "iso": "NL"}}, {"model": "address.state", "pk": 3, "fields": {"country": 36, "name": "Saskatchewan", "iso": "SK"}}, {"model": "address.state", "pk": 4, "fields": {"country": 36, "name": "Prince Edward Island", "iso": "PE"}}, {"model": "address.state", "pk": 5, "fields": {"country": 36, "name": "Ontario", "iso": "ON"}}, {"model": "address.state", "pk": 6, "fields": {"country": 36, "name": "Nova Scotia", "iso": "NS"}}, {"model": "address.state", "pk": 7, "fields": {"country": 36, "name": "Quebec", "iso": "QC"}}, {"model": "address.state", "pk": 8, "fields": {"country": 36, "name": "Alberta", "iso": "AB"}}, {"model": "address.state", "pk": 9, "fields": {"country": 36, "name": "Manitoba", "iso": "MB"}}, {"model": "address.state", "pk": 10, "fields": {"country": 36, "name": "Northwest Territories", "iso": "NT"}}, {"model": "address.state", "pk": 11, "fields": {"country": 36, "name": "New Brunswick", "iso": "NB"}}, {"model": "address.state", "pk": 12, "fields": {"country": 36, "name": "Nunavut", "iso": "NU"}}, {"model": "address.state", "pk": 13, "fields": {"country": 36, "name": "Yukon", "iso": "YT"}}, {"model": "address.state", "pk": 14, "fields": {"country": 223, "name": "Mississippi", "iso": "MS"}}, {"model": "address.state", "pk": 15, "fields": {"country": 223, "name": "Oklahoma", "iso": "OK"}}, {"model": "address.state", "pk": 16, "fields": {"country": 223, "name": "Delaware", "iso": "DE"}}, {"model": "address.state", "pk": 17, "fields": {"country": 223, "name": "Minnesota", "iso": "MN"}}, {"model": "address.state", "pk": 18, "fields": {"country": 223, "name": "Illinois", "iso": "IL"}}, {"model": "address.state", "pk": 19, "fields": {"country": 223, "name": "Arkansas", "iso": "AR"}}, {"model": "address.state", "pk": 20, "fields": {"country": 223, "name": "New Mexico", "iso": "NM"}}, {"model": "address.state", "pk": 21, "fields": {"country": 223, "name": "Indiana", "iso": "IN"}}, {"model": "address.state", "pk": 22, "fields": {"country": 223, "name": "Maryland", "iso": "MD"}}, {"model": "address.state", "pk": 23, "fields": {"country": 223, "name": "Louisiana", "iso": "LA"}}, {"model": "address.state", "pk": 24, "fields": {"country": 223, "name": "Idaho", "iso": "ID"}}, {"model": "address.state", "pk": 25, "fields": {"country": 223, "name": "Rhode Island and Providence Plantations", "iso": "RI"}}, {"model": "address.state", "pk": 26, "fields": {"country": 223, "name": "Wyoming", "iso": "WY"}}, {"model": "address.state", "pk": 27, "fields": {"country": 223, "name": "Tennessee", "iso": "TN"}}, {"model": "address.state", "pk": 28, "fields": {"country": 223, "name": "Arizona", "iso": "AZ"}}, {"model": "address.state", "pk": 29, "fields": {"country": 223, "name": "Iowa", "iso": "IA"}}, {"model": "address.state", "pk": 30, "fields": {"country": 223, "name": "Michigan", "iso": "MI"}}, {"model": "address.state", "pk": 31, "fields": {"country": 223, "name": "Kansas", "iso": "KS"}}, {"model": "address.state", "pk": 32, "fields": {"country": 223, "name": "Utah", "iso": "UT"}}, {"model": "address.state", "pk": 33, "fields": {"country": 223, "name": "Virginia", "iso": "VA"}}, {"model": "address.state", "pk": 34, "fields": {"country": 223, "name": "Oregon", "iso": "OR"}}, {"model": "address.state", "pk": 35, "fields": {"country": 223, "name": "Connecticut", "iso": "CT"}}, {"model": "address.state", "pk": 36, "fields": {"country": 223, "name": "Montana", "iso": "MT"}}, {"model": "address.state", "pk": 37, "fields": {"country": 223, "name": "California", "iso": "CA"}}, {"model": "address.state", "pk": 38, "fields": {"country": 223, "name": "Massachusetts", "iso": "MA"}}, {"model": "address.state", "pk": 39, "fields": {"country": 223, "name": "West Virginia", "iso": "WV"}}, {"model": "address.state", "pk": 40, "fields": {"country": 223, "name": "South Carolina", "iso": "SC"}}, {"model": "address.state", "pk": 41, "fields": {"country": 223, "name": "New Hampshire", "iso": "NH"}}, {"model": "address.state", "pk": 42, "fields": {"country": 223, "name": "Wisconsin", "iso": "WI"}}, {"model": "address.state", "pk": 43, "fields": {"country": 223, "name": "Vermont", "iso": "VT"}}, {"model": "address.state", "pk": 44, "fields": {"country": 223, "name": "Georgia", "iso": "GA"}}, {"model": "address.state", "pk": 45, "fields": {"country": 223, "name": "North Dakota", "iso": "ND"}}, {"model": "address.state", "pk": 46, "fields": {"country": 223, "name": "Pennsylvania", "iso": "PA"}}, {"model": "address.state", "pk": 47, "fields": {"country": 223, "name": "Florida", "iso": "FL"}}, {"model": "address.state", "pk": 48, "fields": {"country": 223, "name": "Alaska", "iso": "AK"}}, {"model": "address.state", "pk": 49, "fields": {"country": 223, "name": "Kentucky", "iso": "KY"}}, {"model": "address.state", "pk": 50, "fields": {"country": 223, "name": "Hawaii", "iso": "HI"}}, {"model": "address.state", "pk": 51, "fields": {"country": 223, "name": "Nebraska", "iso": "NE"}}, {"model": "address.state", "pk": 52, "fields": {"country": 223, "name": "Missouri", "iso": "MO"}}, {"model": "address.state", "pk": 53, "fields": {"country": 223, "name": "Ohio", "iso": "OH"}}, {"model": "address.state", "pk": 54, "fields": {"country": 223, "name": "Alabama", "iso": "AL"}}, {"model": "address.state", "pk": 55, "fields": {"country": 223, "name": "New York", "iso": "NY"}}, {"model": "address.state", "pk": 56, "fields": {"country": 223, "name": "South Dakota", "iso": "SD"}}, {"model": "address.state", "pk": 57, "fields": {"country": 223, "name": "Colorado", "iso": "CO"}}, {"model": "address.state", "pk": 58, "fields": {"country": 223, "name": "New Jersey", "iso": "NJ"}}, {"model": "address.state", "pk": 59, "fields": {"country": 223, "name": "Washington", "iso": "WA"}}, {"model": "address.state", "pk": 60, "fields": {"country": 223, "name": "North Carolina", "iso": "NC"}}, {"model": "address.state", "pk": 61, "fields": {"country": 223, "name": "District of Columbia", "iso": "DC"}}, {"model": "address.state", "pk": 62, "fields": {"country": 223, "name": "Texas", "iso": "TX"}}, {"model": "address.state", "pk": 63, "fields": {"country": 223, "name": "Nevada", "iso": "NV"}}, {"model": "address.state", "pk": 64, "fields": {"country": 223, "name": "Maine", "iso": "ME"}}, {"model": "address.state", "pk": 65, "fields": {"country": 14, "name": "Northern Territory", "iso": "NT"}}, {"model": "address.state", "pk": 66, "fields": {"country": 14, "name": "Western Australia", "iso": "WA"}}, {"model": "address.state", "pk": 67, "fields": {"country": 14, "name": "Australian Capital Territory", "iso": "ACT"}}, {"model": "address.state", "pk": 68, "fields": {"country": 14, "name": "Victoria", "iso": "VIC"}}, {"model": "address.state", "pk": 69, "fields": {"country": 14, "name": "New South Wales", "iso": "NSW"}}, {"model": "address.state", "pk": 70, "fields": {"country": 14, "name": "Queensland", "iso": "QLD"}}, {"model": "address.state", "pk": 71, "fields": {"country": 14, "name": "Tasmania", "iso": "TAS"}}, {"model": "address.state", "pk": 72, "fields": {"country": 14, "name": "South Australia", "iso": "SA"}}, {"model": "address.state", "pk": 73, "fields": {"country": 11, "name": "Buenos Aires", "iso": "B"}}, {"model": "address.state", "pk": 74, "fields": {"country": 11, "name": "Catamarca", "iso": "K"}}, {"model": "address.state", "pk": 75, "fields": {"country": 11, "name": "Chaco", "iso": "H"}}, {"model": "address.state", "pk": 76, "fields": {"country": 11, "name": "<PERSON><PERSON>", "iso": "U"}}, {"model": "address.state", "pk": 77, "fields": {"country": 11, "name": "Ciudad Autónoma de Buenos Aires", "iso": "C"}}, {"model": "address.state", "pk": 78, "fields": {"country": 11, "name": "Corrientes", "iso": "W"}}, {"model": "address.state", "pk": 79, "fields": {"country": 11, "name": "Córdoba", "iso": "X"}}, {"model": "address.state", "pk": 80, "fields": {"country": 11, "name": "Entre Ríos", "iso": "E"}}, {"model": "address.state", "pk": 81, "fields": {"country": 11, "name": "Formosa", "iso": "P"}}, {"model": "address.state", "pk": 82, "fields": {"country": 11, "name": "<PERSON><PERSON><PERSON>", "iso": "Y"}}, {"model": "address.state", "pk": 83, "fields": {"country": 11, "name": "La Pampa", "iso": "L"}}, {"model": "address.state", "pk": 84, "fields": {"country": 11, "name": "La Rioja", "iso": "F"}}, {"model": "address.state", "pk": 85, "fields": {"country": 11, "name": "Mendoza", "iso": "M"}}, {"model": "address.state", "pk": 86, "fields": {"country": 11, "name": "Misiones", "iso": "N"}}, {"model": "address.state", "pk": 87, "fields": {"country": 11, "name": "Neuquén", "iso": "Q"}}, {"model": "address.state", "pk": 88, "fields": {"country": 11, "name": "Río Negro", "iso": "R"}}, {"model": "address.state", "pk": 89, "fields": {"country": 11, "name": "Salta", "iso": "A"}}, {"model": "address.state", "pk": 90, "fields": {"country": 11, "name": "San Juan", "iso": "J"}}, {"model": "address.state", "pk": 91, "fields": {"country": 11, "name": "San Luis", "iso": "D"}}, {"model": "address.state", "pk": 92, "fields": {"country": 11, "name": "Santa Cruz", "iso": "Z"}}, {"model": "address.state", "pk": 93, "fields": {"country": 11, "name": "Santa Fe", "iso": "S"}}, {"model": "address.state", "pk": 94, "fields": {"country": 11, "name": "Santiago del Estero", "iso": "G"}}, {"model": "address.state", "pk": 95, "fields": {"country": 11, "name": "Tierra del Fuego", "iso": "V"}}, {"model": "address.state", "pk": 96, "fields": {"country": 11, "name": "Tucumán", "iso": "T"}}, {"model": "address.state", "pk": 97, "fields": {"country": 29, "name": "Acre", "iso": "AC"}}, {"model": "address.state", "pk": 98, "fields": {"country": 29, "name": "Alagoas", "iso": "AL"}}, {"model": "address.state", "pk": 99, "fields": {"country": 29, "name": "Amapá", "iso": "AP"}}, {"model": "address.state", "pk": 100, "fields": {"country": 29, "name": "Amazonas", "iso": "AM"}}, {"model": "address.state", "pk": 101, "fields": {"country": 29, "name": "Bahia", "iso": "BA"}}, {"model": "address.state", "pk": 102, "fields": {"country": 29, "name": "Ceará", "iso": "CE"}}, {"model": "address.state", "pk": 103, "fields": {"country": 29, "name": "Distrito Federal", "iso": "DF"}}, {"model": "address.state", "pk": 104, "fields": {"country": 29, "name": "Espírito Santo", "iso": "ES"}}, {"model": "address.state", "pk": 105, "fields": {"country": 29, "name": "Goiás", "iso": "GO"}}, {"model": "address.state", "pk": 106, "fields": {"country": 29, "name": "Maranhão", "iso": "MA"}}, {"model": "address.state", "pk": 107, "fields": {"country": 29, "name": "<PERSON><PERSON>", "iso": "MT"}}, {"model": "address.state", "pk": 108, "fields": {"country": 29, "name": "Mato Grosso do Sul", "iso": "MS"}}, {"model": "address.state", "pk": 109, "fields": {"country": 29, "name": "Minas Gerais", "iso": "MG"}}, {"model": "address.state", "pk": 110, "fields": {"country": 29, "name": "Paraná", "iso": "PR"}}, {"model": "address.state", "pk": 111, "fields": {"country": 29, "name": "Paraíba", "iso": "PB"}}, {"model": "address.state", "pk": 112, "fields": {"country": 29, "name": "Pará", "iso": "PA"}}, {"model": "address.state", "pk": 113, "fields": {"country": 29, "name": "Pernambuco", "iso": "PE"}}, {"model": "address.state", "pk": 114, "fields": {"country": 29, "name": "Piauí", "iso": "PI"}}, {"model": "address.state", "pk": 115, "fields": {"country": 29, "name": "Rio Grande do Norte", "iso": "RN"}}, {"model": "address.state", "pk": 116, "fields": {"country": 29, "name": "Rio Grande do Sul", "iso": "RS"}}, {"model": "address.state", "pk": 117, "fields": {"country": 29, "name": "Rio de Janeiro", "iso": "RJ"}}, {"model": "address.state", "pk": 118, "fields": {"country": 29, "name": "Rondônia", "iso": "RO"}}, {"model": "address.state", "pk": 119, "fields": {"country": 29, "name": "Roraima", "iso": "RR"}}, {"model": "address.state", "pk": 120, "fields": {"country": 29, "name": "Santa Catarina", "iso": "SC"}}, {"model": "address.state", "pk": 121, "fields": {"country": 29, "name": "<PERSON><PERSON><PERSON>", "iso": "SE"}}, {"model": "address.state", "pk": 122, "fields": {"country": 29, "name": "São Paulo", "iso": "SP"}}, {"model": "address.state", "pk": 123, "fields": {"country": 29, "name": "Tocantins", "iso": "TO"}}, {"model": "address.state", "pk": 124, "fields": {"country": 45, "name": "<PERSON><PERSON>", "iso": "AH"}}, {"model": "address.state", "pk": 125, "fields": {"country": 45, "name": "Macao SAR", "iso": "MO"}}, {"model": "address.state", "pk": 126, "fields": {"country": 45, "name": "Beijing Shi", "iso": "BJ"}}, {"model": "address.state", "pk": 127, "fields": {"country": 45, "name": "Chongqing Shi", "iso": "CQ"}}, {"model": "address.state", "pk": 128, "fields": {"country": 45, "name": "<PERSON><PERSON>", "iso": "FJ"}}, {"model": "address.state", "pk": 129, "fields": {"country": 45, "name": "<PERSON><PERSON><PERSON>", "iso": "GS"}}, {"model": "address.state", "pk": 130, "fields": {"country": 45, "name": "Guangdong Sheng", "iso": "GD"}}, {"model": "address.state", "pk": 131, "fields": {"country": 45, "name": "Guangxi Zhuangzu Zizhiqu", "iso": "GX"}}, {"model": "address.state", "pk": 132, "fields": {"country": 45, "name": "<PERSON><PERSON><PERSON>", "iso": "GZ"}}, {"model": "address.state", "pk": 133, "fields": {"country": 45, "name": "<PERSON><PERSON>", "iso": "HI"}}, {"model": "address.state", "pk": 134, "fields": {"country": 45, "name": "<PERSON><PERSON>", "iso": "HE"}}, {"model": "address.state", "pk": 135, "fields": {"country": 45, "name": "Heilongjiang Sheng", "iso": "HL"}}, {"model": "address.state", "pk": 136, "fields": {"country": 45, "name": "<PERSON><PERSON>", "iso": "HA"}}, {"model": "address.state", "pk": 137, "fields": {"country": 45, "name": "Hong Kong SAR", "iso": "HK"}}, {"model": "address.state", "pk": 138, "fields": {"country": 45, "name": "<PERSON><PERSON>", "iso": "HB"}}, {"model": "address.state", "pk": 139, "fields": {"country": 45, "name": "<PERSON><PERSON>", "iso": "HN"}}, {"model": "address.state", "pk": 140, "fields": {"country": 45, "name": "<PERSON><PERSON>", "iso": "JS"}}, {"model": "address.state", "pk": 141, "fields": {"country": 45, "name": "Jiangxi Sheng", "iso": "JX"}}, {"model": "address.state", "pk": 142, "fields": {"country": 45, "name": "<PERSON><PERSON>", "iso": "JL"}}, {"model": "address.state", "pk": 143, "fields": {"country": 45, "name": "Liaoning Sheng", "iso": "LN"}}, {"model": "address.state", "pk": 144, "fields": {"country": 45, "name": "Nei Mongol Zizhiqu", "iso": "NM"}}, {"model": "address.state", "pk": 145, "fields": {"country": 45, "name": "<PERSON><PERSON><PERSON>", "iso": "NX"}}, {"model": "address.state", "pk": 146, "fields": {"country": 45, "name": "Qing<PERSON>", "iso": "QH"}}, {"model": "address.state", "pk": 147, "fields": {"country": 45, "name": "Shaanxi Sheng", "iso": "SN"}}, {"model": "address.state", "pk": 148, "fields": {"country": 45, "name": "Shandong Sheng", "iso": "SD"}}, {"model": "address.state", "pk": 149, "fields": {"country": 45, "name": "Shanghai Shi", "iso": "SH"}}, {"model": "address.state", "pk": 150, "fields": {"country": 45, "name": "Shan<PERSON>", "iso": "SX"}}, {"model": "address.state", "pk": 151, "fields": {"country": 45, "name": "Sichuan Sheng", "iso": "SC"}}, {"model": "address.state", "pk": 152, "fields": {"country": 45, "name": "Taiwan Sheng", "iso": "TW"}}, {"model": "address.state", "pk": 153, "fields": {"country": 45, "name": "Tianjin Shi", "iso": "TJ"}}, {"model": "address.state", "pk": 154, "fields": {"country": 45, "name": "Xinjiang Uygur <PERSON>u", "iso": "XJ"}}, {"model": "address.state", "pk": 155, "fields": {"country": 45, "name": "<PERSON><PERSON><PERSON>", "iso": "XZ"}}, {"model": "address.state", "pk": 156, "fields": {"country": 45, "name": "Yunnan Sheng", "iso": "YN"}}, {"model": "address.state", "pk": 157, "fields": {"country": 45, "name": "Zhejiang Sheng", "iso": "ZJ"}}, {"model": "address.state", "pk": 158, "fields": {"country": 96, "name": "Aceh", "iso": "AC"}}, {"model": "address.state", "pk": 159, "fields": {"country": 96, "name": "Bali", "iso": "BA"}}, {"model": "address.state", "pk": 160, "fields": {"country": 96, "name": "<PERSON><PERSON>", "iso": "BT"}}, {"model": "address.state", "pk": 161, "fields": {"country": 96, "name": "<PERSON><PERSON><PERSON><PERSON>", "iso": "BE"}}, {"model": "address.state", "pk": 162, "fields": {"country": 96, "name": "Gorontalo", "iso": "GO"}}, {"model": "address.state", "pk": 163, "fields": {"country": 96, "name": "Jakarta Raya", "iso": "JK"}}, {"model": "address.state", "pk": 164, "fields": {"country": 96, "name": "Jambi", "iso": "JA"}}, {"model": "address.state", "pk": 165, "fields": {"country": 96, "name": "<PERSON><PERSON>", "iso": "JW"}}, {"model": "address.state", "pk": 166, "fields": {"country": 96, "name": "<PERSON>awa Barat", "iso": "JB"}}, {"model": "address.state", "pk": 167, "fields": {"country": 96, "name": "Jawa Tengah", "iso": "JT"}}, {"model": "address.state", "pk": 168, "fields": {"country": 96, "name": "<PERSON><PERSON>", "iso": "JI"}}, {"model": "address.state", "pk": 169, "fields": {"country": 96, "name": "Kalimantan", "iso": "KA"}}, {"model": "address.state", "pk": 170, "fields": {"country": 96, "name": "Kalimantan Barat", "iso": "KB"}}, {"model": "address.state", "pk": 171, "fields": {"country": 96, "name": "<PERSON><PERSON><PERSON>", "iso": "KS"}}, {"model": "address.state", "pk": 172, "fields": {"country": 96, "name": "Kalimantan Tengah", "iso": "KT"}}, {"model": "address.state", "pk": 173, "fields": {"country": 96, "name": "<PERSON><PERSON><PERSON>", "iso": "KI"}}, {"model": "address.state", "pk": 174, "fields": {"country": 96, "name": "Kalimantan Utara", "iso": "KU"}}, {"model": "address.state", "pk": 175, "fields": {"country": 96, "name": "<PERSON><PERSON><PERSON><PERSON>ka Belitung", "iso": "BB"}}, {"model": "address.state", "pk": 176, "fields": {"country": 96, "name": "<PERSON><PERSON><PERSON><PERSON>", "iso": "KR"}}, {"model": "address.state", "pk": 177, "fields": {"country": 96, "name": "Lampung", "iso": "LA"}}, {"model": "address.state", "pk": 178, "fields": {"country": 96, "name": "Maluku", "iso": "ML"}}, {"model": "address.state", "pk": 179, "fields": {"country": 96, "name": "Maluku", "iso": "MA"}}, {"model": "address.state", "pk": 180, "fields": {"country": 96, "name": "Maluku Utara", "iso": "MU"}}, {"model": "address.state", "pk": 181, "fields": {"country": 96, "name": "Nusa Tenggara", "iso": "NU"}}, {"model": "address.state", "pk": 182, "fields": {"country": 96, "name": "Nusa Tenggara Barat", "iso": "NB"}}, {"model": "address.state", "pk": 183, "fields": {"country": 96, "name": "Nusa Tenggara Timur", "iso": "NT"}}, {"model": "address.state", "pk": 184, "fields": {"country": 96, "name": "Papua", "iso": "PP"}}, {"model": "address.state", "pk": 185, "fields": {"country": 96, "name": "Papua", "iso": "PA"}}, {"model": "address.state", "pk": 186, "fields": {"country": 96, "name": "Papua Barat", "iso": "PB"}}, {"model": "address.state", "pk": 187, "fields": {"country": 96, "name": "Papua Pengunungan", "iso": "PE"}}, {"model": "address.state", "pk": 188, "fields": {"country": 96, "name": "Papua Selatan", "iso": "PS"}}, {"model": "address.state", "pk": 189, "fields": {"country": 96, "name": "Papua Tengah", "iso": "PT"}}, {"model": "address.state", "pk": 190, "fields": {"country": 96, "name": "<PERSON><PERSON><PERSON>", "iso": "RI"}}, {"model": "address.state", "pk": 191, "fields": {"country": 96, "name": "Sulawesi", "iso": "SL"}}, {"model": "address.state", "pk": 192, "fields": {"country": 96, "name": "Sulawesi Barat", "iso": "SR"}}, {"model": "address.state", "pk": 193, "fields": {"country": 96, "name": "Sulaw<PERSON>", "iso": "SN"}}, {"model": "address.state", "pk": 194, "fields": {"country": 96, "name": "Sulawesi Ten<PERSON>", "iso": "ST"}}, {"model": "address.state", "pk": 195, "fields": {"country": 96, "name": "Sulawesi Tenggara", "iso": "SG"}}, {"model": "address.state", "pk": 196, "fields": {"country": 96, "name": "Sulawesi Utara", "iso": "SA"}}, {"model": "address.state", "pk": 197, "fields": {"country": 96, "name": "<PERSON><PERSON><PERSON>", "iso": "SM"}}, {"model": "address.state", "pk": 198, "fields": {"country": 96, "name": "Sumatera Barat", "iso": "SB"}}, {"model": "address.state", "pk": 199, "fields": {"country": 96, "name": "<PERSON><PERSON><PERSON>", "iso": "SS"}}, {"model": "address.state", "pk": 200, "fields": {"country": 96, "name": "<PERSON><PERSON><PERSON>", "iso": "SU"}}, {"model": "address.state", "pk": 201, "fields": {"country": 96, "name": "Yogyakarta", "iso": "YO"}}, {"model": "address.state", "pk": 202, "fields": {"country": 99, "name": "Andaman and Nicobar Islands", "iso": "AN"}}, {"model": "address.state", "pk": 203, "fields": {"country": 99, "name": "Andhra Pradesh", "iso": "AP"}}, {"model": "address.state", "pk": 204, "fields": {"country": 99, "name": "Arunāchal Pradesh", "iso": "AR"}}, {"model": "address.state", "pk": 205, "fields": {"country": 99, "name": "Assam", "iso": "AS"}}, {"model": "address.state", "pk": 206, "fields": {"country": 99, "name": "<PERSON><PERSON><PERSON><PERSON>", "iso": "BR"}}, {"model": "address.state", "pk": 207, "fields": {"country": 99, "name": "Chandīgarh", "iso": "CH"}}, {"model": "address.state", "pk": 208, "fields": {"country": 99, "name": "Chhattīsgarh", "iso": "CT"}}, {"model": "address.state", "pk": 209, "fields": {"country": 99, "name": "Delhi", "iso": "DL"}}, {"model": "address.state", "pk": 210, "fields": {"country": 99, "name": "Dādra and Nagar Haveli and Damān and Diu", "iso": "DH"}}, {"model": "address.state", "pk": 211, "fields": {"country": 99, "name": "Goa", "iso": "GA"}}, {"model": "address.state", "pk": 212, "fields": {"country": 99, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "iso": "GJ"}}, {"model": "address.state", "pk": 213, "fields": {"country": 99, "name": "<PERSON><PERSON><PERSON><PERSON>", "iso": "HR"}}, {"model": "address.state", "pk": 214, "fields": {"country": 99, "name": "Himāchal Pradesh", "iso": "HP"}}, {"model": "address.state", "pk": 215, "fields": {"country": 99, "name": "Jammu and Kashmīr", "iso": "JK"}}, {"model": "address.state", "pk": 216, "fields": {"country": 99, "name": "Jhārkhand", "iso": "JH"}}, {"model": "address.state", "pk": 217, "fields": {"country": 99, "name": "Karnātaka", "iso": "KA"}}, {"model": "address.state", "pk": 218, "fields": {"country": 99, "name": "Kerala", "iso": "KL"}}, {"model": "address.state", "pk": 219, "fields": {"country": 99, "name": "<PERSON><PERSON><PERSON><PERSON>", "iso": "LA"}}, {"model": "address.state", "pk": 220, "fields": {"country": 99, "name": "Lakshadweep", "iso": "LD"}}, {"model": "address.state", "pk": 221, "fields": {"country": 99, "name": "Madhya Pradesh", "iso": "MP"}}, {"model": "address.state", "pk": 222, "fields": {"country": 99, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "iso": "MH"}}, {"model": "address.state", "pk": 223, "fields": {"country": 99, "name": "Manipur", "iso": "MN"}}, {"model": "address.state", "pk": 224, "fields": {"country": 99, "name": "Meg<PERSON>ā<PERSON><PERSON>", "iso": "ML"}}, {"model": "address.state", "pk": 225, "fields": {"country": 99, "name": "Mizoram", "iso": "MZ"}}, {"model": "address.state", "pk": 226, "fields": {"country": 99, "name": "Nāgāland", "iso": "NL"}}, {"model": "address.state", "pk": 227, "fields": {"country": 99, "name": "Odisha", "iso": "OR"}}, {"model": "address.state", "pk": 228, "fields": {"country": 99, "name": "Puducherry", "iso": "PY"}}, {"model": "address.state", "pk": 229, "fields": {"country": 99, "name": "Punjab", "iso": "PB"}}, {"model": "address.state", "pk": 230, "fields": {"country": 99, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "iso": "RJ"}}, {"model": "address.state", "pk": 231, "fields": {"country": 99, "name": "Sikkim", "iso": "SK"}}, {"model": "address.state", "pk": 232, "fields": {"country": 99, "name": "Tamil Nādu", "iso": "TN"}}, {"model": "address.state", "pk": 233, "fields": {"country": 99, "name": "Telangāna", "iso": "TG"}}, {"model": "address.state", "pk": 234, "fields": {"country": 99, "name": "<PERSON>ura", "iso": "TR"}}, {"model": "address.state", "pk": 235, "fields": {"country": 99, "name": "Uttar Pradesh", "iso": "UP"}}, {"model": "address.state", "pk": 236, "fields": {"country": 99, "name": "Uttarākhand", "iso": "UT"}}, {"model": "address.state", "pk": 237, "fields": {"country": 99, "name": "West Bengal", "iso": "WB"}}, {"model": "address.state", "pk": 238, "fields": {"country": 105, "name": "Alessandria", "iso": "AL"}}, {"model": "address.state", "pk": 239, "fields": {"country": 105, "name": "Ancona", "iso": "AN"}}, {"model": "address.state", "pk": 240, "fields": {"country": 105, "name": "Arezzo", "iso": "AR"}}, {"model": "address.state", "pk": 241, "fields": {"country": 105, "name": "<PERSON><PERSON><PERSON>", "iso": "AP"}}, {"model": "address.state", "pk": 242, "fields": {"country": 105, "name": "<PERSON><PERSON>", "iso": "AT"}}, {"model": "address.state", "pk": 243, "fields": {"country": 105, "name": "<PERSON><PERSON><PERSON>", "iso": "AV"}}, {"model": "address.state", "pk": 244, "fields": {"country": 105, "name": "Barletta-Andria-Trani", "iso": "BT"}}, {"model": "address.state", "pk": 245, "fields": {"country": 105, "name": "<PERSON><PERSON>", "iso": "BL"}}, {"model": "address.state", "pk": 246, "fields": {"country": 105, "name": "Benevento", "iso": "BN"}}, {"model": "address.state", "pk": 247, "fields": {"country": 105, "name": "Bergamo", "iso": "BG"}}, {"model": "address.state", "pk": 248, "fields": {"country": 105, "name": "Biella", "iso": "BI"}}, {"model": "address.state", "pk": 249, "fields": {"country": 105, "name": "Brescia", "iso": "BS"}}, {"model": "address.state", "pk": 250, "fields": {"country": 105, "name": "Brindisi", "iso": "BR"}}, {"model": "address.state", "pk": 251, "fields": {"country": 105, "name": "Campobasso", "iso": "CB"}}, {"model": "address.state", "pk": 252, "fields": {"country": 105, "name": "Caserta", "iso": "CE"}}, {"model": "address.state", "pk": 253, "fields": {"country": 105, "name": "Cat<PERSON>ro", "iso": "CZ"}}, {"model": "address.state", "pk": 254, "fields": {"country": 105, "name": "Chieti", "iso": "CH"}}, {"model": "address.state", "pk": 255, "fields": {"country": 105, "name": "Como", "iso": "CO"}}, {"model": "address.state", "pk": 256, "fields": {"country": 105, "name": "Cosenza", "iso": "CS"}}, {"model": "address.state", "pk": 257, "fields": {"country": 105, "name": "Cremona", "iso": "CR"}}, {"model": "address.state", "pk": 258, "fields": {"country": 105, "name": "Crotone", "iso": "KR"}}, {"model": "address.state", "pk": 259, "fields": {"country": 105, "name": "Cuneo", "iso": "CN"}}, {"model": "address.state", "pk": 260, "fields": {"country": 105, "name": "Fermo", "iso": "FM"}}, {"model": "address.state", "pk": 261, "fields": {"country": 105, "name": "<PERSON><PERSON><PERSON>", "iso": "FE"}}, {"model": "address.state", "pk": 262, "fields": {"country": 105, "name": "Foggia", "iso": "FG"}}, {"model": "address.state", "pk": 263, "fields": {"country": 105, "name": "Forlì-Cesena", "iso": "FC"}}, {"model": "address.state", "pk": 264, "fields": {"country": 105, "name": "Frosinone", "iso": "FR"}}, {"model": "address.state", "pk": 265, "fields": {"country": 105, "name": "Grosseto", "iso": "GR"}}, {"model": "address.state", "pk": 266, "fields": {"country": 105, "name": "Imperia", "iso": "IM"}}, {"model": "address.state", "pk": 267, "fields": {"country": 105, "name": "Isernia", "iso": "IS"}}, {"model": "address.state", "pk": 268, "fields": {"country": 105, "name": "L'Aquila", "iso": "AQ"}}, {"model": "address.state", "pk": 269, "fields": {"country": 105, "name": "La Spezia", "iso": "SP"}}, {"model": "address.state", "pk": 270, "fields": {"country": 105, "name": "Latina", "iso": "LT"}}, {"model": "address.state", "pk": 271, "fields": {"country": 105, "name": "Lecce", "iso": "LE"}}, {"model": "address.state", "pk": 272, "fields": {"country": 105, "name": "<PERSON><PERSON>", "iso": "LC"}}, {"model": "address.state", "pk": 273, "fields": {"country": 105, "name": "Livorno", "iso": "LI"}}, {"model": "address.state", "pk": 274, "fields": {"country": 105, "name": "<PERSON><PERSON>", "iso": "LO"}}, {"model": "address.state", "pk": 275, "fields": {"country": 105, "name": "Lucca", "iso": "LU"}}, {"model": "address.state", "pk": 276, "fields": {"country": 105, "name": "Macerata", "iso": "MC"}}, {"model": "address.state", "pk": 277, "fields": {"country": 105, "name": "<PERSON><PERSON><PERSON>", "iso": "MN"}}, {"model": "address.state", "pk": 278, "fields": {"country": 105, "name": "Massa-Carrara", "iso": "MS"}}, {"model": "address.state", "pk": 279, "fields": {"country": 105, "name": "<PERSON><PERSON>", "iso": "MT"}}, {"model": "address.state", "pk": 280, "fields": {"country": 105, "name": "Modena", "iso": "MO"}}, {"model": "address.state", "pk": 281, "fields": {"country": 105, "name": "Monza e Brianza", "iso": "MB"}}, {"model": "address.state", "pk": 282, "fields": {"country": 105, "name": "Novara", "iso": "NO"}}, {"model": "address.state", "pk": 283, "fields": {"country": 105, "name": "<PERSON><PERSON><PERSON>", "iso": "NU"}}, {"model": "address.state", "pk": 284, "fields": {"country": 105, "name": "Oristano", "iso": "OR"}}, {"model": "address.state", "pk": 285, "fields": {"country": 105, "name": "<PERSON><PERSON><PERSON>", "iso": "PD"}}, {"model": "address.state", "pk": 286, "fields": {"country": 105, "name": "Parma", "iso": "PR"}}, {"model": "address.state", "pk": 287, "fields": {"country": 105, "name": "Pavia", "iso": "PV"}}, {"model": "address.state", "pk": 288, "fields": {"country": 105, "name": "Perugia", "iso": "PG"}}, {"model": "address.state", "pk": 289, "fields": {"country": 105, "name": "Pesaro e Urbino", "iso": "PU"}}, {"model": "address.state", "pk": 290, "fields": {"country": 105, "name": "Pescara", "iso": "PE"}}, {"model": "address.state", "pk": 291, "fields": {"country": 105, "name": "Piacenza", "iso": "PC"}}, {"model": "address.state", "pk": 292, "fields": {"country": 105, "name": "Pisa", "iso": "PI"}}, {"model": "address.state", "pk": 293, "fields": {"country": 105, "name": "Pistoia", "iso": "PT"}}, {"model": "address.state", "pk": 294, "fields": {"country": 105, "name": "Potenza", "iso": "PZ"}}, {"model": "address.state", "pk": 295, "fields": {"country": 105, "name": "Prato", "iso": "PO"}}, {"model": "address.state", "pk": 296, "fields": {"country": 105, "name": "<PERSON><PERSON>", "iso": "RA"}}, {"model": "address.state", "pk": 297, "fields": {"country": 105, "name": "Reggio Emilia", "iso": "RE"}}, {"model": "address.state", "pk": 298, "fields": {"country": 105, "name": "Riet<PERSON>", "iso": "RI"}}, {"model": "address.state", "pk": 299, "fields": {"country": 105, "name": "<PERSON><PERSON><PERSON>", "iso": "RN"}}, {"model": "address.state", "pk": 300, "fields": {"country": 105, "name": "Rovigo", "iso": "RO"}}, {"model": "address.state", "pk": 301, "fields": {"country": 105, "name": "Salerno", "iso": "SA"}}, {"model": "address.state", "pk": 302, "fields": {"country": 105, "name": "Sassari", "iso": "SS"}}, {"model": "address.state", "pk": 303, "fields": {"country": 105, "name": "<PERSON><PERSON><PERSON>", "iso": "SV"}}, {"model": "address.state", "pk": 304, "fields": {"country": 105, "name": "Siena", "iso": "SI"}}, {"model": "address.state", "pk": 305, "fields": {"country": 105, "name": "Sondr<PERSON>", "iso": "SO"}}, {"model": "address.state", "pk": 306, "fields": {"country": 105, "name": "Sud Sardegna", "iso": "SU"}}, {"model": "address.state", "pk": 307, "fields": {"country": 105, "name": "<PERSON><PERSON>", "iso": "TA"}}, {"model": "address.state", "pk": 308, "fields": {"country": 105, "name": "Teramo", "iso": "TE"}}, {"model": "address.state", "pk": 309, "fields": {"country": 105, "name": "<PERSON><PERSON><PERSON>", "iso": "TR"}}, {"model": "address.state", "pk": 310, "fields": {"country": 105, "name": "Treviso", "iso": "TV"}}, {"model": "address.state", "pk": 311, "fields": {"country": 105, "name": "<PERSON><PERSON><PERSON>", "iso": "VA"}}, {"model": "address.state", "pk": 312, "fields": {"country": 105, "name": "Verbano-Cusio-Ossola", "iso": "VB"}}, {"model": "address.state", "pk": 313, "fields": {"country": 105, "name": "<PERSON><PERSON><PERSON><PERSON>", "iso": "VC"}}, {"model": "address.state", "pk": 314, "fields": {"country": 105, "name": "Verona", "iso": "VR"}}, {"model": "address.state", "pk": 315, "fields": {"country": 105, "name": "<PERSON><PERSON><PERSON>", "iso": "VV"}}, {"model": "address.state", "pk": 316, "fields": {"country": 105, "name": "Vicenza", "iso": "VI"}}, {"model": "address.state", "pk": 317, "fields": {"country": 105, "name": "Viterbo", "iso": "VT"}}, {"model": "address.state", "pk": 318, "fields": {"country": 108, "name": "<PERSON><PERSON>", "iso": "23"}}, {"model": "address.state", "pk": 319, "fields": {"country": 108, "name": "Chiba", "iso": "12"}}, {"model": "address.state", "pk": 320, "fields": {"country": 108, "name": "<PERSON><PERSON><PERSON>", "iso": "18"}}, {"model": "address.state", "pk": 321, "fields": {"country": 108, "name": "<PERSON><PERSON><PERSON>", "iso": "40"}}, {"model": "address.state", "pk": 322, "fields": {"country": 108, "name": "Fukushima", "iso": "07"}}, {"model": "address.state", "pk": 323, "fields": {"country": 108, "name": "Hiroshima", "iso": "34"}}, {"model": "address.state", "pk": 324, "fields": {"country": 108, "name": "Hokkaido", "iso": "01"}}, {"model": "address.state", "pk": 325, "fields": {"country": 108, "name": "Hyogo", "iso": "28"}}, {"model": "address.state", "pk": 326, "fields": {"country": 108, "name": "Gifu", "iso": "21"}}, {"model": "address.state", "pk": 327, "fields": {"country": 108, "name": "<PERSON><PERSON><PERSON>", "iso": "17"}}, {"model": "address.state", "pk": 328, "fields": {"country": 108, "name": "Kagoshima", "iso": "46"}}, {"model": "address.state", "pk": 329, "fields": {"country": 108, "name": "<PERSON><PERSON>", "iso": "39"}}, {"model": "address.state", "pk": 330, "fields": {"country": 108, "name": "Kyoto", "iso": "26"}}, {"model": "address.state", "pk": 331, "fields": {"country": 108, "name": "<PERSON><PERSON>", "iso": "44"}}, {"model": "address.state", "pk": 332, "fields": {"country": 108, "name": "Osaka", "iso": "27"}}, {"model": "address.state", "pk": 333, "fields": {"country": 108, "name": "Shiga", "iso": "25"}}, {"model": "address.state", "pk": 334, "fields": {"country": 108, "name": "<PERSON><PERSON><PERSON>", "iso": "32"}}, {"model": "address.state", "pk": 335, "fields": {"country": 108, "name": "Tokushima", "iso": "36"}}, {"model": "address.state", "pk": 336, "fields": {"country": 108, "name": "<PERSON><PERSON><PERSON>", "iso": "35"}}, {"model": "address.state", "pk": 337, "fields": {"country": 108, "name": "Tochigi", "iso": "09"}}, {"model": "address.state", "pk": 338, "fields": {"country": 108, "name": "Shizuoka", "iso": "22"}}, {"model": "address.state", "pk": 339, "fields": {"country": 108, "name": "<PERSON><PERSON><PERSON>", "iso": "19"}}, {"model": "address.state", "pk": 340, "fields": {"country": 108, "name": "Tokyo", "iso": "13"}}, {"model": "address.state", "pk": 341, "fields": {"country": 108, "name": "<PERSON><PERSON><PERSON>", "iso": "05"}}, {"model": "address.state", "pk": 342, "fields": {"country": 108, "name": "<PERSON><PERSON><PERSON>", "iso": "02"}}, {"model": "address.state", "pk": 343, "fields": {"country": 108, "name": "<PERSON><PERSON><PERSON>", "iso": "38"}}, {"model": "address.state", "pk": 344, "fields": {"country": 108, "name": "<PERSON><PERSON>", "iso": "10"}}, {"model": "address.state", "pk": 345, "fields": {"country": 108, "name": "<PERSON><PERSON><PERSON>", "iso": "08"}}, {"model": "address.state", "pk": 346, "fields": {"country": 108, "name": "Iwate", "iso": "03"}}, {"model": "address.state", "pk": 347, "fields": {"country": 108, "name": "Kagawa", "iso": "37"}}, {"model": "address.state", "pk": 348, "fields": {"country": 108, "name": "Kanagawa", "iso": "14"}}, {"model": "address.state", "pk": 349, "fields": {"country": 108, "name": "<PERSON><PERSON>", "iso": "43"}}, {"model": "address.state", "pk": 350, "fields": {"country": 108, "name": "<PERSON><PERSON>", "iso": "24"}}, {"model": "address.state", "pk": 351, "fields": {"country": 108, "name": "<PERSON><PERSON><PERSON>", "iso": "04"}}, {"model": "address.state", "pk": 352, "fields": {"country": 108, "name": "<PERSON><PERSON><PERSON>", "iso": "45"}}, {"model": "address.state", "pk": 353, "fields": {"country": 108, "name": "Nagano", "iso": "20"}}, {"model": "address.state", "pk": 354, "fields": {"country": 108, "name": "Nagasaki", "iso": "42"}}, {"model": "address.state", "pk": 355, "fields": {"country": 108, "name": "<PERSON><PERSON>", "iso": "29"}}, {"model": "address.state", "pk": 356, "fields": {"country": 108, "name": "Niigata", "iso": "15"}}, {"model": "address.state", "pk": 357, "fields": {"country": 108, "name": "<PERSON><PERSON>", "iso": "33"}}, {"model": "address.state", "pk": 358, "fields": {"country": 108, "name": "Okinawa", "iso": "47"}}, {"model": "address.state", "pk": 359, "fields": {"country": 108, "name": "Saga", "iso": "41"}}, {"model": "address.state", "pk": 360, "fields": {"country": 108, "name": "<PERSON><PERSON><PERSON>", "iso": "11"}}, {"model": "address.state", "pk": 361, "fields": {"country": 108, "name": "Tottori", "iso": "31"}}, {"model": "address.state", "pk": 362, "fields": {"country": 108, "name": "Toyama", "iso": "16"}}, {"model": "address.state", "pk": 363, "fields": {"country": 108, "name": "<PERSON><PERSON><PERSON>", "iso": "30"}}, {"model": "address.state", "pk": 364, "fields": {"country": 108, "name": "Yamagata", "iso": "06"}}, {"model": "address.state", "pk": 365, "fields": {"country": 149, "name": "Aguascalientes", "iso": "AGU"}}, {"model": "address.state", "pk": 366, "fields": {"country": 149, "name": "Baja California", "iso": "BCN"}}, {"model": "address.state", "pk": 367, "fields": {"country": 149, "name": "Baja California Sur", "iso": "BCS"}}, {"model": "address.state", "pk": 368, "fields": {"country": 149, "name": "Campeche", "iso": "CAM"}}, {"model": "address.state", "pk": 369, "fields": {"country": 149, "name": "Chiapas", "iso": "CHP"}}, {"model": "address.state", "pk": 370, "fields": {"country": 149, "name": "Chihuahua", "iso": "CHH"}}, {"model": "address.state", "pk": 371, "fields": {"country": 149, "name": "Ciudad de México", "iso": "CMX"}}, {"model": "address.state", "pk": 372, "fields": {"country": 149, "name": "Coahuila de Zaragoza", "iso": "COA"}}, {"model": "address.state", "pk": 373, "fields": {"country": 149, "name": "Colima", "iso": "COL"}}, {"model": "address.state", "pk": 374, "fields": {"country": 149, "name": "Durango", "iso": "DUR"}}, {"model": "address.state", "pk": 375, "fields": {"country": 149, "name": "Guanajuato", "iso": "GUA"}}, {"model": "address.state", "pk": 376, "fields": {"country": 149, "name": "Guerrero", "iso": "GRO"}}, {"model": "address.state", "pk": 377, "fields": {"country": 149, "name": "Hidalgo", "iso": "HID"}}, {"model": "address.state", "pk": 378, "fields": {"country": 149, "name": "Jalisco", "iso": "JAL"}}, {"model": "address.state", "pk": 379, "fields": {"country": 149, "name": "Michoacán de Ocampo", "iso": "MIC"}}, {"model": "address.state", "pk": 380, "fields": {"country": 149, "name": "<PERSON><PERSON>", "iso": "MOR"}}, {"model": "address.state", "pk": 381, "fields": {"country": 149, "name": "México", "iso": "MEX"}}, {"model": "address.state", "pk": 382, "fields": {"country": 149, "name": "Nayarit", "iso": "NAY"}}, {"model": "address.state", "pk": 383, "fields": {"country": 149, "name": "Nuevo León", "iso": "NLE"}}, {"model": "address.state", "pk": 384, "fields": {"country": 149, "name": "Oaxaca", "iso": "OAX"}}, {"model": "address.state", "pk": 385, "fields": {"country": 149, "name": "Puebla", "iso": "PUE"}}, {"model": "address.state", "pk": 386, "fields": {"country": 149, "name": "<PERSON><PERSON><PERSON><PERSON>", "iso": "QUE"}}, {"model": "address.state", "pk": 387, "fields": {"country": 149, "name": "Quintana Roo", "iso": "ROO"}}, {"model": "address.state", "pk": 388, "fields": {"country": 149, "name": "San Luis Potosí", "iso": "SLP"}}, {"model": "address.state", "pk": 389, "fields": {"country": 149, "name": "Sinaloa", "iso": "SIN"}}, {"model": "address.state", "pk": 390, "fields": {"country": 149, "name": "Sonora", "iso": "SON"}}, {"model": "address.state", "pk": 391, "fields": {"country": 149, "name": "Tabasco", "iso": "TAB"}}, {"model": "address.state", "pk": 392, "fields": {"country": 149, "name": "Tamaulip<PERSON>", "iso": "TAM"}}, {"model": "address.state", "pk": 393, "fields": {"country": 149, "name": "Tlaxcala", "iso": "TLA"}}, {"model": "address.state", "pk": 394, "fields": {"country": 149, "name": "Veracruz de Ignacio de la Llave", "iso": "VER"}}, {"model": "address.state", "pk": 395, "fields": {"country": 149, "name": "Yucatán", "iso": "YUC"}}, {"model": "address.state", "pk": 396, "fields": {"country": 149, "name": "Zacatecas", "iso": "ZAC"}}, {"model": "address.state", "pk": 397, "fields": {"country": 207, "name": "<PERSON><PERSON>", "iso": "37"}}, {"model": "address.state", "pk": 398, "fields": {"country": 207, "name": "<PERSON>", "iso": "15"}}, {"model": "address.state", "pk": 399, "fields": {"country": 207, "name": "<PERSON>ueng Kan", "iso": "38"}}, {"model": "address.state", "pk": 400, "fields": {"country": 207, "name": "<PERSON><PERSON>", "iso": "31"}}, {"model": "address.state", "pk": 401, "fields": {"country": 207, "name": "Chachoengsao", "iso": "24"}}, {"model": "address.state", "pk": 402, "fields": {"country": 207, "name": "<PERSON><PERSON>", "iso": "18"}}, {"model": "address.state", "pk": 403, "fields": {"country": 207, "name": "Chaiyaphum", "iso": "36"}}, {"model": "address.state", "pk": 404, "fields": {"country": 207, "name": "<PERSON><PERSON><PERSON><PERSON>", "iso": "22"}}, {"model": "address.state", "pk": 405, "fields": {"country": 207, "name": "<PERSON>", "iso": "50"}}, {"model": "address.state", "pk": 406, "fields": {"country": 207, "name": "<PERSON>", "iso": "57"}}, {"model": "address.state", "pk": 407, "fields": {"country": 207, "name": "<PERSON><PERSON>", "iso": "20"}}, {"model": "address.state", "pk": 408, "fields": {"country": 207, "name": "Chumphon", "iso": "86"}}, {"model": "address.state", "pk": 409, "fields": {"country": 207, "name": "<PERSON><PERSON><PERSON>", "iso": "46"}}, {"model": "address.state", "pk": 410, "fields": {"country": 207, "name": "Kamphaeng Phet", "iso": "62"}}, {"model": "address.state", "pk": 411, "fields": {"country": 207, "name": "Kanchanaburi", "iso": "71"}}, {"model": "address.state", "pk": 412, "fields": {"country": 207, "name": "<PERSON><PERSON>", "iso": "40"}}, {"model": "address.state", "pk": 413, "fields": {"country": 207, "name": "<PERSON><PERSON><PERSON>", "iso": "81"}}, {"model": "address.state", "pk": 414, "fields": {"country": 207, "name": "<PERSON><PERSON>", "iso": "10"}}, {"model": "address.state", "pk": 415, "fields": {"country": 207, "name": "Lampang", "iso": "52"}}, {"model": "address.state", "pk": 416, "fields": {"country": 207, "name": "<PERSON><PERSON><PERSON>", "iso": "51"}}, {"model": "address.state", "pk": 417, "fields": {"country": 207, "name": "<PERSON><PERSON>", "iso": "42"}}, {"model": "address.state", "pk": 418, "fields": {"country": 207, "name": "<PERSON><PERSON> B<PERSON>", "iso": "16"}}, {"model": "address.state", "pk": 419, "fields": {"country": 207, "name": "<PERSON>", "iso": "58"}}, {"model": "address.state", "pk": 420, "fields": {"country": 207, "name": "<PERSON><PERSON>", "iso": "44"}}, {"model": "address.state", "pk": 421, "fields": {"country": 207, "name": "<PERSON><PERSON><PERSON><PERSON>", "iso": "49"}}, {"model": "address.state", "pk": 422, "fields": {"country": 207, "name": "Nakhon Nayok", "iso": "26"}}, {"model": "address.state", "pk": 423, "fields": {"country": 207, "name": "Nakhon Pathom", "iso": "73"}}, {"model": "address.state", "pk": 424, "fields": {"country": 207, "name": "Nakhon Phanom", "iso": "48"}}, {"model": "address.state", "pk": 425, "fields": {"country": 207, "name": "<PERSON><PERSON><PERSON>", "iso": "30"}}, {"model": "address.state", "pk": 426, "fields": {"country": 207, "name": "<PERSON><PERSON><PERSON>", "iso": "60"}}, {"model": "address.state", "pk": 427, "fields": {"country": 207, "name": "Nakhon Si Thammarat", "iso": "80"}}, {"model": "address.state", "pk": 428, "fields": {"country": 207, "name": "<PERSON>", "iso": "55"}}, {"model": "address.state", "pk": 429, "fields": {"country": 207, "name": "<PERSON><PERSON><PERSON><PERSON>", "iso": "96"}}, {"model": "address.state", "pk": 430, "fields": {"country": 207, "name": "<PERSON><PERSON>", "iso": "39"}}, {"model": "address.state", "pk": 431, "fields": {"country": 207, "name": "<PERSON><PERSON>", "iso": "43"}}, {"model": "address.state", "pk": 432, "fields": {"country": 207, "name": "Nonthaburi", "iso": "12"}}, {"model": "address.state", "pk": 433, "fields": {"country": 207, "name": "<PERSON><PERSON>", "iso": "13"}}, {"model": "address.state", "pk": 434, "fields": {"country": 207, "name": "<PERSON><PERSON>", "iso": "94"}}, {"model": "address.state", "pk": 435, "fields": {"country": 207, "name": "Phangnga", "iso": "82"}}, {"model": "address.state", "pk": 436, "fields": {"country": 207, "name": "Phatthalung", "iso": "93"}}, {"model": "address.state", "pk": 437, "fields": {"country": 207, "name": "<PERSON><PERSON><PERSON><PERSON>", "iso": "S"}}, {"model": "address.state", "pk": 438, "fields": {"country": 207, "name": "<PERSON><PERSON><PERSON>", "iso": "56"}}, {"model": "address.state", "pk": 439, "fields": {"country": 207, "name": "Phetchabun", "iso": "67"}}, {"model": "address.state", "pk": 440, "fields": {"country": 207, "name": "Phetchaburi", "iso": "76"}}, {"model": "address.state", "pk": 441, "fields": {"country": 207, "name": "<PERSON><PERSON><PERSON>", "iso": "66"}}, {"model": "address.state", "pk": 442, "fields": {"country": 207, "name": "Phitsanulok", "iso": "65"}}, {"model": "address.state", "pk": 443, "fields": {"country": 207, "name": "Phra Nakhon Si Ayutthaya", "iso": "14"}}, {"model": "address.state", "pk": 444, "fields": {"country": 207, "name": "Phrae", "iso": "54"}}, {"model": "address.state", "pk": 445, "fields": {"country": 207, "name": "Phuke<PERSON>", "iso": "83"}}, {"model": "address.state", "pk": 446, "fields": {"country": 207, "name": "<PERSON><PERSON><PERSON>", "iso": "25"}}, {"model": "address.state", "pk": 447, "fields": {"country": 207, "name": "<PERSON><PERSON><PERSON><PERSON>", "iso": "77"}}, {"model": "address.state", "pk": 448, "fields": {"country": 207, "name": "<PERSON><PERSON><PERSON>", "iso": "85"}}, {"model": "address.state", "pk": 449, "fields": {"country": 207, "name": "Ratchaburi", "iso": "70"}}, {"model": "address.state", "pk": 450, "fields": {"country": 207, "name": "<PERSON><PERSON>", "iso": "21"}}, {"model": "address.state", "pk": 451, "fields": {"country": 207, "name": "R<PERSON>", "iso": "45"}}, {"model": "address.state", "pk": 452, "fields": {"country": 207, "name": "<PERSON>", "iso": "27"}}, {"model": "address.state", "pk": 453, "fields": {"country": 207, "name": "Sakon <PERSON>", "iso": "47"}}, {"model": "address.state", "pk": 454, "fields": {"country": 207, "name": "<PERSON><PERSON>", "iso": "11"}}, {"model": "address.state", "pk": 455, "fields": {"country": 207, "name": "<PERSON><PERSON>", "iso": "74"}}, {"model": "address.state", "pk": 456, "fields": {"country": 207, "name": "<PERSON><PERSON>", "iso": "75"}}, {"model": "address.state", "pk": 457, "fields": {"country": 207, "name": "Saraburi", "iso": "19"}}, {"model": "address.state", "pk": 458, "fields": {"country": 207, "name": "Satun", "iso": "91"}}, {"model": "address.state", "pk": 459, "fields": {"country": 207, "name": "<PERSON>", "iso": "33"}}, {"model": "address.state", "pk": 460, "fields": {"country": 207, "name": "<PERSON>", "iso": "17"}}, {"model": "address.state", "pk": 461, "fields": {"country": 207, "name": "<PERSON><PERSON><PERSON>", "iso": "90"}}, {"model": "address.state", "pk": 462, "fields": {"country": 207, "name": "Sukhothai", "iso": "64"}}, {"model": "address.state", "pk": 463, "fields": {"country": 207, "name": "<PERSON><PERSON><PERSON>", "iso": "72"}}, {"model": "address.state", "pk": 464, "fields": {"country": 207, "name": "Surat Thani", "iso": "84"}}, {"model": "address.state", "pk": 465, "fields": {"country": 207, "name": "Surin", "iso": "32"}}, {"model": "address.state", "pk": 466, "fields": {"country": 207, "name": "Tak", "iso": "63"}}, {"model": "address.state", "pk": 467, "fields": {"country": 207, "name": "<PERSON><PERSON>", "iso": "92"}}, {"model": "address.state", "pk": 468, "fields": {"country": 207, "name": "Trat", "iso": "23"}}, {"model": "address.state", "pk": 469, "fields": {"country": 207, "name": "<PERSON><PERSON>", "iso": "34"}}, {"model": "address.state", "pk": 470, "fields": {"country": 207, "name": "<PERSON><PERSON>", "iso": "41"}}, {"model": "address.state", "pk": 471, "fields": {"country": 207, "name": "<PERSON><PERSON><PERSON>", "iso": "61"}}, {"model": "address.state", "pk": 472, "fields": {"country": 207, "name": "<PERSON><PERSON><PERSON>", "iso": "53"}}, {"model": "address.state", "pk": 473, "fields": {"country": 207, "name": "<PERSON><PERSON>", "iso": "95"}}, {"model": "address.state", "pk": 474, "fields": {"country": 207, "name": "<PERSON><PERSON><PERSON><PERSON>", "iso": "35"}}, {"model": "address.state", "pk": 475, "fields": {"country": 223, "name": "American Samoa", "iso": "AS"}}, {"model": "address.state", "pk": 476, "fields": {"country": 223, "name": "Guam", "iso": "GU"}}, {"model": "address.state", "pk": 477, "fields": {"country": 223, "name": "Northern Mariana Islands", "iso": "MP"}}, {"model": "address.state", "pk": 478, "fields": {"country": 223, "name": "Puerto Rico", "iso": "PR"}}, {"model": "address.state", "pk": 479, "fields": {"country": 223, "name": "United States Minor Outlying Islands", "iso": "UM"}}, {"model": "address.state", "pk": 480, "fields": {"country": 223, "name": "Virgin Islands", "iso": "VI"}}]