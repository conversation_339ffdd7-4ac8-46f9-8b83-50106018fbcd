from decimal import Decimal

from apps.ice_applications.app_conf_settings import DisplayStoreAdjustmentsInInvoice
from apps.returns.tests.utils import RefundBuilder
from apps.testing.factories import (
    ApplicationFactory,
    CustomerInvoiceRefundTransactionFactory,
    CustomerInvoiceSellTransactionFactory,
    MerchantFactory,
    MerchantOrderRefundTransactionFactory,
    MerchantOrderSellTransactionFactory,
    OrderItemPaymentTermFactory,
    StoreAdjustmentTransactionFactory,
    TermPaymentCustomerInvoiceLineFactory,
)
from apps.testing.factories.orders import ProductOfferOrderItemFactory
from django.test.utils import override_settings
from ims.tests import BaseTestCase
from mock.mock import patch
from reference.status import ORDER_ITEM_STATUS_CONFIRMED
from reference.status.order import ORDER_ITEM_STATUS_AUTH_SUCCESS
from reference.status.payment import PAYMENT_STATUS_AUTH_SUCCESS
from reference.transaction_codes import (
    TRANSACTION_INTERNAL_SALE,
    TRANSACTION_OPERATOR_SALE,
)


class InvoiceContextTestCase(BaseTestCase):
    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @override_settings(CELERY_TASK_EAGER_PROPAGATES_EXCEPTIONS=True)
    def test_merchant_order_shipping_and_products_volumes(self):
        # given
        ts1 = MerchantOrderSellTransactionFactory.create(
            merchant_order__price=Decimal("500"),
            merchant_order__vat_on_products=Decimal("100"),
            merchant_order__shipping=Decimal("50"),
            merchant_order__vat_on_shipping=Decimal("10"),
            amount=Decimal("550"),
            vat_amount=Decimal("110"),
        )

        tr1 = MerchantOrderRefundTransactionFactory.create(
            application=ts1.application,
            merchant=ts1.merchant,
            currency=ts1.currency,
            merchant_order__price=Decimal("500"),
            merchant_order__vat_on_products=Decimal("100"),
            merchant_order__shipping=Decimal("50"),
            merchant_order__vat_on_shipping=Decimal("10"),
            amount=-Decimal("550"),
            vat_amount=-Decimal("110"),
            refund__tax_rate=20,
            refund__amount_vat_excluded=Decimal("500"),
            refund__amount_vat_included=Decimal("600"),
            refund__shipping_vat_excluded=Decimal("50"),
            refund__shipping_vat_included=Decimal("60"),
        )

        transactions = [ts1, tr1]

        for t in transactions:
            t.actions.process()

        store_transaction = ts1.balancetransactions.get()
        balance = store_transaction.balances.get()

        # when
        balance.actions.close()

        # then
        invoice = balance.invoices.get()
        context = invoice.generate_context()

        self.assertEqual(
            context["sales_volume_vat_inc_without_shipping"], Decimal("600")
        )
        self.assertEqual(context["shipping_volume_vat_inc"], Decimal("60"))
        self.assertEqual(
            context["refund_volume_vat_inc_without_shipping"],
            tr1.merchant_order.price_vat_included,
        )
        self.assertEqual(
            context["refund_shipping_vat_inc"], tr1.merchant_order.shipping_vat_included
        )

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @override_settings(CELERY_TASK_EAGER_PROPAGATES_EXCEPTIONS=True)
    @patch(
        "apps.orders.actions.merchant_order_actions.manager."
        "MerchantOrderActionsManager._operator_must_collect_vat",
        lambda _: True,
    )
    def test_merchant_order_volumes_are_vat_excl_for_internal_sale(self):
        # given
        order_item = ProductOfferOrderItemFactory(
            status=ORDER_ITEM_STATUS_AUTH_SUCCESS,
            merchant_order__order___payment__status=PAYMENT_STATUS_AUTH_SUCCESS,
            merchant_order__order__auto_create_payment=True,
            merchant_order__shipping=Decimal("50"),
            merchant_order__vat_on_shipping=Decimal("2.75"),
            merchant_order__vat_rate_on_shipping=Decimal("5.50"),
        )
        merchant_order = order_item.merchant_order
        # set VAT as collected by operator thanks to mock
        merchant_order.actions.update_collected_vat_amounts()
        merchant_order.actions.confirm(refresh_model=False)
        merchant_order.actions.process()
        # merchant_order.actions.refund()
        refund = RefundBuilder().build(
            merchant_order.user,
            merchant_order,
            full=True,
            quantity=order_item.quantity,
            shipping=merchant_order.shipping_vat_included,
        )
        refund.actions.process()

        sale_trans = merchant_order.transactions.get(
            transaction_type=TRANSACTION_OPERATOR_SALE
        )
        # sale proces will also process refund trans
        sale_trans.actions.process()

        internal_sale = merchant_order.transactions.get(
            transaction_type=TRANSACTION_INTERNAL_SALE
        )
        store_transaction = internal_sale.balancetransactions.get()
        store_balance = store_transaction.balances.get()

        # when
        store_balance.actions.close()

        # then
        invoice = store_balance.invoices.get()
        context = invoice.generate_context()

        self.assertEqual(
            context["sales_volume_vat_inc_without_shipping"], merchant_order.price
        )
        self.assertEqual(context["shipping_volume_vat_inc"], merchant_order.shipping)
        self.assertEqual(
            context["refund_volume_vat_inc_without_shipping"],
            refund.amount_vat_excluded,
        )
        self.assertEqual(
            context["refund_shipping_vat_inc"], refund.shipping_vat_excluded
        )

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @override_settings(CELERY_TASK_EAGER_PROPAGATES_EXCEPTIONS=True)
    @patch("commissions.utils.get_app_commission_for_invoice")
    def test_customer_invoice_shipping_and_products_amounts_on_invoice(
        self, mocked_comm
    ):
        commission_amount = Decimal("42")
        mocked_comm.return_value = commission_amount, Decimal("10"), Decimal("5.5")
        # given
        oipt = OrderItemPaymentTermFactory(
            order_item__quantity=1,
            order_item__invoiceable_quantity=1,
            order_item__status=ORDER_ITEM_STATUS_CONFIRMED,
            order_item__price=Decimal("1000.00"),
            order_item__tax_rate=Decimal("10.50"),
            order_item__merchant_order__shipping=Decimal("50"),
            order_item__merchant_order__vat_on_shipping=Decimal("2.75"),
            order_item__merchant_order__vat_rate_on_shipping=Decimal("5.50"),
        )
        product_line = TermPaymentCustomerInvoiceLineFactory(
            application=oipt.order_item.application,
            merchant=oipt.order_item.merchant_order.merchant,
            user=oipt.order_item.merchant_order.user,
            order_item=oipt.order_item,
            unit_price=oipt.order_item.price,
            tax_rate=oipt.order_item.tax_rate,
            quantity=1,
            line_type="product",
        )
        customer_invoice = product_line.invoice
        merchant_order = product_line.order_item.merchant_order
        TermPaymentCustomerInvoiceLineFactory(
            application=customer_invoice.application,
            invoice=customer_invoice,
            order_item=None,
            merchant_order=merchant_order,
            unit_price=merchant_order.shipping,
            quantity=1,
            line_type="shipping",
        )

        sale_transaction = CustomerInvoiceSellTransactionFactory(
            amount=merchant_order.amount_vat_included,
            vat_amount=merchant_order.vat,
            application=merchant_order.application,
            merchant=merchant_order.merchant,
            currency=merchant_order.currency,
            customer_invoice=customer_invoice,
        )
        CustomerInvoiceRefundTransactionFactory(
            application=merchant_order.application,
            merchant=merchant_order.merchant,
            currency=merchant_order.currency,
            customer_invoice=customer_invoice,
            amount=-Decimal("120"),
            refund__shipping_vat_excluded=Decimal("25"),
            refund__vat_on_shipping=Decimal("1.38"),
        )

        customer_invoice.issuer.handle_invoice_upload = True
        customer_invoice.emit()
        customer_invoice._generate_id_number()
        customer_invoice.merchant_order = oipt.order_item.merchant_order
        customer_invoice.save()
        customer_invoice.actions.pay()
        sell_transaction = customer_invoice.transactions.filter_sale().last()
        sell_transaction.payment.actions.create_transactions_if_needed()
        comm_transaction = customer_invoice.transactions.filter_commission().get()

        # sale proces will also process refund trans
        sale_transaction.actions.process()
        comm_transaction.actions.process()

        store_transaction = sale_transaction.balancetransactions.get()
        balance = store_transaction.balances.get()

        # when
        balance.actions.close()

        # then
        invoice = balance.invoices.get()
        context = invoice.generate_context()

        self.assertEqual(
            context["sales_volume_vat_inc_without_shipping"], Decimal("1105.00")
        )
        self.assertEqual(context["shipping_volume_vat_inc"], Decimal("52.75"))
        self.assertEqual(
            context["refund_volume_vat_inc_without_shipping"], Decimal("120.00")
        )
        self.assertEqual(context["refund_shipping_vat_inc"], Decimal("26.38"))

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @override_settings(CELERY_TASK_EAGER_PROPAGATES_EXCEPTIONS=True)
    @patch("commissions.utils.get_app_commission_for_invoice")
    @patch(
        "apps.orders.models.MerchantOrder."
        "get_discount_amount_on_shipping_financed_by_application"
    )
    @patch(
        "apps.orders.models.MerchantOrder."
        "get_application_promotion_compensation_on_items"
    )
    @patch("apps.returns.models.Refund.get_application_promotion_compensation_on_items")
    def test_operator_promotions_are_included_in_sales_volumes(
        self,
        mocked_item_refund_prom,
        mocked_item_prom,
        mocked_shipping_prom,
        mocked_comm,
    ):
        mocked_item_refund_prom.return_value = Decimal("10")
        mocked_item_prom.return_value = Decimal("10")
        mocked_shipping_prom.return_value = Decimal("50"), Decimal("2.75")
        mocked_comm.return_value = Decimal("42"), Decimal("10"), Decimal("5.5")
        order_item = ProductOfferOrderItemFactory.create(
            status=ORDER_ITEM_STATUS_AUTH_SUCCESS,
            merchant_order__order__auto_create_payment=True,
            merchant_order__order___payment__status=PAYMENT_STATUS_AUTH_SUCCESS,
            merchant_order__shipping=Decimal("20.00"),
            merchant_order__vat_on_shipping=Decimal("5.00"),
            quantity=5,
        )
        merchant_order = order_item.merchant_order

        merchant_order.actions.confirm(refresh_model=False)
        merchant_order.actions.process()
        refund = RefundBuilder().build(
            merchant_order.user,
            merchant_order,
            full=True,
            quantity=order_item.quantity,
            shipping=Decimal("25.00"),
        )
        refund.actions.process()
        sale_trans = merchant_order.transactions.filter_sale().get()
        item_prom_trans = merchant_order.transactions.filter_app_promotion().get(
            # TRANSACTION_APPLICATION_PROMOTION_DESC_ITEMS
            description="Marketplace promotion on items"
        )
        shipping_prom_trans = merchant_order.transactions.filter_app_promotion().get(
            # TRANSACTION_APPLICATION_PROMOTION_DESC_SHIPPING
            description="Marketplace promotion on shipping"
        )

        # sale proces will also process refund trans
        sale_trans.actions.process()
        item_prom_trans.actions.process()
        shipping_prom_trans.actions.process()

        merchant_sale_subtransaction = item_prom_trans.balancetransactions.get()
        merchant_balance = merchant_sale_subtransaction.balances.get()

        # when
        merchant_balance.actions.close()

        # then
        invoice = merchant_balance.invoices.get()
        context = invoice.generate_context()

        self.assertEqual(
            context["sales_volume_vat_inc_without_shipping"],
            merchant_order.price_vat_included + Decimal("10"),
        )
        self.assertEqual(
            context["shipping_volume_vat_inc"],
            merchant_order.shipping_vat_included + Decimal("52.75"),
        )
        self.assertEqual(
            context["refund_volume_vat_inc_without_shipping"],
            merchant_order.price_vat_included + Decimal("10"),
        )
        self.assertEqual(context["refund_shipping_vat_inc"], Decimal("25.00"))

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @override_settings(CELERY_TASK_EAGER_PROPAGATES_EXCEPTIONS=True)
    def test_store_adjustments_in_favor_of_merchant(self):
        app = ApplicationFactory(
            app_settings={DisplayStoreAdjustmentsInInvoice.key_name: True}
        )
        merchant = MerchantFactory.create_for_application(app)
        tsa_for_mp = StoreAdjustmentTransactionFactory(
            application=app,
            merchant=merchant,
            currency=app.default_currency,
            amount="100.00",
            vat_amount="8.50",
        )
        tsa_for_merchant = StoreAdjustmentTransactionFactory(
            application=app,
            merchant=merchant,
            currency=app.default_currency,
            amount="-250.00",
            vat_amount="-23.70",
        )

        store_transaction = tsa_for_mp.balancetransactions.get()
        balance = store_transaction.balances.get()
        balance.actions.close()
        invoice = balance.invoices.get()
        context = invoice.generate_context()

        store_adjustment_context = context["invoice_lines"][-1]
        self.assertEqual(store_adjustment_context["item_type"], "STR")

        sa_amount = tsa_for_mp.amount + tsa_for_merchant.amount
        tax_rate = abs(
            (tsa_for_mp.vat_amount + tsa_for_merchant.vat_amount) / sa_amount
        ) * Decimal(100)
        tax_amount = sa_amount * tax_rate / Decimal(100)
        sa_amount_without_tax = sa_amount - tax_amount

        # The Merchant owes 250€ to the Operator, so amount must be positive
        self.assertLess(sa_amount, 0)

        self.assertEqual(
            store_adjustment_context["tax_rate"], tax_rate.quantize(Decimal("1.00"))
        )
        self.assertEqual(
            store_adjustment_context["total_amount_without_tax"],
            sa_amount_without_tax.quantize(Decimal("1.00")),
        )
        self.assertEqual(
            store_adjustment_context["unit_amount_without_tax"],
            sa_amount_without_tax.quantize(Decimal("1.00")),
        )
        self.assertEqual(
            store_adjustment_context["total_amount_with_tax"],
            sa_amount.quantize(Decimal("1.00")),
        )
        self.assertEqual(
            store_adjustment_context["total_amount_with_tax"],
            sa_amount.quantize(Decimal("1.00")),
        )

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @override_settings(CELERY_TASK_EAGER_PROPAGATES_EXCEPTIONS=True)
    def test_store_adjustments_in_favor_of_marketplace(self):
        app = ApplicationFactory(
            app_settings={DisplayStoreAdjustmentsInInvoice.key_name: True}
        )
        merchant = MerchantFactory.create_for_application(app)
        tsa_for_mp = StoreAdjustmentTransactionFactory(
            application=app,
            merchant=merchant,
            currency=app.default_currency,
            amount="350.00",
            vat_amount="50.00",
        )
        tsa_for_merchant = StoreAdjustmentTransactionFactory(
            application=app,
            merchant=merchant,
            currency=app.default_currency,
            amount="-100.00",
            vat_amount="-12.00",
        )

        store_transaction = tsa_for_mp.balancetransactions.get()
        balance = store_transaction.balances.get()
        balance.actions.close()
        invoice = balance.invoices.get()
        context = invoice.generate_context()

        store_adjustment_context = context["invoice_lines"][-1]
        self.assertEqual(store_adjustment_context["item_type"], "STR")

        sa_amount = tsa_for_mp.amount + tsa_for_merchant.amount
        tax_rate = abs(
            (tsa_for_mp.vat_amount + tsa_for_merchant.vat_amount) / sa_amount
        ) * Decimal(100)

        tax_amount = sa_amount * tax_rate / Decimal(100)
        sa_amount_without_tax = sa_amount - tax_amount

        # The Merchant owes 250€ to the Operator, so amount must be positive
        self.assertGreater(sa_amount, 0)

        self.assertEqual(
            store_adjustment_context["tax_rate"], tax_rate.quantize(Decimal("1.00"))
        )
        self.assertEqual(
            store_adjustment_context["total_amount_without_tax"],
            sa_amount_without_tax.quantize(Decimal("1.00")),
        )
        self.assertEqual(
            store_adjustment_context["unit_amount_without_tax"],
            sa_amount_without_tax.quantize(Decimal("1.00")),
        )
        self.assertEqual(
            store_adjustment_context["total_amount_with_tax"],
            sa_amount.quantize(Decimal("1.00")),
        )
        self.assertEqual(
            store_adjustment_context["total_amount_with_tax"],
            sa_amount.quantize(Decimal("1.00")),
        )

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @override_settings(CELERY_TASK_EAGER_PROPAGATES_EXCEPTIONS=True)
    def test_store_adjustments_finaly_equals_to_0(self):
        app = ApplicationFactory(
            app_settings={DisplayStoreAdjustmentsInInvoice.key_name: True}
        )
        merchant = MerchantFactory.create_for_application(app)
        tsa_for_mp = StoreAdjustmentTransactionFactory(
            application=app,
            merchant=merchant,
            currency=app.default_currency,
            amount="100.00",
            vat_amount="5.00",
        )
        StoreAdjustmentTransactionFactory(
            application=app,
            merchant=merchant,
            currency=app.default_currency,
            amount="-100.00",
            vat_amount="-5.00",
        )

        store_transaction = tsa_for_mp.balancetransactions.get()
        balance = store_transaction.balances.get()
        balance.actions.close()
        invoice = balance.invoices.get()
        context = invoice.generate_context()

        store_adjustment_context = context["invoice_lines"][-1]
        self.assertEqual(store_adjustment_context["item_type"], "STR")

        self.assertEqual(store_adjustment_context["tax_rate"], Decimal("0.00"))
        self.assertEqual(
            store_adjustment_context["total_amount_without_tax"], Decimal("0.00")
        )
        self.assertEqual(
            store_adjustment_context["unit_amount_without_tax"], Decimal("0.00")
        )
        self.assertEqual(
            store_adjustment_context["total_amount_with_tax"], Decimal("0.00")
        )
        self.assertEqual(
            store_adjustment_context["total_amount_with_tax"], Decimal("0.00")
        )
