# Generated by Django 3.2.20 on 2023-08-24 09:28

import apps.invoices.models.invoice
from decimal import Decimal
import django.core.files.storage
import django.core.serializers.json
from django.db import migrations, models
import django.db.models.deletion
import ims.api.mixins
import ims.models.mixin
import lib.fields.no_choice_migration_charfield
import mp_utils.model_mixins.decimal_field_check_mixin
import mp_utils.model_mixins.exportable_model_mixin


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("ice_applications", "0001_initial"),
        ("address", "0002_initial"),
        ("currencies", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Invoice",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "id_number",
                    models.CharField(
                        blank=True,
                        help_text="Invoice Identification Number",
                        max_length=50,
                        null=True,
                        verbose_name="ID number",
                    ),
                ),
                (
                    "created_on",
                    models.DateTimeField(
                        auto_now_add=True,
                        help_text="The date of the invoice",
                        verbose_name="Created on",
                    ),
                ),
                (
                    "issued_on",
                    models.DateTimeField(
                        help_text="Issuing date of the invoice",
                        null=True,
                        verbose_name="Issued on",
                    ),
                ),
                (
                    "due_on",
                    models.DateTimeField(
                        blank=True,
                        help_text="The date before which the invoice needs to be paid",
                        null=True,
                        verbose_name="Due on",
                    ),
                ),
                (
                    "is_recurring",
                    models.BooleanField(
                        default=False, help_text="Is this a recurring invoice ?"
                    ),
                ),
                (
                    "period_start_on",
                    models.DateTimeField(
                        blank=True,
                        help_text="If applicable, the start date of the invoice period",
                        null=True,
                        verbose_name="Period start on",
                    ),
                ),
                (
                    "period_end_on",
                    models.DateTimeField(
                        blank=True,
                        help_text="If applicable, the end date of the invoice period",
                        null=True,
                        verbose_name="Period end on",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("not_paid", "Not paid"),
                            ("paid", "Successfully Paid"),
                            ("failed_payment", "Failed on payment"),
                        ],
                        default="not_paid",
                        max_length=50,
                    ),
                ),
                (
                    "visibility",
                    models.CharField(
                        choices=[
                            ("hidden", "Hidden (staff only)"),
                            ("shown", "Shown"),
                            ("deleted", "Deleted"),
                        ],
                        default="hidden",
                        max_length=50,
                        verbose_name="Visibility",
                    ),
                ),
                (
                    "next_retry",
                    models.DateTimeField(
                        blank=True,
                        help_text="When we will try to collect the payment",
                        null=True,
                        verbose_name="Next retry",
                    ),
                ),
                (
                    "vat_rate",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=20
                    ),
                ),
                (
                    "sub_total_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="Sub Total Amount of the invoice (before discounts)",
                        max_digits=20,
                        verbose_name="Sub total amount",
                    ),
                ),
                (
                    "tax_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="The tax amount applied on the sub total amount",
                        max_digits=20,
                        verbose_name="Tax amount",
                    ),
                ),
                (
                    "total_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="Total Amount of the invoice (after discounts)",
                        max_digits=20,
                        verbose_name="Total amount",
                    ),
                ),
                (
                    "refund_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="Amount that has been refunded on this invoice",
                        max_digits=20,
                        verbose_name="Refund amount",
                    ),
                ),
                (
                    "discount_type",
                    models.PositiveSmallIntegerField(
                        blank=True,
                        choices=[
                            (10, "In Percentage based on original price"),
                            (20, "In Amount based on original price"),
                        ],
                        help_text="discount type",
                        null=True,
                    ),
                ),
                (
                    "discount_value",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="The percentage or amount of the discount (depending on the discount_type)",
                        max_digits=20,
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "pdf_file",
                    models.FileField(
                        blank=True,
                        max_length=1024,
                        null=True,
                        storage=django.core.files.storage.FileSystemStorage(),
                        upload_to=apps.invoices.models.invoice.InvoiceFilePathGenerator(),
                    ),
                ),
                (
                    "pdf_file_bkp",
                    models.FileField(
                        blank=True,
                        default=None,
                        max_length=1024,
                        null=True,
                        storage=django.core.files.storage.FileSystemStorage(),
                        upload_to="",
                    ),
                ),
                (
                    "extra_infos",
                    models.JSONField(
                        blank=True,
                        db_column="extra_infos",
                        default=dict,
                        encoder=django.core.serializers.json.DjangoJSONEncoder,
                        help_text="Extra infos",
                    ),
                ),
                (
                    "invoice_type",
                    models.CharField(
                        choices=[("debit", "Invoice"), ("credit", "Credit note")],
                        max_length=32,
                    ),
                ),
                (
                    "amounts_are_with_tax",
                    models.BooleanField(
                        default=False,
                        help_text="Does the amount include the tax_amount ?",
                    ),
                ),
                (
                    "is_net_of_tax",
                    models.BooleanField(
                        default=False,
                        help_text="Is this invoice (or credit note) net of tax ?",
                    ),
                ),
                (
                    "siret_number",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("vat_number", models.CharField(blank=True, max_length=100, null=True)),
                (
                    "payment_method",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("B", "Marketplace Balance"),
                            ("DC", "Debit Card"),
                            ("DD", "Direct Debit"),
                            ("CA", "Cash"),
                            ("CH", "Check"),
                        ],
                        max_length=5,
                        null=True,
                    ),
                ),
                (
                    "paid_on",
                    models.DateTimeField(
                        blank=True,
                        help_text="When the Invoice has been paid",
                        null=True,
                    ),
                ),
                (
                    "language",
                    lib.fields.no_choice_migration_charfield.NoChoiceMigrationCharfield(
                        default="fr", max_length=7
                    ),
                ),
                (
                    "admin_comments",
                    models.TextField(
                        blank=True,
                        help_text="comments for admin only",
                        verbose_name="Admin comments",
                    ),
                ),
                (
                    "application",
                    models.ForeignKey(
                        db_index=False,
                        help_text="The application issuing the invoice",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="invoices_issued",
                        to="ice_applications.application",
                    ),
                ),
                (
                    "application_balance",
                    models.ForeignKey(
                        blank=True,
                        db_index=False,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="invoices",
                        to="ice_applications.apppaymentbalance",
                    ),
                ),
                (
                    "country",
                    models.ForeignKey(
                        blank=True,
                        db_index=False,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="address.country",
                        verbose_name="Country",
                    ),
                ),
                (
                    "currency",
                    models.ForeignKey(
                        db_index=False,
                        default="EUR",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="currencies.currency",
                    ),
                ),
            ],
            bases=(
                mp_utils.model_mixins.exportable_model_mixin.ExportableModelMixin,
                ims.api.mixins.ApiUtilsMixin,
                ims.models.mixin.SanitizeCharFieldMixin,
                ims.models.mixin.ChangedFieldLookupMixin,
                mp_utils.model_mixins.decimal_field_check_mixin.DecimalFieldCheckMixin,
                models.Model,
            ),
        ),
        migrations.CreateModel(
            name="InvoiceLineItem",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "date_from",
                    models.DateTimeField(
                        blank=True,
                        help_text="If applicable, the start date of the invoice item",
                        null=True,
                    ),
                ),
                (
                    "date_to",
                    models.DateTimeField(
                        blank=True,
                        help_text="If applicable, the start date of the invoice item",
                        null=True,
                    ),
                ),
                (
                    "unit_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="Unit Amount (or sale volume) to be multiplied by quantity",
                        max_digits=20,
                    ),
                ),
                (
                    "quantity",
                    models.DecimalField(
                        decimal_places=4,
                        default=Decimal("0.0000"),
                        help_text="Quantity (or commission rate) to be multiplied by unit_amount",
                        max_digits=20,
                    ),
                ),
                (
                    "total_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="Total amount of this item",
                        max_digits=20,
                    ),
                ),
                (
                    "tax_rate",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="Tax amount of this item in percent",
                        max_digits=20,
                    ),
                ),
                (
                    "tax_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="Tax amount of this item",
                        max_digits=20,
                    ),
                ),
                (
                    "description",
                    models.CharField(
                        blank=True,
                        help_text="Line Item description",
                        max_length=500,
                        null=True,
                    ),
                ),
                (
                    "item_type",
                    models.CharField(
                        choices=[
                            ("lco", "License application"),
                            ("COS", "Commissions on sales"),
                            ("COM", "Commissions"),
                            ("COMR", "Commission refunds"),
                            ("CON", "Contest"),
                            ("SC", "Sponsored Content"),
                            ("TMO", "Technology Module"),
                            ("TOC", "Technology Operating Cost"),
                            ("TMA", "Technology Maintenance"),
                            ("REF", "Commissions on Refund"),
                            ("OP", "Compensation on marketplace promotions"),
                            ("OPR", "Refund of compensation on marketplace promotions"),
                            ("STR", "Store Adjustments"),
                        ],
                        max_length=5,
                    ),
                ),
                (
                    "application",
                    models.ForeignKey(
                        blank=True,
                        db_index=False,
                        help_text="[DEPRECATED as we do 1 invoice per application. Use application in Invoice model instead]",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="ice_applications.application",
                    ),
                ),
                (
                    "invoice",
                    models.ForeignKey(
                        db_index=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="line_items",
                        to="invoices.invoice",
                    ),
                ),
            ],
            options={
                "ordering": ["id"],
            },
            bases=(
                ims.api.mixins.ApiUtilsMixin,
                ims.models.mixin.SanitizeCharFieldMixin,
                ims.models.mixin.ChangedFieldLookupMixin,
                mp_utils.model_mixins.decimal_field_check_mixin.DecimalFieldCheckMixin,
                models.Model,
            ),
        ),
    ]
