# -*- coding: utf-8 -*-


import logging

from apps.webhooks.decorators import whook_receiver

logger = logging.getLogger(__name__)


# Webhooks : handle django signals triggered by application & return a dict.


@whook_receiver("new_package_tracking")
@whook_receiver("package_tracking_number_added")
@whook_receiver("package_tracking_status_changed")
@whook_receiver("package_tracking_overall_status_changed")
def handle_new_package_tracking(sender, **kwargs):
    from apps.shipping.api.resources import PackageTrackingResource
    from apps.shipping.models import PackageTracking
    from django.test import RequestFactory

    package_or_id = sender
    if type(package_or_id) == PackageTracking:
        package_tracking = package_or_id
    else:
        package_tracking = PackageTracking.objects.get(id=package_or_id)
    # get return request resource
    ptr = PackageTrackingResource()
    # take testin's request factory to generate a new request object
    request_factory = RequestFactory()
    # set request's url
    url = "/api/v1/%s/" % ptr._meta.resource_name
    logger.debug("url : %s", url)
    request = request_factory.get(
        "/api/v1/package_tracking/", data={"pk": package_tracking.id}
    )
    # Add request's user (application owner here)
    request.user = package_tracking.merchant_order.merchant.application.contact_user
    # user return request resource to generate a dict serialized return
    # request model object for prepared resource.
    payload = ptr.get_detail(request, pk=package_tracking.id, bundle_data_only=True)

    additionnal_keys = ["new_overall_status", "new_tracking_number", "new_status"]

    for key in additionnal_keys:
        if key in kwargs:
            payload[key] = kwargs[key]
        else:
            payload[key] = ""

    logger.debug("payload : %s", payload)

    return payload
