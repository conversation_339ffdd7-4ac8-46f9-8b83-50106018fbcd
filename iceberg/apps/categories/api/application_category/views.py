from apps.categories.api.application_category.filters import ApplicationCategoryFilter
from apps.categories.api.application_category.paginations import (
    ApplicationCategoryPagination,
)
from apps.categories.api.application_category.serializers import (
    ApplicationCategorySerializer,
)
from apps.categories.models import ApplicationCategory
from django_filters.rest_framework import DjangoFilterBackend
from drf_izberg.filters.backend.application_filter import ApplicationFilterBackend
from drf_izberg.permissions.actions import ActionsPermissions
from drf_izberg.permissions.constants.actions import (
    CREATE,
    DESTROY,
    LIST,
    PARTIAL_UPDATE,
    RETRIEVE,
    UPDATE,
)
from drf_izberg.permissions.constants.roles import ALL_ROLES, EDIT_ROLES
from drf_izberg.permissions.constants.scopes import PIM_SCOPE
from drf_izberg.permissions.http_methods import HTTPMethodsPermissions
from drf_izberg.permissions.owners import OwnersPermissions
from drf_izberg.permissions.roles import RolesPermissions
from drf_izberg.permissions.scopes import ScopesPermissions
from drf_izberg.permissions.write_fields import WriteFieldsPermissions
from drf_izberg.views.base import IzbergModelViewSet
from drf_izberg.views.transition_xworkflows_mixin import TransitionXWorkflowsMixin
from ims.api.const import MERCHANT_OWNER, OPERATOR_OWNER
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.filters import OrderingFilter
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response


class ApplicationCategoryViewSet(IzbergModelViewSet, TransitionXWorkflowsMixin):
    permission_classes = [
        IsAuthenticated,
        HTTPMethodsPermissions,
        OwnersPermissions,
        ScopesPermissions,
        ActionsPermissions,
        RolesPermissions,
        WriteFieldsPermissions,
    ]
    permission_constraints = {
        OPERATOR_OWNER: {
            LIST: ALL_ROLES,
            RETRIEVE: ALL_ROLES,
            CREATE: EDIT_ROLES,
            UPDATE: EDIT_ROLES,
            PARTIAL_UPDATE: EDIT_ROLES,
            DESTROY: EDIT_ROLES,
            "can_delete": EDIT_ROLES,
        },
        MERCHANT_OWNER: {
            LIST: ALL_ROLES,
            RETRIEVE: ALL_ROLES,
        },
        # BUYER_OWNER: {
        #     LIST: ALL_ROLES,
        #     RETRIEVE: ALL_ROLES,
        # },
        # ANONYMOUS_OWNER: {
        #     LIST: ALL_ROLES,
        #     RETRIEVE: ALL_ROLES,
        # },
    }
    permission_scopes = [PIM_SCOPE]

    resource_name = "application-category"

    queryset = ApplicationCategory.objects.all()
    serializer_class = ApplicationCategorySerializer
    http_method_names = ["get", "post", "patch", "delete"]
    filter_backends = [
        ApplicationFilterBackend,
        DjangoFilterBackend,
        OrderingFilter,
    ]
    ordering_fields = [
        "id",
        "parents",
    ]
    ordering = []
    filterset_class = ApplicationCategoryFilter
    application_filter_relation = "application_id"
    pagination_class = ApplicationCategoryPagination
    legacy_schema_extra_info = {
        "filters": {
            "id": 1,
            "parents": 1,
        },
        "allowed_detail_http_methods": [
            "get",
            "patch",
            "delete",
        ],
        "allowed_list_http_methods": [
            "get",
            "post",
        ],
    }

    def perform_destroy(self, instance):
        self.perform_xworkflows_transition(
            instance=instance,
            request=self.request,
            transition_name="delete_action",
        )

    @action(
        detail=True,
        url_path="can-delete",
        name="Category can be deleted",
    )
    def can_delete(self, request: Request, pk: int, **kwargs) -> Response:
        category = self.get_object()

        response_payload = self.can_perform_xworkflows_transition(
            instance=category,
            transition_name="delete_action",
            method_name="can_delete",
        )
        return Response(response_payload, status.HTTP_200_OK)
