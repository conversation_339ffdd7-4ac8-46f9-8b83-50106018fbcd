from apps.currencies.api.currency.views import CurrencyViewSet
from drf_izberg.permissions.constants.roles import READ_ROLE, WRITE_ROLE
from drf_izberg.permissions.constants.scopes import ANY_SCOPE, PIM_SCOPE
from drf_izberg.tests.base import BasePermissionsAPITestCase
from ims.api.const import INTERNAL_OWNER, MERCHANT_OWNER, OPERATOR_OWNER


class CurrencyPermissionsTestCase(BasePermissionsAPITestCase):
    def setUp(self):
        super().setUp()
        self.view = CurrencyViewSet()
        self.view.action = None

    def test_operator_any_scope_read(self):
        self.request.owner_type = OPERATOR_OWNER
        self.request.scopes = [f"{ANY_SCOPE}:{READ_ROLE}"]

        self._should_pass_permissions_with_action("list")
        self._should_pass_permissions_with_action("retrieve")

        self._should_raise_with_action("create")
        self._should_raise_with_action("update")
        self._should_raise_with_action("destroy")

    def test_merchant_any_scope_read(self):
        self.request.owner_type = MERCHANT_OWNER
        self.request.scopes = [f"{ANY_SCOPE}:{READ_ROLE}"]

        self._should_pass_permissions_with_action("list")
        self._should_pass_permissions_with_action("retrieve")

        self._should_raise_with_action("create")
        self._should_raise_with_action("update")
        self._should_raise_with_action("destroy")

    def test_internal_any_scope_read(self):
        self.request.owner_type = INTERNAL_OWNER
        self.request.scopes = [f"{ANY_SCOPE}:{READ_ROLE}"]

        self._should_pass_permissions_with_action("list")
        self._should_pass_permissions_with_action("retrieve")

        self._should_raise_with_action("create")
        self._should_raise_with_action("update")
        self._should_raise_with_action("destroy")

    def test_operator_with_restricted_scope_and_role(self):
        self.request.owner_type = OPERATOR_OWNER
        self.request.scopes = [f"{PIM_SCOPE}:{WRITE_ROLE}"]
        self._should_pass_permissions_with_action("list")
        self._should_pass_permissions_with_action("retrieve")
