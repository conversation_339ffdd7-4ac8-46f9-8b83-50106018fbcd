# -*- coding: utf-8 -*-
from decimal import Decimal

from django.utils.translation import gettext_lazy as _

from ..base import APPLICATION_STAFF, DecimalSetting
from .group import CartGroupMixin


class AbandonedAndCancelledCartsLifetime(DecimalSetting, CartGroupMixin):
    key_name = "abandoned_and_cancelled_carts_lifetime"
    verbose_name = _(
        "Carts maximum lifetime before being considered as abandoned in "
        "days. (default 180)"
    )
    default_value = Decimal("180")
    read_permission = APPLICATION_STAFF
    write_permission = APPLICATION_STAFF
