from apps.address.constants import FRANCE_COUNTRY_ID
from apps.ice_applications.api.application.serializers import (
    ApplicationLinkDomainSerializer,
    ApplicationResetPimDataSerializer,
    ApplicationSerializer,
)
from apps.ice_applications.models import Application
from apps.testing.factories import ApplicationFactory, UserFactory
from drf_izberg.tests.base import BaseSerializerAPITestCase
from ims.api.const import OPERATOR_OWNER
from rest_framework.exceptions import ValidationError


class ApplicationSerializerTestCase(BaseSerializerAPITestCase):
    API_VERSION = "v2"
    # This attribute controls the maximum length of diffs output by assert methods
    # that report diffs on failure.
    maxDiff = None

    def setUp(self):
        super().setUp()
        self.request.owner_type = OPERATOR_OWNER

    def test_serializer(self):
        """
        Given an instance of Application
        When serializing the instance
        Then I get a valid output
        """
        application = ApplicationFactory(id=1)
        contact_user = application.contact_user
        country = application.country
        currencies = list(application.currencies.all())
        default_currency = application.default_currency

        serializer = ApplicationSerializer(
            instance=application,
            context={"request": self.request},
        )
        output = {
            "api_key": application.api_key,
            "app_type": application.app_type,
            "app_type_localized": application.get_app_type_display(),
            "category": application.category,
            "company": application.company,
            "company_id": application.company_id,
            "contact_user": {
                "email": contact_user.email,
                "first_name": contact_user.first_name,
                "last_name": contact_user.last_name,
                "external_id": contact_user.external_id,
                "id": contact_user.id,
                "uid": contact_user.uid,
                "username": contact_user.username,
                "application": None,  # Factory doesn't set application to user
                "date_joined": contact_user.date_joined.astimezone().isoformat(),
                "display_name": contact_user.display_name,
                "last_login": contact_user.last_login,
                "resource_uri": (
                    f"http://testserver/{self.API_VERSION}/user/{contact_user.id}/"
                ),
            },
            "country": {
                "id": country.id,
                "pk": country.id,
                "name": country.name,
                "resource_uri": (
                    f"http://testserver/{self.API_VERSION}/country/" f"{country.id}/"
                ),
            },
            "created_on": application.created_on.astimezone().isoformat(),
            "currencies": [
                {
                    "id": currency.pk,
                    "pk": currency.pk,
                    "resource_uri": (
                        f"http://testserver/{self.API_VERSION}/currency/"
                        f"{currency.pk}/"
                    ),
                }
                for currency in currencies
            ],
            "default_currency": {
                "code": default_currency.code,
                "is_active": default_currency.is_active,
                "is_default": default_currency.is_default,
                "name": default_currency.name,
                "resource_uri": (
                    f"http://testserver/{self.API_VERSION}/currency/"
                    f"{default_currency.code}/"
                ),
                "symbol": default_currency.symbol,
            },
            "description": application.description,
            "domain_id": application.domain_id,
            "environment": application.environment,
            "environment_localized": application.get_environment_display(),
            "icon": str(application.icon) or None,
            "id": application.id,
            "is_master": application.is_master,
            "language": application.language,
            "languages": list(application.languages),
            "logo": str(application.logo) or None,
            "long_description": application.long_description,
            "mobile_website": application.mobile_website,
            "name": application.name,
            "namespace": application.namespace,
            "personal_shopper": application.personal_shopper,
            "resource_uri": (
                f"http://testserver/{self.API_VERSION}/application/"
                f"{application.id}/"
            ),
            "sample": application.sample,
            "status": str(application.status),
            "status_localized": application.get_status_display(),
            "switchboard_number": application.switchboard_number,
            "timezone": str(application.timezone),
            "website": application.website or "",
        }
        self.assertEqual(serializer.data, output)

    def test_serializer_validation(self):
        """
        Given a payload of data
        When serializing the payload
        Then I get a valid instance
        """
        application_count_before = Application.objects.count()

        user = UserFactory(is_staff=True)
        payload = {
            "name": "Test application created",
            "contact_user": f"/{self.API_VERSION}/user/{user.id}/",
        }

        serializer = ApplicationSerializer(
            data=payload,
            context={"request": self.request},
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()

        self.assertEqual(Application.objects.count(), application_count_before + 1)
        application = Application.objects.last()
        self.assertEqual(application.name, payload["name"])

    def test_serializer_validation_missing_field__raises_400(self):
        """
        Given a payload of wrong data
        When serializing the payload
        Then I get a validation error
        """
        payload = {
            "name": "Test application created",
            "country": f"/{self.API_VERSION}/country/{FRANCE_COUNTRY_ID}/",
        }

        serializer = ApplicationSerializer(
            data=payload,
            context={"request": self.request},
        )
        with self.assertRaises(ValidationError) as error:
            serializer.is_valid(raise_exception=True)

        self.assertEqual(
            error.exception.detail,
            {
                "contact_user": ["This field is required."],
            },
        )


class ApplicationLinkDomainSerializerTestCase(BaseSerializerAPITestCase):
    def test_serializer_validation(self):
        """
        Given a payload of data
        When serializing the payload
        Then I get a valid data
        """

        payload = {
            "domain_id": "AAA-BBBB",
        }

        serializer = ApplicationLinkDomainSerializer(data=payload)
        serializer.is_valid(raise_exception=True)

        self.assertEqual(
            serializer.data,
            payload,
        )

    def test_serializer_validation_missing_field__raises_400(self):
        """
        Given a payload of wrong data
        When serializing the payload
        Then I get a validation error
        """
        payload = {}

        serializer = ApplicationLinkDomainSerializer(data=payload)
        with self.assertRaises(ValidationError) as error:
            serializer.is_valid(raise_exception=True)
        self.assertEqual(
            error.exception.detail,
            {
                "domain_id": ["This field is required."],
            },
        )


class ApplicationResetPimDataSerializerTestCase(BaseSerializerAPITestCase):
    def test_serializer_validation(self):
        """
        Given a payload of data
        When serializing the payload
        Then I get a valid data without these keys:
        - application_namespace
        - application_id
        - environment
        """

        application = ApplicationFactory(id=1)

        payload = {
            "application_namespace": application.namespace,
            "application_id": application.id,
            "environment": "development",
            "product_offer_filters": {"merchant_id": 2},
            "delete_products": True,
            "dry_run": False,
        }

        serializer = ApplicationResetPimDataSerializer(
            data=payload,
            context={"application": application},
        )
        serializer.is_valid(raise_exception=True)

        self.assertEqual(
            serializer.data,
            {
                # value from our payload
                "product_offer_filters": {"merchant_id": 2},
                "delete_products": True,
                "dry_run": False,
                # Default value
                "delete_product_offers": False,
                "delete_product_variations": False,
                "product_variation_filters": {},
            },
        )

    def test_serializer_validation_missing_field__raises_400(self):
        """
        Given a payload of wrong data
        When serializing the payload
        Then I get a validation error
        """
        application = ApplicationFactory(id=1)

        payload = {
            "application_id": application.id,
            "environment": "development",
            "product_offer_filters": {"merchant_id": 2},
            "delete_products": True,
            "dry_run": False,
        }

        serializer = ApplicationResetPimDataSerializer(
            data=payload,
            context={"application": application},
        )
        with self.assertRaises(ValidationError) as error:
            serializer.is_valid(raise_exception=True)

        self.assertEqual(
            error.exception.detail,
            {
                "application_namespace": ["This field is required."],
            },
        )

    def test_serializer_validation_wrong_application_id__raises_400(self):
        """
        Given a payload of wrong data
        When serializing the payload
        Then I get a validation error
        """
        application = ApplicationFactory(id=1)

        payload = {
            "application_namespace": application.namespace,
            "application_id": 999,
            "environment": "development",
        }

        serializer = ApplicationResetPimDataSerializer(
            data=payload,
            context={"application": application},
        )
        with self.assertRaises(ValidationError) as error:
            serializer.is_valid(raise_exception=True)

        self.assertEqual(
            error.exception.detail,
            {
                "application_id": ["Mismatch id"],
            },
        )

    def test_serializer_validation_wrong_environment__raises_400(self):
        """
        Given a payload of wrong data
        When serializing the payload
        Then I get a validation error
        """
        application = ApplicationFactory(id=1)

        payload = {
            "application_namespace": application.namespace,
            "application_id": application.id,
            "environment": "batman",
        }

        serializer = ApplicationResetPimDataSerializer(
            data=payload,
            context={"application": application},
        )
        with self.assertRaises(ValidationError) as error:
            serializer.is_valid(raise_exception=True)

        self.assertEqual(
            error.exception.detail,
            {
                "environment": ["Mismatch environment"],
            },
        )

    def test_serializer_validation_application_namespace__raises_400(self):
        """
        Given a payload of wrong data
        When serializing the payload
        Then I get a validation error
        """
        application = ApplicationFactory(id=1)

        payload = {
            "application_namespace": "BATCAVE",
            "application_id": application.id,
            "environment": "development",
        }

        serializer = ApplicationResetPimDataSerializer(
            data=payload,
            context={"application": application},
        )
        with self.assertRaises(ValidationError) as error:
            serializer.is_valid(raise_exception=True)

        self.assertEqual(
            error.exception.detail,
            {
                "application_namespace": ["Mismatch namespace"],
            },
        )
