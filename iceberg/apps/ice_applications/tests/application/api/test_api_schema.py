# -*- coding: utf-8 -*-
from unittest.mock import patch

from drf_izberg.tests.base import BaseAPITestCase


class ApplicationViewsSchemaTestCase(BaseAPITestCase):
    API_VERSION = "v2"
    # This attribute controls the maximum length of diffs output by assert methods
    # that report diffs on failure.
    maxDiff = None

    # We have to mock <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> because /schema/ is a special endpoint
    # with his own override of permissions.
    # see IzbergSchemaMixin in drf_izberg.views.schema_mixins
    @patch("rest_framework.permissions.IsAuthenticated.has_permission", lambda *_: True)
    def test_schema(self):
        resp = self.api_client.get(f"/{self.API_VERSION}/application/schema/")

        self.assertHttpOK(resp)
        data = self.deserialize(resp)

        # The expected schema is slightly different from the actual Tastypie schema.
        # - Most listed fields have their attributes blank and default changed.
        #   DRF will use the value of the model by default OR the value set on the
        #   serializer. Tastypie seems to be different and are sometimes wrong.
        #   Example: id with blank: True -> Wrong
        # - We are adding the attribute "required" that defined if the field is required
        #   by our serializer to pass validation.
        # - in methods, for each 'action' listed, we also add the allowed_http_methods
        #
        # Therefore, we cannot directly use our schema.json, we need to change
        # those values. You must be careful when changing the value, do not simply use
        # what our DRF schema generator is giving, but check on the model and
        # serializer that the information is correct
        expected_data = {
            "allowed_detail_http_methods": ["get", "post", "put", "delete", "patch"],
            "allowed_list_http_methods": ["get", "post"],
            "default_format": "application/json",
            "default_limit": 20,
            "doc": "Application resource",
            "editable_for_statuses": [],
            "fields": {
                "api_key": {
                    "blank": False,
                    "creatable": False,
                    "default": "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa",
                    "editable": False,
                    "help_text": "",
                    "max_length": 128,
                    "name": "Api key",
                    "nullable": False,
                    "readonly": True,
                    "required": False,
                    "type": "string",
                    "unique": False,
                },
                "app_type": {
                    "blank": False,
                    "choices": [
                        {"help_text": "Marketplace Operator", "value": 1},
                        {"help_text": "Feed Manager", "value": 2},
                    ],
                    "choices_to_hide": [],
                    "creatable": True,
                    "default": 1,
                    "editable": False,
                    "help_text": "",
                    "name": "App type",
                    "nullable": False,
                    "readonly": True,
                    "required": False,
                    "type": "integer",
                    "unique": False,
                    "virtual_choices": [],
                },
                "app_type_localized": {
                    "blank": False,
                    "creatable": False,
                    "default": "",
                    "editable": False,
                    "help_text": "",
                    "max_length": None,
                    "name": "App type localized",
                    "nullable": False,
                    "readonly": True,
                    "required": False,
                    "type": "string",
                    "unique": False,
                },
                "category": {
                    "blank": False,
                    "choices": [
                        {"help_text": "Debug", "value": 1},
                        {"help_text": "Media", "value": 2},
                        {"help_text": "Developers", "value": 3},
                        {"help_text": "ECommerce", "value": 4},
                    ],
                    "choices_to_hide": [],
                    "creatable": True,
                    "default": 1,
                    "editable": True,
                    "help_text": "",
                    "name": "Category",
                    "nullable": False,
                    "readonly": False,
                    "required": False,
                    "type": "integer",
                    "unique": False,
                    "virtual_choices": [],
                },
                "company": {
                    "blank": False,
                    "creatable": True,
                    "default": "",
                    "editable": True,
                    "help_text": "",
                    "name": "Company",
                    "nullable": True,
                    "readonly": False,
                    "related_type": "to_one",
                    "required": False,
                    "type": "related",
                    "unique": False,
                },
                "company_id": {
                    "blank": False,
                    "creatable": False,
                    "default": "",
                    "editable": False,
                    "help_text": "",
                    "name": "Company",
                    "nullable": True,
                    "readonly": True,
                    "required": False,
                    "type": "related",
                    "unique": False,
                },
                "contact_user": {
                    "blank": False,
                    "creatable": True,
                    "default": "",
                    "editable": False,
                    "help_text": "",
                    "name": "Contact user",
                    "nullable": False,
                    "readonly": True,
                    "related_type": "to_one",
                    "required": True,
                    "type": "related",
                    "unique": False,
                },
                "country": {
                    "blank": False,
                    "creatable": True,
                    "default": 72,
                    "editable": False,
                    "help_text": "",
                    "name": "Country",
                    "nullable": False,
                    "readonly": True,
                    "related_type": "to_one",
                    "required": False,
                    "type": "related",
                    "unique": False,
                },
                "created_on": {
                    "blank": False,
                    "creatable": False,
                    "default": "",
                    "editable": False,
                    "help_text": "",
                    "name": "Created on",
                    "nullable": False,
                    "readonly": True,
                    "required": False,
                    "type": "datetime",
                    "unique": False,
                },
                "currencies": {
                    "blank": False,
                    "creatable": True,
                    "default": "",
                    "editable": True,
                    "help_text": "",
                    "name": "Currencies",
                    "nullable": True,
                    "readonly": False,
                    "related_type": "to_many",
                    "required": False,
                    "type": "related",
                    "unique": False,
                },
                "default_currency": {
                    "blank": False,
                    "creatable": True,
                    "default": "EUR",
                    "editable": True,
                    "help_text": "",
                    "name": "Default currency",
                    "nullable": False,
                    "readonly": False,
                    "related_type": "to_one",
                    "required": False,
                    "type": "related",
                    "unique": False,
                },
                "description": {
                    "blank": True,
                    "creatable": True,
                    "default": "",
                    "editable": True,
                    "help_text": "",
                    "max_length": 255,
                    "name": "Description",
                    "nullable": False,
                    "readonly": False,
                    "required": False,
                    "type": "string",
                    "unique": False,
                },
                "domain_id": {
                    "blank": True,
                    "creatable": True,
                    "default": "",
                    "editable": False,
                    "help_text": "",
                    "max_length": 32,
                    "name": "Domain id",
                    "nullable": True,
                    "readonly": True,
                    "required": False,
                    "type": "string",
                    "unique": False,
                },
                "environment": {
                    "blank": False,
                    "choices": [
                        {"help_text": "Production", "value": "production"},
                        {"help_text": "Sandbox", "value": "sandbox"},
                        {"help_text": "Development", "value": "development"},
                    ],
                    "choices_to_hide": [],
                    "creatable": False,
                    "default": "development",
                    "editable": False,
                    "help_text": "",
                    "max_length": 255,
                    "name": "Environment",
                    "nullable": False,
                    "readonly": True,
                    "required": False,
                    "type": "string",
                    "unique": False,
                    "virtual_choices": [],
                },
                "environment_localized": {
                    "blank": False,
                    "creatable": False,
                    "default": "",
                    "editable": False,
                    "help_text": "",
                    "max_length": None,
                    "name": "Environment localized",
                    "nullable": False,
                    "readonly": True,
                    "required": False,
                    "type": "string",
                    "unique": False,
                },
                "icon": {
                    "blank": False,
                    "creatable": False,
                    "default": "",
                    "editable": False,
                    "help_text": "16x16",
                    "max_length": 100,
                    "name": "Icon",
                    "nullable": True,
                    "readonly": True,
                    "required": False,
                    "type": "string",
                    "unique": False,
                },
                "id": {
                    "blank": False,
                    "creatable": False,
                    "default": "",
                    "editable": False,
                    "help_text": "",
                    "name": "Id",
                    "nullable": False,
                    "readonly": True,
                    "required": False,
                    "type": "integer",
                    "unique": True,
                },
                "is_master": {
                    "blank": False,
                    "creatable": False,
                    "default": False,
                    "editable": False,
                    "help_text": "If true, user permission will not "
                    "filter by Application. Example: Store "
                    "Manager can see orders from every "
                    "store",
                    "name": "Is a master application?",
                    "nullable": False,
                    "readonly": True,
                    "required": False,
                    "type": "boolean",
                    "unique": False,
                },
                "language": {
                    "blank": False,
                    "choices": [
                        {"help_text": "Afrikaans", "value": "af"},
                        {"help_text": "Arabic", "value": "ar"},
                        {"help_text": "Algerian Arabic", "value": "ar-dz"},
                        {"help_text": "Asturian", "value": "ast"},
                        {"help_text": "Azerbaijani", "value": "az"},
                        {"help_text": "Bulgarian", "value": "bg"},
                        {"help_text": "Belarusian", "value": "be"},
                        {"help_text": "Bengali", "value": "bn"},
                        {"help_text": "Breton", "value": "br"},
                        {"help_text": "Bosnian", "value": "bs"},
                        {"help_text": "Catalan", "value": "ca"},
                        {"help_text": "Czech", "value": "cs"},
                        {"help_text": "Welsh", "value": "cy"},
                        {"help_text": "Danish", "value": "da"},
                        {"help_text": "German", "value": "de"},
                        {"help_text": "Lower Sorbian", "value": "dsb"},
                        {"help_text": "Greek", "value": "el"},
                        {"help_text": "English", "value": "en"},
                        {"help_text": "Australian English", "value": "en-au"},
                        {"help_text": "British English", "value": "en-gb"},
                        {"help_text": "Esperanto", "value": "eo"},
                        {"help_text": "Spanish", "value": "es"},
                        {"help_text": "Argentinian Spanish", "value": "es-ar"},
                        {"help_text": "Colombian Spanish", "value": "es-co"},
                        {"help_text": "Mexican Spanish", "value": "es-mx"},
                        {"help_text": "Nicaraguan Spanish", "value": "es-ni"},
                        {"help_text": "Venezuelan Spanish", "value": "es-ve"},
                        {"help_text": "Estonian", "value": "et"},
                        {"help_text": "Basque", "value": "eu"},
                        {"help_text": "Persian", "value": "fa"},
                        {"help_text": "Finnish", "value": "fi"},
                        {"help_text": "French", "value": "fr"},
                        {"help_text": "Frisian", "value": "fy"},
                        {"help_text": "Irish", "value": "ga"},
                        {"help_text": "Scottish Gaelic", "value": "gd"},
                        {"help_text": "Galician", "value": "gl"},
                        {"help_text": "Hebrew", "value": "he"},
                        {"help_text": "Hindi", "value": "hi"},
                        {"help_text": "Croatian", "value": "hr"},
                        {"help_text": "Upper Sorbian", "value": "hsb"},
                        {"help_text": "Hungarian", "value": "hu"},
                        {"help_text": "Armenian", "value": "hy"},
                        {"help_text": "Interlingua", "value": "ia"},
                        {"help_text": "Indonesian", "value": "id"},
                        {"help_text": "Igbo", "value": "ig"},
                        {"help_text": "Ido", "value": "io"},
                        {"help_text": "Icelandic", "value": "is"},
                        {"help_text": "Italian", "value": "it"},
                        {"help_text": "Japanese", "value": "ja"},
                        {"help_text": "Georgian", "value": "ka"},
                        {"help_text": "Kabyle", "value": "kab"},
                        {"help_text": "Kazakh", "value": "kk"},
                        {"help_text": "Khmer", "value": "km"},
                        {"help_text": "Kannada", "value": "kn"},
                        {"help_text": "Korean", "value": "ko"},
                        {"help_text": "Kyrgyz", "value": "ky"},
                        {"help_text": "Luxembourgish", "value": "lb"},
                        {"help_text": "Lithuanian", "value": "lt"},
                        {"help_text": "Latvian", "value": "lv"},
                        {"help_text": "Macedonian", "value": "mk"},
                        {"help_text": "Malayalam", "value": "ml"},
                        {"help_text": "Mongolian", "value": "mn"},
                        {"help_text": "Marathi", "value": "mr"},
                        {"help_text": "Malay", "value": "ms"},
                        {"help_text": "Burmese", "value": "my"},
                        {"help_text": "Norwegian Bokmål", "value": "nb"},
                        {"help_text": "Nepali", "value": "ne"},
                        {"help_text": "Dutch", "value": "nl"},
                        {"help_text": "Norwegian Nynorsk", "value": "nn"},
                        {"help_text": "Ossetic", "value": "os"},
                        {"help_text": "Punjabi", "value": "pa"},
                        {"help_text": "Polish", "value": "pl"},
                        {"help_text": "Portuguese", "value": "pt"},
                        {"help_text": "Brazilian Portuguese", "value": "pt-br"},
                        {"help_text": "Romanian", "value": "ro"},
                        {"help_text": "Russian", "value": "ru"},
                        {"help_text": "Slovak", "value": "sk"},
                        {"help_text": "Slovenian", "value": "sl"},
                        {"help_text": "Albanian", "value": "sq"},
                        {"help_text": "Serbian", "value": "sr"},
                        {"help_text": "Serbian Latin", "value": "sr-latn"},
                        {"help_text": "Swedish", "value": "sv"},
                        {"help_text": "Swahili", "value": "sw"},
                        {"help_text": "Tamil", "value": "ta"},
                        {"help_text": "Telugu", "value": "te"},
                        {"help_text": "Tajik", "value": "tg"},
                        {"help_text": "Thai", "value": "th"},
                        {"help_text": "Turkmen", "value": "tk"},
                        {"help_text": "Turkish", "value": "tr"},
                        {"help_text": "Tatar", "value": "tt"},
                        {"help_text": "Udmurt", "value": "udm"},
                        {"help_text": "Ukrainian", "value": "uk"},
                        {"help_text": "Urdu", "value": "ur"},
                        {"help_text": "Uzbek", "value": "uz"},
                        {"help_text": "Vietnamese", "value": "vi"},
                        {"help_text": "Simplified Chinese", "value": "zh-hans"},
                        {"help_text": "Traditional Chinese", "value": "zh-hant"},
                    ],
                    "choices_to_hide": [],
                    "creatable": True,
                    "default": "fr",
                    "editable": True,
                    "help_text": "Default language of the application",
                    "max_length": 7,
                    "name": "Language",
                    "nullable": False,
                    "readonly": False,
                    "required": False,
                    "type": "string",
                    "unique": False,
                    "virtual_choices": [],
                },
                "languages": {
                    "blank": False,
                    "choices": [
                        {"value": "af", "help_text": "Afrikaans"},
                        {"value": "ar", "help_text": "Arabic"},
                        {"value": "ar-dz", "help_text": "Algerian Arabic"},
                        {"value": "ast", "help_text": "Asturian"},
                        {"value": "az", "help_text": "Azerbaijani"},
                        {"value": "bg", "help_text": "Bulgarian"},
                        {"value": "be", "help_text": "Belarusian"},
                        {"value": "bn", "help_text": "Bengali"},
                        {"value": "br", "help_text": "Breton"},
                        {"value": "bs", "help_text": "Bosnian"},
                        {"value": "ca", "help_text": "Catalan"},
                        {"value": "cs", "help_text": "Czech"},
                        {"value": "cy", "help_text": "Welsh"},
                        {"value": "da", "help_text": "Danish"},
                        {"value": "de", "help_text": "German"},
                        {"value": "dsb", "help_text": "Lower Sorbian"},
                        {"value": "el", "help_text": "Greek"},
                        {"value": "en", "help_text": "English"},
                        {"value": "en-au", "help_text": "Australian English"},
                        {"value": "en-gb", "help_text": "British English"},
                        {"value": "eo", "help_text": "Esperanto"},
                        {"value": "es", "help_text": "Spanish"},
                        {"value": "es-ar", "help_text": "Argentinian Spanish"},
                        {"value": "es-co", "help_text": "Colombian Spanish"},
                        {"value": "es-mx", "help_text": "Mexican Spanish"},
                        {"value": "es-ni", "help_text": "Nicaraguan Spanish"},
                        {"value": "es-ve", "help_text": "Venezuelan Spanish"},
                        {"value": "et", "help_text": "Estonian"},
                        {"value": "eu", "help_text": "Basque"},
                        {"value": "fa", "help_text": "Persian"},
                        {"value": "fi", "help_text": "Finnish"},
                        {"value": "fr", "help_text": "French"},
                        {"value": "fy", "help_text": "Frisian"},
                        {"value": "ga", "help_text": "Irish"},
                        {"value": "gd", "help_text": "Scottish Gaelic"},
                        {"value": "gl", "help_text": "Galician"},
                        {"value": "he", "help_text": "Hebrew"},
                        {"value": "hi", "help_text": "Hindi"},
                        {"value": "hr", "help_text": "Croatian"},
                        {"value": "hsb", "help_text": "Upper Sorbian"},
                        {"value": "hu", "help_text": "Hungarian"},
                        {"value": "hy", "help_text": "Armenian"},
                        {"value": "ia", "help_text": "Interlingua"},
                        {"value": "id", "help_text": "Indonesian"},
                        {"value": "ig", "help_text": "Igbo"},
                        {"value": "io", "help_text": "Ido"},
                        {"value": "is", "help_text": "Icelandic"},
                        {"value": "it", "help_text": "Italian"},
                        {"value": "ja", "help_text": "Japanese"},
                        {"value": "ka", "help_text": "Georgian"},
                        {"value": "kab", "help_text": "Kabyle"},
                        {"value": "kk", "help_text": "Kazakh"},
                        {"value": "km", "help_text": "Khmer"},
                        {"value": "kn", "help_text": "Kannada"},
                        {"value": "ko", "help_text": "Korean"},
                        {"value": "ky", "help_text": "Kyrgyz"},
                        {"value": "lb", "help_text": "Luxembourgish"},
                        {"value": "lt", "help_text": "Lithuanian"},
                        {"value": "lv", "help_text": "Latvian"},
                        {"value": "mk", "help_text": "Macedonian"},
                        {"value": "ml", "help_text": "Malayalam"},
                        {"value": "mn", "help_text": "Mongolian"},
                        {"value": "mr", "help_text": "Marathi"},
                        {"value": "ms", "help_text": "Malay"},
                        {"value": "my", "help_text": "Burmese"},
                        {"value": "nb", "help_text": "Norwegian Bokmål"},
                        {"value": "ne", "help_text": "Nepali"},
                        {"value": "nl", "help_text": "Dutch"},
                        {"value": "nn", "help_text": "Norwegian Nynorsk"},
                        {"value": "os", "help_text": "Ossetic"},
                        {"value": "pa", "help_text": "Punjabi"},
                        {"value": "pl", "help_text": "Polish"},
                        {"value": "pt", "help_text": "Portuguese"},
                        {"value": "pt-br", "help_text": "Brazilian Portuguese"},
                        {"value": "ro", "help_text": "Romanian"},
                        {"value": "ru", "help_text": "Russian"},
                        {"value": "sk", "help_text": "Slovak"},
                        {"value": "sl", "help_text": "Slovenian"},
                        {"value": "sq", "help_text": "Albanian"},
                        {"value": "sr", "help_text": "Serbian"},
                        {"value": "sr-latn", "help_text": "Serbian Latin"},
                        {"value": "sv", "help_text": "Swedish"},
                        {"value": "sw", "help_text": "Swahili"},
                        {"value": "ta", "help_text": "Tamil"},
                        {"value": "te", "help_text": "Telugu"},
                        {"value": "tg", "help_text": "Tajik"},
                        {"value": "th", "help_text": "Thai"},
                        {"value": "tk", "help_text": "Turkmen"},
                        {"value": "tr", "help_text": "Turkish"},
                        {"value": "tt", "help_text": "Tatar"},
                        {"value": "udm", "help_text": "Udmurt"},
                        {"value": "uk", "help_text": "Ukrainian"},
                        {"value": "ur", "help_text": "Urdu"},
                        {"value": "uz", "help_text": "Uzbek"},
                        {"value": "vi", "help_text": "Vietnamese"},
                        {"value": "zh-hans", "help_text": "Simplified Chinese"},
                        {"value": "zh-hant", "help_text": "Traditional Chinese"},
                    ],
                    "choices_to_hide": [],
                    "creatable": True,
                    "default": [],
                    "editable": True,
                    "help_text": (
                        "List of the languages in which you want your "
                        "product to be potentially shown"
                    ),
                    "name": "Languages",
                    "nullable": True,
                    "readonly": False,
                    "required": False,
                    "type": "list",
                    "unique": False,
                    "virtual_choices": [],
                },
                "logo": {
                    "blank": False,
                    "creatable": False,
                    "default": "",
                    "editable": False,
                    "help_text": "1024x1024",
                    "max_length": 100,
                    "name": "Logo",
                    "nullable": True,
                    "readonly": True,
                    "required": False,
                    "type": "string",
                    "unique": False,
                },
                "long_description": {
                    "blank": True,
                    "creatable": True,
                    "default": "",
                    "editable": True,
                    "help_text": "",
                    "max_length": None,
                    "name": "Long description",
                    "nullable": False,
                    "readonly": False,
                    "required": False,
                    "type": "string",
                    "unique": False,
                },
                "mobile_website": {
                    "blank": True,
                    "creatable": True,
                    "default": "",
                    "editable": True,
                    "help_text": "",
                    "max_length": 200,
                    "name": "Mobile website url",
                    "nullable": True,
                    "readonly": False,
                    "required": False,
                    "type": "string",
                    "unique": False,
                },
                "name": {
                    "blank": False,
                    "creatable": True,
                    "default": "",
                    "editable": True,
                    "help_text": "",
                    "max_length": 255,
                    "name": "Name",
                    "nullable": False,
                    "readonly": False,
                    "required": False,
                    "type": "string",
                    "unique": False,
                },
                "namespace": {
                    "blank": False,
                    "creatable": True,
                    "default": "",
                    "editable": False,
                    "help_text": "",
                    "max_length": 255,
                    "name": "Namespace",
                    "nullable": False,
                    "readonly": True,
                    "required": False,
                    "type": "string",
                    "unique": False,
                },
                "personal_shopper": {
                    "blank": False,
                    "creatable": True,
                    "default": False,
                    "editable": True,
                    "help_text": "Active the personal shopper on this application?",
                    "name": "Personal shopper",
                    "nullable": False,
                    "readonly": False,
                    "required": False,
                    "type": "boolean",
                    "unique": False,
                },
                "resource_uri": {
                    "blank": False,
                    "creatable": False,
                    "default": "",
                    "editable": False,
                    "help_text": "",
                    "max_length": None,
                    "name": "Resource uri",
                    "nullable": False,
                    "readonly": True,
                    "required": False,
                    "type": "string",
                    "unique": False,
                },
                "sample": {
                    "blank": False,
                    "creatable": False,
                    "default": False,
                    "editable": False,
                    "help_text": "If True, only Modizy staff will be able "
                    "to edit this application",
                    "name": "Sample application?",
                    "nullable": False,
                    "readonly": True,
                    "required": False,
                    "type": "boolean",
                    "unique": False,
                },
                "status": {
                    "blank": False,
                    "choices": [
                        {"help_text": "Initial", "value": "initial"},
                        {"help_text": "Active", "value": "active"},
                        {"help_text": "Paused", "value": "paused"},
                        {"help_text": "Deleted", "value": "deleted"},
                    ],
                    "choices_to_hide": [],
                    "creatable": False,
                    "default": "initial",
                    "editable": False,
                    "help_text": "",
                    "max_length": 16,
                    "name": "Status",
                    "nullable": False,
                    "readonly": True,
                    "required": False,
                    "transitions": {
                        "activate": {
                            "from_states": ["initial", "paused"],
                            "to_state": "active",
                        },
                        "delete_action": {
                            "from_states": ["initial", "active", "paused"],
                            "to_state": "deleted",
                        },
                        "pause": {
                            "from_states": ["initial", "active"],
                            "to_state": "paused",
                        },
                    },
                    "type": "string",
                    "unique": False,
                    "virtual_choices": [],
                },
                "status_localized": {
                    "blank": False,
                    "creatable": False,
                    "default": "",
                    "editable": False,
                    "help_text": "",
                    "max_length": None,
                    "name": "Status localized",
                    "nullable": False,
                    "readonly": True,
                    "required": False,
                    "type": "string",
                    "unique": False,
                },
                "switchboard_number": {
                    "blank": True,
                    "creatable": True,
                    "default": "",
                    "editable": True,
                    "help_text": "",
                    "max_length": 255,
                    "name": "Switchboard number",
                    "nullable": True,
                    "readonly": False,
                    "required": False,
                    "type": "string",
                    "unique": False,
                },
                "timezone": {
                    "blank": False,
                    "choices": [
                        {"value": "Africa/Abidjan", "help_text": "Africa/Abidjan"},
                        {"value": "Africa/Accra", "help_text": "Africa/Accra"},
                        {
                            "value": "Africa/Addis_Ababa",
                            "help_text": "Africa/Addis Ababa",
                        },
                        {"value": "Africa/Algiers", "help_text": "Africa/Algiers"},
                        {"value": "Africa/Asmara", "help_text": "Africa/Asmara"},
                        {"value": "Africa/Bamako", "help_text": "Africa/Bamako"},
                        {"value": "Africa/Bangui", "help_text": "Africa/Bangui"},
                        {"value": "Africa/Banjul", "help_text": "Africa/Banjul"},
                        {"value": "Africa/Bissau", "help_text": "Africa/Bissau"},
                        {"value": "Africa/Blantyre", "help_text": "Africa/Blantyre"},
                        {
                            "value": "Africa/Brazzaville",
                            "help_text": "Africa/Brazzaville",
                        },
                        {"value": "Africa/Bujumbura", "help_text": "Africa/Bujumbura"},
                        {"value": "Africa/Cairo", "help_text": "Africa/Cairo"},
                        {
                            "value": "Africa/Casablanca",
                            "help_text": "Africa/Casablanca",
                        },
                        {"value": "Africa/Ceuta", "help_text": "Africa/Ceuta"},
                        {"value": "Africa/Conakry", "help_text": "Africa/Conakry"},
                        {"value": "Africa/Dakar", "help_text": "Africa/Dakar"},
                        {
                            "value": "Africa/Dar_es_Salaam",
                            "help_text": "Africa/Dar es Salaam",
                        },
                        {"value": "Africa/Djibouti", "help_text": "Africa/Djibouti"},
                        {"value": "Africa/Douala", "help_text": "Africa/Douala"},
                        {"value": "Africa/El_Aaiun", "help_text": "Africa/El Aaiun"},
                        {"value": "Africa/Freetown", "help_text": "Africa/Freetown"},
                        {"value": "Africa/Gaborone", "help_text": "Africa/Gaborone"},
                        {"value": "Africa/Harare", "help_text": "Africa/Harare"},
                        {
                            "value": "Africa/Johannesburg",
                            "help_text": "Africa/Johannesburg",
                        },
                        {"value": "Africa/Juba", "help_text": "Africa/Juba"},
                        {"value": "Africa/Kampala", "help_text": "Africa/Kampala"},
                        {"value": "Africa/Khartoum", "help_text": "Africa/Khartoum"},
                        {"value": "Africa/Kigali", "help_text": "Africa/Kigali"},
                        {"value": "Africa/Kinshasa", "help_text": "Africa/Kinshasa"},
                        {"value": "Africa/Lagos", "help_text": "Africa/Lagos"},
                        {
                            "value": "Africa/Libreville",
                            "help_text": "Africa/Libreville",
                        },
                        {"value": "Africa/Lome", "help_text": "Africa/Lome"},
                        {"value": "Africa/Luanda", "help_text": "Africa/Luanda"},
                        {
                            "value": "Africa/Lubumbashi",
                            "help_text": "Africa/Lubumbashi",
                        },
                        {"value": "Africa/Lusaka", "help_text": "Africa/Lusaka"},
                        {"value": "Africa/Malabo", "help_text": "Africa/Malabo"},
                        {"value": "Africa/Maputo", "help_text": "Africa/Maputo"},
                        {"value": "Africa/Maseru", "help_text": "Africa/Maseru"},
                        {"value": "Africa/Mbabane", "help_text": "Africa/Mbabane"},
                        {"value": "Africa/Mogadishu", "help_text": "Africa/Mogadishu"},
                        {"value": "Africa/Monrovia", "help_text": "Africa/Monrovia"},
                        {"value": "Africa/Nairobi", "help_text": "Africa/Nairobi"},
                        {"value": "Africa/Ndjamena", "help_text": "Africa/Ndjamena"},
                        {"value": "Africa/Niamey", "help_text": "Africa/Niamey"},
                        {
                            "value": "Africa/Nouakchott",
                            "help_text": "Africa/Nouakchott",
                        },
                        {
                            "value": "Africa/Ouagadougou",
                            "help_text": "Africa/Ouagadougou",
                        },
                        {
                            "value": "Africa/Porto-Novo",
                            "help_text": "Africa/Porto-Novo",
                        },
                        {"value": "Africa/Sao_Tome", "help_text": "Africa/Sao Tome"},
                        {"value": "Africa/Tripoli", "help_text": "Africa/Tripoli"},
                        {"value": "Africa/Tunis", "help_text": "Africa/Tunis"},
                        {"value": "Africa/Windhoek", "help_text": "Africa/Windhoek"},
                        {"value": "America/Adak", "help_text": "America/Adak"},
                        {
                            "value": "America/Anchorage",
                            "help_text": "America/Anchorage",
                        },
                        {"value": "America/Anguilla", "help_text": "America/Anguilla"},
                        {"value": "America/Antigua", "help_text": "America/Antigua"},
                        {
                            "value": "America/Araguaina",
                            "help_text": "America/Araguaina",
                        },
                        {
                            "value": "America/Argentina/Buenos_Aires",
                            "help_text": "America/Argentina/Buenos Aires",
                        },
                        {
                            "value": "America/Argentina/Catamarca",
                            "help_text": "America/Argentina/Catamarca",
                        },
                        {
                            "value": "America/Argentina/Cordoba",
                            "help_text": "America/Argentina/Cordoba",
                        },
                        {
                            "value": "America/Argentina/Jujuy",
                            "help_text": "America/Argentina/Jujuy",
                        },
                        {
                            "value": "America/Argentina/La_Rioja",
                            "help_text": "America/Argentina/La Rioja",
                        },
                        {
                            "value": "America/Argentina/Mendoza",
                            "help_text": "America/Argentina/Mendoza",
                        },
                        {
                            "value": "America/Argentina/Rio_Gallegos",
                            "help_text": "America/Argentina/Rio Gallegos",
                        },
                        {
                            "value": "America/Argentina/Salta",
                            "help_text": "America/Argentina/Salta",
                        },
                        {
                            "value": "America/Argentina/San_Juan",
                            "help_text": "America/Argentina/San Juan",
                        },
                        {
                            "value": "America/Argentina/San_Luis",
                            "help_text": "America/Argentina/San Luis",
                        },
                        {
                            "value": "America/Argentina/Tucuman",
                            "help_text": "America/Argentina/Tucuman",
                        },
                        {
                            "value": "America/Argentina/Ushuaia",
                            "help_text": "America/Argentina/Ushuaia",
                        },
                        {"value": "America/Aruba", "help_text": "America/Aruba"},
                        {"value": "America/Asuncion", "help_text": "America/Asuncion"},
                        {"value": "America/Atikokan", "help_text": "America/Atikokan"},
                        {"value": "America/Bahia", "help_text": "America/Bahia"},
                        {
                            "value": "America/Bahia_Banderas",
                            "help_text": "America/Bahia Banderas",
                        },
                        {"value": "America/Barbados", "help_text": "America/Barbados"},
                        {"value": "America/Belem", "help_text": "America/Belem"},
                        {"value": "America/Belize", "help_text": "America/Belize"},
                        {
                            "value": "America/Blanc-Sablon",
                            "help_text": "America/Blanc-Sablon",
                        },
                        {
                            "value": "America/Boa_Vista",
                            "help_text": "America/Boa Vista",
                        },
                        {"value": "America/Bogota", "help_text": "America/Bogota"},
                        {"value": "America/Boise", "help_text": "America/Boise"},
                        {
                            "value": "America/Cambridge_Bay",
                            "help_text": "America/Cambridge Bay",
                        },
                        {
                            "value": "America/Campo_Grande",
                            "help_text": "America/Campo Grande",
                        },
                        {"value": "America/Cancun", "help_text": "America/Cancun"},
                        {"value": "America/Caracas", "help_text": "America/Caracas"},
                        {"value": "America/Cayenne", "help_text": "America/Cayenne"},
                        {"value": "America/Cayman", "help_text": "America/Cayman"},
                        {"value": "America/Chicago", "help_text": "America/Chicago"},
                        {
                            "value": "America/Chihuahua",
                            "help_text": "America/Chihuahua",
                        },
                        {
                            "help_text": "America/Ciudad Juarez",
                            "value": "America/Ciudad_Juarez",
                        },
                        {
                            "value": "America/Costa_Rica",
                            "help_text": "America/Costa Rica",
                        },
                        {"value": "America/Creston", "help_text": "America/Creston"},
                        {"value": "America/Cuiaba", "help_text": "America/Cuiaba"},
                        {"value": "America/Curacao", "help_text": "America/Curacao"},
                        {
                            "value": "America/Danmarkshavn",
                            "help_text": "America/Danmarkshavn",
                        },
                        {"value": "America/Dawson", "help_text": "America/Dawson"},
                        {
                            "value": "America/Dawson_Creek",
                            "help_text": "America/Dawson Creek",
                        },
                        {"value": "America/Denver", "help_text": "America/Denver"},
                        {"value": "America/Detroit", "help_text": "America/Detroit"},
                        {"value": "America/Dominica", "help_text": "America/Dominica"},
                        {"value": "America/Edmonton", "help_text": "America/Edmonton"},
                        {"value": "America/Eirunepe", "help_text": "America/Eirunepe"},
                        {
                            "value": "America/El_Salvador",
                            "help_text": "America/El Salvador",
                        },
                        {
                            "value": "America/Fort_Nelson",
                            "help_text": "America/Fort Nelson",
                        },
                        {
                            "value": "America/Fortaleza",
                            "help_text": "America/Fortaleza",
                        },
                        {
                            "value": "America/Glace_Bay",
                            "help_text": "America/Glace Bay",
                        },
                        {
                            "value": "America/Goose_Bay",
                            "help_text": "America/Goose Bay",
                        },
                        {
                            "value": "America/Grand_Turk",
                            "help_text": "America/Grand Turk",
                        },
                        {"value": "America/Grenada", "help_text": "America/Grenada"},
                        {
                            "value": "America/Guadeloupe",
                            "help_text": "America/Guadeloupe",
                        },
                        {
                            "value": "America/Guatemala",
                            "help_text": "America/Guatemala",
                        },
                        {
                            "value": "America/Guayaquil",
                            "help_text": "America/Guayaquil",
                        },
                        {"value": "America/Guyana", "help_text": "America/Guyana"},
                        {"value": "America/Halifax", "help_text": "America/Halifax"},
                        {"value": "America/Havana", "help_text": "America/Havana"},
                        {
                            "value": "America/Hermosillo",
                            "help_text": "America/Hermosillo",
                        },
                        {
                            "value": "America/Indiana/Indianapolis",
                            "help_text": "America/Indiana/Indianapolis",
                        },
                        {
                            "value": "America/Indiana/Knox",
                            "help_text": "America/Indiana/Knox",
                        },
                        {
                            "value": "America/Indiana/Marengo",
                            "help_text": "America/Indiana/Marengo",
                        },
                        {
                            "value": "America/Indiana/Petersburg",
                            "help_text": "America/Indiana/Petersburg",
                        },
                        {
                            "value": "America/Indiana/Tell_City",
                            "help_text": "America/Indiana/Tell City",
                        },
                        {
                            "value": "America/Indiana/Vevay",
                            "help_text": "America/Indiana/Vevay",
                        },
                        {
                            "value": "America/Indiana/Vincennes",
                            "help_text": "America/Indiana/Vincennes",
                        },
                        {
                            "value": "America/Indiana/Winamac",
                            "help_text": "America/Indiana/Winamac",
                        },
                        {"value": "America/Inuvik", "help_text": "America/Inuvik"},
                        {"value": "America/Iqaluit", "help_text": "America/Iqaluit"},
                        {"value": "America/Jamaica", "help_text": "America/Jamaica"},
                        {"value": "America/Juneau", "help_text": "America/Juneau"},
                        {
                            "value": "America/Kentucky/Louisville",
                            "help_text": "America/Kentucky/Louisville",
                        },
                        {
                            "value": "America/Kentucky/Monticello",
                            "help_text": "America/Kentucky/Monticello",
                        },
                        {
                            "value": "America/Kralendijk",
                            "help_text": "America/Kralendijk",
                        },
                        {"value": "America/La_Paz", "help_text": "America/La Paz"},
                        {"value": "America/Lima", "help_text": "America/Lima"},
                        {
                            "value": "America/Los_Angeles",
                            "help_text": "America/Los Angeles",
                        },
                        {
                            "value": "America/Lower_Princes",
                            "help_text": "America/Lower Princes",
                        },
                        {"value": "America/Maceio", "help_text": "America/Maceio"},
                        {"value": "America/Managua", "help_text": "America/Managua"},
                        {"value": "America/Manaus", "help_text": "America/Manaus"},
                        {"value": "America/Marigot", "help_text": "America/Marigot"},
                        {
                            "value": "America/Martinique",
                            "help_text": "America/Martinique",
                        },
                        {
                            "value": "America/Matamoros",
                            "help_text": "America/Matamoros",
                        },
                        {"value": "America/Mazatlan", "help_text": "America/Mazatlan"},
                        {
                            "value": "America/Menominee",
                            "help_text": "America/Menominee",
                        },
                        {"value": "America/Merida", "help_text": "America/Merida"},
                        {
                            "value": "America/Metlakatla",
                            "help_text": "America/Metlakatla",
                        },
                        {
                            "value": "America/Mexico_City",
                            "help_text": "America/Mexico City",
                        },
                        {"value": "America/Miquelon", "help_text": "America/Miquelon"},
                        {"value": "America/Moncton", "help_text": "America/Moncton"},
                        {
                            "value": "America/Monterrey",
                            "help_text": "America/Monterrey",
                        },
                        {
                            "value": "America/Montevideo",
                            "help_text": "America/Montevideo",
                        },
                        {
                            "value": "America/Montserrat",
                            "help_text": "America/Montserrat",
                        },
                        {"value": "America/Nassau", "help_text": "America/Nassau"},
                        {"value": "America/New_York", "help_text": "America/New York"},
                        {"value": "America/Nome", "help_text": "America/Nome"},
                        {"value": "America/Noronha", "help_text": "America/Noronha"},
                        {
                            "value": "America/North_Dakota/Beulah",
                            "help_text": "America/North Dakota/Beulah",
                        },
                        {
                            "value": "America/North_Dakota/Center",
                            "help_text": "America/North Dakota/Center",
                        },
                        {
                            "value": "America/North_Dakota/New_Salem",
                            "help_text": "America/North Dakota/New Salem",
                        },
                        {"value": "America/Nuuk", "help_text": "America/Nuuk"},
                        {"value": "America/Ojinaga", "help_text": "America/Ojinaga"},
                        {"value": "America/Panama", "help_text": "America/Panama"},
                        {
                            "value": "America/Paramaribo",
                            "help_text": "America/Paramaribo",
                        },
                        {"value": "America/Phoenix", "help_text": "America/Phoenix"},
                        {
                            "value": "America/Port-au-Prince",
                            "help_text": "America/Port-au-Prince",
                        },
                        {
                            "value": "America/Port_of_Spain",
                            "help_text": "America/Port of Spain",
                        },
                        {
                            "value": "America/Porto_Velho",
                            "help_text": "America/Porto Velho",
                        },
                        {
                            "value": "America/Puerto_Rico",
                            "help_text": "America/Puerto Rico",
                        },
                        {
                            "value": "America/Punta_Arenas",
                            "help_text": "America/Punta Arenas",
                        },
                        {
                            "value": "America/Rankin_Inlet",
                            "help_text": "America/Rankin Inlet",
                        },
                        {"value": "America/Recife", "help_text": "America/Recife"},
                        {"value": "America/Regina", "help_text": "America/Regina"},
                        {"value": "America/Resolute", "help_text": "America/Resolute"},
                        {
                            "value": "America/Rio_Branco",
                            "help_text": "America/Rio Branco",
                        },
                        {"value": "America/Santarem", "help_text": "America/Santarem"},
                        {"value": "America/Santiago", "help_text": "America/Santiago"},
                        {
                            "value": "America/Santo_Domingo",
                            "help_text": "America/Santo Domingo",
                        },
                        {
                            "value": "America/Sao_Paulo",
                            "help_text": "America/Sao Paulo",
                        },
                        {
                            "value": "America/Scoresbysund",
                            "help_text": "America/Scoresbysund",
                        },
                        {"value": "America/Sitka", "help_text": "America/Sitka"},
                        {
                            "value": "America/St_Barthelemy",
                            "help_text": "America/St Barthelemy",
                        },
                        {"value": "America/St_Johns", "help_text": "America/St Johns"},
                        {"value": "America/St_Kitts", "help_text": "America/St Kitts"},
                        {"value": "America/St_Lucia", "help_text": "America/St Lucia"},
                        {
                            "value": "America/St_Thomas",
                            "help_text": "America/St Thomas",
                        },
                        {
                            "value": "America/St_Vincent",
                            "help_text": "America/St Vincent",
                        },
                        {
                            "value": "America/Swift_Current",
                            "help_text": "America/Swift Current",
                        },
                        {
                            "value": "America/Tegucigalpa",
                            "help_text": "America/Tegucigalpa",
                        },
                        {"value": "America/Thule", "help_text": "America/Thule"},
                        {"value": "America/Tijuana", "help_text": "America/Tijuana"},
                        {"value": "America/Toronto", "help_text": "America/Toronto"},
                        {"value": "America/Tortola", "help_text": "America/Tortola"},
                        {
                            "value": "America/Vancouver",
                            "help_text": "America/Vancouver",
                        },
                        {
                            "value": "America/Whitehorse",
                            "help_text": "America/Whitehorse",
                        },
                        {"value": "America/Winnipeg", "help_text": "America/Winnipeg"},
                        {"value": "America/Yakutat", "help_text": "America/Yakutat"},
                        {"value": "Antarctica/Casey", "help_text": "Antarctica/Casey"},
                        {"value": "Antarctica/Davis", "help_text": "Antarctica/Davis"},
                        {
                            "value": "Antarctica/DumontDUrville",
                            "help_text": "Antarctica/DumontDUrville",
                        },
                        {
                            "value": "Antarctica/Macquarie",
                            "help_text": "Antarctica/Macquarie",
                        },
                        {
                            "value": "Antarctica/Mawson",
                            "help_text": "Antarctica/Mawson",
                        },
                        {
                            "value": "Antarctica/McMurdo",
                            "help_text": "Antarctica/McMurdo",
                        },
                        {
                            "value": "Antarctica/Palmer",
                            "help_text": "Antarctica/Palmer",
                        },
                        {
                            "value": "Antarctica/Rothera",
                            "help_text": "Antarctica/Rothera",
                        },
                        {"value": "Antarctica/Syowa", "help_text": "Antarctica/Syowa"},
                        {"value": "Antarctica/Troll", "help_text": "Antarctica/Troll"},
                        {
                            "value": "Antarctica/Vostok",
                            "help_text": "Antarctica/Vostok",
                        },
                        {
                            "value": "Arctic/Longyearbyen",
                            "help_text": "Arctic/Longyearbyen",
                        },
                        {"value": "Asia/Aden", "help_text": "Asia/Aden"},
                        {"value": "Asia/Almaty", "help_text": "Asia/Almaty"},
                        {"value": "Asia/Amman", "help_text": "Asia/Amman"},
                        {"value": "Asia/Anadyr", "help_text": "Asia/Anadyr"},
                        {"value": "Asia/Aqtau", "help_text": "Asia/Aqtau"},
                        {"value": "Asia/Aqtobe", "help_text": "Asia/Aqtobe"},
                        {"value": "Asia/Ashgabat", "help_text": "Asia/Ashgabat"},
                        {"value": "Asia/Atyrau", "help_text": "Asia/Atyrau"},
                        {"value": "Asia/Baghdad", "help_text": "Asia/Baghdad"},
                        {"value": "Asia/Bahrain", "help_text": "Asia/Bahrain"},
                        {"value": "Asia/Baku", "help_text": "Asia/Baku"},
                        {"value": "Asia/Bangkok", "help_text": "Asia/Bangkok"},
                        {"value": "Asia/Barnaul", "help_text": "Asia/Barnaul"},
                        {"value": "Asia/Beirut", "help_text": "Asia/Beirut"},
                        {"value": "Asia/Bishkek", "help_text": "Asia/Bishkek"},
                        {"value": "Asia/Brunei", "help_text": "Asia/Brunei"},
                        {"value": "Asia/Chita", "help_text": "Asia/Chita"},
                        {"value": "Asia/Choibalsan", "help_text": "Asia/Choibalsan"},
                        {"value": "Asia/Colombo", "help_text": "Asia/Colombo"},
                        {"value": "Asia/Damascus", "help_text": "Asia/Damascus"},
                        {"value": "Asia/Dhaka", "help_text": "Asia/Dhaka"},
                        {"value": "Asia/Dili", "help_text": "Asia/Dili"},
                        {"value": "Asia/Dubai", "help_text": "Asia/Dubai"},
                        {"value": "Asia/Dushanbe", "help_text": "Asia/Dushanbe"},
                        {"value": "Asia/Famagusta", "help_text": "Asia/Famagusta"},
                        {"value": "Asia/Gaza", "help_text": "Asia/Gaza"},
                        {"value": "Asia/Hebron", "help_text": "Asia/Hebron"},
                        {"value": "Asia/Ho_Chi_Minh", "help_text": "Asia/Ho Chi Minh"},
                        {"value": "Asia/Hong_Kong", "help_text": "Asia/Hong Kong"},
                        {"value": "Asia/Hovd", "help_text": "Asia/Hovd"},
                        {"value": "Asia/Irkutsk", "help_text": "Asia/Irkutsk"},
                        {"value": "Asia/Jakarta", "help_text": "Asia/Jakarta"},
                        {"value": "Asia/Jayapura", "help_text": "Asia/Jayapura"},
                        {"value": "Asia/Jerusalem", "help_text": "Asia/Jerusalem"},
                        {"value": "Asia/Kabul", "help_text": "Asia/Kabul"},
                        {"value": "Asia/Kamchatka", "help_text": "Asia/Kamchatka"},
                        {"value": "Asia/Karachi", "help_text": "Asia/Karachi"},
                        {"value": "Asia/Kathmandu", "help_text": "Asia/Kathmandu"},
                        {"value": "Asia/Khandyga", "help_text": "Asia/Khandyga"},
                        {"value": "Asia/Kolkata", "help_text": "Asia/Kolkata"},
                        {"value": "Asia/Krasnoyarsk", "help_text": "Asia/Krasnoyarsk"},
                        {
                            "value": "Asia/Kuala_Lumpur",
                            "help_text": "Asia/Kuala Lumpur",
                        },
                        {"value": "Asia/Kuching", "help_text": "Asia/Kuching"},
                        {"value": "Asia/Kuwait", "help_text": "Asia/Kuwait"},
                        {"value": "Asia/Macau", "help_text": "Asia/Macau"},
                        {"value": "Asia/Magadan", "help_text": "Asia/Magadan"},
                        {"value": "Asia/Makassar", "help_text": "Asia/Makassar"},
                        {"value": "Asia/Manila", "help_text": "Asia/Manila"},
                        {"value": "Asia/Muscat", "help_text": "Asia/Muscat"},
                        {"value": "Asia/Nicosia", "help_text": "Asia/Nicosia"},
                        {
                            "value": "Asia/Novokuznetsk",
                            "help_text": "Asia/Novokuznetsk",
                        },
                        {"value": "Asia/Novosibirsk", "help_text": "Asia/Novosibirsk"},
                        {"value": "Asia/Omsk", "help_text": "Asia/Omsk"},
                        {"value": "Asia/Oral", "help_text": "Asia/Oral"},
                        {"value": "Asia/Phnom_Penh", "help_text": "Asia/Phnom Penh"},
                        {"value": "Asia/Pontianak", "help_text": "Asia/Pontianak"},
                        {"value": "Asia/Pyongyang", "help_text": "Asia/Pyongyang"},
                        {"value": "Asia/Qatar", "help_text": "Asia/Qatar"},
                        {"value": "Asia/Qostanay", "help_text": "Asia/Qostanay"},
                        {"value": "Asia/Qyzylorda", "help_text": "Asia/Qyzylorda"},
                        {"value": "Asia/Riyadh", "help_text": "Asia/Riyadh"},
                        {"value": "Asia/Sakhalin", "help_text": "Asia/Sakhalin"},
                        {"value": "Asia/Samarkand", "help_text": "Asia/Samarkand"},
                        {"value": "Asia/Seoul", "help_text": "Asia/Seoul"},
                        {"value": "Asia/Shanghai", "help_text": "Asia/Shanghai"},
                        {"value": "Asia/Singapore", "help_text": "Asia/Singapore"},
                        {
                            "value": "Asia/Srednekolymsk",
                            "help_text": "Asia/Srednekolymsk",
                        },
                        {"value": "Asia/Taipei", "help_text": "Asia/Taipei"},
                        {"value": "Asia/Tashkent", "help_text": "Asia/Tashkent"},
                        {"value": "Asia/Tbilisi", "help_text": "Asia/Tbilisi"},
                        {"value": "Asia/Tehran", "help_text": "Asia/Tehran"},
                        {"value": "Asia/Thimphu", "help_text": "Asia/Thimphu"},
                        {"value": "Asia/Tokyo", "help_text": "Asia/Tokyo"},
                        {"value": "Asia/Tomsk", "help_text": "Asia/Tomsk"},
                        {"value": "Asia/Ulaanbaatar", "help_text": "Asia/Ulaanbaatar"},
                        {"value": "Asia/Urumqi", "help_text": "Asia/Urumqi"},
                        {"value": "Asia/Ust-Nera", "help_text": "Asia/Ust-Nera"},
                        {"value": "Asia/Vientiane", "help_text": "Asia/Vientiane"},
                        {"value": "Asia/Vladivostok", "help_text": "Asia/Vladivostok"},
                        {"value": "Asia/Yakutsk", "help_text": "Asia/Yakutsk"},
                        {"value": "Asia/Yangon", "help_text": "Asia/Yangon"},
                        {
                            "value": "Asia/Yekaterinburg",
                            "help_text": "Asia/Yekaterinburg",
                        },
                        {"value": "Asia/Yerevan", "help_text": "Asia/Yerevan"},
                        {"value": "Atlantic/Azores", "help_text": "Atlantic/Azores"},
                        {"value": "Atlantic/Bermuda", "help_text": "Atlantic/Bermuda"},
                        {"value": "Atlantic/Canary", "help_text": "Atlantic/Canary"},
                        {
                            "value": "Atlantic/Cape_Verde",
                            "help_text": "Atlantic/Cape Verde",
                        },
                        {"value": "Atlantic/Faroe", "help_text": "Atlantic/Faroe"},
                        {"value": "Atlantic/Madeira", "help_text": "Atlantic/Madeira"},
                        {
                            "value": "Atlantic/Reykjavik",
                            "help_text": "Atlantic/Reykjavik",
                        },
                        {
                            "value": "Atlantic/South_Georgia",
                            "help_text": "Atlantic/South Georgia",
                        },
                        {
                            "value": "Atlantic/St_Helena",
                            "help_text": "Atlantic/St Helena",
                        },
                        {"value": "Atlantic/Stanley", "help_text": "Atlantic/Stanley"},
                        {
                            "value": "Australia/Adelaide",
                            "help_text": "Australia/Adelaide",
                        },
                        {
                            "value": "Australia/Brisbane",
                            "help_text": "Australia/Brisbane",
                        },
                        {
                            "value": "Australia/Broken_Hill",
                            "help_text": "Australia/Broken Hill",
                        },
                        {"value": "Australia/Darwin", "help_text": "Australia/Darwin"},
                        {"value": "Australia/Eucla", "help_text": "Australia/Eucla"},
                        {"value": "Australia/Hobart", "help_text": "Australia/Hobart"},
                        {
                            "value": "Australia/Lindeman",
                            "help_text": "Australia/Lindeman",
                        },
                        {
                            "value": "Australia/Lord_Howe",
                            "help_text": "Australia/Lord Howe",
                        },
                        {
                            "value": "Australia/Melbourne",
                            "help_text": "Australia/Melbourne",
                        },
                        {"value": "Australia/Perth", "help_text": "Australia/Perth"},
                        {"value": "Australia/Sydney", "help_text": "Australia/Sydney"},
                        {"value": "Canada/Atlantic", "help_text": "Canada/Atlantic"},
                        {"value": "Canada/Central", "help_text": "Canada/Central"},
                        {"value": "Canada/Eastern", "help_text": "Canada/Eastern"},
                        {"value": "Canada/Mountain", "help_text": "Canada/Mountain"},
                        {
                            "value": "Canada/Newfoundland",
                            "help_text": "Canada/Newfoundland",
                        },
                        {"value": "Canada/Pacific", "help_text": "Canada/Pacific"},
                        {"value": "Europe/Amsterdam", "help_text": "Europe/Amsterdam"},
                        {"value": "Europe/Andorra", "help_text": "Europe/Andorra"},
                        {"value": "Europe/Astrakhan", "help_text": "Europe/Astrakhan"},
                        {"value": "Europe/Athens", "help_text": "Europe/Athens"},
                        {"value": "Europe/Belgrade", "help_text": "Europe/Belgrade"},
                        {"value": "Europe/Berlin", "help_text": "Europe/Berlin"},
                        {
                            "value": "Europe/Bratislava",
                            "help_text": "Europe/Bratislava",
                        },
                        {"value": "Europe/Brussels", "help_text": "Europe/Brussels"},
                        {"value": "Europe/Bucharest", "help_text": "Europe/Bucharest"},
                        {"value": "Europe/Budapest", "help_text": "Europe/Budapest"},
                        {"value": "Europe/Busingen", "help_text": "Europe/Busingen"},
                        {"value": "Europe/Chisinau", "help_text": "Europe/Chisinau"},
                        {
                            "value": "Europe/Copenhagen",
                            "help_text": "Europe/Copenhagen",
                        },
                        {"value": "Europe/Dublin", "help_text": "Europe/Dublin"},
                        {"value": "Europe/Gibraltar", "help_text": "Europe/Gibraltar"},
                        {"value": "Europe/Guernsey", "help_text": "Europe/Guernsey"},
                        {"value": "Europe/Helsinki", "help_text": "Europe/Helsinki"},
                        {
                            "value": "Europe/Isle_of_Man",
                            "help_text": "Europe/Isle of Man",
                        },
                        {"value": "Europe/Istanbul", "help_text": "Europe/Istanbul"},
                        {"value": "Europe/Jersey", "help_text": "Europe/Jersey"},
                        {
                            "value": "Europe/Kaliningrad",
                            "help_text": "Europe/Kaliningrad",
                        },
                        {"value": "Europe/Kirov", "help_text": "Europe/Kirov"},
                        {"value": "Europe/Kyiv", "help_text": "Europe/Kyiv"},
                        {"value": "Europe/Lisbon", "help_text": "Europe/Lisbon"},
                        {"value": "Europe/Ljubljana", "help_text": "Europe/Ljubljana"},
                        {"value": "Europe/London", "help_text": "Europe/London"},
                        {
                            "value": "Europe/Luxembourg",
                            "help_text": "Europe/Luxembourg",
                        },
                        {"value": "Europe/Madrid", "help_text": "Europe/Madrid"},
                        {"value": "Europe/Malta", "help_text": "Europe/Malta"},
                        {"value": "Europe/Mariehamn", "help_text": "Europe/Mariehamn"},
                        {"value": "Europe/Minsk", "help_text": "Europe/Minsk"},
                        {"value": "Europe/Monaco", "help_text": "Europe/Monaco"},
                        {"value": "Europe/Moscow", "help_text": "Europe/Moscow"},
                        {"value": "Europe/Oslo", "help_text": "Europe/Oslo"},
                        {"value": "Europe/Paris", "help_text": "Europe/Paris"},
                        {"value": "Europe/Podgorica", "help_text": "Europe/Podgorica"},
                        {"value": "Europe/Prague", "help_text": "Europe/Prague"},
                        {"value": "Europe/Riga", "help_text": "Europe/Riga"},
                        {"value": "Europe/Rome", "help_text": "Europe/Rome"},
                        {"value": "Europe/Samara", "help_text": "Europe/Samara"},
                        {
                            "value": "Europe/San_Marino",
                            "help_text": "Europe/San Marino",
                        },
                        {"value": "Europe/Sarajevo", "help_text": "Europe/Sarajevo"},
                        {"value": "Europe/Saratov", "help_text": "Europe/Saratov"},
                        {
                            "value": "Europe/Simferopol",
                            "help_text": "Europe/Simferopol",
                        },
                        {"value": "Europe/Skopje", "help_text": "Europe/Skopje"},
                        {"value": "Europe/Sofia", "help_text": "Europe/Sofia"},
                        {"value": "Europe/Stockholm", "help_text": "Europe/Stockholm"},
                        {"value": "Europe/Tallinn", "help_text": "Europe/Tallinn"},
                        {"value": "Europe/Tirane", "help_text": "Europe/Tirane"},
                        {"value": "Europe/Ulyanovsk", "help_text": "Europe/Ulyanovsk"},
                        {"value": "Europe/Vaduz", "help_text": "Europe/Vaduz"},
                        {"value": "Europe/Vatican", "help_text": "Europe/Vatican"},
                        {"value": "Europe/Vienna", "help_text": "Europe/Vienna"},
                        {"value": "Europe/Vilnius", "help_text": "Europe/Vilnius"},
                        {"value": "Europe/Volgograd", "help_text": "Europe/Volgograd"},
                        {"value": "Europe/Warsaw", "help_text": "Europe/Warsaw"},
                        {"value": "Europe/Zagreb", "help_text": "Europe/Zagreb"},
                        {"value": "Europe/Zurich", "help_text": "Europe/Zurich"},
                        {"value": "GMT", "help_text": "GMT"},
                        {
                            "value": "Indian/Antananarivo",
                            "help_text": "Indian/Antananarivo",
                        },
                        {"value": "Indian/Chagos", "help_text": "Indian/Chagos"},
                        {"value": "Indian/Christmas", "help_text": "Indian/Christmas"},
                        {"value": "Indian/Cocos", "help_text": "Indian/Cocos"},
                        {"value": "Indian/Comoro", "help_text": "Indian/Comoro"},
                        {"value": "Indian/Kerguelen", "help_text": "Indian/Kerguelen"},
                        {"value": "Indian/Mahe", "help_text": "Indian/Mahe"},
                        {"value": "Indian/Maldives", "help_text": "Indian/Maldives"},
                        {"value": "Indian/Mauritius", "help_text": "Indian/Mauritius"},
                        {"value": "Indian/Mayotte", "help_text": "Indian/Mayotte"},
                        {"value": "Indian/Reunion", "help_text": "Indian/Reunion"},
                        {"value": "Pacific/Apia", "help_text": "Pacific/Apia"},
                        {"value": "Pacific/Auckland", "help_text": "Pacific/Auckland"},
                        {
                            "value": "Pacific/Bougainville",
                            "help_text": "Pacific/Bougainville",
                        },
                        {"value": "Pacific/Chatham", "help_text": "Pacific/Chatham"},
                        {"value": "Pacific/Chuuk", "help_text": "Pacific/Chuuk"},
                        {"value": "Pacific/Easter", "help_text": "Pacific/Easter"},
                        {"value": "Pacific/Efate", "help_text": "Pacific/Efate"},
                        {"value": "Pacific/Fakaofo", "help_text": "Pacific/Fakaofo"},
                        {"value": "Pacific/Fiji", "help_text": "Pacific/Fiji"},
                        {"value": "Pacific/Funafuti", "help_text": "Pacific/Funafuti"},
                        {
                            "value": "Pacific/Galapagos",
                            "help_text": "Pacific/Galapagos",
                        },
                        {"value": "Pacific/Gambier", "help_text": "Pacific/Gambier"},
                        {
                            "value": "Pacific/Guadalcanal",
                            "help_text": "Pacific/Guadalcanal",
                        },
                        {"value": "Pacific/Guam", "help_text": "Pacific/Guam"},
                        {"value": "Pacific/Honolulu", "help_text": "Pacific/Honolulu"},
                        {"value": "Pacific/Kanton", "help_text": "Pacific/Kanton"},
                        {
                            "value": "Pacific/Kiritimati",
                            "help_text": "Pacific/Kiritimati",
                        },
                        {"value": "Pacific/Kosrae", "help_text": "Pacific/Kosrae"},
                        {
                            "value": "Pacific/Kwajalein",
                            "help_text": "Pacific/Kwajalein",
                        },
                        {"value": "Pacific/Majuro", "help_text": "Pacific/Majuro"},
                        {
                            "value": "Pacific/Marquesas",
                            "help_text": "Pacific/Marquesas",
                        },
                        {"value": "Pacific/Midway", "help_text": "Pacific/Midway"},
                        {"value": "Pacific/Nauru", "help_text": "Pacific/Nauru"},
                        {"value": "Pacific/Niue", "help_text": "Pacific/Niue"},
                        {"value": "Pacific/Norfolk", "help_text": "Pacific/Norfolk"},
                        {"value": "Pacific/Noumea", "help_text": "Pacific/Noumea"},
                        {
                            "value": "Pacific/Pago_Pago",
                            "help_text": "Pacific/Pago Pago",
                        },
                        {"value": "Pacific/Palau", "help_text": "Pacific/Palau"},
                        {"value": "Pacific/Pitcairn", "help_text": "Pacific/Pitcairn"},
                        {"value": "Pacific/Pohnpei", "help_text": "Pacific/Pohnpei"},
                        {
                            "value": "Pacific/Port_Moresby",
                            "help_text": "Pacific/Port Moresby",
                        },
                        {
                            "value": "Pacific/Rarotonga",
                            "help_text": "Pacific/Rarotonga",
                        },
                        {"value": "Pacific/Saipan", "help_text": "Pacific/Saipan"},
                        {"value": "Pacific/Tahiti", "help_text": "Pacific/Tahiti"},
                        {"value": "Pacific/Tarawa", "help_text": "Pacific/Tarawa"},
                        {
                            "value": "Pacific/Tongatapu",
                            "help_text": "Pacific/Tongatapu",
                        },
                        {"value": "Pacific/Wake", "help_text": "Pacific/Wake"},
                        {"value": "Pacific/Wallis", "help_text": "Pacific/Wallis"},
                        {"value": "US/Alaska", "help_text": "US/Alaska"},
                        {"value": "US/Arizona", "help_text": "US/Arizona"},
                        {"value": "US/Central", "help_text": "US/Central"},
                        {"value": "US/Eastern", "help_text": "US/Eastern"},
                        {"value": "US/Hawaii", "help_text": "US/Hawaii"},
                        {"value": "US/Mountain", "help_text": "US/Mountain"},
                        {"value": "US/Pacific", "help_text": "US/Pacific"},
                        {"value": "UTC", "help_text": "UTC"},
                    ],
                    "choices_to_hide": [],
                    "creatable": True,
                    "default": "Europe/Paris",
                    "editable": False,
                    "help_text": "Time zone of the application",
                    "max_length": 63,
                    "name": "Timezone",
                    "nullable": False,
                    "readonly": True,
                    "required": False,
                    "type": "string",
                    "unique": False,
                    "virtual_choices": [],
                },
                "website": {
                    "blank": False,
                    "creatable": True,
                    "default": "",
                    "editable": True,
                    "help_text": "",
                    "max_length": 200,
                    "name": "Website url",
                    "nullable": False,
                    "readonly": False,
                    "required": False,
                    "type": "string",
                    "unique": False,
                },
            },
            "filtering": {
                "contact_user": "exact",
                "created_on": 1,
                "domain_id": 1,
                "environment": 1,
                "id": 1,
                "last_modified": 1,
                "name": 1,
                "namespace": "iexact",
                "status": 1,
            },
            "methods": [
                {
                    "allowed_http_methods": ["get", "post"],
                    "doc_html": "",
                    "doc_text": "",
                    "regex": "/v1/application/:id/activate/",
                },
                {
                    "allowed_http_methods": ["post"],
                    "doc_html": "",
                    "doc_text": "",
                    "regex": "/v1/application/:id/link-domain/",
                },
                {
                    "allowed_http_methods": ["get"],
                    "doc_html": "",
                    "doc_text": "",
                    "regex": "/v1/application/mine/",
                },
                {
                    "allowed_http_methods": ["get", "post"],
                    "doc_html": "",
                    "doc_text": "",
                    "regex": "/v1/application/:id/pause/",
                },
                {
                    "allowed_http_methods": ["post"],
                    "doc_html": "",
                    "doc_text": "",
                    "regex": "/v1/application/:id/reset-pim-data/",
                },
                {
                    "allowed_http_methods": ["get"],
                    "doc_html": "",
                    "doc_text": "",
                    "regex": "/v1/application/:id/settings/",
                },
                {
                    "allowed_http_methods": ["post"],
                    "doc_html": "",
                    "doc_text": "",
                    "regex": "/v1/application/:id/unlink-domain/",
                },
            ],
            "ordering": [
                "contact_user",
                "created_on",
                "domain_id",
                "environment",
                "id",
                "name",
                "namespace",
                "status",
            ],
        }

        self.assertEqual(data, expected_data)
