# -*- coding: utf-8 -*-
import json
import os

from apps.cart.tests.test_cart_setup_mixin import CartTestSetupMixin
from apps.ice_applications.tests import ApplicationSettingsTestsSetupMixin
from apps.mp_messages.models import Message
from apps.mp_messages.tests.message_setup_mixin import MessageTestSetupMixin
from apps.testing.factories import (
    ApplicationFactory,
    MerchantFactory,
    MerchantToAppMessageFactory,
    MessageLabelFactory,
    MessageTypeFactory,
)
from django.test.client import MULTIPART_CONTENT
from django.test.utils import override_settings
from django.utils import timezone
from ims.tests import BaseResourceTestCase
from ims.tests.jwt_tokens import GLOBAL_TOKENS

from ..api.resources import MessageResource


class TestMessageResource(
    CartTestSetupMixin, MessageTestSetupMixin, BaseResourceTestCase
):
    def test_creatable_fields(self):
        res = MessageResource()
        self.assertListEqual(
            res._meta.creatable_fields,
            [
                "application",
                "sender",
                "receiver",
                "merchant_order",
                "subject",
                "body",
                "parent_msg",
            ],
        )

    def test_create_message_with_receiver_none(self):
        CartTestSetupMixin.set_up(self)
        self.msg = self.create_message(self.cart_user, self.application)
        resp = self.api_client.post(
            "/v1/message/",
            data={
                "sender": {
                    "id": self.cart_user.id,
                    "resource_uri": "/v1/user/{}/".format(self.cart_user.id),
                },
                "receiver": None,
                "subject": "Message subject",
                "body": "This is the body of the message",
            },
            authentication=self.get_token(self.cart_user),
        )

        self.assertHttpBadRequest(resp)
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["This field cannot be blank."], "field": "receiver"}]},
        )

    def test_create_message_with_no_receiver(self):
        CartTestSetupMixin.set_up(self)
        self.msg = self.create_message(self.cart_user, self.application)
        resp = self.api_client.post(
            "/v1/message/",
            data={
                "sender": {
                    "id": self.cart_user.id,
                    "resource_uri": "/v1/user/{}/".format(self.cart_user.id),
                },
                "subject": "Message subject",
                "body": "This is the body of the message",
            },
            authentication=self.get_token(self.cart_user),
        )

        self.assertHttpBadRequest(resp)
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["This field cannot be blank."], "field": "receiver"}]},
        )

    def test_create_message_with_sender_none(self):
        CartTestSetupMixin.set_up(self)
        self.msg = self.create_message(self.cart_user, self.application)
        resp = self.api_client.post(
            "/v1/message/",
            data={
                "sender": None,
                "receiver": {
                    "id": self.application.id,
                    "resource_uri": "/v1/application/{}/".format(self.application.id),
                },
                "subject": "Message subject",
                "body": "This is the body of the message",
            },
            authentication=self.get_token(self.cart_user),
        )

        self.assertHttpBadRequest(resp)
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["This field cannot be blank."], "field": "sender"}]},
        )

    def test_create_message_with_no_sender(self):
        CartTestSetupMixin.set_up(self)
        self.msg = self.create_message(self.cart_user, self.application)
        resp = self.api_client.post(
            "/v1/message/",
            data={
                "sender": {
                    "id": self.application.id,
                    "resource_uri": "/v1/application/{}/".format(self.application.id),
                },
                "subject": "Message subject",
                "body": "This is the body of the message",
            },
            authentication=self.get_token(self.cart_user),
        )

        self.assertHttpBadRequest(resp)
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["This field cannot be blank."], "field": "receiver"}]},
        )


class TestMessageUserToAppResource(
    CartTestSetupMixin, MessageTestSetupMixin, BaseResourceTestCase
):
    """user -> app"""

    def setUp(self):
        super(TestMessageUserToAppResource, self).setUp()
        CartTestSetupMixin.set_up(self)
        self.msg = self.create_message(self.cart_user, self.application)

    def test_get_message_unauthorized(self):
        resp = self.api_client.get("/v1/message/{}/".format(self.msg.id))
        self.assertHttpUnauthorized(resp)

    def test_get_messages_as_app_admin(self):
        resp = self.api_client.get(
            "/v1/message/", authentication=self.get_app_admin_token()
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["objects"][0]["id"], self.msg.id)

    def test_get_messages_as_izberg_staff(self):
        resp = self.api_client.get(
            "/v1/message/",
            authentication=self.get_token(self.izberg_user, app_token=False),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["objects"][0]["id"], self.msg.id)

    def test_get_message_as_app_admin(self):
        resp = self.api_client.get(
            "/v1/message/{}/".format(self.msg.id),
            authentication=self.get_app_admin_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["id"], self.msg.id)

    def test_get_messages_as_app_user(self):
        resp = self.api_client.get(
            "/v1/message/", authentication=self.get_token(self.cart_user)
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["objects"][0]["id"], self.msg.id)

    def test_get_messages_as_user(self):
        resp = self.api_client.get(
            "/v1/message/",
            authentication=self.get_token(self.cart_user, app_token=False),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["objects"][0]["id"], self.msg.id)

    def test_get_message_as_user(self):
        resp = self.api_client.get(
            "/v1/message/{}/".format(self.msg.id),
            authentication=self.get_token(self.cart_user),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["id"], self.msg.id)

    def test_create_message(self):
        resp = self.api_client.post(
            "/v1/message/",
            data={
                "sender": {
                    "id": self.cart_user.id,
                    "resource_uri": "/v1/user/{}/".format(self.cart_user.id),
                },
                "receiver": {
                    "id": self.application.id,
                    "resource_uri": "/v1/application/{}/".format(self.application.id),
                },
                "subject": "Message subject",
                "body": "This is the body of the message",
            },
            authentication=self.get_token(self.cart_user),
        )
        self.assertHttpCreated(resp)
        data = self.deserialize(resp)
        self.assertEqual(
            data["from_resource_uri"], "/v1/user/{}/".format(self.cart_user.id)
        )
        self.assertEqual(
            data["to_resource_uri"], "/v1/application/{}/".format(self.application.id)
        )
        self.assertEqual(
            # root message should be itself
            data["root_msg"],
            data["resource_uri"],
        )
        self.assertEqual(data["parent_msg"], None)

    def test_create_message_as_app_staff_for_user_fails(self):
        resp = self.api_client.post(
            "/v1/message/",
            data={
                "sender": {
                    "id": self.cart_user.id,
                    "resource_uri": "/v1/user/{}/".format(self.cart_user.id),
                },
                "receiver": {
                    "id": self.application.id,
                    "resource_uri": "/v1/application/{}/".format(self.application.id),
                },
                "subject": "Message subject",
                "body": "This is the body of the message",
            },
            authentication=self.get_app_admin_token(),
        )
        self.assertHttpUnauthorized(resp)

    def test_create_message_with_no_subject_is_accepted(self):
        resp = self.api_client.post(
            "/v1/message/",
            data={
                "sender": {
                    "id": self.cart_user.id,
                    "resource_uri": "/v1/user/{}/".format(self.cart_user.id),
                },
                "receiver": {
                    "id": self.application.id,
                    "resource_uri": "/v1/application/{}/".format(self.application.id),
                },
                "body": "This is the bôdy of the message",
            },
            authentication=self.get_token(self.cart_user),
        )
        self.assertHttpCreated(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["subject"], "")

    def test_create_message_with_no_body_returns_400(self):
        resp = self.api_client.post(
            "/v1/message/",
            data={
                "sender": {
                    "id": self.cart_user.id,
                    "resource_uri": "/v1/user/{}/".format(self.cart_user.id),
                },
                "receiver": {
                    "id": self.application.id,
                    "resource_uri": "/v1/application/{}/".format(self.application.id),
                },
                "sujet": "This is the sûbjeçt of the message",
            },
            authentication=self.get_token(self.cart_user),
        )
        self.assertHttpBadRequest(resp)


class TestMessageAppToUserResource(
    CartTestSetupMixin, MessageTestSetupMixin, BaseResourceTestCase
):
    """app -> user"""

    def setUp(self):
        super(TestMessageAppToUserResource, self).setUp()
        CartTestSetupMixin.set_up(self)
        self.msg = self.create_message(self.application, self.cart_user)

    def test_get_message_unauthorized(self):
        resp = self.api_client.get("/v1/message/{}/".format(self.msg.id))
        self.assertHttpUnauthorized(resp)

    def test_get_messages_as_izberg_staff(self):
        resp = self.api_client.get(
            "/v1/message/",
            authentication=self.get_token(self.izberg_user, app_token=False),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["objects"][0]["id"], self.msg.id)

    def test_get_messages_as_app_admin(self):
        resp = self.api_client.get(
            "/v1/message/", authentication=self.get_app_admin_token()
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["objects"][0]["id"], self.msg.id)

    def test_get_message_as_app_admin(self):
        resp = self.api_client.get(
            "/v1/message/{}/".format(self.msg.id),
            authentication=self.get_app_admin_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["id"], self.msg.id)

    def test_get_messages_as_app_user(self):
        resp = self.api_client.get(
            "/v1/message/", authentication=self.get_token(self.cart_user)
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["objects"][0]["id"], self.msg.id)

    def test_get_messages_as_user(self):
        resp = self.api_client.get(
            "/v1/message/",
            authentication=self.get_token(self.cart_user, app_token=False),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["objects"][0]["id"], self.msg.id)

    def test_get_message_as_user(self):
        resp = self.api_client.get(
            "/v1/message/{}/".format(self.msg.id),
            authentication=self.get_token(self.cart_user),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["id"], self.msg.id)

    def test_create_message(self):
        resp = self.api_client.post(
            "/v1/message/",
            data={
                "sender": {
                    "id": self.application.id,
                    "resource_uri": "/v1/application/{}/".format(self.application.id),
                },
                "receiver": {
                    "id": self.cart_user.id,
                    "resource_uri": "/v1/user/{}/".format(self.cart_user.id),
                },
                "subject": "Message subject",
                "body": "This is the body of the message",
            },
            authentication=self.get_app_admin_token(),
        )
        self.assertHttpCreated(resp)
        data = self.deserialize(resp)
        self.assertEqual(
            data["from_resource_uri"], "/v1/application/{}/".format(self.application.id)
        )
        self.assertEqual(
            data["to_resource_uri"], "/v1/user/{}/".format(self.cart_user.id)
        )
        self.assertEqual(
            # root message should be itself
            data["root_msg"],
            data["resource_uri"],
        )

    def test_get_messages_aggregate_count_root_msg(self):
        resp = self.api_client.get(
            "/v1/message/?aggregate_count_on=root_msg",
            authentication=self.get_token(self.cart_user),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["aggregate_count"][0]["count"], 1)


class TestMessageMerchantToUserResource(
    CartTestSetupMixin, MessageTestSetupMixin, BaseResourceTestCase
):
    """merchant -> user"""

    def setUp(self):
        super(TestMessageMerchantToUserResource, self).setUp()
        CartTestSetupMixin.set_up(self)
        self.msg = self.create_message(self.merchant, self.cart_user)

    def test_get_message_unauthorized(self):
        resp = self.api_client.get("/v1/message/{}/".format(self.msg.id))
        self.assertHttpUnauthorized(resp)

    def test_get_messages_as_izberg_staff(self):
        resp = self.api_client.get(
            "/v1/message/",
            authentication=self.get_token(self.izberg_user, app_token=False),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["objects"][0]["id"], self.msg.id)

    def test_get_messages_as_merchant(self):
        resp = self.api_client.get(
            "/v1/message/", authentication=self.get_merchant_token()
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["objects"][0]["id"], self.msg.id)

    def test_get_message_as_merchant(self):
        resp = self.api_client.get(
            "/v1/message/{}/".format(self.msg.id),
            authentication=self.get_merchant_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["id"], self.msg.id)

    def test_get_messages_as_app_user(self):
        resp = self.api_client.get(
            "/v1/message/", authentication=self.get_token(self.cart_user)
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["objects"][0]["id"], self.msg.id)

    def test_get_messages_as_user(self):
        resp = self.api_client.get(
            "/v1/message/",
            authentication=self.get_token(self.cart_user, app_token=False),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["objects"][0]["id"], self.msg.id)

    def test_get_message_as_user(self):
        resp = self.api_client.get(
            "/v1/message/{}/".format(self.msg.id),
            authentication=self.get_token(self.cart_user),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["id"], self.msg.id)

    def test_create_message(self):
        resp = self.api_client.post(
            "/v1/message/",
            data={
                "sender": {
                    "id": self.merchant.id,
                    "resource_uri": "/v1/merchant/{}/".format(self.merchant.id),
                },
                "receiver": {
                    "id": self.cart_user.id,
                    "resource_uri": "/v1/user/{}/".format(self.cart_user.id),
                },
                "subject": "Message subject",
                "body": "This is the body of the message",
                "application": "/v1/application/{}/".format(self.application.id),
            },
            authentication=self.get_merchant_token(),
        )
        self.assertHttpCreated(resp)
        data = self.deserialize(resp)
        self.assertEqual(
            data["from_resource_uri"], "/v1/merchant/{}/".format(self.merchant.id)
        )
        self.assertEqual(
            data["to_resource_uri"], "/v1/user/{}/".format(self.cart_user.id)
        )
        self.assertEqual(
            # root message should be itself
            data["root_msg"],
            data["resource_uri"],
        )

    def test_create_message_with_links(self):
        body = (
            "first link : http://www.link.com "
            "and second link : https://www.secured-link.com"
        )
        expected_body = (
            "first link : "
            '<a href="http://www.link.com">http://www.link.com</a> '
            "and second link : "
            '<a href="https://www.secured-link.com">https://www.secured-link.com</a>'
        )
        resp = self.api_client.post(
            "/v1/message/",
            data={
                "sender": {
                    "id": self.merchant.id,
                    "resource_uri": "/v1/merchant/{}/".format(self.merchant.id),
                },
                "receiver": {
                    "id": self.cart_user.id,
                    "resource_uri": "/v1/user/{}/".format(self.cart_user.id),
                },
                "subject": "Message subject",
                "body": body,
                "application": "/v1/application/{}/".format(self.application.id),
            },
            authentication=self.get_merchant_token(),
        )
        self.assertHttpCreated(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["body"], expected_body)


class TestMessageUserToMerchantResource(
    CartTestSetupMixin, MessageTestSetupMixin, BaseResourceTestCase
):
    """user -> merchant"""

    def setUp(self):
        super(TestMessageUserToMerchantResource, self).setUp()
        CartTestSetupMixin.set_up(self)
        self.msg = self.create_message(self.cart_user, self.merchant)

    def test_get_message_unauthorized(self):
        resp = self.api_client.get("/v1/message/{}/".format(self.msg.id))
        self.assertHttpUnauthorized(resp)

    def test_get_messages_as_izberg_staff(self):
        resp = self.api_client.get(
            "/v1/message/",
            authentication=self.get_token(self.izberg_user, app_token=False),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["objects"][0]["id"], self.msg.id)

    def test_get_messages_as_merchant(self):
        resp = self.api_client.get(
            "/v1/message/", authentication=self.get_merchant_token()
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["objects"][0]["id"], self.msg.id)

    def test_get_message_as_merchant(self):
        resp = self.api_client.get(
            "/v1/message/{}/".format(self.msg.id),
            authentication=self.get_merchant_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["id"], self.msg.id)

    def test_get_messages_as_app_user(self):
        resp = self.api_client.get(
            "/v1/message/", authentication=self.get_token(self.cart_user)
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["objects"][0]["id"], self.msg.id)

    def test_get_messages_as_user(self):
        resp = self.api_client.get(
            "/v1/message/",
            authentication=self.get_token(self.cart_user, app_token=False),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["objects"][0]["id"], self.msg.id)

    def test_get_message_as_user(self):
        resp = self.api_client.get(
            "/v1/message/{}/".format(self.msg.id),
            authentication=self.get_token(self.cart_user),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["id"], self.msg.id)

    def test_create_message(self):
        resp = self.api_client.post(
            "/v1/message/",
            data={
                "sender": {
                    "id": self.cart_user.id,
                    "resource_uri": "/v1/user/{}/".format(self.cart_user.id),
                },
                "receiver": {
                    "id": self.merchant.id,
                    "resource_uri": "/v1/merchant/{}/".format(self.merchant.id),
                },
                "subject": "Message subject",
                "body": "This is the body of the message",
            },
            authentication=self.get_token(self.cart_user),
        )
        self.assertHttpCreated(resp)
        data = self.deserialize(resp)
        self.assertEqual(
            data["from_resource_uri"], "/v1/user/{}/".format(self.cart_user.id)
        )
        self.assertEqual(
            data["to_resource_uri"], "/v1/merchant/{}/".format(self.merchant.id)
        )
        self.assertEqual(
            # root message should be itself
            data["root_msg"],
            data["resource_uri"],
        )


class TestMessageAppToMerchantResource(
    CartTestSetupMixin, MessageTestSetupMixin, BaseResourceTestCase
):
    """app -> merchant"""

    def setUp(self):
        super(TestMessageAppToMerchantResource, self).setUp()
        CartTestSetupMixin.set_up(self)
        self.msg = self.create_message(self.application, self.merchant)

    def test_get_message_unauthorized(self):
        resp = self.api_client.get("/v1/message/{}/".format(self.msg.id))
        self.assertHttpUnauthorized(resp)

    def test_get_messages_as_izberg_staff(self):
        resp = self.api_client.get(
            "/v1/message/",
            authentication=self.get_token(self.izberg_user, app_token=False),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["objects"][0]["id"], self.msg.id)

    def test_get_messages_as_merchant(self):
        resp = self.api_client.get(
            "/v1/message/", authentication=self.get_merchant_token()
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["objects"][0]["id"], self.msg.id)

    def test_get_message_as_merchant(self):
        resp = self.api_client.get(
            "/v1/message/{}/".format(self.msg.id),
            authentication=self.get_merchant_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["id"], self.msg.id)

    def test_get_messages_as_app_admin(self):
        resp = self.api_client.get(
            "/v1/message/", authentication=self.get_app_admin_token()
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["objects"][0]["id"], self.msg.id)

    def test_get_message_as_app_admin(self):
        resp = self.api_client.get(
            "/v1/message/{}/".format(self.msg.id),
            authentication=self.get_app_admin_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["id"], self.msg.id)

    def test_create_message(self):
        resp = self.api_client.post(
            "/v1/message/",
            data={
                "sender": {
                    "id": self.application.id,
                    "resource_uri": "/v1/application/{}/".format(self.application.id),
                },
                "receiver": {
                    "id": self.merchant.id,
                    "resource_uri": "/v1/merchant/{}/".format(self.merchant.id),
                },
                "subject": "Message subject",
                "body": "This is the body of the message",
            },
            authentication=self.get_app_admin_token(),
        )
        self.assertHttpCreated(resp)
        data = self.deserialize(resp)
        self.assertEqual(
            data["from_resource_uri"], "/v1/application/{}/".format(self.application.id)
        )
        self.assertEqual(
            data["to_resource_uri"], "/v1/merchant/{}/".format(self.merchant.id)
        )
        self.assertEqual(
            # root message should be itself
            data["root_msg"],
            data["resource_uri"],
        )


class TestMessageMerchantToAppResource(
    CartTestSetupMixin, MessageTestSetupMixin, BaseResourceTestCase
):
    """merchant -> app"""

    def setUp(self):
        super(TestMessageMerchantToAppResource, self).setUp()
        CartTestSetupMixin.set_up(self)
        self.msg = self.create_message(self.merchant, self.application)

    def test_get_message_unauthorized(self):
        resp = self.api_client.get("/v1/message/{}/".format(self.msg.id))
        self.assertHttpUnauthorized(resp)

    def test_get_messages_as_izberg_staff(self):
        resp = self.api_client.get(
            "/v1/message/",
            authentication=self.get_token(self.izberg_user, app_token=False),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["objects"][0]["id"], self.msg.id)

    def test_get_messages_as_merchant(self):
        resp = self.api_client.get(
            "/v1/message/", authentication=self.get_merchant_token()
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["objects"][0]["id"], self.msg.id)

    def test_get_message_as_merchant(self):
        resp = self.api_client.get(
            "/v1/message/{}/".format(self.msg.id),
            authentication=self.get_merchant_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["id"], self.msg.id)

    def test_get_messages_as_app_admin(self):
        resp = self.api_client.get(
            "/v1/message/", authentication=self.get_app_admin_token()
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["objects"][0]["id"], self.msg.id)

    def test_get_message_as_app_admin(self):
        resp = self.api_client.get(
            "/v1/message/{}/".format(self.msg.id),
            authentication=self.get_app_admin_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["id"], self.msg.id)

    def test_create_message(self):
        resp = self.api_client.post(
            "/v1/message/",
            data={
                "sender": {
                    "id": self.merchant.id,
                    "resource_uri": "/v1/merchant/{}/".format(self.merchant.id),
                },
                "receiver": {
                    "id": self.application.id,
                    "resource_uri": "/v1/application/{}/".format(self.application.id),
                },
                "subject": "Message subject",
                "body": "This is the body of the message",
                "application": "/v1/application/{}/".format(self.application.id),
            },
            authentication=self.get_merchant_token(),
        )
        self.assertHttpCreated(resp)
        data = self.deserialize(resp)
        self.assertEqual(
            data["from_resource_uri"], "/v1/merchant/{}/".format(self.merchant.id)
        )
        self.assertEqual(
            data["to_resource_uri"], "/v1/application/{}/".format(self.application.id)
        )
        self.assertEqual(data["from_display_name"], self.merchant.name)
        self.assertEqual(
            # root message should be itself
            data["root_msg"],
            data["resource_uri"],
        )


class TestMessageInbox(CartTestSetupMixin, MessageTestSetupMixin, BaseResourceTestCase):
    def setUp(self):
        super(TestMessageInbox, self).setUp()
        CartTestSetupMixin.set_up(self)

    def test_merchant_inbox(self):
        msg = self.create_message(self.cart_user, self.merchant)
        resp = self.api_client.get(
            "/v1/merchant/{}/inbox/".format(self.merchant.id),
            authentication=self.get_merchant_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["objects"][0]["id"], msg.id)

    def test_user_inbox(self):
        msg = self.create_message(self.merchant, self.cart_user)
        resp = self.api_client.get(
            "/v1/user/{}/inbox/".format(self.cart_user.id),
            authentication=self.get_token(self.cart_user),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["objects"][0]["id"], msg.id)

    # Drop the override_settings with DeprecatedFeature.APPLICATION_NESTED_ROUTES and
    # edit test with the new inbox route or move it?
    @override_settings(DISABLE_DEPRECATORS=True)
    def test_app_inbox(self):
        msg = self.create_message(self.cart_user, self.application)
        resp = self.api_client.get(
            "/v1/application/{}/inbox/".format(self.application.id),
            authentication=self.get_app_admin_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["objects"][0]["id"], msg.id)

    def test_app_through_merchant_inbox(self):
        msg = self.create_message(self.cart_user, self.merchant)
        resp = self.api_client.get(
            "/v1/merchant/{}/inbox/".format(self.merchant.id),
            authentication=self.get_app_admin_token(app_token=False),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["objects"][0]["id"], msg.id)


class TestMessageOutbox(
    CartTestSetupMixin, MessageTestSetupMixin, BaseResourceTestCase
):
    def setUp(self):
        super(TestMessageOutbox, self).setUp()
        CartTestSetupMixin.set_up(self)

    def test_merchant_outbox(self):
        msg = self.create_message(self.merchant, self.cart_user)
        resp = self.api_client.get(
            "/v1/merchant/{}/outbox/".format(self.merchant.id),
            authentication=self.get_merchant_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["objects"][0]["id"], msg.id)

    def test_user_outbox(self):
        msg = self.create_message(self.cart_user, self.merchant)
        resp = self.api_client.get(
            "/v1/user/{}/outbox/".format(self.cart_user.id),
            authentication=self.get_token(self.cart_user),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["objects"][0]["id"], msg.id)

    # Drop the override_settings with DeprecatedFeature.APPLICATION_NESTED_ROUTES and
    # edit test with the new outbox route or move it?
    @override_settings(DISABLE_DEPRECATORS=True)
    def test_app_outbox(self):
        msg = self.create_message(self.application, self.cart_user)
        resp = self.api_client.get(
            "/v1/application/{}/outbox/".format(self.application.id),
            authentication=self.get_app_admin_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["objects"][0]["id"], msg.id)

    def test_through_merchant_outbox(self):
        msg = self.create_message(self.merchant, self.cart_user)
        resp = self.api_client.get(
            "/v1/merchant/{}/outbox/".format(self.merchant.id),
            authentication=self.get_app_admin_token(app_token=False),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["objects"][0]["id"], msg.id)


class TestMessageReadAction(
    CartTestSetupMixin, MessageTestSetupMixin, BaseResourceTestCase
):
    def setUp(self):
        super(TestMessageReadAction, self).setUp()
        CartTestSetupMixin.set_up(self)

    def test_read_message_as_user_receiver(self):
        msg = self.create_message(self.application, self.cart_user)
        # create another message for another user as non reg of bug 3555
        # (non filtered object_list in auth)
        self.cart_user_2 = self.create_izberg_user("cart_user_2")
        self.create_message(self.application, self.cart_user_2)
        resp = self.api_client.post(
            "/v1/message/{}/read/".format(msg.id),
            authentication=self.get_token(self.cart_user),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["status"], Message.READ)

    def test_read_message_as_user_sender_fails(self):
        msg = self.create_message(self.cart_user, self.application)
        resp = self.api_client.post(
            "/v1/message/{}/read/".format(msg.id),
            authentication=self.get_token(self.cart_user),
        )
        self.assertHttpUnauthorized(resp)

    def test_read_message_as_merchant_receiver(self):
        msg = self.create_message(self.cart_user, self.merchant)
        resp = self.api_client.post(
            "/v1/message/{}/read/".format(msg.id),
            authentication=self.get_merchant_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["status"], Message.READ)

    def test_read_message_as_merchant_sender_fails(self):
        msg = self.create_message(self.merchant, self.application)
        resp = self.api_client.post(
            "/v1/message/{}/read/".format(msg.id),
            authentication=self.get_merchant_token(),
        )
        self.assertHttpUnauthorized(resp)

    def test_read_message_as_app_receiver(self):
        msg = self.create_message(self.merchant, self.application)
        resp = self.api_client.post(
            "/v1/message/{}/read/".format(msg.id),
            authentication=self.get_app_admin_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["status"], Message.READ)

    def test_read_message_as_app_sender(self):
        msg = self.create_message(self.application, self.merchant)
        resp = self.api_client.post(
            "/v1/message/{}/read/".format(msg.id),
            authentication=self.get_app_admin_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["status"], Message.READ)


class TestMessageUnReadAction(
    CartTestSetupMixin, MessageTestSetupMixin, BaseResourceTestCase
):
    def setUp(self):
        super(TestMessageUnReadAction, self).setUp()
        CartTestSetupMixin.set_up(self)

    def test_unread_message_as_user_receiver(self):
        msg = self.create_message(self.application, self.cart_user, status=Message.READ)
        resp = self.api_client.post(
            "/v1/message/{}/unread/".format(msg.id),
            authentication=self.get_token(self.cart_user),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["status"], Message.UNREAD)

    def test_unread_message_as_user_sender_fails(self):
        msg = self.create_message(self.cart_user, self.application, status=Message.READ)
        resp = self.api_client.post(
            "/v1/message/{}/unread/".format(msg.id),
            authentication=self.get_token(self.cart_user),
        )
        self.assertHttpUnauthorized(resp)

    def test_unread_message_as_merchant_receiver(self):
        msg = self.create_message(self.cart_user, self.merchant, status=Message.READ)
        resp = self.api_client.post(
            "/v1/message/{}/unread/".format(msg.id),
            authentication=self.get_merchant_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["status"], Message.UNREAD)

    def test_unread_message_as_merchant_sender_fails(self):
        msg = self.create_message(self.merchant, self.application, status=Message.READ)
        resp = self.api_client.post(
            "/v1/message/{}/unread/".format(msg.id),
            authentication=self.get_merchant_token(),
        )
        self.assertHttpUnauthorized(resp)

    def test_unread_message_as_app_receiver(self):
        msg = self.create_message(self.merchant, self.application, status=Message.READ)
        resp = self.api_client.post(
            "/v1/message/{}/unread/".format(msg.id),
            authentication=self.get_app_admin_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["status"], Message.UNREAD)

    def test_unread_message_as_app_sender(self):
        msg = self.create_message(self.application, self.merchant, status=Message.READ)
        resp = self.api_client.post(
            "/v1/message/{}/unread/".format(msg.id),
            authentication=self.get_app_admin_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["status"], Message.UNREAD)


class TestMessageCloseAction(
    CartTestSetupMixin, MessageTestSetupMixin, BaseResourceTestCase
):
    def setUp(self):
        super(TestMessageCloseAction, self).setUp()
        CartTestSetupMixin.set_up(self)

    def test_close_message_as_user_receiver(self):
        msg = self.create_message(self.application, self.cart_user, status=Message.READ)
        resp = self.api_client.post(
            "/v1/message/{}/close/".format(msg.id),
            authentication=self.get_token(self.cart_user),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["status"], Message.CLOSED)

    def test_close_message_as_user_sender_fails(self):
        msg = self.create_message(self.cart_user, self.application, status=Message.READ)
        resp = self.api_client.post(
            "/v1/message/{}/close/".format(msg.id),
            authentication=self.get_token(self.cart_user),
        )
        self.assertHttpUnauthorized(resp)

    def test_close_message_as_merchant_receiver(self):
        msg = self.create_message(self.cart_user, self.merchant, status=Message.READ)
        resp = self.api_client.post(
            "/v1/message/{}/close/".format(msg.id),
            authentication=self.get_merchant_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["status"], Message.CLOSED)

    def test_close_message_as_merchant_sender_fails(self):
        msg = self.create_message(self.merchant, self.application, status=Message.READ)
        resp = self.api_client.post(
            "/v1/message/{}/close/".format(msg.id),
            authentication=self.get_merchant_token(),
        )
        self.assertHttpUnauthorized(resp)

    def test_close_message_as_app_receiver(self):
        msg = self.create_message(self.merchant, self.application, status=Message.READ)
        resp = self.api_client.post(
            "/v1/message/{}/close/".format(msg.id),
            authentication=self.get_app_admin_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["status"], Message.CLOSED)

    def test_close_message_as_app_sender(self):
        msg = self.create_message(self.application, self.merchant, status=Message.READ)
        resp = self.api_client.post(
            "/v1/message/{}/close/".format(msg.id),
            authentication=self.get_app_admin_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["status"], Message.CLOSED)


class TestMessageStarAction(
    CartTestSetupMixin, MessageTestSetupMixin, BaseResourceTestCase
):
    def setUp(self):
        super(TestMessageStarAction, self).setUp()
        CartTestSetupMixin.set_up(self)

    def test_star_message_as_user_receiver(self):
        msg = self.create_message(self.application, self.cart_user)
        resp = self.api_client.post(
            "/v1/message/{}/starred/".format(msg.id),
            authentication=self.get_token(self.cart_user),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["starred"], True)

    def test_star_message_as_user_sender_fails(self):
        msg = self.create_message(self.cart_user, self.application)
        resp = self.api_client.post(
            "/v1/message/{}/starred/".format(msg.id),
            authentication=self.get_token(self.cart_user),
        )
        self.assertHttpUnauthorized(resp)

    def test_star_message_as_merchant_receiver(self):
        msg = self.create_message(self.cart_user, self.merchant)
        resp = self.api_client.post(
            "/v1/message/{}/starred/".format(msg.id),
            authentication=self.get_merchant_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["starred"], True)

    def test_star_message_as_merchant_sender_fails(self):
        msg = self.create_message(self.merchant, self.application)
        resp = self.api_client.post(
            "/v1/message/{}/starred/".format(msg.id),
            authentication=self.get_merchant_token(),
        )
        self.assertHttpUnauthorized(resp)

    def test_star_message_as_app_receiver(self):
        msg = self.create_message(self.merchant, self.application)
        resp = self.api_client.post(
            "/v1/message/{}/starred/".format(msg.id),
            authentication=self.get_app_admin_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["starred"], True)

    def test_star_message_as_app_sender(self):
        msg = self.create_message(self.application, self.merchant)
        resp = self.api_client.post(
            "/v1/message/{}/starred/".format(msg.id),
            authentication=self.get_app_admin_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["starred"], True)


class TestMessageUnStarAction(
    CartTestSetupMixin, MessageTestSetupMixin, BaseResourceTestCase
):
    def setUp(self):
        super(TestMessageUnStarAction, self).setUp()
        CartTestSetupMixin.set_up(self)

    def test_unstar_message_as_user_receiver(self):
        msg = self.create_message(self.application, self.cart_user, starred=True)
        resp = self.api_client.post(
            "/v1/message/{}/starred/".format(msg.id),
            authentication=self.get_token(self.cart_user),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["starred"], False)

    def test_unstar_message_as_user_sender_fails(self):
        msg = self.create_message(self.cart_user, self.application, starred=True)
        resp = self.api_client.post(
            "/v1/message/{}/starred/".format(msg.id),
            authentication=self.get_token(self.cart_user),
        )
        self.assertHttpUnauthorized(resp)

    def test_unstar_message_as_merchant_receiver(self):
        msg = self.create_message(self.cart_user, self.merchant, starred=True)
        resp = self.api_client.post(
            "/v1/message/{}/starred/".format(msg.id),
            authentication=self.get_merchant_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["starred"], False)

    def test_unstar_message_as_merchant_sender_fails(self):
        msg = self.create_message(self.merchant, self.application, starred=True)
        resp = self.api_client.post(
            "/v1/message/{}/starred/".format(msg.id),
            authentication=self.get_merchant_token(),
        )
        self.assertHttpUnauthorized(resp)

    def test_unstar_message_as_app_receiver(self):
        msg = self.create_message(self.merchant, self.application, starred=True)
        resp = self.api_client.post(
            "/v1/message/{}/starred/".format(msg.id),
            authentication=self.get_app_admin_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["starred"], False)

    def test_unstar_message_as_app_sender(self):
        msg = self.create_message(self.application, self.merchant, starred=True)
        resp = self.api_client.post(
            "/v1/message/{}/starred/".format(msg.id),
            authentication=self.get_app_admin_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["starred"], False)


class MessageAttachmentTest(ApplicationSettingsTestsSetupMixin, BaseResourceTestCase):
    def test_create_message_with_attachment_saves_it_with_meta(self):
        self.application = ApplicationFactory()
        self.user = self.application.contact_user
        filename = "avatâr.log.PNG"
        with open(os.path.dirname(__file__) + "/data/" + filename, "rb") as f:
            form = {
                "application": self.application.get_resource_uri(),
                "sender": self.application.get_resource_uri(),
                "receiver": self.user.get_resource_uri(),
                "body": "Body",
                "subject": "Subject",
                "attachment": f,
            }
            response = self.client.post(
                "/v1/message/",
                data=form,
                HTTP_AUTHORIZATION=self.get_app_token(self.user, self.application),
                HTTP_CONTENT_TYPE=MULTIPART_CONTENT,
            )
        self.assertEqual(response.status_code, 201)
        response_data = json.loads(response.content)
        self.assertEqual(response_data["attachment_count"], 1)
        message_id = response_data["id"]
        response = self.api_client.get(
            "/v1/message-attachment/?message={}".format(message_id),
            authentication=self.get_app_token(self.user, self.application),
        )
        self.assertEqual(response.status_code, 200)
        response_data = json.loads(response.content)
        self.assertEqual(response_data["meta"]["total_count"], 1)
        attachment = response_data["objects"][0]
        self.assertEqual(attachment["file_name"], filename)
        self.assertEqual(attachment["file_extension"], "png")
        self.assertEqual(attachment["file_size"], 5870)

    def test_attachment_file_content_path(self):
        self.application = ApplicationFactory()
        self.user = self.application.contact_user
        filename = "avatar.PNG"
        with open(os.path.dirname(__file__) + "/data/" + filename, "rb") as f:
            form = {
                "application": self.application.get_resource_uri(),
                "sender": self.application.get_resource_uri(),
                "receiver": self.user.get_resource_uri(),
                "body": "Body",
                "subject": "Subject",
                "attachment": f,
            }
            response = self.client.post(
                "/v1/message/",
                data=form,
                HTTP_AUTHORIZATION=self.get_app_token(self.user, self.application),
                HTTP_CONTENT_TYPE=MULTIPART_CONTENT,
            )
        self.assertEqual(response.status_code, 201)
        response_data = json.loads(response.content)
        self.assertEqual(response_data["attachment_count"], 1)
        message_id = response_data["id"]
        response = self.api_client.get(
            "/v1/message-attachment/?message={}".format(message_id),
            authentication=self.get_app_token(self.user, self.application),
        )
        self.assertEqual(response.status_code, 200)
        response_data = json.loads(response.content)
        self.assertEqual(response_data["meta"]["total_count"], 1)
        attachment = response_data["objects"][0]
        now = timezone.now()
        expected_file_path = (
            f"{self.application.id}/"
            f"message_attachment/{now.year}/{now.month}/{now.day}/"
            f"avatar-.+-.+-.+-.+\\.PNG"
        )
        self.assertRegex(attachment["file_content"], expected_file_path)

    def test_create_message_with_too_many_attachment_return_error(self):
        self.application = ApplicationFactory()
        self.user = self.application.contact_user
        filename = "avatâr.log.PNG"
        with open(os.path.dirname(__file__) + "/data/" + filename, "rb") as f:
            form = {
                "application": self.application.get_resource_uri(),
                "sender": self.application.get_resource_uri(),
                "receiver": self.user.get_resource_uri(),
                "body": "Body",
                "subject": "Subject",
                "attachment": [f] * 11,
            }
            response = self.client.post(
                "/v1/message/",
                data=form,
                HTTP_AUTHORIZATION=self.get_app_token(self.user, self.application),
                HTTP_CONTENT_TYPE=MULTIPART_CONTENT,
            )
        self.assertEqual(response.status_code, 400)
        response_data = response.json()
        self.assertEqual(
            response_data,
            {
                "error_code": "MESSAGE.TOO_MANY_ATTACHMENTS",
                "error_description": "Too many attachments",
                "error_context": {"max_attachment_count": 10, "attachment_count": 11},
            },
        )

    @override_settings(MESSAGE_ATTACHMENT_MAX_SIZE_IN_BYTES=100)
    def test_create_message_with_too_big_attachment_return_error(self):
        self.application = ApplicationFactory()
        self.user = self.application.contact_user
        filename = "avatâr.log.PNG"
        with open(os.path.dirname(__file__) + "/data/" + filename, "rb") as f:
            form = {
                "application": self.application.get_resource_uri(),
                "sender": self.application.get_resource_uri(),
                "receiver": self.user.get_resource_uri(),
                "body": "Body",
                "subject": "Subject",
                "attachment": f,
            }
            response = self.client.post(
                "/v1/message/",
                data=form,
                HTTP_AUTHORIZATION=self.get_app_token(self.user, self.application),
                HTTP_CONTENT_TYPE=MULTIPART_CONTENT,
            )
        self.assertEqual(response.status_code, 413)
        response_data = response.json()
        self.assertEqual(
            response_data,
            {
                "error_code": "MESSAGE.ATTACHMENT_TOO_BIG",
                "error_description": "Message attachment(s) is/are too big",
                "error_context": {"attachment_size": 5870, "max_attachment_size": 100},
            },
        )

    def test_create_message_with_invalid_attachment_extension(self):
        self.application = ApplicationFactory()
        self.user = self.application.contact_user
        filename = "invalid.ext"
        with open(os.path.dirname(__file__) + "/data/" + filename, "rb") as f:
            form = {
                "application": self.application.get_resource_uri(),
                "sender": self.application.get_resource_uri(),
                "receiver": self.user.get_resource_uri(),
                "body": "Body",
                "subject": "Subject",
                "attachment": f,
            }
            response = self.client.post(
                "/v1/message/",
                data=form,
                HTTP_AUTHORIZATION=self.get_app_token(self.user, self.application),
                HTTP_CONTENT_TYPE=MULTIPART_CONTENT,
            )
        self.assertEqual(response.status_code, 400)
        response_data = response.json()
        self.assertEqual(
            response_data,
            {
                "error_code": "MESSAGE.INVALID_ATTACHMENT_EXTENSION",
                "error_description": "Invalid attachment extension",
                "error_context": {
                    "received_extension": "ext",
                    "accepted_extensions": [
                        "jpeg",
                        "jpg",
                        "png",
                        "gif",
                        "pdf",
                        "doc",
                        "docx",
                        "csv",
                        "xls",
                        "xlsx",
                        "zip",
                    ],
                },
            },
        )

    def test_create_message_with_attachement_on_disabled_app_raises(self):
        self.application = ApplicationFactory(
            app_settings={"enable_message_attachments": False}
        )
        self.user = self.application.contact_user
        filename = "avatâr.log.PNG"
        with open(os.path.dirname(__file__) + "/data/" + filename, "rb") as f:
            form = {
                "application": self.application.get_resource_uri(),
                "sender": self.application.get_resource_uri(),
                "receiver": self.user.get_resource_uri(),
                "body": "Body",
                "subject": "Subject",
                "attachment": f,
            }
            response = self.client.post(
                "/v1/message/",
                data=form,
                HTTP_AUTHORIZATION=self.get_app_token(self.user, self.application),
                HTTP_CONTENT_TYPE=MULTIPART_CONTENT,
            )
        self.assertEqual(response.status_code, 400)
        response_data = response.json()
        self.assertEqual(
            response_data,
            {
                "error_code": "MESSAGE.ATTACHMENTS_NOT_ENABLED",
                "error_description": (
                    "Message attachments are not enabled for this application"
                ),
                "error_context": {},
            },
        )

    def test_create_message_with_attachment_works_with_jwt_token(self):
        token = "Bearer {}".format(GLOBAL_TOKENS["APPLICATION_1_WRITE"]["token"])
        self.application = ApplicationFactory(id=1)
        self.user = self.application.contact_user
        filename = "avatâr.log.PNG"
        with open(os.path.dirname(__file__) + "/data/" + filename, "rb") as f:
            form = {
                "application": self.application.get_resource_uri(),
                "sender": self.application.get_resource_uri(),
                "receiver": self.user.get_resource_uri(),
                "body": "Body",
                "subject": "Subject",
                "attachment": f,
            }
            response = self.client.post(
                "/v1/message/",
                data=form,
                HTTP_AUTHORIZATION=token,
                HTTP_CONTENT_TYPE=MULTIPART_CONTENT,
            )
        self.assertEqual(response.status_code, 201)
        response_data = json.loads(response.content)
        self.assertEqual(response_data["attachment_count"], 1)
        message_id = response_data["id"]
        response = self.api_client.get(
            "/v1/message-attachment/?message={}".format(message_id),
            authentication=token,
        )
        self.assertEqual(response.status_code, 200)
        response_data = json.loads(response.content)
        self.assertEqual(response_data["meta"]["total_count"], 1)
        attachment = response_data["objects"][0]
        self.assertEqual(attachment["file_name"], filename)
        self.assertEqual(attachment["file_extension"], "png")
        self.assertEqual(attachment["file_size"], 5870)


class MessageLabelTestCase(BaseResourceTestCase):
    def test_create_message_with_new_label(self):
        merchant = MerchantFactory()
        mess_lab = MessageLabelFactory(application=merchant.application)

        resp = self.api_client.post(
            "/v1/message/",
            data={
                "sender": merchant.get_resource_uri(),
                "receiver": mess_lab.application.get_resource_uri(),
                "message_label_key": mess_lab.key,
                "subject": "Message subject",
                "body": "This is the body of the message",
            },
            authentication=self.get_operator_token(application=merchant.application),
        )

        self.assertHttpCreated(resp)
        response_data = self.deserialize(resp)
        self.assertEqual(response_data["message_label_key"], mess_lab.key)
        self.assertEqual(response_data["message_label_name"], str(mess_lab.name))

    def test_edit_message_label(self):
        message = MerchantToAppMessageFactory()
        mess_lab = MessageLabelFactory(application=message.application)

        resp = self.api_client.patch(
            message.get_resource_uri(),
            data={"message_label_key": mess_lab.key},
            authentication=self.get_operator_token(application=mess_lab.application),
        )

        self.assertHttpAccepted(resp)
        response_data = self.deserialize(resp)
        self.assertEqual(response_data["message_label_key"], mess_lab.key)
        self.assertEqual(response_data["message_label_name"], str(mess_lab.name))
        message.refresh_from_db()
        self.assertEqual(message.message_label, mess_lab)

    def test_message_label_simple_filtering(self):
        mess_lab_1 = MessageLabelFactory()
        mess_lab_2 = MessageLabelFactory(
            application=mess_lab_1.application,
        )
        message = MerchantToAppMessageFactory(
            application=mess_lab_1.application,
            message_label_key=mess_lab_1.key,
        )
        MerchantToAppMessageFactory(
            application=mess_lab_1.application,
            message_label_key=mess_lab_2.key,
        )

        resp = self.api_client.get(
            "/v1/message/?message_label_key={}".format(mess_lab_1.key),
            authentication=self.get_operator_token(application=mess_lab_1.application),
        )

        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 1)
        self.assertEqual(data["objects"][0]["id"], message.id)

    def test_message_invalid_label_filtering(self):
        mess_label_1 = MessageLabelFactory()
        mess_label_2 = MessageLabelFactory(
            application=mess_label_1.application,
        )
        MerchantToAppMessageFactory(
            application=mess_label_1.application,
            message_label_key=mess_label_1.key,
        )
        MerchantToAppMessageFactory(
            application=mess_label_1.application,
            message_label_key=mess_label_2.key,
        )

        resp = self.api_client.get(
            "/v1/message/?message_label_key=key-toto",
            authentication=self.get_operator_token(
                application=mess_label_1.application
            ),
        )

        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 0)
        self.assertEqual(data["objects"], [])

    def test_message_label_isnull_true_filtering(self):
        mess_label_1 = MessageLabelFactory()
        MerchantToAppMessageFactory(
            application=mess_label_1.application,
            message_label_key=mess_label_1.key,
        )
        message = MerchantToAppMessageFactory(
            application=mess_label_1.application,
            message_label_key=None,
        )

        resp = self.api_client.get(
            "/v1/message/?message_label_key__isnull=true",
            authentication=self.get_operator_token(
                application=mess_label_1.application
            ),
        )

        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 1)
        self.assertEqual(data["objects"][0]["id"], message.id)

    def test_message_label_isnull_true_filtering_uppercase(self):
        mess_label_1 = MessageLabelFactory()
        MerchantToAppMessageFactory(
            application=mess_label_1.application,
            message_label_key=mess_label_1.key,
        )
        message = MerchantToAppMessageFactory(
            application=mess_label_1.application,
            message_label_key=None,
        )

        resp = self.api_client.get(
            "/v1/message/?message_label_key__isnull=True",
            authentication=self.get_operator_token(
                application=mess_label_1.application
            ),
        )

        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 1)
        self.assertEqual(data["objects"][0]["id"], message.id)

    def test_message_none_label_filtering(self):
        mess_label_1 = MessageLabelFactory()
        MerchantToAppMessageFactory(
            application=mess_label_1.application,
            message_label_key=mess_label_1.key,
        )
        message = MerchantToAppMessageFactory(
            application=mess_label_1.application,
            message_label_key=None,
        )

        resp = self.api_client.get(
            "/v1/message/?message_label_key=None",
            authentication=self.get_operator_token(
                application=mess_label_1.application
            ),
        )

        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 1)
        self.assertEqual(data["objects"][0]["id"], message.id)

    def test_message_with_label_filtering(self):
        mess_label_1 = MessageLabelFactory()
        message = MerchantToAppMessageFactory(
            application=mess_label_1.application,
            message_label_key=mess_label_1.key,
        )
        MerchantToAppMessageFactory(
            application=mess_label_1.application,
            message_label_key=None,
        )

        resp = self.api_client.get(
            "/v1/message/?message_label_key__isnull=false",
            authentication=self.get_operator_token(
                application=mess_label_1.application
            ),
        )

        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 1)
        self.assertEqual(data["objects"][0]["id"], message.id)

    def test_message_label_complex_filtering(self):
        mess_lab_1 = MessageLabelFactory()
        mess_lab_2 = MessageLabelFactory(
            application=mess_lab_1.application,
        )
        message = MerchantToAppMessageFactory(
            application=mess_lab_1.application,
            message_label_key=mess_lab_1.key,
        )
        MerchantToAppMessageFactory(
            application=mess_lab_1.application,
            message_label_key=mess_lab_2.key,
        )

        resp = self.api_client.get(
            f"/v1/message/?message_label_key__exact={mess_lab_1.key}",
            authentication=self.get_operator_token(application=mess_lab_1.application),
        )

        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 1)
        self.assertEqual(data["objects"][0]["id"], message.id)

    def test_message_label_in_filtering(self):
        mess_lab_1 = MessageLabelFactory()
        mess_lab_2 = MessageLabelFactory(
            application=mess_lab_1.application,
        )
        mess_lab_3 = MessageLabelFactory(
            application=mess_lab_1.application,
        )
        message = MerchantToAppMessageFactory(
            application=mess_lab_1.application,
            message_label_key=mess_lab_1.key,
        )
        message_2 = MerchantToAppMessageFactory(
            application=mess_lab_1.application,
            message_label_key=mess_lab_2.key,
        )
        MerchantToAppMessageFactory(
            application=mess_lab_1.application,
            message_label_key=mess_lab_3.key,
        )

        resp = self.api_client.get(
            f"/v1/message/?message_label_key__in=" f"{mess_lab_1.key},{mess_lab_2.key}",
            authentication=self.get_operator_token(application=mess_lab_1.application),
        )

        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 2)
        self.assertEqual(data["objects"][1]["id"], message.id)
        self.assertEqual(data["objects"][0]["id"], message_2.id)


class MessageTypeTestCase(BaseResourceTestCase):
    def test_create_message_with_new_type(self):
        merchant = MerchantFactory()
        mess_type = MessageTypeFactory(application=merchant.application)

        resp = self.api_client.post(
            "/v1/message/",
            data={
                "sender": merchant.get_resource_uri(),
                "receiver": mess_type.application.get_resource_uri(),
                "message_type_key": mess_type.key,
                "subject": "Message subject",
                "body": "This is the body of the message",
            },
            authentication=self.get_operator_token(application=merchant.application),
        )

        self.assertHttpCreated(resp)
        response_data = self.deserialize(resp)
        self.assertEqual(response_data["message_type_key"], mess_type.key)
        self.assertEqual(response_data["message_type_name"], str(mess_type.name))

    def test_edit_message_type(self):
        message = MerchantToAppMessageFactory()
        mess_type = MessageTypeFactory(application=message.application)

        resp = self.api_client.patch(
            message.get_resource_uri(),
            data={"message_type_key": mess_type.key},
            authentication=self.get_operator_token(application=mess_type.application),
        )

        self.assertHttpAccepted(resp)
        response_data = self.deserialize(resp)
        self.assertEqual(response_data["message_type_key"], mess_type.key)
        self.assertEqual(response_data["message_type_name"], str(mess_type.name))
        message.refresh_from_db()
        self.assertEqual(message.message_type, mess_type)

    def test_message_type_simple_filtering(self):
        mess_type_1 = MessageTypeFactory()
        mess_type_2 = MessageTypeFactory(
            application=mess_type_1.application,
        )
        message = MerchantToAppMessageFactory(
            application=mess_type_1.application,
            message_type_key=mess_type_1.key,
        )
        MerchantToAppMessageFactory(
            application=mess_type_1.application,
            message_type_key=mess_type_2.key,
        )

        resp = self.api_client.get(
            "/v1/message/?message_type_key={}".format(mess_type_1.key),
            authentication=self.get_operator_token(application=mess_type_1.application),
        )

        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 1)
        self.assertEqual(data["objects"][0]["id"], message.id)

    def test_message_invalid_type_filtering(self):
        mess_type_1 = MessageTypeFactory()
        mess_type_2 = MessageTypeFactory(
            application=mess_type_1.application,
        )
        MerchantToAppMessageFactory(
            application=mess_type_1.application,
            message_type_key=mess_type_1.key,
        )
        MerchantToAppMessageFactory(
            application=mess_type_1.application,
            message_type_key=mess_type_2.key,
        )

        resp = self.api_client.get(
            "/v1/message/?message_type_key=key-toto",
            authentication=self.get_operator_token(application=mess_type_1.application),
        )

        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 0)
        self.assertEqual(data["objects"], [])

    def test_message_type_isnull_true_filtering(self):
        mess_type_1 = MessageTypeFactory()
        MerchantToAppMessageFactory(
            application=mess_type_1.application,
            message_type_key=mess_type_1.key,
        )
        message = MerchantToAppMessageFactory(
            application=mess_type_1.application,
            message_type_key=None,
        )

        resp = self.api_client.get(
            "/v1/message/?message_type_key__isnull=true",
            authentication=self.get_operator_token(application=mess_type_1.application),
        )

        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 1)
        self.assertEqual(data["objects"][0]["id"], message.id)

    def test_message_type_isnull_true_filtering_uppercase(self):
        mess_type_1 = MessageTypeFactory()
        MerchantToAppMessageFactory(
            application=mess_type_1.application,
            message_type_key=mess_type_1.key,
        )
        message = MerchantToAppMessageFactory(
            application=mess_type_1.application,
            message_type_key=None,
        )

        resp = self.api_client.get(
            "/v1/message/?message_type_key__isnull=True",
            authentication=self.get_operator_token(application=mess_type_1.application),
        )

        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 1)
        self.assertEqual(data["objects"][0]["id"], message.id)

    def test_message_none_type_filtering(self):
        mess_type_1 = MessageTypeFactory()
        MerchantToAppMessageFactory(
            application=mess_type_1.application,
            message_type_key=mess_type_1.key,
        )
        message = MerchantToAppMessageFactory(
            application=mess_type_1.application,
            message_type_key=None,
        )

        resp = self.api_client.get(
            "/v1/message/?message_type_key=None",
            authentication=self.get_operator_token(application=mess_type_1.application),
        )

        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 1)
        self.assertEqual(data["objects"][0]["id"], message.id)

    def test_message_with_type_filtering(self):
        mess_type_1 = MessageTypeFactory()
        message = MerchantToAppMessageFactory(
            application=mess_type_1.application,
            message_type_key=mess_type_1.key,
        )
        MerchantToAppMessageFactory(
            application=mess_type_1.application,
            message_type_key=None,
        )

        resp = self.api_client.get(
            "/v1/message/?message_type_key__isnull=false",
            authentication=self.get_operator_token(application=mess_type_1.application),
        )

        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 1)
        self.assertEqual(data["objects"][0]["id"], message.id)

    def test_message_type_complex_filtering(self):
        mess_type_1 = MessageTypeFactory()
        mess_type_2 = MessageTypeFactory(
            application=mess_type_1.application,
        )
        message = MerchantToAppMessageFactory(
            application=mess_type_1.application,
            message_type_key=mess_type_1.key,
        )
        MerchantToAppMessageFactory(
            application=mess_type_1.application,
            message_type_key=mess_type_2.key,
        )

        resp = self.api_client.get(
            f"/v1/message/?message_type_key__exact={mess_type_1.key}",
            authentication=self.get_operator_token(application=mess_type_1.application),
        )

        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 1)
        self.assertEqual(data["objects"][0]["id"], message.id)

    def test_message_type_in_filtering(self):
        mess_type_1 = MessageTypeFactory()
        mess_type_2 = MessageTypeFactory(
            application=mess_type_1.application,
        )
        mess_type_3 = MessageTypeFactory(
            application=mess_type_1.application,
        )
        message = MerchantToAppMessageFactory(
            application=mess_type_1.application,
            message_type_key=mess_type_1.key,
        )
        message_2 = MerchantToAppMessageFactory(
            application=mess_type_1.application,
            message_type_key=mess_type_2.key,
        )
        MerchantToAppMessageFactory(
            application=mess_type_1.application,
            message_type_key=mess_type_3.key,
        )

        resp = self.api_client.get(
            f"/v1/message/?message_type_key__in="
            f"{mess_type_1.key},{mess_type_2.key}",
            authentication=self.get_operator_token(application=mess_type_1.application),
        )

        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 2)
        self.assertEqual(data["objects"][1]["id"], message.id)
        self.assertEqual(data["objects"][0]["id"], message_2.id)
