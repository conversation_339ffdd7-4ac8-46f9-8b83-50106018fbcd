import os
import sys

from apps.ice_applications.app_conf_settings import EnableMessageAttachments
from apps.mp_messages.api.message.serializers import MessageSerializer
from apps.mp_messages.exceptions import (
    AttachmentTooBigApiException,
    InvalidAttachmentExtensionApiException,
    MessageAttachmentsNotEnabledApiException,
    TooManyAttachmentApiException,
)
from apps.mp_messages.models import Message
from apps.mp_messages.tests.fixtures import MEDIA_ROOT_PATH
from apps.testing.factories import (
    AppToMerchantMessageFactory,
    MerchantFactory,
    MerchantOrderFactory,
    MerchantToAppMessageFactory,
    MessageLabelFactory,
    MessageTypeFactory,
)
from django.conf import settings
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.test import override_settings
from django.test.client import MULTIPART_CONTENT
from drf_izberg.tests.base import BaseSerializerAPITestCase
from ims.api.const import MERCHANT_OWNER, OPERATOR_OWNER
from rest_framework.exceptions import ValidationError


class MessageSerializerTestCase(BaseSerializerAPITestCase):
    API_VERSION = "v2"
    maxDiff = None

    def setUp(self):
        super().setUp()
        self.request.owner_type = OPERATOR_OWNER

    @staticmethod
    def create_uploaded_file(file_io, filename):
        return InMemoryUploadedFile(
            file_io,
            "test",
            filename,
            "image/png",
            sys.getsizeof(file_io),
            None,
        )

    def test_serializer(self):
        app = self.request.application
        merchant = self.request.merchant
        mo = MerchantOrderFactory(merchant=merchant, application=app)
        parent = MerchantToAppMessageFactory(application=app, merchant=merchant)
        msg_label = MessageLabelFactory(application=app)
        msg_type = MessageTypeFactory(application=app)

        msg = AppToMerchantMessageFactory(
            application=app,
            merchant=merchant,
            merchant_order=mo,
            parent_msg=parent,
            message_label_key=msg_label.key,
            message_type_key=msg_type.key,
        )
        next_msg = MerchantToAppMessageFactory(
            parent_msg=msg, merchant=merchant, application=app
        )

        serializer = MessageSerializer(instance=msg, context={"request": self.request})

        expected = {
            "body": msg.body,
            "merchant_order": {
                "id": mo.id,
                "pk": mo.id,
                "resource_uri": (
                    f"http://testserver/{self.API_VERSION}/merchant-order/{mo.id}/"
                ),
            },
            "receiver": msg.get_receiver(),
            "sender": msg.get_sender(),
            "subject": msg.subject,
            "parent_msg": {
                "id": parent.id,
                "pk": parent.id,
                "resource_uri": (
                    f"http://testserver/{self.API_VERSION}/message/{parent.id}/"
                ),
            },
            "message_label_key": msg_label.key,
            "message_type_key": msg_type.key,
            "id": msg.id,
            "application": {
                "id": app.id,
                "pk": app.id,
                "resource_uri": (
                    f"http://testserver/{self.API_VERSION}/application/{app.id}/"
                ),
            },
            "attachment_count": msg.attachment_count,
            "from_email": msg.from_email,
            "from_location": msg.from_location,
            "from_phone_number": msg.from_phone_number,
            "from_resource_uri": msg.from_resource_uri,
            "message_label_name": msg_label.name.en,
            "message_type_name": msg_type.name.en,
            "next_messages": [
                {
                    "id": next_msg.id,
                    "pk": next_msg.id,
                    "resource_uri": (
                        f"http://testserver/{self.API_VERSION}/message/{next_msg.id}/"
                    ),
                }
            ],
            "priority": msg.priority,
            "root_msg": {
                "id": parent.id,
                "pk": parent.id,
                "resource_uri": (
                    f"http://testserver/{self.API_VERSION}/message/{parent.id}/"
                ),
            },
            "sent_at": msg.sent_at.astimezone().isoformat(),
            "starred": msg.starred,
            "status": str(msg.status),
            "status_localized": msg.get_status_display(),
            "to_resource_uri": msg.to_resource_uri,
            "resource_uri": f"http://testserver/{self.API_VERSION}/message/{msg.id}/",
        }
        self.assertEqual(serializer.data, expected)

    def test_serializer_validate_sender(self):
        self.request.owner_type = MERCHANT_OWNER

        serializer = MessageSerializer(
            data={
                "sender": f"/{self.API_VERSION}/merchant/{self.request.merchant.id}/",
                "receiver": (
                    f"/{self.API_VERSION}/application/{self.request.application.id}/"
                ),
                "body": "Test",
            },
            context={"request": self.request},
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()

        msg = Message.objects.last()

        self.assertEqual(int(msg.from_id), self.request.merchant.id)

    def test_serializer_validate_sender_not_same_as_merchant_id_token(self):
        self.request.owner_type = MERCHANT_OWNER
        merchant = MerchantFactory.create_for_application(self.request.application)

        serializer = MessageSerializer(
            data={
                "sender": f"/{self.API_VERSION}/merchant/{merchant.id}/",
                "receiver": (
                    f"/{self.API_VERSION}/application/{self.request.application.id}/"
                ),
                "body": "Test",
            },
            context={"request": self.request},
        )

        with self.assertRaises(ValidationError) as exc_info:
            serializer.is_valid(raise_exception=True)

        self.assertEqual(exc_info.exception.detail, {"sender": ["Sender not found"]})

    @override_settings(MEDIA_ROOT=MEDIA_ROOT_PATH)
    def test_serializer_validate_attachments_not_enable(self):
        self.request.HTTP_CONTENT_TYPE = MULTIPART_CONTENT

        app = self.request.application
        app.set_setting(EnableMessageAttachments, False)
        filename = "avatar.PNG"

        with open(os.path.join(settings.MEDIA_ROOT, filename), "rb") as f:
            uploaded_file = self.create_uploaded_file(f, filename)

            serializer = MessageSerializer(
                data={
                    "sender": (
                        f"/{self.API_VERSION}/merchant/{self.request.merchant.id}/"
                    ),
                    "receiver": f"/{self.API_VERSION}/application/{app.id}/",
                    "body": "Test",
                    "attachments": [uploaded_file],
                },
                context={"request": self.request},
            )
            with self.assertRaises(MessageAttachmentsNotEnabledApiException) as exc:
                serializer.is_valid(raise_exception=True)

            self.assertEqual(
                exc.exception.args[0],
                "Message attachments are not enabled for this application",
            )

    @override_settings(MEDIA_ROOT=MEDIA_ROOT_PATH)
    @override_settings(MESSAGE_ATTACHMENT_MAX_COUNT=1)
    def test_serializer_validate_attachments_exceed_nb_attachments(self):
        self.request.HTTP_CONTENT_TYPE = MULTIPART_CONTENT

        app = self.request.application
        filename_1 = "avatar.PNG"
        filename_2 = "avatâr.log.PNG"

        with (
            open(os.path.join(settings.MEDIA_ROOT, filename_1), "rb") as f1,
            open(os.path.join(settings.MEDIA_ROOT, filename_2), "rb") as f2,
        ):
            uploaded_file_1 = self.create_uploaded_file(f1, filename_1)
            uploaded_file_2 = self.create_uploaded_file(f2, filename_2)

            serializer = MessageSerializer(
                data={
                    "sender": (
                        f"/{self.API_VERSION}/merchant/{self.request.merchant.id}/"
                    ),
                    "receiver": f"/{self.API_VERSION}/application/{app.id}/",
                    "body": "Test",
                    "attachments": [uploaded_file_1, uploaded_file_2],
                },
                context={"request": self.request},
            )
            with self.assertRaises(TooManyAttachmentApiException) as exc:
                serializer.is_valid(raise_exception=True)

            self.assertEqual(exc.exception.args[0], "Too many attachments")

    @override_settings(MEDIA_ROOT=MEDIA_ROOT_PATH)
    @override_settings(MESSAGE_ATTACHMENT_MAX_SIZE_IN_BYTES=20)
    def test_serializer_validate_attachments_exceed_size(self):
        self.request.HTTP_CONTENT_TYPE = MULTIPART_CONTENT

        app = self.request.application
        filename = "avatar.PNG"

        with open(os.path.join(settings.MEDIA_ROOT, filename), "rb") as f:
            uploaded_file = self.create_uploaded_file(f, filename)

            serializer = MessageSerializer(
                data={
                    "sender": (
                        f"/{self.API_VERSION}/merchant/{self.request.merchant.id}/"
                    ),
                    "receiver": f"/{self.API_VERSION}/application/{app.id}/",
                    "body": "Test",
                    "attachments": [uploaded_file],
                },
                context={"request": self.request},
            )
            with self.assertRaises(AttachmentTooBigApiException) as exc:
                serializer.is_valid(raise_exception=True)

            self.assertEqual(
                exc.exception.args[0],
                "Message attachment(s) is/are too big",
            )

    @override_settings(MEDIA_ROOT=MEDIA_ROOT_PATH)
    def test_serializer_validate_attachments_invalid_extension(self):
        self.request.HTTP_CONTENT_TYPE = MULTIPART_CONTENT

        app = self.request.application
        filename = "invalid.ext"

        with open(os.path.join(settings.MEDIA_ROOT, filename), "rb") as f:
            uploaded_file = self.create_uploaded_file(f, filename)

            serializer = MessageSerializer(
                data={
                    "sender": (
                        f"/{self.API_VERSION}/merchant/{self.request.merchant.id}/"
                    ),
                    "receiver": f"/{self.API_VERSION}/application/{app.id}/",
                    "body": "Test",
                    "attachments": [uploaded_file],
                },
                context={"request": self.request},
            )
            with self.assertRaises(InvalidAttachmentExtensionApiException) as exc:
                serializer.is_valid(raise_exception=True)

            self.assertEqual(
                exc.exception.args[0],
                "Invalid attachment extension",
            )

    def test_serializer_uri_field_validation(self):
        serializer = MessageSerializer(
            data={"sender": 3, "receiver": {"sample": "hello world!"}, "body": "Test"},
            context={"request": self.request},
        )

        with self.assertRaises(ValidationError) as exc_info:
            serializer.is_valid(raise_exception=True)

        self.assertEqual(
            exc_info.exception.detail,
            {
                "receiver": ['Invalid format, mandatory key : ["id", "resource_uri"]'],
                "sender": ["Invalid format, expected str or dict"],
            },
        )
