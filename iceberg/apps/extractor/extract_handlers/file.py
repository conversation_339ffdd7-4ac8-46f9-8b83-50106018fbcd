# -*- coding: utf-8 -*-

import logging
import os
import shutil
import subprocess
import time
from io import TextIOBase
from typing import List, Optional, Union

import boto3
import botocore
from apps.ice_applications.models import Application
from django.conf import settings
from django.db.models.fields.files import <PERSON><PERSON>ield, ImageField
from django.db.models.query import QuerySet
from django.db.utils import OperationalError
from psycopg2.errors import QueryCanceled

from ..exceptions import MissingTableExtractFile
from ..utils import get_directory_size, raise_if_local_disk_is_full
from .base import BaseHandler

logger = logging.getLogger(__name__)


class FileHandler(BaseHandler):
    """Handler that stores the exported data in files on the local server"""

    def __init__(
        self,
        application: Application,
        execution_id: str,
        export_path: str,
        export_root_folder: str,
        file_prefix: str = "",
        disable_free_space_check: bool = False,
        **kwargs,
    ):
        super().__init__(application, execution_id)
        self.file_prefix = file_prefix
        if self.file_prefix and self.file_prefix[-1] not in ("_", "-"):
            self.file_prefix += "_"
        self.export_path = export_path
        self.export_root_folder = export_root_folder
        self.archive_created = False
        self.disable_free_space_check = disable_free_space_check

    def get_export_path(self) -> str:
        return self.export_path

    def save_db_table(
        self,
        model_name: str,
        queryset: QuerySet,
        fields: List[str] = None,
    ) -> None:
        self._raise_if_local_disk_is_full()
        csv_path = self._get_csv_path(model_name)
        backoff_in_seconds = 5
        max_retries = 8
        x = 0
        while True:
            try:
                if fields:
                    queryset.to_csv(csv_path, *fields)
                else:
                    queryset.to_csv(csv_path)
            except (OperationalError, QueryCanceled) as err:
                if os.path.exists(csv_path):
                    os.remove(csv_path)
                timeout_msg = "canceling statement due to statement timeout\n"
                if err.args[0] != timeout_msg or x == max_retries:
                    raise
                time_to_sleep = backoff_in_seconds * 2**x
                logger.info(
                    f"Database timeout detected. Retrying in {time_to_sleep} "
                    f"seconds..."
                )
                time.sleep(time_to_sleep)
                x += 1
                logger.info("Retrying...")
            else:
                return

    def save_static_file(
        self,
        model_name: str,
        model_id: str,
        field_name: str,
        file_obj: Union[FileField, ImageField],
    ) -> None:
        self._raise_if_local_disk_is_full()
        static_folder = self._get_statics_folder(model_name, model_id, field_name)
        if not os.path.exists(static_folder):
            os.makedirs(static_folder)
        file_path = self._get_static_file_path(
            model_name, model_id, field_name, file_obj
        )
        if file_path is None:
            return
        # read first, to avoid creating empty files in case of read error
        logger.info(f"Reading file {file_path}...")
        try:
            file_content = file_obj.read()
        except OSError as err:
            logger.error(f"An error occured while downloading the file: {err}")
        else:
            logger.info(f"Saving file {file_path}...")
            with open(file_path, "wb") as fd:
                fd.write(file_content)

    def db_table_already_exported(self, model_name: str) -> bool:
        csv_path = self._get_csv_path(model_name)
        if not csv_path:
            return False
        return os.path.isfile(csv_path)

    def model_statics_already_exported(self, model_name: str) -> bool:
        cursor_path = self._get_model_statics_export_cursor_file_name(model_name)
        return os.path.isfile(cursor_path)

    def model_statics_already_deleted(self, model_name: str) -> bool:
        cursor_path = self._get_model_statics_deleted_cursor_file_name(model_name)
        return os.path.isfile(cursor_path)

    def static_file_already_exported(
        self,
        model_name: str,
        model_id: str,
        field_name: str,
        file_obj: Union[FileField, ImageField],
    ) -> bool:
        file_path = self._get_static_file_path(
            model_name, model_id, field_name, file_obj
        )
        return file_path is not None and os.path.isfile(file_path)

    def open_csv_file_from_name(self, model_name: str, mode: str = "rb") -> TextIOBase:
        try:
            return open(self._get_csv_path(model_name), mode)
        except FileNotFoundError:
            raise MissingTableExtractFile(model_name)

    def get_csv_file_size(self, model_name):
        return os.path.getsize(self._get_csv_path(model_name))

    def get_db_removal_cursor(self, model_name: str, cursor_name: str) -> str:
        cursor_path = self._get_db_removal_cursor_path(model_name, cursor_name)
        if not os.path.isfile(cursor_path):
            return None
        with open(cursor_path, "r") as file_obj:
            return file_obj.read()

    def update_db_removal_cursor(
        self, model_name: str, cursor_name: str, new_value: str
    ) -> None:
        cursor_path = self._get_db_removal_cursor_path(model_name, cursor_name)
        with open(cursor_path, "w") as file_obj:
            file_obj.write(new_value)

    def add_model_statics_exported_cursor(self, model_name: str) -> bool:
        cursor_path = self._get_model_statics_export_cursor_file_name(model_name)
        with open(cursor_path, "w") as file_obj:
            file_obj.write("1")

    def add_model_statics_deleted_cursor(self, model_name: str) -> bool:
        cursor_path = self._get_model_statics_deleted_cursor_file_name(model_name)
        with open(cursor_path, "w") as file_obj:
            file_obj.write("1")

    def make_tar_archive(self) -> bool:
        self._raise_if_local_disk_is_full(
            # The resulting tar archive may be huge (depending on the size of
            # the source directory), but shouldn't be bigger than the source
            # directory. So we want to make sure we won't go over the size
            # limit once the tarball will be created by checking that
            # if we double the space occupied by the source directory, we will
            # still be fine.
            needed=get_directory_size(self.get_export_path())
        )
        ret = subprocess.run(  # noqa : PLW1510
            f"tar zcvf {self._get_archive_name()} {self.application.id}",
            cwd=self.export_root_folder,
            shell=True,
        )
        ret.check_returncode()
        self.archive_created = True
        return self.archive_created

    def delete_files(self) -> None:
        try:
            # We want to catch errors in case the folder would already be
            # deleted by log_output which share the same folder
            shutil.rmtree(self.get_export_path())
        except FileNotFoundError:
            pass
        if self.archive_created:
            os.remove(f"{self.export_root_folder}{self._get_archive_name()}")

    def upload_to_s3(self) -> None:
        if not self.archive_created:
            self.make_tar_archive()
        self._upload_file(self._get_archive_name())

    def upload_log_file_to_s3(self) -> None:
        self._upload_file(
            f"{self.application.id}/logs_{self.execution_id}.txt",
            f"logs_{self.execution_id}.txt",
        )

    def _upload_file(self, file_name: str, object_name: str = None) -> None:
        environment = (
            settings.RUNNING_ON if settings.DEVELOPMENT else settings.ENVIRONMENT
        )
        object_name = (
            f"{environment}/"
            f"{self.application.id}_{self.application.namespace}/"
            f"{object_name or file_name}"
        )
        if self._object_exists_on_s3(object_name):
            raise Exception(
                "Attempting to overwrite a file on S3, which is not supposed"
                " to happen. Please check your code."
            )
        s3_client = boto3.client("s3")
        s3_client.upload_file(
            self.export_root_folder + file_name,
            settings.EXTRACTOR_BUCKET_NAME,
            object_name,
        )

    def _get_archive_name(self) -> str:
        return (
            f"app_{self.application.id}_{self.application.namespace}_"
            f"{self.execution_id}.tar.gz"
        )

    def _object_exists_on_s3(self, object_name):
        s3 = boto3.resource("s3")
        try:
            s3.Object(settings.EXTRACTOR_BUCKET_NAME, object_name).load()
        except botocore.exceptions.ClientError as e:
            if e.response["Error"]["Code"] == "404":
                return False
            raise  # Something else has gone wrong.
        return True

    def _get_csv_path(self, model_name: str) -> str:
        return "{path}{file_prefix}{file_name}.csv".format(
            path=self._get_db_folder(),
            file_prefix=self.file_prefix,
            file_name=self.snaken_model_name(model_name),
        )

    def _get_statics_folder(
        self, model_name: str, model_id: str, field_name: str
    ) -> str:
        return (
            f"{self.get_export_path()}statics/"
            f"{self.snaken_model_name(model_name)}/{model_id}/{field_name}"
        )

    def _get_db_folder(self) -> str:
        db_folder = f"{self.get_export_path()}db_tables/"
        if not getattr(self, "_db_folder_initialized", False):
            if not os.path.exists(db_folder):
                os.makedirs(db_folder)
            self._db_folder_initialized = True
        return db_folder

    def _get_db_removal_cursor_folder(self, cursor_name: str) -> str:
        db_folder = f"{self.get_export_path()}_db_removal_cursors/{cursor_name}/"
        cache_key = f"_db_removal_cursor_folder_initialized_{cursor_name}"
        if not getattr(self, cache_key, False):
            if not os.path.exists(db_folder):
                os.makedirs(db_folder)
            setattr(self, cache_key, True)
        return db_folder

    def _get_db_removal_cursor_path(self, model_name: str, cursor_name: str) -> str:
        model_name = self.snaken_model_name(model_name)
        return self._get_db_removal_cursor_folder(cursor_name) + f"{model_name}.txt"

    def _get_model_statics_cursor_folder(
        self,
        cursor_name: str,
    ) -> str:
        cursor_folder = f"{self.get_export_path()}_model_statics_cursors/{cursor_name}/"
        cache_key = f"_model_statics_cursor_folder_initialized_{cursor_name}"
        if not getattr(self, cache_key, False):
            if not os.path.exists(cursor_folder):
                os.makedirs(cursor_folder)
            setattr(self, cache_key, True)
        return cursor_folder

    def _get_model_statics_export_cursor_file_name(self, model_name: str) -> str:
        model_name = self.snaken_model_name(model_name)
        return self._get_model_statics_cursor_folder("export") + f"{model_name}.txt"

    def _get_model_statics_deleted_cursor_file_name(self, model_name: str) -> str:
        model_name = self.snaken_model_name(model_name)
        return self._get_model_statics_cursor_folder("delete") + f"{model_name}.txt"

    def _get_static_file_path(
        self,
        model_name: str,
        model_id: str,
        field_name: str,
        file_obj: Union[FileField, ImageField],
    ) -> Optional[str]:
        if not file_obj.name:
            return None
        static_folder = self._get_statics_folder(model_name, model_id, field_name)
        file_name = file_obj.name.rsplit("/", 1)[-1]
        return f"{static_folder}/{file_name}"

    def _raise_if_local_disk_is_full(self, needed: Optional[int] = None) -> None:
        if not self.disable_free_space_check:
            raise_if_local_disk_is_full(root_folder=self.export_root_folder)
