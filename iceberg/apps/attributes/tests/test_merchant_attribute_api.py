# -*- coding: utf-8 -*-

from apps.attributes.models import (
    LocalizedMerchantAttributeInfo,
    MerchantAttribute,
    MerchantAttributeValue,
    MerchantAttributeValueChoice,
)
from apps.attributes.models.merchant.workflow import MerchantAttributeWorkflow
from apps.stores.tests.test_merchant_setup_mixin import TestMerchantSetupMixin
from apps.testing.factories import ApplicationFactory, MerchantFactory
from django.core.exceptions import ValidationError
from ims.tests import BaseResourceTestCase, BaseTestCase
from lib.models import Visibility
from mock import patch


class TestMerchantAttributeResource(TestMerchantSetupMixin, BaseResourceTestCase):
    def setUp(self):
        super(TestMerchantAttributeResource, self).setUp()
        TestMerchantSetupMixin.set_up(self)
        self.merchant_attribute = self.create_merchant_attribute()

    def test_create_attr_invalid_attr_kind(self):
        with self.assertRaises(ValidationError):
            self.create_merchant_attribute(attribute_kind="_invalid_")

    def test_create_attr_invalid_attr_kind_from_api(self):
        post_data = {
            "key": "key",
            "name": "name",
            "description": "description",
            "value_type": "int",
            "attribute_kind": "__invalid__",
        }
        resp = self.api_client.post(
            "/v1/merchant_attribute/",
            data=post_data,
            authentication=self.get_token(self.app_admin_user),
        )
        self.assertHttpBadRequest(resp)

    def test_create_merchant_attr_provide_app(self):
        post_data = {
            "application": "/v1/application/%s/" % self.application.pk,
            "merchant": "/v1/merchant/%s/" % self.merchant.pk,
            "key": "key",
            "name": "name",
            "description": "description",
            "value_type": "int",
        }
        resp = self.api_client.post(
            "/v1/merchant_attribute/",
            data=post_data,
            authentication=self.get_token(self.app_admin_user),
        )
        self.assertHttpCreated(resp)

    def test_create_merchant_attr(self):
        post_data = {
            "key": "key",
            "name": "name",
            "description": "description",
            "value_type": "int",
        }
        resp = self.api_client.post(
            "/v1/merchant_attribute/",
            data=post_data,
            authentication=self.get_token(self.app_admin_user),
        )
        self.assertHttpCreated(resp)

    def test_should_update_values_visibility_value(self):
        attribute = TestMerchantSetupMixin.create_merchant_attribute(
            self,
            read_permission=Visibility.PUBLIC,
            write_permission=Visibility.APPLICATION_MERCHANT,
        )
        patch_data = {
            "values_visibility": {
                "read_permission": Visibility.APPLICATION_STAFF,
                "write_permission": Visibility.IZBERG_STAFF,
            }
        }

        self.api_client.patch(
            "/v1/merchant_attribute/{}/".format(attribute.id),
            data=patch_data,
            authentication=self.get_token(self.app_admin_user),
        )

        attribute = MerchantAttribute.objects.get(id=attribute.id)
        self.assertEqual(attribute.read_permission, Visibility.APPLICATION_STAFF)
        self.assertEqual(attribute.write_permission, Visibility.IZBERG_STAFF)

    def test_update_merchant_attr_with_ressource(self):
        merchant_attribute = self.create_merchant_attribute(
            key="aéftyfaspp", attribute_kind=None
        )
        new_kind = merchant_attribute.ATTRIBUTE_KIND_CHOICES[0][0]
        post_data = {
            "name": "name changed",
            "description": "description changed",
            "attribute_kind": new_kind,
        }
        resp = self.api_client.put(
            "/v1/merchant_attribute/%s/" % merchant_attribute.pk,
            data=post_data,
            authentication=self.get_token(self.app_admin_user),
        )
        self.assertHttpAccepted(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["attribute_kind"], new_kind)

    def test_list_merchant_attr_with_ressource(self):
        self.create_merchant_attribute(key="aéftyfas")
        resp = self.api_client.get(
            "/v1/merchant_attribute/",
            authentication=self.get_token(self.app_admin_user),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(
            set(data["objects"][0].keys()),
            {
                "merchant",
                "status",
                "description",
                "attribute_kind",
                "languages",
                "application",
                "created_on",
                "last_modified",
                "value_type",
                "key",
                "validation_rules",
                "position",
                "resource_uri",
                "external_id",
                "id",
                "name",
                "values_visibility",
                "creatable",
                "readonly",
                "editable",
                "value_choices",
            },
        )

    def test_fetch_other_app_attribute_and_value_returns_401(self):
        application2 = self.create_application()
        merchant2 = self.create_merchant(application=application2)
        attribute2 = self.create_merchant_attribute(application=application2)
        value2 = self.create_merchant_attribute_value(
            attribute=attribute2, merchant=merchant2
        )
        resp = self.api_client.get(
            "/v1/merchant_attribute/{}/".format(attribute2.id),
            authentication=self.get_token(self.app_admin_user),
        )
        self.assertHttpUnauthorized(resp)
        resp = self.api_client.get(
            "/v1/merchant_attribute_value/{}/".format(value2.id),
            authentication=self.get_token(self.app_admin_user),
        )
        self.assertHttpUnauthorized(resp)

    def test_fetch_merchant_attr_with_ressource(self):
        merchant_attribute = self.create_merchant_attribute(key="AZERTYUJ")
        resp = self.api_client.get(
            "/v1/merchant_attribute/%s/" % merchant_attribute.pk,
            authentication=self.get_token(self.app_admin_user),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(
            set(data.keys()),
            {
                "merchant",
                "status",
                "description",
                "attribute_kind",
                "languages",
                "application",
                "created_on",
                "last_modified",
                "value_type",
                "key",
                "validation_rules",
                "position",
                "resource_uri",
                "external_id",
                "id",
                "name",
                "meta",
                "values_visibility",
                "creatable",
                "readonly",
                "editable",
                "value_choices",
            },
        )

    def test_should_expose_visibility_field(self):
        resp = self.api_client.get(
            "/v1/merchant_attribute/{}/".format(self.merchant_attribute.id),
            authentication=self.get_token(self.app_admin_user),
        )

        data = self.deserialize(resp)
        self.assertIn("values_visibility", data)

    def test_read_attribute_as_merchant_without_app(self):
        merchant_token = self.get_token(self.merchant_user, app_token=False)
        resp = self.api_client.get(
            "/v1/merchant_attribute/{}/".format(self.merchant_attribute.id),
            authentication=merchant_token,
        )
        self.assertHttpUnauthorized(resp)

    def test_update_attribute_as_merchant_with_app(self):
        merchant_token = self.get_token(self.merchant_user, app_token=False)
        data = {"value": "new value"}
        resp = self.api_client.put(
            "/v1/merchant_attribute/{}/?application={}".format(
                self.merchant_attribute.id, self.application.id
            ),
            data=data,
            authentication=merchant_token,
        )
        self.assertHttpUnauthorized(resp)

    def test_read_attribute_as_merchant_with_app(self):
        merchant_token = self.get_token(self.merchant_user, app_token=False)
        resp = self.api_client.get(
            "/v1/merchant_attribute/{}/?application={}".format(
                self.merchant_attribute.id, self.application.id
            ),
            authentication=merchant_token,
        )
        self.assertHttpOK(resp)

    def test_read_attribute_list_as_merchant_without_app(self):
        merchant_token = self.get_token(self.merchant_user, app_token=False)
        resp = self.api_client.get(
            "/v1/merchant_attribute/", authentication=merchant_token
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 0)

    def test_read_attribute_list_as_merchant_with_app(self):
        merchant_token = self.get_token(self.merchant_user, app_token=False)
        resp = self.api_client.get(
            "/v1/merchant_attribute/?application={}".format(self.application.id),
            authentication=merchant_token,
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 1)

    def test_create_merchant_with_nested_attribute_is_no_longer_saved(self):
        attr_value = "É@ÔÖ€£"
        post_data = {
            "name": "Merchant name",
            "attributes": {self.merchant_attribute.key: attr_value},
        }
        resp = self.api_client.post(
            "/v1/merchant/",
            data=post_data,
            authentication=self.get_token(self.app_admin_user),
        )
        self.assertHttpCreated(resp)
        merchant_data = self.deserialize(resp)
        self.assertEqual(
            MerchantAttributeValue.objects.filter(merchant=merchant_data["id"]).count(),
            0,
        )

    def test_can_delete_attribute(self):
        attribute = MerchantAttribute.objects.create(
            key="zerg",
            application=self.application,
            status=MerchantAttributeWorkflow.VISIBLE,
        )

        resp = self.api_client.get(
            "/v1/merchant_attribute/{}/can-delete/".format(attribute.id),
            authentication=self.get_token(self.app_admin_user),
        )

        self.assertHttpOK(resp)

        data = self.deserialize(resp)
        self.assertEqual(data, {"is_available": True})

    def test_can_delete_value_choice(self):
        attribute = MerchantAttribute.objects.create(
            key="zerg",
            application=self.application,
            status=MerchantAttributeWorkflow.VISIBLE,
        )

        MerchantAttributeValue.objects.create(
            value="toto",
            merchant=self.merchant,
            attribute=attribute,
            application=attribute.application,
        )
        value_choice = MerchantAttributeValueChoice.objects.create(
            attribute=attribute, value="Waw!", application=attribute.application
        )

        resp = self.api_client.get(
            "/v1/merchantattributevaluechoice/{}/can-delete/".format(value_choice.id),
            authentication=self.get_token(self.app_admin_user),
        )

        self.assertHttpOK(resp)

        data = self.deserialize(resp)
        self.assertEqual(data, {"is_available": True})

    def test_delete_value_choice(self):
        attribute = MerchantAttribute.objects.create(
            key="zerg",
            application=self.application,
            status=MerchantAttributeWorkflow.VISIBLE,
        )

        value_choice = MerchantAttributeValueChoice.objects.create(
            attribute=attribute, value="toto", application=attribute.application
        )

        value = MerchantAttributeValue.objects.create(
            value="toto",
            merchant=self.merchant,
            attribute=attribute,
            application=attribute.application,
        )

        resp = self.api_client.delete(
            "/v1/merchantattributevaluechoice/{}/".format(value_choice.id),
            authentication=self.get_token(self.app_admin_user),
        )

        self.assertHttpNoContent(resp)

        self.assertFalse(
            MerchantAttributeValueChoice.objects.filter(id=value_choice.id).exists()
        )

        value.refresh_from_db()
        self.assertEqual(value.value, None)

    def test_localized_infos_creation(self):
        application = ApplicationFactory()
        application.create_user(application.contact_user, True)
        resp = self.api_client.post(
            "/v1/merchant_attribute/",
            data={
                "application": "/v1/application/{}/".format(application.id),
                "name": "EN Name",
                "description": "EN Desc",
                "key": "zerg",
                "language": ["fr", "en"],
            },
            authentication=self.get_token(
                application.contact_user, application=application
            ),
            headers={"HTTP_ACCEPT_LANGUAGE": "en"},
        )
        self.assertHttpCreated(resp)
        data = self.deserialize(resp)
        self.assertEqual(data.get("name"), "EN Name")
        self.assertEqual(data.get("description"), "EN Desc")

    def test_localized_infos_edition(self):
        application = ApplicationFactory()
        application.create_user(application.contact_user, True)
        attribute = MerchantAttribute.objects.create(
            key="zerg",
            application=application,
            status=MerchantAttributeWorkflow.VISIBLE,
            languages=["fr", "en"],
        )

        resp = self.api_client.put(
            "/v1/merchant_attribute/{}/".format(attribute.id),
            data={"name": "EN Name", "description": "EN Desc"},
            authentication=self.get_token(
                application.contact_user, application=application
            ),
            headers={"HTTP_ACCEPT_LANGUAGE": "en"},
        )
        self.assertHttpAccepted(resp)
        attribute.refresh_from_db()
        localized = attribute.get_localized_info(application=application, language="en")
        self.assertEqual(localized.get("status"), "visible")
        self.assertEqual(localized.get("language"), "en")
        self.assertEqual(localized.get("name"), "EN Name")
        self.assertEqual(localized.get("description"), "EN Desc")

        resp = self.api_client.put(
            "/v1/merchant_attribute/{}/".format(attribute.id),
            data={"name": "EN Name edited", "description": "EN Desc edited"},
            authentication=self.get_token(
                application.contact_user, application=application
            ),
            headers={"HTTP_ACCEPT_LANGUAGE": "en"},
        )
        self.assertHttpAccepted(resp)
        attribute.refresh_from_db()
        localized = attribute.get_localized_info(application=application, language="en")
        self.assertEqual(localized.get("status"), "visible")
        self.assertEqual(localized.get("language"), "en")
        self.assertEqual(localized.get("name"), "EN Name edited")
        self.assertEqual(localized.get("description"), "EN Desc edited")

    def test_get_attribute_localized_name(self):
        application = ApplicationFactory(language="fr")
        application.create_user(application.contact_user, True)
        attribute = MerchantAttribute.objects.create(
            key="zerg",
            application=application,
            status=MerchantAttributeWorkflow.VISIBLE,
            languages=[],
        )
        LocalizedMerchantAttributeInfo.objects.create(
            attribute=attribute,
            name="FR Name",
            description="FR desc",
            language="fr",
            status="visible",
            application=application,
        )
        resp = self.api_client.get(
            "/v1/merchant_attribute/{}/".format(attribute.id),
            authentication=self.get_token(
                application.contact_user, application=application
            ),
            HTTP_ACCEPT_LANGUAGE="fr",
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data.get("description"), "FR desc")
        self.assertEqual(data.get("name"), "FR Name")


class TestMerchantAttributeEnabledModel(BaseTestCase):
    def test_update_custom_attributes_from_dict_create_value(self):
        merchant = MerchantFactory()
        merchant_attribute = MerchantAttribute.objects.create(
            key="my-attribute",
            application=merchant.application,
            write_permission=MerchantAttribute.APPLICATION_STAFF,
        )

        # when
        res = merchant.update_custom_attributes_from_dict(
            {"my-attribute": "coûcoû"},
            write_permission=MerchantAttribute.APPLICATION_STAFF,
        )

        # then
        self.assertEqual(res, ["my-attribute"])
        self.assertEqual(merchant_attribute.applied_values.get().merchant, merchant)

    def test_update_custom_attributes_from_dict_update_value(self):
        attribute_key = "my-attribute"
        old_value = "coûcoû"
        new_value = "coûcoû2"
        merchant = MerchantFactory()
        merchant_attribute = MerchantAttribute.objects.create(
            key=attribute_key,
            application=merchant.application,
            write_permission=MerchantAttribute.APPLICATION_STAFF,
        )
        merchant.update_custom_attributes_from_dict(
            {attribute_key: old_value},
            write_permission=merchant_attribute.write_permission,
        )

        # when
        res = merchant.update_custom_attributes_from_dict(
            {attribute_key: new_value},
            write_permission=merchant_attribute.write_permission,
        )

        # then
        self.assertEqual(res, [attribute_key])
        self.assertEqual(merchant_attribute.applied_values.get().value, new_value)

    def test_update_izberg_admin_custom_attributes_by_operator_is_ignored(self):
        attribute_key = "izberg-secret-attr"
        value = "secret-valûe"
        merchant = MerchantFactory()
        merchant_attribute = MerchantAttribute.objects.create(
            key=attribute_key,
            application=merchant.application,
            write_permission=MerchantAttribute.IZBERG_STAFF,
        )
        res = merchant.update_custom_attributes_from_dict(
            {attribute_key: value}, write_permission=MerchantAttribute.APPLICATION_ADMIN
        )

        # then
        self.assertEqual(res, [])
        self.assertFalse(merchant_attribute.applied_values.exists())


class TestMerchantAttributeCacheInvalidation(BaseTestCase):
    @patch(
        "apps.attributes.models.merchant.attribute.MerchantAttribute.invalidate_cache"
    )
    def test_localized_info_creation_invalidate_attribute_cache(
        self, mocked_invalidate_cache
    ):
        # given
        application = ApplicationFactory()
        merchant_attribute = MerchantAttribute.objects.create(
            application=application,
            key="my-attribute",
        )
        mocked_invalidate_cache.reset_mock()

        # when
        merchant_attribute.localized_infos.update_or_create(
            language=application.default_language, name="My Attribute"
        )

        # then
        mocked_invalidate_cache.assert_called_once_with(
            application_ids=None, languages=[application.default_language]
        )

    @patch(
        "apps.attributes.models.merchant.attribute.MerchantAttribute.invalidate_cache"
    )
    def test_localized_info_update_invalidate_attribute_cache(
        self, mocked_invalidate_cache
    ):
        # given
        application = ApplicationFactory()
        merchant_attribute = MerchantAttribute.objects.create(
            application=application,
            key="my-attribute",
        )
        merchant_attribute.localized_infos.create(
            language=application.default_language,
            application=application,
        )
        mocked_invalidate_cache.reset_mock()

        # when
        merchant_attribute.localized_infos.update_or_create(
            language=application.default_language, name="My Attribute"
        )

        # then
        mocked_invalidate_cache.assert_called_once_with(
            application_ids=None, languages=[application.default_language]
        )
