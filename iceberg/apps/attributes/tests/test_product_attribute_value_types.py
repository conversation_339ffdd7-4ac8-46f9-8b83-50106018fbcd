# -*- coding: utf-8 -*-

from inspect import isclass

from apps.attributes.models import ProductAttribute
from apps.ice_applications.models import Application
from apps.products.models import ProductOffer
from apps.stores.models import Merchant
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.models import ContentType
from django.db.models import Model
from ims.tests import BaseTestCase
from reference import status

User = get_user_model()


class TestProductAtributeValueTypesModel(BaseTestCase):
    fixtures = BaseTestCase.fixtures + ["test_product_attributes"]

    def setUp(self):
        super(TestProductAtributeValueTypesModel, self).setUp()
        self.application = Application.objects.get(namespace="app-a")
        merchant_1 = Merchant.objects.get(id=20001)
        merchant_2 = Merchant.objects.get(id=20002)
        merchant_1.actions.create_default_tax_settings()
        merchant_2.actions.create_default_tax_settings()
        self.user_a = User.objects.get(username="user-a")
        self.offer_1 = ProductOffer.objects.get(pk=20001)

        self.offer_2 = ProductOffer.objects.get(pk=20002)

    def create_attribute(self, value_type):
        if isclass(value_type) and issubclass(value_type, Model):
            value_entity_type = ContentType.objects.get_for_model(value_type)
            value_type = "entity"
        else:
            value_entity_type = None

        return ProductAttribute.objects.create(
            application=self.application,
            key="%s_key" % value_type,
            value_type=value_type,
            value_entity_type=value_entity_type,
        )

    def test_add_str_attribute_to_product_offer_is_returned_in_get_data(self):
        value = " YÖLO "
        attribute = self.create_attribute("str")
        self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        data = self.offer_1.get_data()
        self.assertEqual(data.get("attributes", {}).get(attribute.key), value)

    def test_add_text_attribute_to_product_offer_is_returned_in_get_data(self):
        value = " YÖLO "
        attribute = self.create_attribute("text")
        self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        data = self.offer_1.get_data()
        self.assertEqual(data.get("attributes", {}).get(attribute.key), value)

    def test_add_int_attribute_to_product_offer_is_returned_in_get_data(self):
        value = 200
        attribute = self.create_attribute("int")
        self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        data = self.offer_1.get_data()
        self.assertEqual(data.get("attributes", {}).get(attribute.key), value)

    def test_add_decimal_attribute_to_product_offer_is_returned_in_get_data(self):
        from decimal import Decimal

        value = Decimal("200.000024")
        attribute = self.create_attribute("decimal")
        self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        data = self.offer_1.get_data()
        self.assertEqual(data.get("attributes", {}).get(attribute.key), value)

    def test_add_bool_attribute_to_product_offer_is_returned_in_get_data(self):
        value = True
        attribute = self.create_attribute("bool")
        self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        data = self.offer_1.get_data()
        self.assertEqual(data.get("attributes", {}).get(attribute.key), value)

    def test_add_datetime_attribute_to_product_offer_is_returned_in_get_data(self):
        attribute = self.create_attribute("datetime")
        value = attribute.created_on
        self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        data = self.offer_1.get_data()
        self.assertEqual(data.get("attributes", {}).get(attribute.key), value)

    def test_add_date_attribute_to_product_offer_is_returned_in_get_data(self):
        attribute = self.create_attribute("date")
        value = attribute.created_on.date()
        self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        data = self.offer_1.get_data()
        self.assertEqual(data.get("attributes", {}).get(attribute.key), value)

    def test_add_time_attribute_to_product_offer_is_returned_in_get_data(self):
        attribute = self.create_attribute("time")
        value = attribute.created_on.time()
        self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        data = self.offer_1.get_data()
        self.assertEqual(data.get("attributes", {}).get(attribute.key), value)

    def test_add_str_list_attribute_to_product_offer_is_returned_in_get_data(self):
        value = ["YÖLO", "YOLO2"]
        attribute = self.create_attribute("str_list")
        self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        data = self.offer_1.get_data()
        self.assertEqual(data.get("attributes", {}).get(attribute.key), value)

    def test_add_int_list_attribute_to_product_offer_is_returned_in_get_data(self):
        value = [120, -925]
        attribute = self.create_attribute("int_list")
        self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        data = self.offer_1.get_data()
        self.assertEqual(data.get("attributes", {}).get(attribute.key), value)

    def test_add_json_attribute_to_product_offer_is_returned_in_get_data(self):
        value = {"int": 10, "float": 5.60, "string": "yôlo¢", "bool": True}
        attribute = self.create_attribute("json")
        self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        data = self.offer_1.get_data()
        self.assertEqual(data.get("attributes", {}).get(attribute.key), value)

    def test_add_entity_attribute_to__offer_is_returned_as_resource_uri_in_get_data(
        self,
    ):
        value = self.offer_2
        attribute = self.create_attribute(ProductOffer)
        self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        data = self.offer_1.get_data()
        self.assertEqual(
            data.get("attributes", {}).get(attribute.key), value.get_resource_uri()
        )

    def test_add_twice_str_attribute_to_product_offer_returns_updated_false(self):
        value = " YÖLO "
        attribute = self.create_attribute("str")
        updated = self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        self.assertEqual(updated, True)
        updated = self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        self.assertEqual(updated, False)

    def test_add_twice_text_attribute_to_product_offer_returns_updated_false(self):
        value = " YÖLO "
        attribute = self.create_attribute("text")
        updated = self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        self.assertEqual(updated, True)
        updated = self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        self.assertEqual(updated, False)

    def test_add_twice_int_attribute_to_product_offer_returns_updated_false(self):
        value = 200
        attribute = self.create_attribute("int")
        updated = self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        self.assertEqual(updated, True)
        updated = self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        self.assertEqual(updated, False)

    def test_add_twice_decimal_attribute_to_product_offer_returns_updated_false(self):
        from decimal import Decimal

        value = Decimal("200.000024")
        attribute = self.create_attribute("decimal")
        updated = self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        self.assertEqual(updated, True)
        updated = self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        self.assertEqual(updated, False)

    def test_add_twice_bool_attribute_to_product_offer_returns_updated_false(self):
        value = True
        attribute = self.create_attribute("bool")
        updated = self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        self.assertEqual(updated, True)
        updated = self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        self.assertEqual(updated, False)

    def test_add_twice_datetime_attribute_to_product_offer_returns_updated_false(self):
        attribute = self.create_attribute("datetime")
        value = attribute.created_on
        updated = self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        self.assertEqual(updated, True)
        updated = self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        self.assertEqual(updated, False)

    def test_add_twice_date_attribute_to_product_offer_returns_updated_false(self):
        attribute = self.create_attribute("date")
        value = attribute.created_on.date()
        updated = self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        self.assertEqual(updated, True)
        updated = self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        self.assertEqual(updated, False)

    def test_add_twice_time_attribute_to_product_offer_returns_updated_false(self):
        attribute = self.create_attribute("time")
        value = attribute.created_on.time()
        updated = self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        self.assertEqual(updated, True)
        updated = self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        self.assertEqual(updated, False)

    def test_add_twice_str_list_attribute_to_product_offer_returns_updated_false(self):
        value = ["YÖLO", "YOLO2"]
        attribute = self.create_attribute("str_list")
        updated = self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        self.assertEqual(updated, True)
        updated = self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        self.assertEqual(updated, False)

    def test_add_twice_int_list_attribute_to_product_offer_returns_updated_false(self):
        value = [120, -925]
        attribute = self.create_attribute("int_list")
        updated = self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        self.assertEqual(updated, True)
        updated = self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        self.assertEqual(updated, False)

    def test_add_twice_json_attribute_to_product_offer_returns_updated_false(self):
        value = {"int": 10, "float": 5.60, "string": "yôlo¢", "bool": True}
        attribute = self.create_attribute("json")
        updated = self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        self.assertEqual(updated, True)
        updated = self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        self.assertEqual(updated, False)

    def test_add_twice_entity_attribute_to_product_offer_returns_updated_false(self):
        value = self.offer_2
        attribute = self.create_attribute(ProductOffer)
        updated = self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        self.assertEqual(updated, True)
        updated = self.offer_1.set_attribute_value(
            attribute,
            value=value,
        )
        self.assertEqual(updated, False)

    def test_get_offers_data_sort_by_offer_ranking_method(self):
        data = self.offer_1.product.viewers.get_offers_data(
            offer_ranking_method="price"
        )
        self.assertLess(data[0]["price"], data[1]["price"])

        data = self.offer_1.product.viewers.get_offers_data(
            offer_ranking_method="price_with_vat"
        )
        self.assertEqual(data[0]["price_with_vat"], self.offer_2.price_with_vat)
        self.assertEqual(data[1]["price_with_vat"], self.offer_1.price_with_vat)

        data = self.offer_1.product.viewers.get_offers_data(
            offer_ranking_method="-price"
        )
        self.assertEqual(data[0]["id"], self.offer_1.id)

        self.offer_1.status = status.PRODUCT_OFFER_STATUS_DRAFT
        self.offer_1.save()
        data = self.offer_1.product.viewers.get_offers_data(
            offer_ranking_method="-active,-price", visible_only=False
        )
        self.assertEqual(data[0]["id"], self.offer_2.id)

        data = self.offer_1.product.viewers.get_offers_data(
            offer_ranking_method="-active,price", visible_only=False
        )
        self.assertEqual(data[0]["id"], self.offer_2.id)
