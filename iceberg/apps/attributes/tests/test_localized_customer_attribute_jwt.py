# -*- coding: utf-8 -*-


from apps.attributes.models import CustomerEnabledVisibility
from apps.testing.factories import (
    ApplicationFactory,
    LocalizedCustomerAttributeInfoFactory,
    MerchantFactory,
    UserFactory,
)
from ims.tests import BaseResourceTestCase
from ims.tests.jwt_tokens import CUSTOMER_TOKENS, GLOBAL_TOKENS


class GetDetailTestCase(BaseResourceTestCase):
    def test_staff_attr_value_as_internal(self):
        value = LocalizedCustomerAttributeInfoFactory(
            application__id=1,
            attribute__read_permission=CustomerEnabledVisibility.IZBERG_STAFF,
            attribute__write_permission=CustomerEnabledVisibility.IZBERG_STAFF,
        )

        resp = self.api_client.get(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            authentication="Bearer {}".format(
                GLOBAL_TOKENS["INTERNAL_M2M_WRITE"]["token"]
            ),
        )

        self.assertHttpForbidden(resp)
        self.assertEqual(
            self.deserialize(resp),
            {
                "error": "insufficient_scope",
                "error_description": "Invalid owner type for this action",
                "error_code": "OAUTH.INVALID_OWNER_TYPE",
                "error_context": {
                    "action": "read_detail",
                    "owner_type": "internal",
                    "accepted_owner_types": ["operator", "merchant", "buyer"],
                    "resource_name": "localized_customer_attribute",
                },
            },
        )

    def test_staff_attr_value_as_operator(self):
        value = LocalizedCustomerAttributeInfoFactory(
            application__id=1,
            attribute__read_permission=CustomerEnabledVisibility.IZBERG_STAFF,
            attribute__write_permission=CustomerEnabledVisibility.IZBERG_STAFF,
        )

        resp = self.api_client.get(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_READ"]["token"]
            ),
        )

        self.assertHttpUnauthorized(resp)
        self.assertEqual(
            self.deserialize(resp),
            {
                "error_code": "OAUTH.INVALID_TOKEN",
                "error_context": {
                    "action": "read_detail",
                    "scope": "customer:read",
                    "merchant_id": None,
                    "application_id": 1,
                },
                "error_description": "Invalid access token for this action",
                "error": "invalid_token",
            },
        )

    def test_operator_attr_value_as_operator(self):
        value = LocalizedCustomerAttributeInfoFactory(
            application__id=1,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
        )

        resp = self.api_client.get(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_READ"]["token"]
            ),
        )

        self.assertHttpOK(resp)
        self.assertEqual(self.deserialize(resp)["id"], value.id)

    def test_customer_attr_value_as_operator(self):
        value = LocalizedCustomerAttributeInfoFactory(
            application__id=1,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_CUSTOMER,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_CUSTOMER,
        )

        resp = self.api_client.get(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_READ"]["token"]
            ),
        )

        self.assertHttpOK(resp)
        self.assertEqual(self.deserialize(resp)["id"], value.id)

    def test_merchant_attr_value_as_operator(self):
        value = LocalizedCustomerAttributeInfoFactory(
            application__id=1,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_MERCHANT,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
        )

        resp = self.api_client.get(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_READ"]["token"]
            ),
        )

        self.assertHttpOK(resp)
        self.assertEqual(self.deserialize(resp)["id"], value.id)

    def test_public_attr_value_as_operator(self):
        value = LocalizedCustomerAttributeInfoFactory(
            application__id=1,
            attribute__read_permission=CustomerEnabledVisibility.PUBLIC,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
        )

        resp = self.api_client.get(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_READ"]["token"]
            ),
        )

        self.assertHttpOK(resp)
        self.assertEqual(self.deserialize(resp)["id"], value.id)

    def test_staff_attr_value_as_merchant(self):
        merchant = MerchantFactory(application__id=1, id=2)
        value = LocalizedCustomerAttributeInfoFactory(
            application=merchant.application,
            attribute__read_permission=CustomerEnabledVisibility.IZBERG_STAFF,
            attribute__write_permission=CustomerEnabledVisibility.IZBERG_STAFF,
        )

        resp = self.api_client.get(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_MERCHANT_2_READ"]["token"]
            ),
        )

        self.assertHttpUnauthorized(resp)
        self.assertEqual(
            self.deserialize(resp),
            {
                "error_code": "OAUTH.INVALID_TOKEN",
                "error_context": {
                    "action": "read_detail",
                    "scope": "customer:read",
                    "merchant_id": 2,
                    "application_id": 1,
                },
                "error_description": "Invalid access token for this action",
                "error": "invalid_token",
            },
        )

    def test_operator_attr_value_as_merchant(self):
        merchant = MerchantFactory(application__id=1, id=2)
        value = LocalizedCustomerAttributeInfoFactory(
            application=merchant.application,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
        )

        resp = self.api_client.get(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_MERCHANT_2_READ"]["token"]
            ),
        )

        self.assertHttpUnauthorized(resp)
        self.assertEqual(
            self.deserialize(resp),
            {
                "error_code": "OAUTH.INVALID_TOKEN",
                "error_context": {
                    "action": "read_detail",
                    "scope": "customer:read",
                    "merchant_id": 2,
                    "application_id": 1,
                },
                "error_description": "Invalid access token for this action",
                "error": "invalid_token",
            },
        )

    def test_customer_attr_value_as_merchant(self):
        merchant = MerchantFactory(application__id=1, id=2)
        value = LocalizedCustomerAttributeInfoFactory(
            application=merchant.application,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_CUSTOMER,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_CUSTOMER,
        )

        resp = self.api_client.get(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_MERCHANT_2_READ"]["token"]
            ),
        )

        self.assertHttpUnauthorized(resp)
        self.assertEqual(
            self.deserialize(resp),
            {
                "error_code": "OAUTH.INVALID_TOKEN",
                "error_context": {
                    "action": "read_detail",
                    "scope": "customer:read",
                    "merchant_id": 2,
                    "application_id": 1,
                },
                "error_description": "Invalid access token for this action",
                "error": "invalid_token",
            },
        )

    def test_merchant_attr_value_as_merchant(self):
        merchant = MerchantFactory(application__id=1, id=2)
        value = LocalizedCustomerAttributeInfoFactory(
            application=merchant.application,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_MERCHANT,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
        )

        resp = self.api_client.get(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_MERCHANT_2_READ"]["token"]
            ),
        )

        self.assertHttpOK(resp)
        self.assertEqual(self.deserialize(resp)["id"], value.id)

    def test_public_attr_value_as_merchant(self):
        merchant = MerchantFactory(application__id=1, id=2)
        value = LocalizedCustomerAttributeInfoFactory(
            application=merchant.application,
            attribute__read_permission=CustomerEnabledVisibility.PUBLIC,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
        )

        resp = self.api_client.get(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_MERCHANT_2_READ"]["token"]
            ),
        )

        self.assertHttpOK(resp)
        self.assertEqual(self.deserialize(resp)["id"], value.id)

    def test_merchant_attr_value_as_buyer(self):
        app = ApplicationFactory(id=1)
        UserFactory(id=1001, application=app)
        value = LocalizedCustomerAttributeInfoFactory(
            application=app,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_MERCHANT,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
        )

        resp = self.api_client.get(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_BUYER_1001_READ"]["token"]
            ),
        )

        self.assertHttpUnauthorized(resp)
        self.assertEqual(
            self.deserialize(resp),
            {
                "error": "invalid_token",
                "error_description": "Invalid access token for this action",
                "error_code": "OAUTH.INVALID_TOKEN",
                "error_context": {
                    "action": "read_detail",
                    "scope": "customer:read",
                    "application_id": 1,
                    "merchant_id": None,
                },
            },
        )

    def test_public_attr_value_as_buyer(self):
        app = ApplicationFactory(id=1)
        UserFactory(id=1001, application=app)
        value = LocalizedCustomerAttributeInfoFactory(
            application=app,
            attribute__read_permission=CustomerEnabledVisibility.PUBLIC,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
        )

        resp = self.api_client.get(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_BUYER_1001_READ"]["token"]
            ),
        )

        self.assertHttpOK(resp)
        self.assertEqual(self.deserialize(resp)["id"], value.id)


class GetListTestCase(BaseResourceTestCase):
    def test_as_internal_forbidden(self):
        resp = self.api_client.get(
            "/v1/localized_customer_attribute/",
            authentication="Bearer {}".format(
                GLOBAL_TOKENS["INTERNAL_M2M_WRITE"]["token"]
            ),
        )

        self.assertHttpForbidden(resp)
        self.assertEqual(
            self.deserialize(resp),
            {
                "error": "insufficient_scope",
                "error_description": "Invalid owner type for this action",
                "error_code": "OAUTH.INVALID_OWNER_TYPE",
                "error_context": {
                    "action": "read_list",
                    "owner_type": "internal",
                    "accepted_owner_types": ["operator", "merchant", "buyer"],
                    "resource_name": "localized_customer_attribute",
                },
            },
        )

    def test_as_operator_shows_only_visible_from_app(self):
        application = ApplicationFactory(id=1)
        LocalizedCustomerAttributeInfoFactory(
            application=application,
            attribute__read_permission=CustomerEnabledVisibility.IZBERG_STAFF,
            attribute__write_permission=CustomerEnabledVisibility.IZBERG_STAFF,
        )
        app_staff_attr_val = LocalizedCustomerAttributeInfoFactory(
            application=application,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
        )
        customer_attr_val = LocalizedCustomerAttributeInfoFactory(
            application=application,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_CUSTOMER,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
        )
        merchant_attr_val = LocalizedCustomerAttributeInfoFactory(
            application=application,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_MERCHANT,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
        )
        public_attr_val = LocalizedCustomerAttributeInfoFactory(
            application=application,
            attribute__read_permission=CustomerEnabledVisibility.PUBLIC,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
        )
        LocalizedCustomerAttributeInfoFactory(
            application__id=2,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
        )

        resp = self.api_client.get(
            "/v1/localized_customer_attribute/",
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_READ"]["token"]
            ),
        )

        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        attr_ids = {a["id"] for a in data["objects"]}
        expected_ids = {
            app_staff_attr_val.id,
            customer_attr_val.id,
            merchant_attr_val.id,
            public_attr_val.id,
        }
        self.assertEqual(attr_ids, expected_ids)

    def test_as_merchant_shows_only_visible_from_app(self):
        merchant = MerchantFactory(id=2, application__id=1)
        LocalizedCustomerAttributeInfoFactory(
            application=merchant.application,
            attribute__read_permission=CustomerEnabledVisibility.IZBERG_STAFF,
            attribute__write_permission=CustomerEnabledVisibility.IZBERG_STAFF,
        )
        LocalizedCustomerAttributeInfoFactory(
            application=merchant.application,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
        )
        LocalizedCustomerAttributeInfoFactory(
            application=merchant.application,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_CUSTOMER,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
        )
        merchant_attr_val = LocalizedCustomerAttributeInfoFactory(
            application=merchant.application,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_MERCHANT,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
        )
        public_attr_val = LocalizedCustomerAttributeInfoFactory(
            application=merchant.application,
            attribute__read_permission=CustomerEnabledVisibility.PUBLIC,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
        )
        LocalizedCustomerAttributeInfoFactory(
            application__id=2,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
        )

        resp = self.api_client.get(
            "/v1/localized_customer_attribute/",
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_MERCHANT_2_READ"]["token"]
            ),
        )

        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        attr_ids = {a["id"] for a in data["objects"]}
        expected_ids = {merchant_attr_val.id, public_attr_val.id}
        self.assertEqual(attr_ids, expected_ids)

    def test_as_buyer_shows_only_visible_from_app(self):
        app = ApplicationFactory(id=1)
        UserFactory(id=1001, application=app)
        LocalizedCustomerAttributeInfoFactory(
            application=app,
            attribute__read_permission=CustomerEnabledVisibility.IZBERG_STAFF,
            attribute__write_permission=CustomerEnabledVisibility.IZBERG_STAFF,
        )
        LocalizedCustomerAttributeInfoFactory(
            application=app,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
        )
        LocalizedCustomerAttributeInfoFactory(
            application=app,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_CUSTOMER,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
        )
        LocalizedCustomerAttributeInfoFactory(
            application=app,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_MERCHANT,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
        )
        public_attr_val = LocalizedCustomerAttributeInfoFactory(
            application=app,
            attribute__read_permission=CustomerEnabledVisibility.PUBLIC,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
        )
        LocalizedCustomerAttributeInfoFactory(
            application__id=2,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
        )

        resp = self.api_client.get(
            "/v1/localized_customer_attribute/",
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_BUYER_1001_READ"]["token"]
            ),
        )

        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 1)
        self.assertEqual(data["objects"][0]["id"], public_attr_val.id)


class PutTestCase(BaseResourceTestCase):
    def test_izb_staff_attribute_as_internal(self):
        value = LocalizedCustomerAttributeInfoFactory(
            application__id=1,
            attribute__read_permission=CustomerEnabledVisibility.IZBERG_STAFF,
            attribute__write_permission=CustomerEnabledVisibility.IZBERG_STAFF,
            name="name",
        )

        resp = self.api_client.put(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            data={"name": "new name"},
            authentication="Bearer {}".format(
                GLOBAL_TOKENS["INTERNAL_M2M_WRITE"]["token"]
            ),
        )

        self.assertHttpForbidden(resp)
        self.assertEqual(
            self.deserialize(resp),
            {
                "error": "insufficient_scope",
                "error_description": "Invalid owner type for this action",
                "error_code": "OAUTH.INVALID_OWNER_TYPE",
                "error_context": {
                    "action": "update_detail",
                    "owner_type": "internal",
                    "accepted_owner_types": ["operator"],
                    "resource_name": "localized_customer_attribute",
                },
            },
        )

    def test_izb_staff_attribute_as_operator(self):
        value = LocalizedCustomerAttributeInfoFactory(
            application__id=1,
            attribute__read_permission=CustomerEnabledVisibility.IZBERG_STAFF,
            attribute__write_permission=CustomerEnabledVisibility.IZBERG_STAFF,
            name="name",
        )

        resp = self.api_client.put(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            data={"name": "new name"},
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_ADMIN"]["token"]
            ),
        )

        self.assertHttpUnauthorized(resp)
        self.assertEqual(
            self.deserialize(resp),
            {
                "error_description": "Invalid access token for this action",
                "error_context": {
                    "action": "read_detail",
                    "scope": "customer:admin",
                    "merchant_id": None,
                    "application_id": 1,
                },
                "error_code": "OAUTH.INVALID_TOKEN",
                "error": "invalid_token",
            },
        )

    def test_operator_read_and_write_attribute_as_operator(self):
        value = LocalizedCustomerAttributeInfoFactory(
            application__id=1,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
            name="name",
        )

        resp = self.api_client.put(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            data={"name": "new name"},
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_ADMIN"]["token"]
            ),
        )

        self.assertHttpAccepted(resp)
        self.assertEqual(self.deserialize(resp)["id"], value.id)
        self.assertEqual(self.deserialize(resp)["name"], "new name")

    def test_operator_read_only_attribute_as_operator(self):
        value = LocalizedCustomerAttributeInfoFactory(
            application__id=1,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
            attribute__write_permission=CustomerEnabledVisibility.IZBERG_STAFF,
            name="name",
        )

        resp = self.api_client.put(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            data={"name": "new name"},
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_ADMIN"]["token"]
            ),
        )

        self.assertHttpUnauthorized(resp)
        self.assertEqual(
            self.deserialize(resp),
            {
                "error_description": "Invalid access token for this action",
                "error_context": {
                    "action": "update_detail",
                    "scope": "customer:admin",
                    "merchant_id": None,
                    "application_id": 1,
                },
                "error_code": "OAUTH.INVALID_TOKEN",
                "error": "invalid_token",
            },
        )

    def test_merchant_attribute_as_operator(self):
        value = LocalizedCustomerAttributeInfoFactory(
            application__id=1,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_MERCHANT,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
            name="name",
        )

        resp = self.api_client.put(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            data={"name": "new name"},
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_ADMIN"]["token"]
            ),
        )

        self.assertHttpAccepted(resp)
        self.assertEqual(self.deserialize(resp)["id"], value.id)
        self.assertEqual(self.deserialize(resp)["name"], "new name")

    def test_customer_attribute_as_operator(self):
        value = LocalizedCustomerAttributeInfoFactory(
            application__id=1,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_CUSTOMER,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
            name="name",
        )

        resp = self.api_client.put(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            data={"name": "new name"},
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_ADMIN"]["token"]
            ),
        )

        self.assertHttpAccepted(resp)
        self.assertEqual(self.deserialize(resp)["id"], value.id)
        self.assertEqual(self.deserialize(resp)["name"], "new name")

    def test_public_attribute_as_operator(self):
        value = LocalizedCustomerAttributeInfoFactory(
            application__id=1,
            attribute__read_permission=CustomerEnabledVisibility.PUBLIC,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
            name="name",
        )

        resp = self.api_client.put(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            data={"name": "new name"},
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_ADMIN"]["token"]
            ),
        )

        self.assertHttpAccepted(resp)
        self.assertEqual(self.deserialize(resp)["id"], value.id)
        self.assertEqual(self.deserialize(resp)["name"], "new name")

    def test_izb_staff_attribute_as_merchant(self):
        merchant = MerchantFactory(id=2, application__id=1)
        value = LocalizedCustomerAttributeInfoFactory(
            application=merchant.application,
            attribute__read_permission=CustomerEnabledVisibility.IZBERG_STAFF,
            attribute__write_permission=CustomerEnabledVisibility.IZBERG_STAFF,
            name="name",
        )

        resp = self.api_client.put(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            data={"name": "new name"},
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_MERCHANT_2_WRITE"]["token"]
            ),
        )

        self.assertHttpForbidden(resp)
        self.assertEqual(
            self.deserialize(resp),
            {
                "error": "insufficient_scope",
                "error_description": "Invalid owner type for this action",
                "error_code": "OAUTH.INVALID_OWNER_TYPE",
                "error_context": {
                    "action": "update_detail",
                    "owner_type": "merchant",
                    "accepted_owner_types": ["operator"],
                    "resource_name": "localized_customer_attribute",
                },
            },
        )

    def test_operator_attribute_as_merchant(self):
        merchant = MerchantFactory(id=2, application__id=1)
        value = LocalizedCustomerAttributeInfoFactory(
            application=merchant.application,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
            name="name",
        )

        resp = self.api_client.put(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            data={"name": "new name"},
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_MERCHANT_2_WRITE"]["token"]
            ),
        )

        self.assertHttpForbidden(resp)
        self.assertEqual(
            self.deserialize(resp),
            {
                "error": "insufficient_scope",
                "error_description": "Invalid owner type for this action",
                "error_code": "OAUTH.INVALID_OWNER_TYPE",
                "error_context": {
                    "action": "update_detail",
                    "owner_type": "merchant",
                    "accepted_owner_types": ["operator"],
                    "resource_name": "localized_customer_attribute",
                },
            },
        )

    def test_customer_attribute_as_merchant(self):
        merchant = MerchantFactory(id=2, application__id=1)
        value = LocalizedCustomerAttributeInfoFactory(
            application=merchant.application,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_CUSTOMER,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_CUSTOMER,
            name="name",
        )

        resp = self.api_client.put(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            data={"name": "new name"},
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_MERCHANT_2_WRITE"]["token"]
            ),
        )

        self.assertHttpForbidden(resp)
        self.assertEqual(
            self.deserialize(resp),
            {
                "error": "insufficient_scope",
                "error_description": "Invalid owner type for this action",
                "error_code": "OAUTH.INVALID_OWNER_TYPE",
                "error_context": {
                    "action": "update_detail",
                    "owner_type": "merchant",
                    "accepted_owner_types": ["operator"],
                    "resource_name": "localized_customer_attribute",
                },
            },
        )

    def test_merchant_attribute_as_merchant(self):
        merchant = MerchantFactory(id=2, application__id=1)
        value = LocalizedCustomerAttributeInfoFactory(
            application=merchant.application,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_MERCHANT,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
            name="name",
        )

        resp = self.api_client.put(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            data={"name": "new name"},
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_MERCHANT_2_WRITE"]["token"]
            ),
        )

        self.assertHttpForbidden(resp)
        self.assertEqual(
            self.deserialize(resp),
            {
                "error": "insufficient_scope",
                "error_description": "Invalid owner type for this action",
                "error_code": "OAUTH.INVALID_OWNER_TYPE",
                "error_context": {
                    "action": "update_detail",
                    "owner_type": "merchant",
                    "accepted_owner_types": ["operator"],
                    "resource_name": "localized_customer_attribute",
                },
            },
        )

    def test_public_attribute_as_merchant(self):
        merchant = MerchantFactory(id=2, application__id=1)
        value = LocalizedCustomerAttributeInfoFactory(
            application=merchant.application,
            attribute__read_permission=CustomerEnabledVisibility.PUBLIC,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
            name="name",
        )

        resp = self.api_client.put(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            data={"name": "new name"},
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_MERCHANT_2_WRITE"]["token"]
            ),
        )

        self.assertHttpForbidden(resp)
        self.assertEqual(
            self.deserialize(resp),
            {
                "error": "insufficient_scope",
                "error_description": "Invalid owner type for this action",
                "error_code": "OAUTH.INVALID_OWNER_TYPE",
                "error_context": {
                    "action": "update_detail",
                    "owner_type": "merchant",
                    "accepted_owner_types": ["operator"],
                    "resource_name": "localized_customer_attribute",
                },
            },
        )


class PatchTestCase(BaseResourceTestCase):
    def test_operator_read_and_write_attribute_as_operator(self):
        value = LocalizedCustomerAttributeInfoFactory(
            application__id=1,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
            name="name",
        )

        resp = self.api_client.patch(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            data={"name": "new name"},
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_ADMIN"]["token"]
            ),
        )

        self.assertHttpAccepted(resp)
        self.assertEqual(self.deserialize(resp)["id"], value.id)
        self.assertEqual(self.deserialize(resp)["name"], "new name")


class DeleteTestCase(BaseResourceTestCase):
    def test_staff_attr_value_as_internal(self):
        value = LocalizedCustomerAttributeInfoFactory(
            application__id=1,
            attribute__read_permission=CustomerEnabledVisibility.IZBERG_STAFF,
            attribute__write_permission=CustomerEnabledVisibility.IZBERG_STAFF,
        )

        resp = self.api_client.delete(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            authentication="Bearer {}".format(
                GLOBAL_TOKENS["INTERNAL_M2M_WRITE"]["token"]
            ),
        )

        self.assertHttpForbidden(resp)
        self.assertEqual(
            self.deserialize(resp),
            {
                "error": "insufficient_scope",
                "error_description": "Invalid owner type for this action",
                "error_code": "OAUTH.INVALID_OWNER_TYPE",
                "error_context": {
                    "action": "delete_detail",
                    "owner_type": "internal",
                    "accepted_owner_types": ["operator"],
                    "resource_name": "localized_customer_attribute",
                },
            },
        )

    def test_staff_attr_value_as_operator(self):
        value = LocalizedCustomerAttributeInfoFactory(
            application__id=1,
            attribute__read_permission=CustomerEnabledVisibility.IZBERG_STAFF,
            attribute__write_permission=CustomerEnabledVisibility.IZBERG_STAFF,
        )

        resp = self.api_client.delete(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_ADMIN"]["token"]
            ),
        )

        self.assertHttpUnauthorized(resp)
        self.assertEqual(
            self.deserialize(resp),
            {
                "error_code": "OAUTH.INVALID_TOKEN",
                "error_context": {
                    "action": "read_detail",
                    "scope": "customer:admin",
                    "merchant_id": None,
                    "application_id": 1,
                },
                "error_description": "Invalid access token for this action",
                "error": "invalid_token",
            },
        )

    def test_operator_attr_value_as_operator(self):
        value = LocalizedCustomerAttributeInfoFactory(
            application__id=1,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
        )

        resp = self.api_client.delete(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_ADMIN"]["token"]
            ),
        )

        self.assertHttpNoContent(resp)

    def test_customer_attr_value_as_operator(self):
        value = LocalizedCustomerAttributeInfoFactory(
            application__id=1,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_CUSTOMER,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_CUSTOMER,
        )

        resp = self.api_client.delete(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_ADMIN"]["token"]
            ),
        )

        self.assertHttpNoContent(resp)

    def test_merchant_attr_value_as_operator(self):
        value = LocalizedCustomerAttributeInfoFactory(
            application__id=1,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_MERCHANT,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
        )

        resp = self.api_client.delete(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_ADMIN"]["token"]
            ),
        )

        self.assertHttpNoContent(resp)

    def test_public_attr_value_as_operator(self):
        value = LocalizedCustomerAttributeInfoFactory(
            application__id=1,
            attribute__read_permission=CustomerEnabledVisibility.PUBLIC,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
        )

        resp = self.api_client.delete(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_ADMIN"]["token"]
            ),
        )

        self.assertHttpNoContent(resp)

    def test_staff_attr_value_as_merchant(self):
        merchant = MerchantFactory(application__id=1, id=2)
        value = LocalizedCustomerAttributeInfoFactory(
            application=merchant.application,
            attribute__read_permission=CustomerEnabledVisibility.IZBERG_STAFF,
            attribute__write_permission=CustomerEnabledVisibility.IZBERG_STAFF,
        )

        resp = self.api_client.delete(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_MERCHANT_2_WRITE"]["token"]
            ),
        )

        self.assertHttpForbidden(resp)
        self.assertEqual(
            self.deserialize(resp),
            {
                "error": "insufficient_scope",
                "error_description": "Invalid owner type for this action",
                "error_code": "OAUTH.INVALID_OWNER_TYPE",
                "error_context": {
                    "action": "delete_detail",
                    "owner_type": "merchant",
                    "accepted_owner_types": ["operator"],
                    "resource_name": "localized_customer_attribute",
                },
            },
        )

    def test_operator_attr_value_as_merchant(self):
        merchant = MerchantFactory(application__id=1, id=2)
        value = LocalizedCustomerAttributeInfoFactory(
            application=merchant.application,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
        )

        resp = self.api_client.delete(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_MERCHANT_2_WRITE"]["token"]
            ),
        )

        self.assertHttpForbidden(resp)
        self.assertEqual(
            self.deserialize(resp),
            {
                "error": "insufficient_scope",
                "error_description": "Invalid owner type for this action",
                "error_code": "OAUTH.INVALID_OWNER_TYPE",
                "error_context": {
                    "action": "delete_detail",
                    "owner_type": "merchant",
                    "accepted_owner_types": ["operator"],
                    "resource_name": "localized_customer_attribute",
                },
            },
        )

    def test_customer_attr_value_as_merchant(self):
        merchant = MerchantFactory(application__id=1, id=2)
        value = LocalizedCustomerAttributeInfoFactory(
            application=merchant.application,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_CUSTOMER,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_CUSTOMER,
        )

        resp = self.api_client.delete(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_MERCHANT_2_WRITE"]["token"]
            ),
        )

        self.assertHttpForbidden(resp)
        self.assertEqual(
            self.deserialize(resp),
            {
                "error": "insufficient_scope",
                "error_description": "Invalid owner type for this action",
                "error_code": "OAUTH.INVALID_OWNER_TYPE",
                "error_context": {
                    "action": "delete_detail",
                    "owner_type": "merchant",
                    "accepted_owner_types": ["operator"],
                    "resource_name": "localized_customer_attribute",
                },
            },
        )

    def test_merchant_attr_value_as_merchant(self):
        merchant = MerchantFactory(application__id=1, id=2)
        value = LocalizedCustomerAttributeInfoFactory(
            application=merchant.application,
            attribute__read_permission=CustomerEnabledVisibility.APPLICATION_MERCHANT,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
        )

        resp = self.api_client.delete(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_MERCHANT_2_WRITE"]["token"]
            ),
        )

        self.assertHttpForbidden(resp)
        self.assertEqual(
            self.deserialize(resp),
            {
                "error": "insufficient_scope",
                "error_description": "Invalid owner type for this action",
                "error_code": "OAUTH.INVALID_OWNER_TYPE",
                "error_context": {
                    "action": "delete_detail",
                    "owner_type": "merchant",
                    "accepted_owner_types": ["operator"],
                    "resource_name": "localized_customer_attribute",
                },
            },
        )

    def test_public_attr_value_as_merchant(self):
        merchant = MerchantFactory(application__id=1, id=2)
        value = LocalizedCustomerAttributeInfoFactory(
            application=merchant.application,
            attribute__read_permission=CustomerEnabledVisibility.PUBLIC,
            attribute__write_permission=CustomerEnabledVisibility.APPLICATION_STAFF,
        )

        resp = self.api_client.delete(
            "/v1/localized_customer_attribute/{}/".format(value.id),
            authentication="Bearer {}".format(
                CUSTOMER_TOKENS["APPLICATION_1_MERCHANT_2_WRITE"]["token"]
            ),
        )

        self.assertHttpForbidden(resp)
        self.assertEqual(
            self.deserialize(resp),
            {
                "error": "insufficient_scope",
                "error_description": "Invalid owner type for this action",
                "error_code": "OAUTH.INVALID_OWNER_TYPE",
                "error_context": {
                    "action": "delete_detail",
                    "owner_type": "merchant",
                    "accepted_owner_types": ["operator"],
                    "resource_name": "localized_customer_attribute",
                },
            },
        )
