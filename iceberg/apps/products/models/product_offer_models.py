import logging
from copy import copy
from decimal import Decimal
from typing import Optional

import xworkflows
from apps.address.models import Address
from apps.assets.mixins import ImageAssignmentMixin
from apps.attributes.models import ProductAttributeEnabledModel, ProductAttributeValue
from apps.cart_notifications.models.mixins import CartNotificationOfferMixin
from apps.currencies.models import Currency
from apps.deprecation.models import DeprecatedFeature
from apps.ice_applications.app_conf_settings import (
    AllowTrashStatus,
    EnableDiscountEngine,
    IsClosedCatalog,
    MandatoryOfferFields,
    NumberThresholdsPrices,
    ProductOfferModerationEnabled,
)
from apps.products.cache import VariationConcurrencyLocker
from apps.promotions.models import Discount
from apps.promotions.utils import compute_discount_rate
from apps.stores.exceptions import LanguagesNotAvailable
from apps.tax.exceptions import NoTaxRateApplicable
from apps.tax.utils import add_tax_to, validate_tax_rates
from django.conf import settings
from django.contrib.auth.models import AnonymousUser
from django.contrib.contenttypes.fields import GenericRelation
from django.contrib.postgres.fields import ArrayField
from django.core.exceptions import ValidationError
from django.core.serializers.json import DjangoJSONEncoder
from django.core.validators import MaxLengthValidator
from django.db import models
from django.db.models import Q
from django.test import RequestFactory
from django.utils import timezone, translation
from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _
from django_xworkflows import models as xwf_models
from ims.api.const import OPERATOR_OWNER
from ims.fields import IzbergForeignKey
from ims.workflow import IzbergWorkflow
from lib.exceptions import MultiLevelValidationError
from lib.models import (
    ChunkIterateQuerySetMixin,
    CreatorModelMixin,
    ExternalIDModelMixin,
    PostgresIndex,
)
from measurement.measures import Weight
from mp_utils.model_mixins import BatchUpdatableModelMixin
from mp_utils.models import (
    AuthorBaseTransitionLog,
    CacheableModel,
    LocalizedInfo,
    LocalizedModelMixin,
)
from mp_utils.validators import MinValueValidator
from mp_utils.value_tracking import ValueTrackingMixin
from reference import status as statuses
from reference.status import PRODUCT_OFFER_STATUS_DELETED
from tastypie.exceptions import ImmediateHttpResponse

from .product_variation_models import ProductVariation

logger = logging.getLogger(__name__)

User = settings.AUTH_USER_MODEL

# can be used for debug
IGNORE_MISSING_IMAGES = getattr(settings, "IGNORE_PRODUCT_IMAGES", False)


class LocalizedOfferInfo(ValueTrackingMixin, LocalizedInfo):
    """
    Native localized infos of a ProductOffer in a specific language
    """

    api_resource_path = "apps.products.api.resources.LocalizedOfferInfoResource"

    application = IzbergForeignKey(
        "ice_applications.Application",
        on_delete=models.CASCADE,
        db_index=False,
    )
    product_offer = IzbergForeignKey(
        "products.ProductOffer",
        related_name="localized_infos",
        on_delete=models.CASCADE,
        db_index=False,
    )

    name = models.CharField(_("Name"), max_length=255, blank=True, null=True)
    description = models.TextField(
        _("Description"),
        blank=True,
        null=True,
        validators=[MaxLengthValidator(20000)],
    )
    details = models.TextField(_("Details"), null=True, blank=True)

    product_care = models.CharField(
        _("Product care"),
        max_length=1024,
        help_text=_("Product care instructions"),
        null=True,
        blank=True,
    )
    composition = models.CharField(
        _("Composition"), max_length=1024, null=True, blank=True
    )

    condition_note = models.CharField(
        _("Condition note"),
        max_length=1024,
        blank=True,
        null=True,
        help_text=_("Note about item condition (mainly if not new)"),
    )
    legal_disclaimer = models.TextField(
        _("Legal disclaimer"), blank=True, null=True, help_text=_("Legal disclaimer")
    )
    safety_warning = models.TextField(blank=True, null=True)

    MANDATORY_FIELDS = settings.DEFAULT_MANDATORY_LOCALIZED_OFFER_FIELDS

    EDITABLE_FIELDS = [
        "name",
        "description",
        "details",
        "product_care",
        "composition",
        "condition_note",
        "legal_disclaimer",
        "safety_warning",
        "manually_managed_fields",
    ]

    LOCALIZED_FIELDS = [
        "name",
        "description",
        "details",
        "product_care",
        "composition",
        "condition_note",
        "legal_disclaimer",
        "safety_warning",
    ]

    """ Batch update options """
    BATCH_UPDATABLE_FIELDS = {
        # should be enough (to avoid mistakes)
        "name": [],  # no restriction on modes
        "description": [],  # no restriction on modes
        "details": [],  # no restriction on modes
        "product_care": [],  # no restriction on modes
        "composition": [],  # no restriction on modes
        "condition_note": [],  # no restriction on modes
        "legal_disclaimer": [],  # no restriction on modes
        "safety_warning": [],  # no restriction on modes
    }
    # False for now (so it calls invalidate_cache)
    BATCH_UPDATABLE_ALLOW_ATOMIC_UPDATE = False
    BATCH_UPDATE_QUERYSET_EXTRA_FIELDS_TO_RETRIEVE = [
        "manually_managed_fields",
        "availability",
    ]

    class Meta:
        indexes = [
            PostgresIndex(
                fields=["product_offer_id"],
                name="products_localizedofferinfo_product_offer_id_idx",
            ),
            PostgresIndex(
                fields=["application_id"],
                name="products_localizedofferinfo_application_id_idx",
            ),
        ]

    def save(self, *args, **kwargs):
        if not self.id:
            languages = self.product_offer.merchant.languages
            if languages and self.language not in languages:
                raise LanguagesNotAvailable(
                    attribute="language", unavailable_languages=[self.language]
                )
        super(LocalizedOfferInfo, self).save(*args, **kwargs)

    def get_mandatory_fields(self):
        """Rewritten to set MANDATORY_FIELDS only when "main"
        LocalizedOfferInfo (ie not app specific)"""
        return []

    def get_cache_keys_to_invalidate(self, languages=None, application_ids=None):
        languages = [self.language]
        if self.product_offer:
            return self.product_offer.get_cache_keys_to_invalidate(
                languages=languages, application_ids=application_ids
            )
        return []

    @classmethod
    def _validate_batch_arguments(cls, field_name, value, filters_or_queryset, mode):
        """Overwrites _validate_batch_arguments to also check that a mandatory
        field is not set to null"""
        super(LocalizedOfferInfo, cls)._validate_batch_arguments(
            field_name, value, filters_or_queryset, mode
        )
        if field_name in cls.MANDATORY_FIELDS and not value:
            raise ValidationError(
                _("Can't batch update a mandatory field to empty value")
            )

    def _save_obj_after_batch_updated_field(self, field_name, value, mode):
        """Overwrites save function to add updated field_name to
        manually_managed fields"""
        self.manually_managed_fields = list(self.manually_managed_fields or [])
        self.manually_managed_fields = list(
            set(self.manually_managed_fields).union(set(field_name))
        )
        updated_fields = [field_name, "manually_managed_fields"]
        self.save(update_fields=updated_fields)  # saving only updated_fields

    @classmethod
    def _post_batch_update_hook(
        cls,
        field_name,
        value,
        filters_or_queryset,
        mode,
        number_updated,
        modified_ids,
        pre_hook_result,
    ):
        updated_attributes = [field_name, "manually_managed_fields"]
        return cls._post_batch_hook(modified_ids, updated_attributes)

    @classmethod
    def _post_batch_call_hook(
        cls,
        function_name,
        function_params,
        filters_or_queryset,
        number_updated,
        modified_ids,
        pre_hook_result,
    ):
        updated_attributes = cls.BATCH_CALLABLE_FUNCTIONS[function_name].get(
            "updated_attributes", []
        )
        return cls._post_batch_hook(modified_ids, updated_attributes)

    @classmethod
    def _post_batch_hook(cls, modified_ids, updated_attributes):
        """
        Post batch hook that:
        - triggers channel events
        """
        if not modified_ids:
            return
        cls._post_batch_trigger_channel_events(modified_ids)

    @classmethod
    def _post_batch_trigger_channel_events(cls, modified_ids):
        """
        Post batch-update / batch-call hook that triggers sync_request
        on channels
        """
        from apps.channels.channel_settings import HIGH_PRIORITY_QUEUE
        from apps.channels.models.channel_models import SYNCHRONOUS_PRIORITY
        from apps.stores.models import Merchant

        merchant_to_product_ids = {}
        merchant_to_offer_ids = {}

        product_and_merchant_dicts = (
            cls.objects.filter(id__in=modified_ids)
            .only("product_offer__product", "product_offer__merchant", "product_offer")
            .values(
                "product_offer__product", "product_offer__merchant", "product_offer"
            )
        )

        # filling merchant_to_product_ids
        for _dict in product_and_merchant_dicts.iterator():
            if _dict["product_offer__merchant"] not in merchant_to_product_ids:
                merchant_to_product_ids[_dict["product_offer__merchant"]] = set()
                merchant_to_offer_ids[_dict["product_offer__merchant"]] = set()
            merchant_to_product_ids[_dict["product_offer__merchant"]].add(
                _dict["product_offer__product"]
            )
            merchant_to_offer_ids[_dict["product_offer__merchant"]].add(
                _dict["product_offer"]
            )

        # triggering sync_request on each merchant
        merchants = Merchant.objects.filter(id__in=merchant_to_product_ids.keys())
        for merchant in merchants.iterator():
            channel_event = {
                "event": "sync_request",
                "product_ids": merchant_to_product_ids[merchant.id],
                "product_offer_ids": merchant_to_offer_ids[merchant.id],
                "merchant_id": merchant.id,
                "batch_update": True,
                "priority": SYNCHRONOUS_PRIORITY,
                "queue": HIGH_PRIORITY_QUEUE,
            }
            merchant.application.trigger_on_active_channels(channel_event)


class ProductOfferTransitionLog(AuthorBaseTransitionLog):
    offer = IzbergForeignKey(
        "products.ProductOffer",
        related_name="transition_logs",
        on_delete=models.CASCADE,
        db_index=False,
    )
    comment = models.CharField(max_length=255, blank=True, null=True)
    MODIFIED_OBJECT_FIELD = "offer"
    EXTRA_LOG_ATTRIBUTES = AuthorBaseTransitionLog.EXTRA_LOG_ATTRIBUTES + (
        ("comment", "comment", None),
    )

    class Meta:
        ordering = ["-id"]
        indexes = [
            PostgresIndex(
                fields=["offer"],
                name="products_productoffertransitionlog_offer_id_idx",
            ),
        ]


class ProductOffersWorkflow(IzbergWorkflow):
    log_model = "products.ProductOfferTransitionLog"
    # added because wasnt working anymore with 'log_model' on sandbox
    log_model_class = ProductOfferTransitionLog
    states = statuses.PRODUCT_OFFER_STATUSES
    transitions = (
        (
            "draft",
            [
                statuses.PRODUCT_OFFER_STATUS_ACTIVE,
                statuses.PRODUCT_OFFER_STATUS_INACTIVE,
                statuses.PRODUCT_OFFER_STATUS_PENDING,
            ],
            statuses.PRODUCT_OFFER_STATUS_DRAFT,
        ),
        (
            "need_review",
            [
                statuses.PRODUCT_OFFER_STATUS_INACTIVE,
                statuses.PRODUCT_OFFER_STATUS_DRAFT,
                statuses.PRODUCT_OFFER_STATUS_ACTIVE,
                statuses.PRODUCT_OFFER_STATUS_PENDING,
            ],
            statuses.PRODUCT_OFFER_STATUS_PENDING,
        ),
        (
            "activate",
            [
                statuses.PRODUCT_OFFER_STATUS_ACTIVE,
                statuses.PRODUCT_OFFER_STATUS_INACTIVE,
                statuses.PRODUCT_OFFER_STATUS_DRAFT,
                statuses.PRODUCT_OFFER_STATUS_PENDING,
            ],
            statuses.PRODUCT_OFFER_STATUS_ACTIVE,
        ),
        (
            "deactivate",
            [
                statuses.PRODUCT_OFFER_STATUS_ACTIVE,
                statuses.PRODUCT_OFFER_STATUS_DRAFT,
                statuses.PRODUCT_OFFER_STATUS_PENDING,
            ],
            statuses.PRODUCT_OFFER_STATUS_INACTIVE,
        ),
        (
            "ban",
            [
                statuses.PRODUCT_OFFER_STATUS_INACTIVE,
                statuses.PRODUCT_OFFER_STATUS_DRAFT,
                statuses.PRODUCT_OFFER_STATUS_PENDING,
                statuses.PRODUCT_OFFER_STATUS_ACTIVE,
            ],
            statuses.PRODUCT_OFFER_STATUS_BANNED,
        ),
        (
            "unban",
            [statuses.PRODUCT_OFFER_STATUS_BANNED],
            statuses.PRODUCT_OFFER_STATUS_INACTIVE,
        ),
        (
            "trash",
            [
                statuses.PRODUCT_OFFER_STATUS_DRAFT,
                statuses.PRODUCT_OFFER_STATUS_ACTIVE,
                statuses.PRODUCT_OFFER_STATUS_INACTIVE,
                statuses.PRODUCT_OFFER_STATUS_PENDING,
                statuses.PRODUCT_OFFER_STATUS_BANNED,
            ],
            statuses.PRODUCT_OFFER_STATUS_TRASHED,
        ),
        (
            "untrash",
            [
                statuses.PRODUCT_OFFER_STATUS_TRASHED,
            ],
            statuses.PRODUCT_OFFER_STATUS_DRAFT,
        ),
        (
            "delete_action",
            [
                statuses.PRODUCT_OFFER_STATUS_DRAFT,
                statuses.PRODUCT_OFFER_STATUS_INACTIVE,
                statuses.PRODUCT_OFFER_STATUS_PENDING,
                statuses.PRODUCT_OFFER_STATUS_TRASHED,
            ],
            statuses.PRODUCT_OFFER_STATUS_DELETED,
        ),
    )
    initial_state = statuses.PRODUCT_OFFER_STATUS_DRAFT


class ProductOfferQueryset(ChunkIterateQuerySetMixin, models.QuerySet):
    def filter_active(self, **kwargs):
        return self.filter(status=statuses.PRODUCT_OFFER_STATUS_ACTIVE).filter(**kwargs)

    def filter_inactive(self, **kwargs):
        return self.filter(status=statuses.PRODUCT_OFFER_STATUS_INACTIVE).filter(
            **kwargs
        )

    def filter_draft(self, **kwargs):
        return self.filter(status=statuses.PRODUCT_OFFER_STATUS_DRAFT).filter(**kwargs)

    def filter_trashed(self, **kwargs):
        return self.filter(status=statuses.PRODUCT_OFFER_STATUS_TRASHED).filter(
            **kwargs
        )

    def exclude_deleted(self):
        return self.filter(status__in=statuses.PRODUCT_OFFER_STATUSES_NON_DELETED)

    def exclude_trashed_deleted(self):
        return self.filter(
            status__in=statuses.PRODUCT_OFFER_STATUSES_NON_TRASHED_DELETED
        )


ProductOfferManager = ProductOfferQueryset.as_manager


class ProductOffer(
    xwf_models.WorkflowEnabled,
    ImageAssignmentMixin,
    CartNotificationOfferMixin,
    ProductAttributeEnabledModel,
    BatchUpdatableModelMixin,
    ValueTrackingMixin,
    CreatorModelMixin,
    CacheableModel,
    LocalizedModelMixin,
    ExternalIDModelMixin,
):
    """
    Offer model that only contains the native non-localized infos
    """

    track_values_state_on_init = True
    track_values_state_on_save = True
    api_resource_path = "apps.products.api.resources.ProductOfferResource"

    CONDITION_CHOICES = (
        (0, _("New")),
        (1, _("Second Hand")),
    )

    objects = ProductOfferManager()

    product = IzbergForeignKey(
        "products.Product",
        related_name="product_offers",
        on_delete=models.CASCADE,
        db_index=False,
    )
    application = IzbergForeignKey(
        "ice_applications.Application",
        related_name="product_offers",
        on_delete=models.CASCADE,
        db_index=False,
    )
    merchant = IzbergForeignKey(
        "stores.Merchant",
        related_name="product_offers",
        db_index=False,
        on_delete=models.CASCADE,
    )

    condition = models.SmallIntegerField(choices=CONDITION_CHOICES, default=0)
    # External ref: ID for the merchant
    sku = models.CharField(
        _("SKU"),
        max_length=255,
        null=True,
        blank=True,
    )
    created_on = models.DateTimeField(auto_now_add=True)
    visible = models.BooleanField(
        default=False, help_text=_("[DEPRECATED] Visibility is now based on status")
    )
    last_modified = models.DateTimeField(auto_now=True)
    # If variations, is the lowest price of variations. Is considered WITHOUT
    # VAT
    price = models.DecimalField(
        _("Price"),
        max_digits=20,
        decimal_places=6,
        null=True,
        blank=True,
        validators=[MinValueValidator(Decimal("0.00"))],
    )
    # If variations, is the previous price of the cheapest variation.
    # Is NOW considered WITHOUT VAT
    previous_price = models.DecimalField(
        _("Previous price"),
        max_digits=20,
        decimal_places=6,
        null=True,
        blank=True,
        validators=[MinValueValidator(Decimal("0.00"))],
    )
    currency = IzbergForeignKey(
        Currency, blank=True, db_index=False, on_delete=models.CASCADE
    )
    stock = models.IntegerField(
        _("Stock"),
        help_text=_(
            "Number of items in stock (if has variations, it is the"
            "sum of the variations' stocks)"
        ),
        default=0,
        validators=[MinValueValidator(0)],
    )

    weight_numeral = models.DecimalField(
        help_text=_("Weight in Kilogram"),
        max_digits=20,
        decimal_places=3,
        null=True,
        blank=True,
        validators=[MinValueValidator(Decimal("0.000"))],
    )

    product_attribute_values = GenericRelation(
        ProductAttributeValue,
        object_id_field="entity_id",
        content_type_field="entity_type",
        related_query_name="entity_product_offer",
    )

    @property
    def actions(self):
        from ..actions import ProductOfferActionsManager

        if not hasattr(self, "_actions"):
            self._actions = ProductOfferActionsManager(self)
        return self._actions

    @property
    def inventory(self):
        from ..actions import ProductOfferInventoryManager

        if not hasattr(self, "_inventory"):
            self._inventory = ProductOfferInventoryManager(self)
        return self._inventory

    @property
    def weight(self):
        """weight is the same as weight_numeral"""
        logger.warning(
            "ProductOffer.weight is deprecated. Please use "
            "ProductOffer.product.get_package_weight([unity]) instead"
        )
        return self.product.get_package_weight("kg") or self.weight_numeral

    @weight.setter
    def weight(self, value):
        self.weight_numeral = value

    @weight.deleter
    def weight(self):
        del self.weight_numeral

    @property
    def is_variation(self):
        return False

    # END @@@ MANDATORY EXPORT FIELDS

    # Additionnal fields
    wrapping = models.DecimalField(
        _("Wrapping"),
        default=Decimal("0"),
        max_digits=20,
        decimal_places=6,
        help_text=_("Price to wrap product"),
    )
    wrappable = models.BooleanField(
        _("Wrappable"), default=True, help_text=_("Can it be wrapped")
    )

    SYNC_ALL_FIELDS, SYNC_STOCK_AND_PRICE, NO_SYNC = (1, 2, 3)
    CRAWLING_POLICY_CHOICES = (
        (
            SYNC_ALL_FIELDS,
            _("Sync all the fields from the feed (except manually modified fields)"),
        ),
        (SYNC_STOCK_AND_PRICE, _("Only sync stock and price from the feed")),
        (NO_SYNC, _("Don't sync anything from the feed (manage it manually)")),
    )

    crawling_policy = models.PositiveSmallIntegerField(
        choices=CRAWLING_POLICY_CHOICES,
        default=SYNC_ALL_FIELDS,
    )
    last_crawled = models.DateTimeField(null=True, blank=True)

    shipping_info = models.CharField(max_length=512, null=True, blank=True)

    exclusive = models.BooleanField(default=False)
    isnew = models.BooleanField(default=False)

    merchant_url = models.CharField(
        _("Url of the offer on merchant website"),
        max_length=1024,
        null=True,
        blank=True,
    )

    # To Remove
    warranty = models.CharField(max_length=255, null=True, blank=True)
    cancellation = models.BooleanField(default=False)

    # AVAILIBILITY DELAY, ETC...

    IN_STOCK, ON_DEMAND = ["in_stock", "on_demand"]
    RESTOCKING, TO_BE_RELEASED = ["restocking", "to_be_released"]
    OUT_OF_STOCK, ENDED = ["out_of_stock", "ended"]

    AVAILIBILITY_CHOICES = (
        (IN_STOCK, _("In stock")),
        (OUT_OF_STOCK, _("Out of stock")),
        (ON_DEMAND, _("On demand")),
        (RESTOCKING, _("Restocking")),  # will be back in stock
        (TO_BE_RELEASED, _("To be released")),  # for new products
        (ENDED, _("Ended")),  # wont be in stock again
    )
    availability = models.CharField(
        choices=AVAILIBILITY_CHOICES,
        max_length=50,
        null=True,
        blank=True,
        default=OUT_OF_STOCK,
    )
    restock_date = models.DateTimeField(
        _("Restock date"),
        null=True,
        blank=True,
        help_text=_("When will the stock be refilled"),
    )
    start_selling_date = models.DateTimeField(
        null=True, blank=True, help_text=_("When the product starts being sold.")
    )
    end_selling_date = models.DateTimeField(
        null=True, blank=True, help_text=_("When the product stops being sold.")
    )

    status = xwf_models.StateField(ProductOffersWorkflow)

    is_abstract = models.BooleanField(
        _("Is abstract"),
        default=False,
        help_text=_("Will this offer contain variations ?"),
    )

    number_of_items = models.PositiveIntegerField(
        _("Number of discrete items"),
        help_text=_(
            "Number of discrete items included in a sale unit. "
            "Ex: if the offer is a pack of 10 boxes of "
            "12 chocolates, number_of_items=12."
        ),
        blank=True,
        null=True,
    )
    number_of_units = models.PositiveIntegerField(
        _("Number of sale units."),
        default=1,
        blank=True,
        null=True,
        help_text=_(
            "Number of sale units. "
            "Ex: if the offer is a pack of 10 boxes of "
            "12 chocolates, number_of_units=10."
        ),
    )

    max_order_quantity = models.PositiveIntegerField(
        _("Maximum number of products in one order"), blank=True, null=True
    )
    min_order_quantity = models.PositiveIntegerField(
        _("Minimum number of products in one order"), blank=True, null=True
    )
    merchant_private_notes = models.TextField(blank=True, null=True)
    offer_latitude = models.DecimalField(
        _("Latitude of geolocalized offer"),
        max_digits=9,
        decimal_places=6,
        blank=True,
        null=True,
    )
    offer_longitude = models.DecimalField(
        _("Longitude of geolocalized offer"),
        max_digits=9,
        decimal_places=6,
        blank=True,
        null=True,
    )
    tax_code = models.CharField(_("US tax code"), max_length=50, blank=True, null=True)

    eco_tax = models.DecimalField(
        _("Eco-tax included in price"),
        max_digits=20,
        decimal_places=6,
        null=True,
        blank=True,
        validators=[MinValueValidator(Decimal("0.00"))],
        default=Decimal("0.00"),
    )

    product_tax_group = IzbergForeignKey(
        "tax.ProductTaxGroup",
        related_name="product_offers",
        blank=True,
        null=True,
        on_delete=models.CASCADE,
        db_index=False,
    )

    manually_managed_fields = ArrayField(
        models.CharField(
            blank=True,
            null=True,
            max_length=255,
        ),
        default=list,
        null=True,
        blank=True,
        help_text=_(
            "Fields that can't be modified by the crawler (user modifications only)"
        ),
        db_column="manually_managed_fields",
    )

    # EU tax related fields
    shipped_from_eu_vat_zone = models.BooleanField(
        _("Shipped from EU VAT zone"),
        help_text=_(
            "Set true if B2C EU VAT rules are applicable in the "
            "shipped_from_zone, else false"
        ),
        blank=True,
        null=True,
        default=None,
    )
    shipped_from_country = IzbergForeignKey(
        "address.Country",
        default=None,
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
        help_text=_("Set the country from where you ship the offer"),
        related_name="product_offers",
        db_index=False,
    )
    shipped_from_region = models.CharField(
        _("Shipped from region"),
        help_text=_(
            "Use this field to provide additional information about a "
            "specific zone in a country. Required only if this zone doesn't "
            "have the same tax rules than the linked country"
        ),
        blank=True,
        null=True,
        default=None,
        max_length=255,
    )
    tax_rates = models.JSONField(
        # [{"zone_key": "rate_key"}, ...]
        _("Tax rates"),
        default=None,
        null=True,
        blank=True,
        encoder=DjangoJSONEncoder,
    )

    # FIELDS REGROUPED IN SETS FOR CREATION/UPDATE

    SPECIAL_AFFECTATION_KEYS = {
        "discounts",
        "files",
        "associates",
        "line",
        "images",
        "product_tax_group",
        "currency",
    }

    CREATABLE_FIELDS = ["merchant", "product", "currency"]

    EDITABLE_FIELDS = [
        "price",
        "previous_price",
        "stock",
        "wrapping",
        "wrappable",
        "shipping_info",
        "restock_date",
        "start_selling_date",
        "end_selling_date",
        "exclusive",
        "isnew",
        "merchant_url",
        "warranty",
        "cancellation",
        "availability",
        "images",
        "number_of_items",
        "number_of_units",
        "max_order_quantity",
        "min_order_quantity",
        "merchant_private_notes",
        "offer_latitude",
        "offer_longitude",
        "tax_code",
        "external_id",
        "product_tax_group",
        "sku",
        "eco_tax",
        "crawling_policy",
        "weight_numeral",
        "condition",
        "manually_managed_fields",
        "shipped_from_eu_vat_zone",
        "shipped_from_country",
        "shipped_from_region",
        "tax_rates",
    ]

    """ Batch update options """
    BATCH_UPDATABLE_FIELDS = {"stock": []}

    BATCH_UPDATABLE_ALLOW_ATOMIC_UPDATE = False
    BATCH_UPDATE_QUERYSET_EXTRA_FIELDS_TO_RETRIEVE = ["manually_managed_fields"]

    BATCH_CALLABLE_FUNCTIONS = {
        "activate_if_valid": {"updated_attributes": ["status"]},
        "deactivate_if_available": {
            "updated_attributes": ["status"],
            "previous_status": statuses.PRODUCT_OFFER_STATUS_ACTIVE,
        },
        "ban_if_available": {"updated_attributes": ["status"]},
        "unban_if_available": {"updated_attributes": ["status"]},
        "remove_all_manually_managed_fields": {
            "updated_attributes": ["manually_managed_fields"]
        },
    }

    MANDATORY_FIELDS = settings.DEFAULT_MANDATORY_OFFER_FIELDS

    MERCHANT_PRIVATE_FIELDS = ["merchant_private_notes"]

    STOCK_AND_PRICE_FIELDS = ["stock", "price", "previous_price"]

    XML_PARENT_TO_CHILD_NAME = {"variation_type": "varying_attribute"}

    # shortcut to statuses
    DRAFT = statuses.PRODUCT_OFFER_STATUS_DRAFT
    ACTIVE = statuses.PRODUCT_OFFER_STATUS_ACTIVE
    INACTIVE = statuses.PRODUCT_OFFER_STATUS_INACTIVE
    DELETED = statuses.PRODUCT_OFFER_STATUS_DELETED
    BANNED = statuses.PRODUCT_OFFER_STATUS_BANNED
    STATUS_CHOICES = statuses.PRODUCT_OFFER_STATUSES
    STATUSES_LIST = statuses.PRODUCT_OFFER_STATUSES_LIST
    STATUSES_LIST_NEW = statuses.PRODUCT_OFFER_STATUSES_LIST_NEW

    class Meta:
        indexes = [
            PostgresIndex(
                fields=["created_on"],
                name="products_productoffer_created_on_idx",
            ),
            PostgresIndex(
                fields=["merchant_id", "id"],
                name="products_productoffer_merchant_id__id_idx",
            ),
            PostgresIndex(
                fields=["merchant_id"],
                name="products_productoffer_merchant_id_idx",
            ),
            PostgresIndex(
                fields=["merchant_id"],
                name="products_productoffer_merchant_id_pidx",
                condition=~Q(status=PRODUCT_OFFER_STATUS_DELETED),
            ),
            PostgresIndex(
                fields=["product_id"],
                name="products_productoffer_product_id_idx",
            ),
            PostgresIndex(
                fields=["sku"],
                name="products_productoffer_sku_idx",
            ),
            PostgresIndex(
                fields=["status"],
                name="products_productoffer_status_pidx",
                condition=~Q(status=PRODUCT_OFFER_STATUS_DELETED),
            ),
            PostgresIndex(
                fields=["application_id"],
                name="products_productoffer_application_id_idx",
            ),
            PostgresIndex(
                fields=["application_id", "status"],
                name="products_productoffer_application_id__status_pidx",
                condition=~Q(status=PRODUCT_OFFER_STATUS_DELETED),
            ),
            PostgresIndex(
                fields=["application_id", "status", "merchant_id", "id"],
                name="products_productoffer_a_id__status__m_id__id_pidx",
                condition=~Q(status=PRODUCT_OFFER_STATUS_DELETED),
            ),
            PostgresIndex(
                fields=["last_modified"],
                name="products_productoffer_last_modified_pidx",
                condition=Q(status=PRODUCT_OFFER_STATUS_DELETED),
            ),
        ]

    def get_mandatory_fields(self):
        """
        Alter the mandatory fields based on the object:
        - if abstract >> variations are needed
        - if crawling_policy is not manual >> sku is needed
        """
        mandatory_fields = copy(self.application.get_setting(MandatoryOfferFields))
        if self.is_abstract:
            mandatory_fields.append("variations")
        else:
            if self.crawling_policy != self.NO_SYNC:
                # we need an SKU if the offer is not manual sync
                mandatory_fields.append("sku")
        return mandatory_fields

    def _clean_currency(self):
        if self.currency_id is None:
            # set to merchant default
            self.currency_id = self.merchant.default_currency_id
            return
        if self.currency_id == self.merchant.default_currency_id:
            # valid, no need to check extra "currencies"
            return
        currency_in_merchant_currencies = self.merchant.currencies.filter(
            code=self.currency_id
        ).exists()
        if not currency_in_merchant_currencies:
            raise ValidationError(
                {
                    "currency": _(
                        '"{}" not in merchant currencies'.format(self.currency_id)
                    )
                }
            )

    def _clean_wrappable(self):
        if self.wrappable is None:
            self.wrappable = True

    def _clean_sku(self):
        skip = (
            not self.sku
            or self.status
            in [
                statuses.PRODUCT_OFFER_STATUS_TRASHED,
                statuses.PRODUCT_OFFER_STATUS_DELETED,
            ]
            or (
                self.id
                and not self.field_has_changed_since_save("status")
                and not self.field_has_changed_since_save("sku")
            )
        )
        if skip:
            return
        qs = (
            ProductOffer.objects.exclude_trashed_deleted()
            .exclude(id=self.id)
            .filter(sku=self.sku, merchant=self.merchant)
        )
        if qs.exists():
            raise ValidationError(
                {
                    "sku": _(
                        "An offer with sku %(sku)s already exists for "
                        "merchant %(mid)s"
                    )
                    % {"sku": self.sku, "mid": self.merchant_id}
                }
            )

    def _clean_product_tax_group(self):
        if not self.product_tax_group:
            self.product_tax_group = (
                self.merchant.tax_settings.default_product_tax_group
            )
        invalid_app_id = (
            self.product_tax_group.application_id
            and self.product_tax_group.application_id != self.application_id
        )
        if invalid_app_id:
            raise ValidationError({"product_tax_group": _("Invalid product tax group")})

    def _clean_tax_info(self):
        self._clean_product_tax_group()
        if not self.id:
            if self.shipped_from_eu_vat_zone is None:
                self.shipped_from_eu_vat_zone = (
                    self.merchant.tax_settings.default_shipped_from_eu_vat_zone
                )
            if self.shipped_from_country is None:
                self.shipped_from_country = (
                    self.merchant.tax_settings.default_shipped_from_country
                )
            if self.shipped_from_region is None:
                self.shipped_from_region = (
                    self.merchant.tax_settings.default_shipped_from_region
                )
            if self.tax_rates is None:
                self.tax_rates = (
                    self.merchant.tax_settings.get_default_offer_tax_rates()
                )
        should_validate_tax_rates = (
            not self.id
            or self.field_has_changed_since_save("tax_rates")
            or self.field_has_changed_since_save("shipped_from_country_id")
        )
        if should_validate_tax_rates:
            validate_tax_rates(
                "tax_rates",
                self.tax_rates,
                self.application,
                enabled_tax_zones=self.merchant.tax_settings.enabled_tax_zones,
                shipped_from_country=self.shipped_from_country,
            )
            self.tax_rates = self.tax_rates or []

    def _clean_conditional_prices(self):
        if self.id and self.application.get_setting(NumberThresholdsPrices) > 0:
            from apps.products.models.conditional_prices_model import ConditionalPrices

            if (
                getattr(self, "conditional_prices", False)
                and self.conditional_prices.type == ConditionalPrices.PERCENT_THRESHOLDS
            ):
                self.conditional_prices.clean_thresholds_percents()
                self.conditional_prices.clean_thresholds_percent_prices()
                self.conditional_prices.save()

    def clean(self):
        self._clean_currency()
        self._clean_sku()
        self._clean_wrappable()
        self._clean_tax_info()
        if self.previous_price == "":
            self.previous_price = None
        if self.price in ("", None):
            self.price = Decimal("0.0")
        if self.weight_numeral == "":
            self.weight_numeral = None
        self._fill_missing_application()
        if self.weight_numeral:
            self.weight_numeral = Decimal(str(self.weight_numeral).replace(",", "."))
        self._clean_availability()
        self._clean_stock()
        self._clean_limit_order_quantity()
        self._clean_number_of_units()
        self._clean_conditional_prices()

    def __str__(self):
        return "ProductOffer[{}]".format(self.id)

    def _clean_availability(self):
        if self.availability:
            self.availability = self.availability.replace(" ", "_")

    def _clean_stock(self):
        # stock is an IntegerField (probably should be a PositiveIntegerField)
        # We check for positivity here for backward compatibility reason,
        # any other invalid input will be handled in CacheableModel's full_clean
        # by clean_fields otherwise the error would appear twice.
        try:
            self.stock = int(self.stock)
        except (ValueError, TypeError):
            # invalid input will be catched by clean_field called later
            return
        # if self.stock < 0:
        #     raise NegativeStockException()

    def _clean_limit_order_quantity(self):
        self._clean_max_order_quantity()
        self._clean_min_order_quantity()
        if (
            self.min_order_quantity is not None
            and self.max_order_quantity is not None
            and self.min_order_quantity > self.max_order_quantity
        ):
            raise ValidationError(
                {
                    "min_order_quantity": _(
                        "Minimum order quantity can't be higher than Maximum order quantity"
                    )
                }
            )

    def _clean_max_order_quantity(self):
        field = self._meta.get_field("max_order_quantity")
        try:
            self.max_order_quantity = field.clean(self.max_order_quantity, self)
        except ValidationError as err:
            raise ValidationError({"max_order_quantity": err}) from err

    def _clean_min_order_quantity(self):
        field = self._meta.get_field("min_order_quantity")
        try:
            self.min_order_quantity = field.clean(self.min_order_quantity, self)
        except ValidationError as err:
            raise ValidationError({"min_order_quantity": err}) from err

    def _clean_number_of_units(self):
        field = self._meta.get_field("number_of_units")
        try:
            self.number_of_units = field.clean(self.number_of_units, self)
        except ValidationError as err:
            raise ValidationError({"number_of_units": err}) from err

    def save(
        self,
        simple_stock_update=False,
        trigger_cart_notification=True,
        update_fields=None,
        *args,
        **kwargs,
    ):
        if simple_stock_update:
            self._clean_availability()
            self._clean_stock()
        elif update_fields and set(update_fields) == {"sku", "last_modified"}:
            # optimized cleaning on sku update
            self._clean_sku()
        else:
            self.clean()
        super().save(*args, update_fields=update_fields, **kwargs)
        if trigger_cart_notification:
            self.actions.save_cart_notification_check(instance=self)

    def get_languages_for_cache(self):
        """directly use the application languages as no other allowed"""
        return self.application.languages

    def __setattr__(self, attrname, value):
        # delete cached _price_with_vat when price changes
        if attrname == "price" and hasattr(self, "_price_with_vat"):
            del self._price_with_vat
        # delete cached _previous_price_with_vat when previous_price changes
        if attrname == "previous_price" and hasattr(self, "_previous_price_with_vat"):
            del self._previous_price_with_vat
        super(ProductOffer, self).__setattr__(attrname, value)

    def _fill_missing_application(self):
        if not self.application_id and self.merchant_id:
            self.application_id = self.merchant.application_id

    @classmethod
    def get_product_ids_from_offer_ids(cls, offer_ids):
        return ProductOffer.objects.filter(id__in=offer_ids).values_list(
            "product", flat=True
        )

    def get_applicable_tax_rate(self, address=None, customer_tax_group=None):
        return self.application.tax_handler.get_applicable_tax_rate(
            address=address or self.merchant.tax_settings.default_shipping_address,
            tax_rates_and_zones=self.tax_rates or [],
            # legacy handler context
            product_tax_group=self.product_tax_group,
            merchant=self.merchant,
            customer_tax_group=customer_tax_group
            or self.merchant.tax_settings.default_customer_tax_group,
        )

    def get_applicable_tax_rate_for_shipping(
        self, address=None, customer_tax_group=None
    ):
        return self.application.tax_handler.get_applicable_tax_rate(
            address=address or self.merchant.tax_settings.default_shipping_address,
            tax_rates_and_zones=self.merchant.tax_settings.shipping_tax_rates,
            fallback_value=self.merchant.tax_settings.fallback_shipping_tax_rate,
            # legacy handler context
            product_tax_group=self.merchant.tax_settings.product_tax_group_for_shipping,
            merchant=self.merchant,
            customer_tax_group=customer_tax_group
            or self.merchant.tax_settings.default_customer_tax_group,
        )

    def get_price_without_vat(self):
        # now price is always without vat
        return self.price

    def set_price_without_vat(self, value):
        # now price is always without vat
        self.price = Decimal(str(value).replace(",", ".")).quantize(Decimal("0.000001"))

    price_without_vat = property(get_price_without_vat, set_price_without_vat)

    def get_previous_price_without_vat(self):
        # now previous_price is always without vat
        return self.previous_price

    def set_previous_price_without_vat(self, value):
        # now previous_price is always without vat
        self.previous_price = Decimal(str(value).replace(",", ".")).quantize(
            Decimal("0.000001")
        )

    previous_price_without_vat = property(
        get_previous_price_without_vat, set_previous_price_without_vat
    )

    def _silent_get_price_with_vat(self):
        """Used for property, shouldn't be used anywhere else"""
        return self.get_price_with_vat(silent=True)

    def get_price_with_vat(self, address=None, customer_tax_group=None, silent=False):
        default_conditions = address is None and customer_tax_group is None
        if not hasattr(self, "_price_with_vat") or not default_conditions:
            if self.price_without_vat is None:
                return None
            tax_rate = self.get_applicable_tax_rate(
                address=address, customer_tax_group=customer_tax_group
            )
            if not tax_rate:
                if not silent:
                    raise NoTaxRateApplicable(product_offer_id=self.id)

                logger.warning(
                    "No tax rule applicable for offer {} ({}), "
                    "default_shipping_country {} and "
                    "default_customer_tax_group {}".format(
                        self.id,
                        self.product_tax_group,
                        self.merchant.tax_settings.default_shipping_country,
                        self.merchant.tax_settings.default_customer_tax_group,
                    )
                )
                price_with_vat = None
            else:
                price_with_vat = tax_rate.add_tax_to(self.price, 2)

            if default_conditions:
                # caching only if default_conditions
                self._price_with_vat = price_with_vat

        if default_conditions:
            return self._price_with_vat
        else:
            return price_with_vat

    def set_price_with_vat(self, value, address=None):
        tax_rate = self.get_applicable_tax_rate(address=address)
        if not tax_rate:
            raise NoTaxRateApplicable(product_offer_id=self.id)
        self.price = tax_rate.remove_tax_from(value)

    price_with_vat = property(_silent_get_price_with_vat, set_price_with_vat)

    def _silent_get_previous_price_with_vat(self):
        """Used for property, shouldn't be used anywhere else"""
        return self.get_previous_price_with_vat(silent=True)

    def get_previous_price_with_vat(
        self, address=None, customer_tax_group=None, silent=False
    ):
        if not self.previous_price:
            # no previous_price or 0, fast returning it
            return self.previous_price
        default_conditions = address is None and customer_tax_group is None
        if not hasattr(self, "_previous_price_with_vat") or not default_conditions:
            tax_rate = self.get_applicable_tax_rate(
                address=address, customer_tax_group=customer_tax_group
            )
            if not tax_rate:
                if not silent:
                    raise NoTaxRateApplicable(product_offer_id=self.id)
                logger.warning(
                    "No tax rule applicable for offer %s (%s), "
                    "default_shipping_country %s and "
                    "default_customer_tax_group %s"
                    % (
                        self.id,
                        self.product_tax_group,
                        self.merchant.tax_settings.default_shipping_country,
                        customer_tax_group
                        or self.merchant.tax_settings.default_customer_tax_group,
                    )
                )
                previous_price_with_vat = None
            else:
                previous_price_with_vat = tax_rate.add_tax_to(self.previous_price, 2)
            if default_conditions:
                self._previous_price_with_vat = previous_price_with_vat

        if default_conditions:
            return self._previous_price_with_vat
        else:
            return previous_price_with_vat

    def set_previous_price_with_vat(self, value):
        tax_rate = self.get_applicable_tax_rate()
        if not tax_rate:
            raise NoTaxRateApplicable(product_offer_id=self.id)
        self.previous_price = tax_rate.remove_tax_from(value)

    previous_price_with_vat = property(
        _silent_get_previous_price_with_vat, set_price_with_vat
    )

    @property
    def item_based_shipping_amount(self):
        return Decimal("0.00")

    @property
    def item_based_shipping_amount_with_tax(self):
        return Decimal("0.00")

    # END PROPERTIES

    def is_valid(self):
        """Check if all the mandatory fields are filled."""
        from ..forms import ProductOfferIsValidForm

        form = ProductOfferIsValidForm(self)
        form.full_clean()
        self._errors = form.errors
        self._validation_form = form
        return form.is_valid()

    def get_stock(self):
        if self.is_abstract:
            variations = self.variations.exclude_trashed_deleted()
            variation_stock = sum(
                [
                    max(0, variation_stock)
                    for variation_stock in variations.values_list("stock", flat=True)
                ]
            )
            return variation_stock
        else:
            return self.stock

    def get_price(self):
        if self.is_abstract:
            variations = self.variations.exclude_trashed_deleted().filter(
                price__isnull=False
            )
            if self.status.is_active:
                variations = variations.filter(
                    status=statuses.PRODUCT_VARIATION_STATUS_ACTIVE
                )
            try:
                return variations.order_by("price")[0].price
            except IndexError:
                return self.price
        else:
            return self.price

    def get_previous_price(self):
        if self.is_abstract:
            variations = self.variations.exclude_trashed_deleted().filter(
                price__isnull=False
            )
            if self.status.is_active:
                variations = variations.filter(
                    status=statuses.PRODUCT_VARIATION_STATUS_ACTIVE
                )
            try:
                # previous_price of cheapest variation if set
                cheapest_variation = variations.order_by("price")[0]
            except IndexError:
                return self.previous_price
            return cheapest_variation.previous_price
        else:
            return self.previous_price

    def update_availability(self):
        """
        Will define the availabity based on several infos:
        - stock
        - start_selling_date
        - end_selling_date
        - restock_date
        NB: if it was set (manually) to ON_DEMAND, wont change it
        Returns the new availability or None if hasnt changed
        """
        date_now = timezone.now()
        previous_availability = self.availability
        if previous_availability == self.ON_DEMAND:
            new_availability = self.ON_DEMAND
        elif self.end_selling_date and date_now > self.end_selling_date:
            new_availability = self.ENDED
        elif self.start_selling_date and date_now < self.start_selling_date:
            new_availability = self.TO_BE_RELEASED
        elif self.get_stock() == 0:
            if self.restock_date and date_now < self.restock_date:
                new_availability = self.RESTOCKING
            else:
                new_availability = self.OUT_OF_STOCK
        else:
            new_availability = self.IN_STOCK

        if new_availability and previous_availability != new_availability:
            self.availability = new_availability
            logger.debug(
                "Offer(%s) : previous_availability=%s, new_availability=%s"
                % (self.id, previous_availability, new_availability)
            )
            return new_availability
        else:
            return None

    def _validate_offer(self, errors, fast_return=False):
        """
        Tries to validate offer by activating it.
        """
        if not self.is_valid():
            logger.warning(
                "Product offer {} is not valid.\n{}".format(
                    self.id, self._errors.as_text()
                )
            )
            if fast_return:
                raise ValidationError({"offer": self._errors})
            else:
                errors["offer"] = self._errors
        return errors

    def _check_variation_types_are_the_same(self):
        """
        Checks that the variation_type of all the
        non deleted variations are the same.
        If not, raise ValidationError
        """
        variations = self.variations.exclude_deleted()
        # might need to change values_list because returns string value like
        # '["color","size"]'
        # and might not be ordered the same way'
        # only function end up in max recursion errors for some reason
        variation_types = list(variations.values_list("variation_type", flat=True))
        are_the_same = not variation_types or all(
            set(variation_types[0]) == set(variation_type)
            for variation_type in variation_types
        )
        if not are_the_same:
            raise ValidationError(
                {"variations": [_("Variations don't have the same variation type")]}
            )

    def _validate_variations(
        self,
        errors,
        fast_return=False,
        ignore_directly_modified_vars=False,
        from_variation_id=None,
    ):
        """
        Tries to validate variations by activating them.
        If no one is valid, adds ValidationErrors in errors["variations"].
        Also checks that all variation_types are the same
        """
        if not self.is_abstract:
            return errors
        try:
            self._check_variation_types_are_the_same()
        except ValidationError as err:
            if fast_return:
                raise err
            else:
                # return now because other checks would be unrelevant
                errors = err.update_error_dict(errors)
                return errors

        variations = self.variations.exclude_trashed_deleted()
        if from_variation_id:
            variations = variations.filter(id=from_variation_id)
        variations = list(variations)  # list because we need them all
        if not variations:
            return errors

        at_least_one_valid_variation = False
        variation_errors = {}
        for variation in variations:
            ignore_variation = not from_variation_id and (
                ignore_directly_modified_vars and variation.was_directly_modified()
            )
            if ignore_variation:
                continue

            variation_errors = self._activate_variation_if_valid(
                variation, variation_errors
            )
            at_least_one_valid_variation = (
                at_least_one_valid_variation or variation.status.is_active
            )

        if not at_least_one_valid_variation:
            if fast_return:
                raise ValidationError({"variations": variation_errors})
            else:
                errors["variations"] = variation_errors or _("No valid variation")
        return errors

    def _activate_variation_if_valid(self, variation, variation_errors):
        # ignore transition logging and save if already active
        log = save = not variation.status.is_active
        try:
            variation.activate(log=log, save=save, nested=True)
        except ValidationError as err:
            if variation.status.is_active:
                logger.warning(
                    (
                        "10964: This variation is passed in draft variation_id: {} as error: {}"
                    ).format(variation.id, err)
                )
                variation.draft(nested=True)
            variation_errors = err.update_error_dict(variation_errors)
        return variation_errors

    def activate_or_moderate(self, ignore_perms=False, owner_type=None, **kwargs):
        """
        rely on app config to decide if offer should be activated
        or go through operator moderation
        """
        user = kwargs.get("user")
        skip_moderation = ignore_perms or (
            self.status.is_active
            or not self.application.get_setting(ProductOfferModerationEnabled)
            or owner_type == OPERATOR_OWNER
            or (user and self.application.is_application_staff(user))
        )
        if skip_moderation:
            # log transition and save offer only if not already active
            log_and_save = not self.status.is_active
            self.activate(**kwargs, log=log_and_save, save=log_and_save)
        else:
            # log transition and save offer only if not already pending
            log_and_save = not self.status.is_pending
            self.need_review(**kwargs, log=log_and_save, save=log_and_save)

    @xworkflows.before_transition("need_review")
    @xworkflows.before_transition("activate")
    def before_transition_activate(self, *args, **kwargs):
        """
        Does all the validations on self, product and variations before activating the
        offer.
        This will try to activate product and variations and do other checks.
        If fast_return==True, raises ValidationError on first error found, else raise
        all the errors
        We generally want all the errors (so fast_return=False by default)
        """
        fast_return = kwargs.get("fast_return", False)
        validate_variations = kwargs.get("validate_variations", True)
        from_variation_id = kwargs.get("from_variation_id", None)
        ignore_directly_modified_vars = kwargs.get(
            "ignore_directly_modified_vars", False
        )

        errors = {}
        errors = self._validate_offer(errors, fast_return)
        if not self.application.get_setting(IsClosedCatalog):
            errors = self.product.validate_product(errors, fast_return, **kwargs)
        if validate_variations:
            errors = self._validate_variations(
                errors=errors,
                fast_return=fast_return,
                ignore_directly_modified_vars=ignore_directly_modified_vars,
                from_variation_id=from_variation_id,
            )

        if errors:
            raise MultiLevelValidationError(errors)

    def update_stock(self, save=False):
        old_stock = self.stock
        self.stock = new_stock = self.get_stock()
        logger.debug(
            "update_stock: old_stock=%s, new_stock=%s" % (old_stock, new_stock)
        )
        if not old_stock == new_stock:
            if save:
                self.save()
            return new_stock

    def update_price(self, save=False):
        old_price = self.price
        self.price = new_price = self.get_price()
        if not old_price == new_price:
            if save:
                self.save()
            return new_price

    def update_previous_price(self, save=False):
        old_previous_price = self.previous_price
        self.previous_price = new_previous_price = self.get_previous_price()
        if not old_previous_price == new_previous_price:
            if save:
                self.save()
            return new_previous_price

    def get_applicable_discounts(
        self,
        customer_tax_group=None,
        address=None,
        quantity=1,
        discount_codes=None,
        user=None,
        cart=None,
        **extra_context,
    ):
        if cart and cart.selected_payment_type == cart.TERM_PAYMENT:
            return []
        applicable_discounts = []
        if address:
            country_filter = Q(country=address.country) | Q(country=None)
        else:
            country_filter = None

        discounts_no_code = Discount.objects.actives_no_code(
            application=self.application,
            reduction_type__in=Discount.PRODUCT_OFFER_BASED,
            currency_id=self.currency_id,
        ).filter(Q(merchant=self.merchant) | Q(merchant=None))
        if country_filter:
            discounts_no_code = discounts_no_code.filter(country_filter)

        if discount_codes:
            discounts_with_codes = Discount.objects.actives(
                application=self.application,
                discount_code__in=discount_codes,
                reduction_type__in=Discount.PRODUCT_OFFER_BASED,
                currency_id=self.currency_id,
            ).filter(Q(merchant=self.merchant) | Q(merchant=None))
            if country_filter:
                discounts_with_codes = discounts_with_codes.filter(country_filter)
        else:
            discounts_with_codes = []

        discounts = sorted(
            list(discounts_no_code) + list(discounts_with_codes),
            key=lambda x: (x.priority, Decimal(x.reduction_value)),
            reverse=True,
        )
        for discount in discounts:
            if not discount.is_applicable_now(**extra_context):
                continue
            if not discount.is_applicable_for_context(
                customer_tax_group=customer_tax_group,
                address=address,
                user=user,
                **extra_context,
            ):
                continue
            if not discount.is_applicable_for_product_offer(self, quantity=quantity):
                continue

            applicable_discounts.append(discount)
            if discount.combinability == Discount.EXCLUSIVE:
                # stop here
                break

        return applicable_discounts

    def get_best_discount(
        self,
        customer_tax_group=None,
        address=None,
        quantity=1,
        discount_codes=None,
        user=None,
        **extra_context,
    ):
        """returns the best discount (based on priority)"""
        if not self.application.get_setting(EnableDiscountEngine):
            return
        applicable_discounts = self.get_applicable_discounts(
            customer_tax_group=customer_tax_group,
            address=address,
            quantity=quantity,
            discount_codes=discount_codes,
            user=user,
            **extra_context,
        )
        if not applicable_discounts:
            return
        else:
            return applicable_discounts[0]

    def get_original_price_dict(
        self, address=None, customer_tax_group=None, silent=False, cart_item_quantity=0
    ):
        from apps.tax.models.tax_rate import TaxRate  # noqa

        tax_rate = self.get_applicable_tax_rate(
            address=address, customer_tax_group=customer_tax_group
        )
        if not tax_rate and not silent:
            raise NoTaxRateApplicable(product_offer_id=self.id)
        tax_rate_value = tax_rate.current_rate if tax_rate else None
        tax_rate_key = tax_rate.rate_key if isinstance(tax_rate, TaxRate) else None

        price_without_vat = self.price_without_vat
        if (
            self.application.get_setting(NumberThresholdsPrices) > 0
            and getattr(self, "conditional_prices", None) is not None
            and cart_item_quantity > 0
        ):
            price_without_vat = (
                self.conditional_prices.get_price_from_conditions(
                    cart_item_quantity=cart_item_quantity
                )
                or price_without_vat
            )

        result = {
            "price_without_vat": price_without_vat,
            "previous_price_without_vat": self.previous_price_without_vat,
            "currency": self.currency,
            "tax_rate": tax_rate_value,
            "tax_rate_key": tax_rate_key,
        }
        if tax_rate:
            result["price_with_vat"] = add_tax_to(price_without_vat, tax_rate_value, 2)
            result["previous_price_with_vat"] = (
                add_tax_to(self.previous_price_without_vat, tax_rate_value, 2)
                if self.previous_price_without_vat is not None
                else None
            )
        else:
            result["price_with_vat"] = None
            result["previous_price_with_vat"] = None
        return result

    @staticmethod
    def should_include_key(keys_to_include, *keys):
        if keys_to_include is None:
            return True
        for key in keys:
            if key in keys_to_include:
                return True
        return False

    def get_data(
        self,
        language="fr",
        from_api=False,
        visible_only=None,
        default_language=None,
        currency=None,
        country=None,
        customer_tax_group=None,
        ignore_discounts=False,
        full_merchant=False,
        full_product=False,
        include_applicable_discounts=False,
        include_upcoming_discounts=False,
        keys_to_include=None,
    ):
        from apps.products.api.resources import ProductOfferResource

        address = Address(country=country) if country is not None else None
        language = language or self.merchant.prefered_language
        if not ignore_discounts:
            discount = self.get_best_discount(
                customer_tax_group=customer_tax_group,
                address=address,
            )
        else:
            discount = None
        include_price = self.should_include_key(
            keys_to_include,
            "original_price_without_vat",
            "original_price_with_vat",
            "original_previous_price_without_vat",
            "original_previous_price_with_vat",
            "price_without_vat",
            "price",
            "price_with_vat",
            "previous_price_without_vat",
            "previous_price",
            "previous_price_with_vat",
            "discount_start_date",
            "discount_end_date",
            "discount_rate",
            "original_prices",
        )
        if include_price:
            if discount:
                price_dict = discount.get_discounted_price_dict_for_product_offer(
                    self,
                    customer_tax_group=customer_tax_group,
                    address=address,
                    silent=True,
                )
            else:
                price_dict = self.get_original_price_dict(
                    address=address,
                    customer_tax_group=customer_tax_group,
                    silent=True,
                )

        if from_api:
            por = ProductOfferResource()
            request = RequestFactory().get("/")
            request.user = AnonymousUser()
            data = por.get_detail(request, pk=self.id, bundle_data_only=True)
        else:
            data = {}
            fields_to_return = set(ProductOfferResource._meta.fields) - set(
                self.MERCHANT_PRIVATE_FIELDS
            )
            for key in fields_to_return:
                if hasattr(self, key) and self.should_include_key(keys_to_include, key):
                    value = getattr(self, key)
                    if (
                        "RelatedManager" not in value.__class__.__name__
                        and not isinstance(value, models.Model)
                    ):
                        data[key] = value
            data["currency"] = self.currency.code
            data["price"] = price_dict["price_without_vat"]
            data["price_without_vat"] = price_dict["price_without_vat"]
            data["price_with_vat"] = price_dict["price_with_vat"]
            data["previous_price_without_vat"] = price_dict[
                "previous_price_without_vat"
            ]
            data["previous_price"] = data["previous_price_without_vat"]
            data["previous_price_with_vat"] = price_dict["previous_price_with_vat"]

        data["discount_start_date"] = None
        data["discount_end_date"] = None
        data["discount_rate"] = None
        if (
            data["previous_price_with_vat"]
            and data["price_with_vat"] < data["previous_price_with_vat"]
        ):
            data["discounted"] = True
            data["discount_start_date"] = price_dict.get("start_date", None)
            data["discount_end_date"] = price_dict.get("end_date", None)
            if self.should_include_key(keys_to_include, "discount_rate"):
                try:
                    data["discount_rate"] = compute_discount_rate(
                        data["price_with_vat"], data["previous_price_with_vat"]
                    )
                except Exception as e:
                    logger.exception("in discount_rate: {}".format(e))
        else:
            data["discounted"] = False

        if discount:
            if self.should_include_key(keys_to_include, "applied_discounts"):
                data["applied_discounts"] = [discount.to_dict()]
            data["original_prices"] = price_dict.get("original_price_dict")
        else:
            data["applied_discounts"] = []

        if include_applicable_discounts and self.should_include_key(
            keys_to_include, "applicable_discounts"
        ):
            data["applicable_discounts"] = (
                self.discount_manager.get_applicable_discounts_as_dict(country=country)
            )

        if include_upcoming_discounts and self.should_include_key(
            keys_to_include, "upcoming_discounts"
        ):
            data["upcoming_discounts"] = (
                self.discount_manager.get_upcoming_discounts_as_dict(country=country)
            )

        if visible_only is None:
            # visible_only true if offer is active
            visible_only = self.status.is_active

        localized_info = self.get_localized_info(
            language=language,
            visible_only=visible_only,
            exclude=["status", "product_offer_id"],
            default_language=default_language,
        )

        # merging localized and non localized manually_managed_fields
        loc_manually_managed_fields = localized_info.pop("manually_managed_fields", [])
        data["manually_managed_fields"] = list(
            set(loc_manually_managed_fields).union(
                set(self.manually_managed_fields or [])
            )
        )

        if localized_info:
            data.update(localized_info)
        if self.should_include_key(
            keys_to_include, "variations"
        ) and not DeprecatedFeature.is_decommissioned(
            DeprecatedFeature.PRODUCT_OFFER_VARIATIONS
        ):
            data["variations"] = self.get_variations_data(
                language=language,
                visible_only=visible_only,
                currency=currency,
                country=country,
                customer_tax_group=customer_tax_group,
                discount=discount,
            )

        self._inject_shipping1(data, keys_to_include, address, customer_tax_group)

        if self.should_include_key(
            keys_to_include, "images"
        ) and not DeprecatedFeature.is_decommissioned(
            DeprecatedFeature.OFFER_IMAGE_FIELDS
        ):
            data["images"] = []

        if self.should_include_key(keys_to_include, "assigned_images"):
            data["assigned_images"] = self.asset_actions.list_assigned_images()
        if self.should_include_key(keys_to_include, "default_image"):
            data["default_image"] = self.default_image_url
            append_http = (
                data["default_image"]
                and settings.DEBUG
                and not data["default_image"].startswith("http:")
                and not data["default_image"].startswith("https:")
            )
            if append_http:
                data["default_image"] = "http:" + data["default_image"]

        if self.should_include_key(keys_to_include, "merchant"):
            if full_merchant:
                try:
                    data["merchant"] = self.merchant.get_data_from_api_resource()
                except ImmediateHttpResponse:
                    # not accessible (not active), fall back on non full
                    # version
                    full_merchant = False
                except Exception as err:
                    logger.exception(f"while getting full_merchant: {err}")
                    full_merchant = False

            if not full_merchant:
                data["merchant"] = self.merchant.get_backbone_friendly_dict(
                    extra_fields=["name", "slug", "status"]
                )
                data["merchant"]["keywords"] = self.merchant.keywords_list

            data["merchant"]["group_keys"] = self.merchant.group_keys

        self._inject_tax_data(data, address, customer_tax_group)

        if self.should_include_key(keys_to_include, "resource_uri"):
            data["resource_uri"] = self.get_resource_uri()

        with translation.override(language):
            data["status_localized"] = self.get_status_display()
            data["availability_localized"] = self.get_availability_display()

        data["created_on"] = self.created_on

        data["requested_language"] = language

        data["created_on"] = self.created_on
        if self.should_include_key(keys_to_include, "product"):
            if full_product:
                data["product"] = self.product.get_data(
                    language=language,
                    visible_only=visible_only,
                    default_language=default_language,
                    include_offers=False,
                )
            else:
                data["product"] = {
                    "resource_uri": self.product.get_resource_uri(),
                    "id": self.product_id,
                    "pk": self.product_id,
                    "external_id": self.product.external_id,
                }
        if self.should_include_key(keys_to_include, "attributes"):
            data["attributes"] = self.get_attribute_values_as_dict(
                language=language,
                default_language=default_language,
            )
        if self.should_include_key(keys_to_include, "shipped_from_country"):
            data["shipped_from_country"] = (
                self.shipped_from_country.get_resource_uri()
                if self.shipped_from_country
                else None
            )

        if self.application.get_setting(NumberThresholdsPrices) > 0:
            data["conditional_prices"] = (
                self.conditional_prices.get_resource_uri()
                if hasattr(self, "conditional_prices")
                else None
            )
        return data

    def _inject_tax_data(self, data, address, customer_tax_group):
        from apps.tax.models.tax_rate import TaxRate  # avoid circular import

        data["product_tax_group_id"] = self.product_tax_group_id

        tax_rate = self.get_applicable_tax_rate(
            address=address, customer_tax_group=customer_tax_group
        )
        for tf in ("tax_rate_key", "tax_rate"):
            data[tf] = None
        if tax_rate:
            data["tax_rate"] = tax_rate.current_rate
            if isinstance(tax_rate, TaxRate):
                data["tax_rate_key"] = tax_rate.rate_key

    def _inject_shipping1(self, data, keys_to_include, address, customer_tax_group):
        data.update(
            {
                "delivery_time": None,
                "free_return": None,
                "free_shipping": None,
                "shipping_estimation": None,
                "shipping_estimation_coutry": None,
            }
        )
        return data

    def get_variations_data(
        self,
        language="fr",
        visible_only=None,
        default_language=None,
        currency=None,
        country=None,
        customer_tax_group=None,
        discount=None,
        limit=500,
    ):
        data = []
        if not self.is_abstract:
            return data
        variations = self.variations.exclude_deleted()

        if visible_only is None:
            visible_only = self.status.is_active
        if visible_only:
            variations = variations.filter(
                status=statuses.PRODUCT_VARIATION_STATUS_ACTIVE
            )
        for variation in variations[:limit]:
            data.append(
                variation.get_data(
                    language=language,
                    visible_only=visible_only,
                    default_language=default_language,
                    currency=currency,
                    country=country,
                    customer_tax_group=customer_tax_group,
                    discount=discount,
                )
            )
        return data

    def get_updatable_fields(
        self, is_creation, is_crawler=False, from_api: Optional[bool] = False
    ):
        if is_creation:
            updatable_fields = set(self.CREATABLE_FIELDS).union(
                set(self.EDITABLE_FIELDS)
            )
        else:
            updatable_fields = set(self.EDITABLE_FIELDS)

        if is_crawler and self.crawling_policy == self.SYNC_STOCK_AND_PRICE:
            updatable_fields = updatable_fields & set(self.STOCK_AND_PRICE_FIELDS)

        if self.is_abstract and "stock" in updatable_fields:
            updatable_fields.remove("stock")

        if not is_creation and self.crawling_policy != self.NO_SYNC:
            logger.debug(
                "crawling_policy!=NO_SYNC, removing sku from offer updatable_fields"
            )
            updatable_fields = updatable_fields - {"sku"}

        if self.manually_managed_fields and not from_api:
            # removing manually managed fields as we are not dealing with a
            # user update
            logger.info(
                "removing manually managed fields %s as we are not "
                "dealing with a user update" % (self.manually_managed_fields)
            )
            updatable_fields = updatable_fields - set(self.manually_managed_fields)

        return updatable_fields

    def _update_product_tax_group(self, value):
        """
        @summary: Update product_tax_group
        NB: update only if value, not setting to None
        @param value: ProductTaxGroup obj or id
        @result: updated (bool)
        """
        from apps.tax.models import ProductTaxGroup

        if not value:
            return False
        old_value = self.product_tax_group_id
        if isinstance(value, ProductTaxGroup):
            value = value.id
        self.product_tax_group_id = value
        try:
            # deleting possibly cached obj
            # so it is updated
            del self._product_tax_group_cache
        except AttributeError:
            pass
        return old_value != value

    def _update_manually_managed_fields(self, updated_offer_attributes):
        if "manually_managed_fields" in updated_offer_attributes:
            # has been explicitely updated, not touching it
            return
        to_remove = {
            "price_with_vat",
            "price_without_vat",
            "previous_price_with_vat",
            "previous_price_without_vat",
        }
        if self.is_abstract:
            to_remove = to_remove.union({"stock", "availability", "price"})
        update_attributes = set(updated_offer_attributes).difference(to_remove)
        self.manually_managed_fields = list(
            set(self.manually_managed_fields or []).union(set(update_attributes))
        )

    def update_info_from_dict(
        self,
        product_info_dict,
        is_creation,
        allow_null_or_empty_values=True,
        is_crawler=False,
        from_api: Optional[bool] = False,
    ):
        if not product_info_dict:
            return []
        ignore_crawling = is_crawler and self.crawling_policy == self.NO_SYNC
        if ignore_crawling:
            return []
        updated_offer_attributes = set()
        updatable_fields = self.get_updatable_fields(
            is_creation, is_crawler, from_api=from_api
        )
        for key, value in product_info_dict.items():
            if key not in updatable_fields:
                continue
            if key in self.SPECIAL_AFFECTATION_KEYS:
                if key == "product_tax_group":
                    updated = self._update_product_tax_group(value)
                    if updated:
                        updated_offer_attributes.add(key)
                elif key == "currency":
                    updated_offer_attributes.add(key)
                    self.currency_id = value
            else:
                # direct affectation
                if allow_null_or_empty_values or value not in [None, ""]:
                    if hasattr(self, key):
                        old_value = getattr(self, key)
                        try:
                            value = self._meta.get_field(key).clean(value, self)
                        except Exception:
                            pass
                        if self._value_has_changed(old_value, value):
                            updated_offer_attributes.add(key)
                            setattr(self, key, value)
        self.full_clean()
        new_availability = self.update_availability()
        new_price = self.update_price()
        new_previous_price = self.update_previous_price()
        new_stock = self.update_stock()
        if new_availability:
            updated_offer_attributes.add("availability")
        if new_stock is not None:
            updated_offer_attributes.add("stock")
        if new_price is not None:
            updated_offer_attributes.add("price")
        if new_previous_price is not None:
            updated_offer_attributes.add("previous_price")
        if "price" in updated_offer_attributes:
            updated_offer_attributes = updated_offer_attributes.union(
                {"price_with_vat", "price_without_vat"}
            )
        if "previous_price" in updated_offer_attributes:
            updated_offer_attributes = updated_offer_attributes.union(
                {"previous_price_with_vat", "previous_price_without_vat"}
            )
        if updated_offer_attributes:
            if from_api:
                self._update_manually_managed_fields(updated_offer_attributes)
            self.last_crawled = timezone.now() if is_crawler else None
        elif from_api:
            # clearing potential last_crawled date on offer
            # to reflect that it is API managed and should be ignored by
            # the mapper CatalogCleaningManager
            self._clear_last_crawled()
        has_stock_update = "stock" in updatable_fields and "stock" in product_info_dict
        if has_stock_update:
            # trusting given stock value, hence cleaning cached one
            self.inventory.clear_cached_stock()
        return list(updated_offer_attributes)

    def remove_all_manually_managed_fields(self, *args, **kwargs) -> bool:
        from apps.products.models import LocalizedVariationInfo

        self.manually_managed_fields = []
        self.save(update_fields=("manually_managed_fields",))
        ProductVariation.objects.filter(product_offer=self).update(
            manually_managed_fields=[]
        )
        LocalizedVariationInfo.objects.filter(
            product_variation__product_offer=self
        ).update(manually_managed_fields=[])
        return True

    def update_stock_field(self, new_stock: int) -> bool:
        """
        Update stock field value of product offer or all variations of product offer
        """
        updated_attributes = ["stock", "last_modified"]
        if self.is_abstract:
            for variation in self.variations.exclude_trashed_deleted():
                variation.stock = new_stock
                variation.save(
                    update_fields=updated_attributes, simple_stock_update=True
                )
        else:
            self.stock = new_stock

        if self.is_abstract:
            self.update_stock()

        if self.update_availability():
            updated_attributes.append("availability")

        if self.field_has_changed_since_save("stock"):
            self.save(update_fields=updated_attributes, simple_stock_update=True)
            return True
        return False

    def update_localized_info_from_dict(
        self,
        localized_info_dict,
        is_creation,
        is_crawler=False,
        from_api: Optional[bool] = False,
    ):
        editable_keys = LocalizedOfferInfo.EDITABLE_FIELDS + ["language"]
        localized_info_dict = localized_info_dict or {}
        localized_info_dict = {
            key: value
            for (key, value) in localized_info_dict.items()
            if key in editable_keys
        }
        if not localized_info_dict or list(localized_info_dict.keys()) == ["language"]:
            # empty localized infos
            return []

        if is_crawler and self.crawling_policy != self.SYNC_ALL_FIELDS:
            return []
        if self.id is None:
            self.save()
        try:
            localized_obj = self.localized_infos.get(
                language=localized_info_dict["language"],
            )
            localized_obj_is_creation = False
        except LocalizedOfferInfo.DoesNotExist:
            localized_obj = self.localized_infos.create(
                application=self.application,
                language=localized_info_dict["language"],
            )
            localized_obj_is_creation = True
        logger.debug(
            "localized_obj, localized_obj_is_creation = %s, %s"
            % (localized_obj, localized_obj_is_creation)
        )
        updated_attributes = localized_obj.update_from_dict(
            localized_info_dict,
            is_creation=localized_obj_is_creation,
            from_api=from_api,
        )
        return updated_attributes

    def check_update_status(self, **transition_kwargs):
        """
        @summary: Checks/updates the status of the offer,
        i.e. checks that an active offer is still valid.
        If not valid anymore, changes the status to draft.
        Does nothing for other statuses than active.
        @result: True if status has been updated, else False
        """
        if self.status.is_active:
            # Checking if the offer is still valid by
            # doing an activate on it. If ValidationError >> draft
            try:
                self.activate(log=False, save=False)
            except ValidationError as err:
                logger.debug(
                    "activation error for offer %s,"
                    " setting it to draft (%s)" % (self.id, err)
                )
                self.draft(**transition_kwargs)
                return True
        return False

    def update_from_dicts(
        self,
        product_info_dict,
        localized_info_dict,
        is_creation=False,
        is_crawler=False,
        check_still_valid=False,
        channel_sync=True,
        from_api: Optional[bool] = False,
        custom_attribute_dict=None,
        channel_queue="normal",
        save: Optional[bool] = False,
        **transition_kwargs,
    ):
        if from_api:
            # When coming from API, validate custom attributes values first to
            # raise a 400 before saving anything, to stay atomic
            self.validate_custom_attributes_values(custom_attribute_dict)
        updated_offer_attributes = self.update_info_from_dict(
            product_info_dict, is_creation, is_crawler=is_crawler, from_api=from_api
        )
        # Save when we update an attributes or if we force to save
        if updated_offer_attributes or save:
            self.save()

        updated_offer_attributes += self.update_localized_info_from_dict(
            localized_info_dict, is_creation, is_crawler=is_crawler, from_api=from_api
        )

        if custom_attribute_dict:
            updated_offer_attributes += self.update_custom_attribute_from_dict(
                custom_attribute_dict,
                is_creation,
                from_api=from_api,
                language=localized_info_dict.get("language"),
            )
        if check_still_valid:
            status_updated = self.check_update_status(**transition_kwargs)
            if status_updated:
                updated_offer_attributes.append("status")

        if channel_sync and updated_offer_attributes:
            self.channel_sync(
                updated_offer_attributes=updated_offer_attributes,
                queue=channel_queue,
                is_creation=is_creation,
            )

        logger.debug("update_from_dicts done for offer %s" % self.id)
        return updated_offer_attributes

    @xworkflows.transition_check("trash")
    @xworkflows.transition_check("untrash")
    def _before_transition_trash(self, *args, **kwargs) -> bool:
        return self.application.get_setting(AllowTrashStatus)

    @xworkflows.before_transition("delete_action")
    def before_transition_delete_action(self, force_deletion=False, *args, **kwargs):
        ignore_check = (
            force_deletion or self.status == statuses.PRODUCT_OFFER_STATUS_TRASHED
        )
        if ignore_check:
            return
        has_been_active = self.transition_logs.filter(
            to_state=statuses.PRODUCT_OFFER_STATUS_ACTIVE
        ).exists()
        if has_been_active:
            raise ValidationError(
                _("Can't delete a product offer that has already been active")
            )

    @xworkflows.after_transition("draft")
    def _cascade_draft_variations(self, *args, **kwargs):
        if kwargs.get("nested", False) or not self.is_abstract:
            return
        for variation in self.variations.filter_active():
            variation.draft(nested=True)

    @xworkflows.after_transition("deactivate")
    def _cascade_deactivate_variations(self, *args, **kwargs):
        if kwargs.get("nested", False) or not self.is_abstract:
            return
        for variation in self.variations.filter_active():
            variation.deactivate(nested=True)

    @xworkflows.after_transition("delete_action")
    def _cascade_delete_variations(self, *args, **kwargs):
        if not self.is_abstract:
            return
        for var in self.variations.all():
            if var.delete_action.is_available():
                var.delete_action(nested=True)

    @xworkflows.after_transition("trash")
    def _cascade_trash_variations(self, *args, **kwargs):
        if kwargs.get("nested", False) or not self.is_abstract:
            return
        for var in self.variations.all():
            if var.trash.is_available():
                var.trash(nested=True)

    @xworkflows.after_transition("untrash")
    def _cascade_untrash_variations(self, *args, **kwargs):
        if kwargs.get("nested", False) or not self.is_abstract:
            return
        for var in self.variations.all():
            if var.untrash.is_available():
                var.untrash(nested=True)

    @xworkflows.after_transition("trash", priority=-10)
    @xworkflows.after_transition("untrash", priority=-10)
    @xworkflows.after_transition("delete_action", priority=-10)
    def _trigger_channel_sync(self, *args, **kwargs):
        if kwargs.get("nested", False):
            return
        self.channel_sync()

    def get_cache_keys_to_invalidate(self, languages=None, application_ids=None):
        keys = super(ProductOffer, self).get_cache_keys_to_invalidate(
            languages=languages, application_ids=application_ids
        )
        if self.product:
            keys += self.product.get_cache_keys_to_invalidate(
                languages=languages, application_ids=application_ids
            )
        logger.debug(
            "ProductOffer.get_cache_keys_to_invalidate(%s, %s) = %s"
            % (languages, application_ids, keys)
        )
        return keys

    def channel_sync(self, **kwargs):
        """
        Sync offer with related channels
        """
        channel_event = kwargs
        channel_event.update(
            {
                "event": "sync_request",
                "product_ids": [self.product_id],
                "product_offer_ids": [self.id],
                "merchant_id": self.merchant_id,
            }
        )
        logger.debug("ProductOffer[%s].channel_sync(%s)", self.id, channel_event)
        self.application.trigger_on_active_channels(
            channel_event, currency=self.currency
        )

    # END INSTANCE METHODS

    @property
    def name(self):
        """name property for retro compatibility + deprecation warning message"""
        if settings.DEVELOPMENT or settings.TEST_IS_RUNNING:
            logger.warning(
                "ProductOffer.name is deprecated, use name in localized infos."
            )
        return (
            self.get_localized_info(
                default_language=self.merchant.prefered_language
            ).get("name", None)
            or self.product.name
        )

    @cached_property
    def default_image_url(self):
        return self.main_assigned_image_url or self.product.main_assigned_image_url

    @cached_property
    def discount_manager(self):
        from apps.products.discount_manager import ProductOfferDiscountManager

        return ProductOfferDiscountManager(self)

    @classmethod
    def _validate_batch_arguments(cls, field_name, value, filters_or_queryset, mode):
        """
        Overwrites _validate_batch_arguments to also check that a mandatory field is
        not set to null
        """
        super(ProductOffer, cls)._validate_batch_arguments(
            field_name, value, filters_or_queryset, mode
        )
        if field_name in cls.MANDATORY_FIELDS and not value:
            raise ValidationError(
                _("Can't batch update a mandatory field to empty value")
            )

    def _save_obj_after_batch_updated_field(self, field_name, value, mode):
        """
        Overwrites save function to:
        - add updated field_name to manually_managed fields
        """
        self.manually_managed_fields = list(self.manually_managed_fields or [])
        self.manually_managed_fields = list(
            set(self.manually_managed_fields).union(set(field_name))
        )
        updated_fields = [
            field_name,
            "manually_managed_fields",
            "last_modified",
        ]
        if field_name == "stock" and self.update_availability():
            updated_fields.append("availability")
        # saving only updated_fields
        self.save(update_fields=updated_fields)

    @classmethod
    def _post_batch_update_hook(
        cls,
        field_name,
        value,
        filters_or_queryset,
        mode,
        number_updated,
        modified_ids,
        pre_hook_result,
    ):
        updated_attributes = [field_name, "manually_managed_fields"]
        return cls._post_batch_hook(modified_ids, updated_attributes)

    @classmethod
    def _post_batch_call_hook(
        cls,
        function_name,
        function_params,
        filters_or_queryset,
        number_updated,
        modified_ids,
        pre_hook_result,
    ):
        updated_attributes = cls.BATCH_CALLABLE_FUNCTIONS[function_name].get(
            "updated_attributes", []
        )
        previous_status = cls.BATCH_CALLABLE_FUNCTIONS[function_name].get(
            "previous_status", None
        )
        return cls._post_batch_hook(modified_ids, updated_attributes, previous_status)

    @classmethod
    def _post_batch_hook(cls, modified_ids, updated_attributes, previous_status=None):
        """
        Post batch hook that:
        - triggers channel events
        """
        if not modified_ids:
            return
        cls._post_batch_trigger_channel_events(modified_ids)

    @classmethod
    def _post_batch_trigger_channel_events(cls, modified_ids):
        """
        Post batch-update / batch-call hook that triggers sync_request on channels
        """
        from apps.channels.channel_settings import HIGH_PRIORITY_QUEUE
        from apps.channels.models.channel_models import SYNCHRONOUS_PRIORITY
        from apps.stores.models import Merchant

        merchant_to_product_ids = {}
        merchant_to_offer_ids = {}

        product_and_merchant_dicts = (
            cls.objects.filter(id__in=modified_ids)
            .only("product", "merchant", "id")
            .values("product", "merchant", "id")
        )

        # filling merchant_to_product_ids
        for _dict in product_and_merchant_dicts.iterator():
            if _dict["merchant"] not in merchant_to_product_ids:
                merchant_to_product_ids[_dict["merchant"]] = set()
                merchant_to_offer_ids[_dict["merchant"]] = set()
            merchant_to_product_ids[_dict["merchant"]].add(_dict["product"])
            merchant_to_offer_ids[_dict["merchant"]].add(_dict["id"])

        # triggering sync_request on each merchant
        merchants = Merchant.objects.filter(id__in=merchant_to_product_ids.keys())
        for _merchant in merchants.iterator():
            channel_event = {
                "event": "sync_request",
                "product_ids": merchant_to_product_ids[_merchant.id],
                "product_offer_ids": merchant_to_offer_ids[_merchant.id],
                "merchant_id": _merchant.id,
                "batch_update": True,
                "priority": SYNCHRONOUS_PRIORITY,
                "queue": HIGH_PRIORITY_QUEUE,
            }
            _merchant.application.trigger_on_active_channels(channel_event)

    def activate_if_valid(
        self, from_api: Optional[bool] = False, **extra_transition_kwargs
    ):
        """
        For batch_update
        Try to activate the ProductOffer if not already active.
        Returns True if activated, False if not (or already active).

        """
        if self.status.is_active:
            return False
        if not self.activate.is_available():
            return False
        try:
            self.activate_or_moderate(
                previous_status=self.status, **extra_transition_kwargs
            )
        except ValidationError:
            return False
        return True

    def deactivate_if_available(
        self, from_api: Optional[bool] = False, **extra_transition_kwargs
    ):
        """
        For batch_update
        Deactivate the ProductOffer if available.
        Returns True if deactivated, False if not.
        """
        if not self.deactivate.is_available():
            return False
        self.deactivate(previous_status=self.status, **extra_transition_kwargs)
        return True

    def submit_if_valid(self, **extra_transition_kwargs):
        """
        For batch_update
        Set the product offer to "pending" if available.
        Returns True if submitted, False if not.
        """
        invalid_status = (
            self.status.is_pending
            or self.status.is_active
            or not self.need_review.is_available()
        )
        if invalid_status:
            return False
        try:
            self.need_review(previous_status=self.status, **extra_transition_kwargs)
        except ValidationError:
            return False
        else:
            return True

    def ban_if_available(
        self,
        ignore_perms=False,
        from_api: Optional[bool] = False,
        **extra_transition_kwargs,
    ):
        """
        For batch_update
        Ban the ProductOffer if available.
        Returns True if ban, False if not.
        """
        if not self.ban.is_available():
            return False
        user = extra_transition_kwargs.get("user")
        unauthorized = not ignore_perms and (
            not user or not self.application.is_application_staff(user)
        )
        if unauthorized:
            return False
        self.ban(previous_status=self.status, **extra_transition_kwargs)
        return True

    def unban_if_available(
        self,
        ignore_perms=False,
        from_api: Optional[bool] = False,
        **extra_transition_kwargs,
    ):
        """
        For batch_update
        Unban the ProductOffer if available.
        Returns True if unban, False if not.
        """
        if not self.unban.is_available():
            return False
        user = extra_transition_kwargs.get("user")
        unauthorized = not ignore_perms and (
            not user or not self.application.is_application_staff(user)
        )
        if unauthorized:
            return False
        self.unban(previous_status=self.status, **extra_transition_kwargs)
        return True

    def should_be_auto_activated(self):
        """
        Returns True if this offer should be auto-activated.
        Checks app conf setting AutoOfferActivation and offer status
        """
        from apps.ice_applications.app_conf_settings import AutoOfferActivation

        auto_activate = self.status.is_draft and self.application.get_setting(
            AutoOfferActivation
        )
        return auto_activate

    @classmethod
    def find_matching_offer(
        cls, sku, merchant_id, raise_on_duplicates=False, raise_not_found=False
    ):
        """
        Returns matching offer based on given sku and merchant_id
        """
        # We want to avoid try ... except here as it triggers a false positive Sentry.
        # We force the queryset evaluation by calling first
        # and use count to not trigger an unnecessary SELECT as the result is now cached
        offers = cls.objects.exclude_trashed_deleted().filter(
            merchant_id=merchant_id, sku=sku
        )
        offer = offers.first()
        if not offer and raise_not_found:
            raise cls.DoesNotExist
        elif offers.count() > 1:
            if raise_on_duplicates:
                raise cls.MultipleObjectsReturned
            logger.exception(
                "Should keep only one offer between %s (using %s): "
                % (offers.values_list("id", flat=True), offer.id)
            )
        return offer

    def _create_or_update_variation(
        self,
        variation_dict,
        raise_on_first_error,
        updated_variation_attributes,
        is_crawler,
        errors_dict,
        updated_variation_skus,
        from_api: Optional[bool] = False,
    ):
        variation_info = variation_dict["variation_info"]
        localized_variation_info = variation_dict["localized_variation_info"]
        custom_attributes = variation_dict.get("custom_attributes", {})
        try:
            variation = self.variations.exclude_deleted().get(sku=variation_info["sku"])
            is_new_variation = False
        except ProductVariation.DoesNotExist:
            variation = ProductVariation(
                product_offer=self,
                merchant=self.merchant,
                sku=variation_info["sku"],
                application=self.application,
            )
            is_new_variation = True

        try:
            updated_variation_attributes += variation.update_from_dicts(
                variation_info,
                localized_variation_info,
                is_creation=is_new_variation,
                is_crawler=is_crawler,
                from_api=from_api,
                custom_attribute_dict=custom_attributes,
            )
        except ValidationError as err:
            if raise_on_first_error:
                raise
            else:
                err.update_error_dict(errors_dict)
        else:
            updated_variation_skus.append(variation.sku)
        return variation

    def update_variations_from_dict(
        self,
        variation_dicts,
        errors_dict=None,
        is_crawler=False,
        from_api: Optional[bool] = False,
    ):
        """
        Update offer variations from variation_dicts.
        If an errors_dict is given, returns the tuple
        (updated_variation_attributes, updated_variation_skus, errors_dict)
        else returns (updated_variation_attributes, updated_variation_skus)
        """
        from apps.products.models import ProductVariation

        # if no errors_dict, raise at first error found
        raise_on_first_error = errors_dict is None
        updated_variation_skus = []
        received_variation_ids = set()
        updated_variation_attributes = []

        if self.crawling_policy == self.NO_SYNC:
            logger.debug("Offer %s is in NO_SYNC, not handling variations" % self.id)
            if errors_dict is not None:
                return updated_variation_attributes, updated_variation_skus, errors_dict
            else:
                return updated_variation_attributes, updated_variation_skus

        existing_variation_ids = set(
            self.variations.exclude_deleted().values_list("id", flat=True)
        )

        if not variation_dicts:
            if existing_variation_ids:
                logger.warning(
                    "Offer {} had variations but received empty "
                    "variation_dicts: not deleting them for security reason".format(
                        self.id
                    )
                )
            if errors_dict is not None:
                return updated_variation_attributes, updated_variation_skus, errors_dict
            else:
                return updated_variation_attributes, updated_variation_skus

        # actual update
        for variation_dict in variation_dicts:
            with VariationConcurrencyLocker(
                self.merchant_id, variation_dict["variation_info"]["sku"]
            ):
                variation = self._create_or_update_variation(
                    variation_dict,
                    raise_on_first_error,
                    updated_variation_attributes,
                    is_crawler,
                    errors_dict,
                    updated_variation_skus,
                    from_api=from_api,
                )
                received_variation_ids.add(variation.id)

        # delete existing variations that are no longer in variation_dicts
        try:
            variations_ids_to_delete = existing_variation_ids - received_variation_ids
            variations_to_delete = ProductVariation.objects.filter(
                id__in=variations_ids_to_delete
            )
            for variation in variations_to_delete.iterator():
                if not variation.delete_action.is_available():
                    continue
                variation.delete_action()
                if "status" not in updated_variation_attributes:
                    updated_variation_attributes.append("status")
        except Exception:
            logger.exception("couldn't soft delete missing variations: ")

        updated_variation_attributes = list(set(updated_variation_attributes))

        if errors_dict is not None:
            return updated_variation_attributes, updated_variation_skus, errors_dict
        else:
            return updated_variation_attributes, updated_variation_skus

    def _field_names_to_track(self, *attributes):
        """
        overwritten to exclude last_modified/last_crawled
        that may change even if no value is actually changed
        """
        field_names = super(ProductOffer, self)._field_names_to_track(*attributes)
        if "last_modified" in field_names:
            field_names.remove("last_modified")
        if "last_crawled" in field_names:
            field_names.remove("last_crawled")
        return field_names

    def _clear_last_crawled(self) -> bool:
        if not self.last_crawled:
            return False
        self.last_crawled = None
        self.last_modified = timezone.now()
        self.__class__.objects.filter(id=self.id).update(
            last_crawled=self.last_crawled, last_modified=self.last_modified
        )
        return True

    def auto_handle_channels(self, updated_attributes=None, **kwargs):
        """
        @summary: If updated attributes are given or are found
        in self._updated_attributes (see ValueTrackingMixin),
        triggers channel sync if applicable.
        NB: flush self._updated_attributes at the end
        @result: Set of updated_attributes
        """
        if updated_attributes is None:
            updated_attributes = self._updated_attributes
        else:
            updated_attributes = self._updated_attributes.union(set(updated_attributes))
        if updated_attributes:
            self.channel_sync(updated_offer_attributes=updated_attributes, **kwargs)
            self._flush_updated_attributes()
        return updated_attributes

    def get_application_category_ids(self):
        return self.product.get_application_category_ids()

    def get_package_weight(self, unit):
        """
        Get the offer maximum package_weight converted in `unit`
        For instance, unity can be 'kg' for kilograms
        if not valid fallback on product
        """
        if self.weight_numeral not in ("", "0", 0, None):
            return getattr(Weight(kg=self.weight_numeral), unit)
        return self.product.get_package_weight(unit)

    def get_item_weight(self, unit):
        """
        Get the offer maximum item_weight converted in `unit`
        For instance, unity can be 'kg' for kilograms
        if not valid fallback on product
        """
        if self.weight_numeral not in ("", "0", 0, None):
            return getattr(Weight(kg=self.weight_numeral), unit)
        return self.product.get_item_weight(unit)

    def delete_non_available_attribute_values(self):
        soft_deleted_values = []
        available_attributes = self.get_available_attributes(
            self.product.direct_app_category_ids
        )
        values_to_delete = self.get_attribute_values(exclude_keys=available_attributes)
        for value in values_to_delete.iterator():
            value.delete_action()
            soft_deleted_values.append(value)
        return soft_deleted_values

    def apply_default_tax_data(self, only=None):
        tax_settings = self.merchant.tax_settings
        if not only or "shipped_from_country" in only:
            self.shipped_from_country = tax_settings.default_shipped_from_country
        if not only or "shipped_from_eu_vat_zone" in only:
            self.shipped_from_eu_vat_zone = (
                tax_settings.default_shipped_from_eu_vat_zone
            )
        if not only or "shipped_from_region" in only:
            self.shipped_from_region = tax_settings.default_shipped_from_region
        if not only or "tax_rates" in only:
            self.tax_rates = tax_settings.default_offer_tax_rates
        fields = (
            "shipped_from_country_id",
            "shipped_from_eu_vat_zone",
            "shipped_from_region",
            "tax_rates",
        )

        if self.field_has_changed_since_save(*fields):
            self.save()
            return True
        return False
