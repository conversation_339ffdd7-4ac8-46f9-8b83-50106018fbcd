import logging
import re
from random import randint
from time import sleep

import xworkflows
from django.conf import settings
from django.core.cache import cache
from django.core.exceptions import ValidationError
from django.db import models, transaction
from django.db.models import UniqueConstraint
from django.template.defaultfilters import slugify
from django.utils.encoding import force_str, smart_str
from django.utils.translation import gettext_lazy as _
from django_xworkflows import models as xwf_models
from ims.api.exceptions import ResourceProtectedException
from ims.models.mixin import ApplicationIntegrityCheckMixin
from ims.workflow import IzbergWorkflow
from lib.models import PostgresIndex
from mp_utils.functions import remove_accents
from mp_utils.model_mixins import ExportableModelMixin
from mp_utils.models import CacheableModel
from redis import OutOfMemoryError
from reference import status as statuses
from reference.status import PRODUCT_BRAND_STATUSES_NON_DELETED, PRODUCT_STATUS_ACTIVE

from ..exceptions import BrandRemovalError

logger = logging.getLogger(__name__)

User = settings.AUTH_USER_MODEL

BRAND_CACHE_KEY = "brand:find_helper_to_id:{find_helper}:{application_id}"


class ProductBrandWorkflow(IzbergWorkflow):
    states = statuses.PRODUCT_BRAND_STATUSES

    transitions = (
        (
            "activate",
            [
                statuses.PRODUCT_BRAND_STATUS_ACTIVE,
                statuses.PRODUCT_BRAND_STATUS_DELETED,
            ],
            statuses.PRODUCT_BRAND_STATUS_ACTIVE,
        ),
        (
            "delete_action",
            [
                statuses.PRODUCT_BRAND_STATUS_ACTIVE,
                statuses.PRODUCT_BRAND_STATUS_DELETED,
            ],
            statuses.PRODUCT_BRAND_STATUS_DELETED,
        ),
    )

    initial_state = statuses.PRODUCT_BRAND_STATUS_ACTIVE


class BrandManager(models.Manager):
    def exclude_deleted(self):
        return self.filter(status__in=statuses.PRODUCT_BRAND_STATUSES_NON_DELETED)

    def active_brands(self):
        return self.filter(status=statuses.PRODUCT_BRAND_STATUS_ACTIVE)


class Brand(
    xwf_models.WorkflowEnabled,
    ApplicationIntegrityCheckMixin,
    ExportableModelMixin,
    CacheableModel,
):
    """Describe a brand that can be assigned to a product"""

    api_resource_path = "apps.products.api.resources.BrandResource"

    objects = BrandManager()

    status = xwf_models.StateField(
        ProductBrandWorkflow,
        choices=statuses.PRODUCT_BRAND_STATUSES,
        default=statuses.PRODUCT_BRAND_STATUS_ACTIVE,
    )

    # TODO: check if both index are used (name and slug) make them a unique partial index including app_id
    name = models.CharField(max_length=255)
    slug = models.SlugField(_("Brand slug"))
    find_helper = models.CharField(
        _("Find helper (used to match automatically a brand_name to a brand)"),
        max_length=256,
        blank=True,
        null=True,
    )
    description = models.TextField(null=True, blank=True)
    url = models.TextField(null=True, blank=True)
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        db_index=False,
        on_delete=models.CASCADE,
    )
    merchant = models.ForeignKey(
        "stores.Merchant",
        null=True,
        blank=True,
        db_index=False,
        on_delete=models.CASCADE,
    )
    application = models.ForeignKey(
        "ice_applications.Application",
        on_delete=models.CASCADE,
        db_index=False,
    )

    number_products = models.IntegerField(
        _("Number of mkp products"),
        editable=False,
        blank=True,
        null=True,
        default=0,
    )
    number_affil_products = models.IntegerField(
        _("Number of affil products"),
        editable=False,
        blank=True,
        null=True,
        default=0,
    )

    trendiness = models.FloatField(
        _("Trendiness (default=100)"),
        default=100,
    )

    keywords = models.CharField(
        _("Keywords (comma separated, mainly for seo)"),
        max_length=300,
        blank=True,
        null=True,
    )

    average_price = models.DecimalField(
        _("Average price"),
        max_digits=10,
        decimal_places=2,
        editable=False,
        blank=True,
        null=True,
    )

    def __str__(self):
        return "{}[{}]".format(self.__class__.__name__, self.id)

    EXPORTABLE_FIELDS = (
        ("id", _("Brand ID")),
        ("name", _("Name")),
        ("slug", _("Slug")),
        ("find_helper", _("Find helper")),
        ("merchant", _("Merchant")),
    )

    ACTIVE = statuses.PRODUCT_BRAND_STATUS_ACTIVE
    DELETED = statuses.PRODUCT_BRAND_STATUS_DELETED

    class Meta:
        indexes = [
            PostgresIndex(
                fields=["application_id"],
                name="products_brand_application_id_idx",
            )
        ]

        constraints = [
            UniqueConstraint(
                fields=["application_id", "slug"],
                condition=models.Q(
                    status__in=PRODUCT_BRAND_STATUSES_NON_DELETED,
                ),
                name="products_brand_unique_application_id_and_slug",
            )
        ]

    def get_absolute_url(self):
        """
        Used in resource, because include_absolute_url = True in resource Meta
        """
        if self.id is not None:
            return "/v1/brand/%s/" % self.id
        else:
            return ""

    @xworkflows.before_transition("activate")
    def _validate_uniqueness_on_activate(self, *args, **kwargs):
        self._validate_uniqueness()

    @xworkflows.before_transition("delete_action")
    def prevent_brands_removal_with_product_families(self, *args, **kwargs):
        if self.familyselector_set.exists():
            raise BrandRemovalError("Brand assigned to a family selector")

    @xworkflows.after_transition("delete_action")
    def clean_products_after_soft_removal(self, *args, **kwargs):
        products = self.products.all()
        products_ids_to_revalidate = self.get_related_products_ids_copy()
        updated_count = products.update(brand=None)
        if updated_count == 0:
            return
        self.revalidate_products_from_ids_list(products_ids_to_revalidate)

    def get_related_products_ids_copy(self):
        all_related_products = self.products.all()
        related_products_ids = all_related_products.values_list("id", flat=True)
        int_ids_list = list(int(pid) for pid in related_products_ids)
        return int_ids_list

    def revalidate_products_from_ids_list(self, products_ids_list):
        # import made inside the function to avoid circular dependency
        from apps.products.models import Product

        # Rely on Product model's manager instead of self.products because
        # we will potentially need to revalidate products that do not
        # belong to the brand anymore (for ex in case of brand deletion)
        int_ids = list(map(int, products_ids_list))
        products = Product.objects.filter(id__in=int_ids, status=PRODUCT_STATUS_ACTIVE)
        errors = {}
        for product in products:
            errors = product.validate_product(errors, fast_return=False)
        return errors

    def _validate_uniqueness(self):
        brand_existing = (
            Brand.objects.exclude_deleted()
            .filter(application=self.application_id, slug=self.slug)
            .exists()
        )

        if brand_existing:
            raise ValidationError(
                {"slug": _(f"Brand '{self.name}' with slug {self.slug} already exists")}
            )

    def clean(self):
        if not self.slug:
            self.slug = slugify(self.name)[:50]
        if not self.find_helper:
            self.find_helper = slugify_find_helper(slugify(self.name)[:50])

        if self.id is None:
            self._fill_app_for_merchant_brand()
            self._validate_uniqueness()

    def _fill_app_for_merchant_brand(self):
        if self.merchant_id and not self.application_id:
            self.application_id = self.merchant.application_id

    def save(self, from_api=False, *args, **kwargs):
        self.full_clean()
        super().save(from_api=from_api, *args, **kwargs)

    def to_dict(self, non_empty_only=True, exclude=None):
        data = {}
        exclude = exclude or []
        for key, value in self.__dict__.items():
            if non_empty_only and not value:
                continue
            if key in exclude or key.startswith("_"):
                continue
            data[key] = value
        if "resource_uri" not in exclude:
            data["resource_uri"] = self.get_resource_uri()
        return data

    def _pre_deletion_checks(self):
        if self.shipping_provider_assignments.exclude_deleted().exists():
            raise ResourceProtectedException(
                self,
                error_description=_(
                    "Cannot delete a brand with active shipping assignments"
                ),
            )

    def delete(self, *args, **kwargs):
        self._pre_deletion_checks()
        super(Brand, self).delete(*args, **kwargs)

    @xworkflows.transition_check("delete_action")
    def _check_used_before_deletion(self, *args, **kwargs):
        self._pre_deletion_checks()
        return True

    @xworkflows.after_transition("delete_action")
    def invalidate_matching_cache(self, *args, **kwargs):
        BrandMatching.invalidate_cache(self)


SOFT_NAME_RE = re.compile(r"[^a-z0-9éèêàôöçœïîâ $&/.\[\]]")
FIND_HELPER_RE = re.compile(
    r"(^the | the |^le | le |^la | la |^les | les |^l'| l'| et | and |[^a-z0-9])*"
)


def slugify_find_helper(brand_name):
    brand_name = force_str(brand_name)
    soft_name = SOFT_NAME_RE.sub("", brand_name.lower())
    return FIND_HELPER_RE.sub("", remove_accents(soft_name))


class BrandMatching:
    @classmethod
    def invalidate_cache(cls, brand: Brand) -> bool:
        # Handle generic brand
        if not brand.application_id:
            return False

        cache_key = BRAND_CACHE_KEY.format(
            find_helper=brand.find_helper,
            application_id=brand.application_id,
        )

        return cache.delete(cache_key)

    def get_or_create_brand_id(
        self,
        brand_name,
        application=None,
    ):
        """
        Get the brand id corresponding to brand_name, based on regex
        """
        if not isinstance(brand_name, str) or not (brand_name):
            raise Exception(
                "brand_name must be a non-empty string "
                "(brand_name= %s, type= %s)" % (brand_name, type(brand_name))
            )
        if not application:
            raise Exception("application must be given")

        is_new = False
        find_helper = slugify_find_helper(brand_name)
        application_id = application.id if application else None
        cache_key = BRAND_CACHE_KEY.format(
            find_helper=find_helper,
            application_id=application_id,
        )
        brand_id = cache.get(cache_key, False)
        if brand_id:
            return brand_id, is_new
        else:
            try:
                brand = Brand.objects.exclude_deleted().get(
                    find_helper=find_helper, application=application
                )
            except Brand.DoesNotExist:
                # Add some random delay to avoid concurrency. 50 - 70 ms
                sleep((50 + randint(0, 20)) / 1000)
                brand, is_new = Brand.objects.exclude_deleted().get_or_create(
                    find_helper=find_helper,
                    application=application,
                    defaults={"name": smart_str(brand_name)},
                )
            except Brand.MultipleObjectsReturned:
                # Should not happen, but show must go on.
                brand = (
                    Brand.objects.exclude_deleted()
                    .filter(find_helper=find_helper, application=application)
                    .first()
                )
                logger.info(
                    f"Multiple Brand returned with the given find_helper"
                    f": {find_helper} on application: {application.id}"
                )

            if transaction.get_autocommit():
                # not in a transaction, no risk of brand creation being
                # rolledback
                # putting in cache as we probably meet this brand again in the
                # same feed parsing
                try:
                    cache.set(cache_key, brand.id, 60 * 30)
                except OutOfMemoryError as e:
                    logger.error(
                        "Out of memory error while trying to set {} cache key: {}".format(
                            cache_key, e
                        )
                    )
            return brand.id, is_new


brand_matching = BrandMatching()
