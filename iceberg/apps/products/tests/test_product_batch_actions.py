# -*- coding: utf-8 -*-

from apps.tasking.models import (
    ProductBatchRemoveManuallyManagedFieldsAction,
    ProductBatchUpdateApplicationCategoryAction,
    ProductBatchUpdateKeywordsAction,
)
from apps.tasking.redis_client import get_client
from apps.testing.factories import (
    ApplicationCategoryFactory,
    ApplicationFactory,
    LocalizedProductInfoFactory,
    MerchantFactory,
    ProductFactory,
)
from ims.tests import BaseResourceTestCase
from mock import patch


class BatchActionMixin:
    def create_products(self, count, **kwargs):
        return ProductFactory.create_batch(size=count, **kwargs)

    def get_action(self):
        client = get_client()
        _, ids = client.scan(0, "*")
        if ids:
            full_id = ids[0].split(b":")[2]
            app_id, merchant_id, action_id = full_id.split(b"-", 2)
            action = self.action_cls(application_id=app_id, merchant_id=merchant_id)
            action.id = full_id
            return action
        return None

    def do_api_filters_request(
        self,
        dry_run,
        products,
        token=None,
        application=None,
        merchant=None,
        izb_staff=False,
        **body_kwargs,
    ):
        data = {
            "filters": {"id__in": [_product.id for _product in products]},
            "filter_type": "api_filters",
            **body_kwargs,
        }
        if dry_run is not None:
            data["dry_run"] = dry_run
        if not application and products:
            application = products[0].application

        path = "/v1/product/batch/{}/".format(self.action_name)
        if merchant:
            path += f"?merchant={merchant.id}"

        if not token:
            if merchant:
                token = self.get_merchant_token(merchant=merchant)
            elif izb_staff:
                token = self.get_staff_token()
            else:
                token = self.get_operator_token(application)
        return self.api_client.post(path, data=data, authentication=token)

    def do_algolia_filters_request(self, dry_run, product, application=None, **kwargs):
        if not application:
            application = product.application
        data = {
            "filters": {
                "channel_id": application.backoffice_channel.id,
                "requests": [
                    {
                        "index": "my:algolia:index",
                        "query": "",
                        "facets": ["merchant.group_keys", "price_with_vat", "status"],
                        "disjunctiveFacets": [
                            "merchant.name",
                            "product.brand.name",
                            "product.gender",
                            "product.keywords",
                            "availability",
                            "product.application_categories_dict.name",
                            "discounted",
                            "applied_discounts.operation_type",
                        ],
                        "hierarchicalFacets": [],
                        "facetsRefinements": {"status": ["active"]},
                        "facetsExcludes": {},
                        "disjunctiveFacetsRefinements": {},
                        "numericRefinements": {},
                        "tagRefinements": [],
                        "hierarchicalFacetsRefinements": {},
                        "typoTolerance": "false",
                        "length": 1,
                        "managedParameters": [
                            "index",
                            "facets",
                            "disjunctiveFacets",
                            "facetsRefinements",
                            "facetsExcludes",
                            "disjunctiveFacetsRefinements",
                            "numericRefinements",
                            "tagRefinements",
                            "hierarchicalFacets",
                            "hierarchicalFacetsRefinements",
                        ],
                        "params": "query=",
                        "indexName": "my:algolia:index:name",
                    }
                ],
            },
            "filter_type": "algolia",
            **kwargs,
        }

        if dry_run is not None:
            data["dry_run"] = dry_run
        return self.api_client.post(
            "/v1/product/batch/{}/".format(self.action_name),
            data=data,
            authentication=self.get_operator_token(application),
        )


class BatchRemoveAllManuallyManagedFieldsTestCase(
    BaseResourceTestCase, BatchActionMixin
):
    action_name = "remove-manually-managed-fields"
    action_cls = ProductBatchRemoveManuallyManagedFieldsAction

    def create_products(self, count, **kwargs):
        return ProductFactory.create_batch(
            size=count, manually_managed_fields=["brand", "name"], **kwargs
        )

    def test_dry_run_false_api_filters_as_operateur(self):
        product = self.create_products(2)[0]

        resp = self.do_api_filters_request(False, [product])

        self.assertEqual(resp.status_code, 202)
        self.assertEqual(resp.content, b"")
        self.assertIn("/v1/async-action/", resp["Location"])

        action = self.get_action()
        data = action.get_as_dict()
        self.assertIsNotNone(data.pop("created_on"))
        self.assertEqual(
            data,
            {
                "status": "in_queue",
                "ended_on": None,
                "total_products": 1,
                "ignored_products": 0,
                "errored_products": 0,
                "action_name": self.action_cls.action_name,
                "modified_products": 0,
                "lock_action": True,
                "id": action.id,
                "result": {},
            },
        )

    def test_dry_run_false_api_filters_as_merchant(self):
        merchant = MerchantFactory()
        product = self.create_products(
            count=2, application=merchant.application, merchant_admin=merchant
        )[0]

        resp = self.do_api_filters_request(False, [product], merchant=merchant)

        self.assertEqual(resp.status_code, 202)
        self.assertEqual(resp.content, b"")
        self.assertIn("/v1/async-action/", resp["Location"])

        action = self.get_action()
        data = action.get_as_dict()
        self.assertIsNotNone(data.pop("created_on"))
        self.assertEqual(
            data,
            {
                "status": "in_queue",
                "ended_on": None,
                "total_products": 1,
                "ignored_products": 0,
                "errored_products": 0,
                "action_name": self.action_cls.action_name,
                "modified_products": 0,
                "lock_action": True,
                "id": action.id,
                "result": {},
            },
        )

    def test_dry_run_true(self):
        product = self.create_products(1)[0]

        resp = self.do_api_filters_request(True, [product])
        self.assertHttpOK(resp)
        self.assertEqual(self.deserialize(resp), {"product_count": 1, "dry_run": True})
        self.assertIsNone(self.get_action())

    def test_dry_run_not_set_defaults_true(self):
        product = self.create_products(1)[0]

        resp = self.do_api_filters_request(None, [product])
        self.assertHttpOK(resp)
        self.assertEqual(self.deserialize(resp), {"product_count": 1, "dry_run": True})
        self.assertIsNone(self.get_action())

    def test_as_merchant(self):
        merchant = MerchantFactory()
        product = self.create_products(
            count=1, application=merchant.application, merchant_admin=merchant
        )[0]

        resp = self.do_api_filters_request(True, [product], merchant=merchant)

        self.assertHttpOK(resp)
        self.assertEqual(self.deserialize(resp), {"product_count": 1, "dry_run": True})
        self.assertIsNone(self.get_action())

    def test_as_izb_staff(self):
        product = self.create_products(1)[0]

        resp = self.do_api_filters_request(False, [product])

        self.assertEqual(resp.status_code, 202)

    def test_unfiltered_request_as_izberg_staff(self):
        self.create_products(1)

        resp = self.api_client.post(
            "/v1/product/batch/{}/".format(self.action_name),
            data={"dry_run": False, "filters": {}, "filter_type": "api_filters"},
            authentication=self.get_staff_token(),
        )

        self.assertEqual(resp.status_code, 400)
        self.assertIsNone(self.get_action())
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Missing mandatory parameter"], "field": "merchant"}]},
        )

    def test_as_customer(self):
        product = self.create_products(1)[0]

        resp = self.api_client.post(
            "/v1/product/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": [product.id]},
                "filter_type": "api_filters",
            },
            authentication=self.get_app_user_token(application=product.application),
        )
        self.assertEqual(resp.status_code, 401)

    def test_with_empty_queryset(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/product/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": []},
                "filter_type": "api_filters",
            },
            authentication=self.get_operator_token(application=application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": [
                            "Trying to batch-update 0 item. Please check your "
                            "filters."
                        ],
                        "field": "__all__",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_invalid_api_filters(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/product/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"invalid": "value"},
                "filter_type": "api_filters",
            },
            authentication=self.get_operator_token(application=application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": ["The 'invalid' field does not allow filtering."],
                        "field": "__all__",
                    }
                ]
            },
        )

    def test_not_owner_of_ids(self):
        product = self.create_products(1)[0]
        application = ApplicationFactory()

        resp = self.do_api_filters_request(False, [product], application=application)
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": [
                            "Trying to batch-update 0 item. Please check your "
                            "filters."
                        ],
                        "field": "__all__",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_with_empty_body(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/product/batch/{}/".format(self.action_name),
            data={},
            authentication=self.get_operator_token(application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Missing mandatory key"], "field": "filter_type"}]},
        )
        self.assertIsNone(self.get_action())

    def test_invalid_api_filter_value(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/product/batch/{}/".format(self.action_name),
            data={
                "dry_run": False,
                "filters": {"id__in": ["blablabla"]},
                "filter_type": "api_filters",
            },
            authentication=self.get_operator_token(application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Invalid filters provided"], "field": "filters"}]},
        )
        self.assertIsNone(self.get_action())

    @patch(
        "apps.products.algolia_items_retriever.AlgoliaItemsRetriever."
        "get_algolia_index"
    )
    @patch("apps.products.algolia_items_retriever.AlgoliaItemsRetriever._do_search")
    def test_dry_run_false_algolia_filters(self, mocked_search, mocked_get_index):
        product = self.create_products(1)[0]
        mocked_search.return_value = [{"id": product.id}]

        resp = self.do_algolia_filters_request(False, product)
        self.assertEqual(resp.status_code, 202)
        self.assertEqual(resp.content, b"")
        self.assertIn("/v1/async-action/", resp["Location"])

        action = self.get_action()
        data = action.get_as_dict()
        self.assertIsNotNone(data.pop("created_on"))
        self.assertEqual(
            data,
            {
                "status": "in_queue",
                "ended_on": None,
                "total_products": 1,
                "action_name": self.action_cls.action_name,
                "ignored_products": 0,
                "errored_products": 0,
                "modified_products": 0,
                "lock_action": True,
                "id": action.id,
                "result": {},
            },
        )

    @patch(
        "apps.products.algolia_items_retriever.AlgoliaItemsRetriever."
        "get_algolia_index"
    )
    @patch("apps.products.algolia_items_retriever.AlgoliaItemsRetriever._do_search")
    def test_dry_run_false_algolia_filters_diffent_number_found(
        self, mocked_search, mocked_get_index
    ):
        product = self.create_products(1)[0]
        mocked_search.return_value = []

        resp = self.do_algolia_filters_request(False, product)
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": ["Expected count 1 does not match computed 0"],
                        "field": "__all__",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())


class BatchUpdateApplicationCategoryTestCase(BaseResourceTestCase, BatchActionMixin):
    action_name = "update-application-category"
    action_cls = ProductBatchUpdateApplicationCategoryAction

    def create_category(self, application, count=1):
        if count > 1:
            return [
                ApplicationCategoryFactory.create(application=application)
                for _ in range(count)
            ]
        return ApplicationCategoryFactory.create(application=application)

    def test_dry_run_false_api_filters_as_operateur(self):
        product = self.create_products(2)[0]
        category = self.create_category(product.application)

        resp = self.do_api_filters_request(
            False, [product], category_id=category.id, action="assign"
        )

        self.assertEqual(resp.status_code, 202)
        self.assertEqual(resp.content, b"")
        self.assertIn("/v1/async-action/", resp["Location"])

        action = self.get_action()
        data = action.get_as_dict()
        self.assertIsNotNone(data.pop("created_on"))
        self.assertEqual(
            data,
            {
                "status": "in_queue",
                "ended_on": None,
                "total_products": 1,
                "ignored_products": 0,
                "errored_products": 0,
                "action_name": self.action_cls.action_name,
                "modified_products": 0,
                "category_id": category.id,
                "action": "assign",
                "lock_action": True,
                "id": action.id,
                "result": {},
            },
        )

    def test_dry_run_false_api_filters_as_merchant(self):
        merchant = MerchantFactory()
        product = self.create_products(
            count=2, application=merchant.application, merchant_admin_id=merchant.id
        )[0]

        category = self.create_category(application=product.application)

        resp = self.do_api_filters_request(
            False,
            [product],
            merchant=merchant,
            category_id=category.id,
            action="assign",
        )

        self.assertEqual(resp.status_code, 202)
        self.assertEqual(resp.content, b"")
        self.assertIn("/v1/async-action/", resp["Location"])

        action = self.get_action()
        data = action.get_as_dict()
        self.assertIsNotNone(data.pop("created_on"))
        self.assertEqual(
            data,
            {
                "status": "in_queue",
                "ended_on": None,
                "total_products": 1,
                "ignored_products": 0,
                "errored_products": 0,
                "action_name": self.action_cls.action_name,
                "modified_products": 0,
                "category_id": category.id,
                "action": "assign",
                "lock_action": True,
                "id": action.id,
                "result": {},
            },
        )

    def test_dry_run_true(self):
        product = self.create_products(1)[0]

        category = self.create_category(application=product.application)

        resp = self.do_api_filters_request(
            True, [product], category_id=category.id, action="assign"
        )
        self.assertHttpOK(resp)
        self.assertEqual(self.deserialize(resp), {"product_count": 1, "dry_run": True})
        self.assertIsNone(self.get_action())

    def test_dry_run_not_set_defaults_true(self):
        product = self.create_products(1)[0]

        category = self.create_category(application=product.application)

        resp = self.do_api_filters_request(
            None, [product], category_id=category.id, action="assign"
        )
        self.assertHttpOK(resp)
        self.assertEqual(self.deserialize(resp), {"product_count": 1, "dry_run": True})
        self.assertIsNone(self.get_action())

    def test_as_merchant(self):
        merchant = MerchantFactory()
        product = self.create_products(
            count=1, application=merchant.application, merchant_admin_id=merchant.id
        )[0]

        category = self.create_category(application=merchant.application)

        resp = self.do_api_filters_request(
            True, [product], merchant=merchant, category_id=category.id, action="assign"
        )

        self.assertHttpOK(resp)
        self.assertEqual(self.deserialize(resp), {"product_count": 1, "dry_run": True})
        self.assertIsNone(self.get_action())

    def test_as_izb_staff(self):
        product = self.create_products(1)[0]
        category = self.create_category(application=product.application)

        resp = self.do_api_filters_request(
            False, [product], category_id=category.id, action="assign"
        )

        self.assertEqual(resp.status_code, 202)

    def test_unfiltered_request_as_izberg_staff(self):
        product = self.create_products(1)[0]
        category = self.create_category(application=product.application)

        resp = self.api_client.post(
            "/v1/product/batch/{}/".format(self.action_name),
            data={
                "dry_run": False,
                "filters": {},
                "filter_type": "api_filters",
                "category_id": category.id,
                "action": "assign",
            },
            authentication=self.get_staff_token(),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertIsNone(self.get_action())
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Missing mandatory parameter"], "field": "merchant"}]},
        )

    def test_as_customer(self):
        product = self.create_products(1)[0]

        resp = self.api_client.post(
            "/v1/product/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": [product.id]},
                "filter_type": "api_filters",
            },
            authentication=self.get_app_user_token(application=product.application),
        )
        self.assertEqual(resp.status_code, 401)

    def test_with_empty_queryset(self):
        application = ApplicationFactory()
        category = self.create_category(application=application)

        resp = self.api_client.post(
            "/v1/product/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": []},
                "filter_type": "api_filters",
                "category_id": category.id,
                "action": "assign",
            },
            authentication=self.get_operator_token(application=application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": [
                            "Trying to batch-update 0 item. Please check your "
                            "filters."
                        ],
                        "field": "__all__",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_invalid_api_filters(self):
        application = ApplicationFactory()
        category = self.create_category(application=application)

        resp = self.api_client.post(
            "/v1/product/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"invalid": "value"},
                "filter_type": "api_filters",
                "category_id": category.id,
                "action": "assign",
            },
            authentication=self.get_operator_token(application=application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": ["The 'invalid' field does not allow filtering."],
                        "field": "__all__",
                    }
                ]
            },
        )

    def test_not_owner_of_ids(self):
        product = self.create_products(1)[0]
        application = ApplicationFactory()
        category = ApplicationCategoryFactory(application=application)

        resp = self.do_api_filters_request(
            False,
            [product],
            application=application,
            category_id=category.id,
            action="assign",
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": [
                            "Trying to batch-update 0 item. Please check your "
                            "filters."
                        ],
                        "field": "__all__",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_with_empty_body(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/product/batch/{}/".format(self.action_name),
            data={},
            authentication=self.get_operator_token(application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": [
                            "Missing mandatory key or invalid value,"
                            " only an integer is allowed"
                        ],
                        "field": "category_id",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_invalid_api_filter_value(self):
        application = ApplicationFactory()
        category = self.create_category(application=application)

        resp = self.api_client.post(
            "/v1/product/batch/{}/".format(self.action_name),
            data={
                "dry_run": False,
                "filters": {"id__in": ["blablabla"]},
                "filter_type": "api_filters",
                "category_id": category.id,
                "action": "assign",
            },
            authentication=self.get_operator_token(application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Invalid filters provided"], "field": "filters"}]},
        )
        self.assertIsNone(self.get_action())

    def test_with_no_action(self):
        product = self.create_products(1)[0]
        category = self.create_category(application=product.application)

        resp = self.api_client.post(
            "/v1/product/batch/{}/".format(self.action_name),
            data={
                "dry_run": False,
                "filters": {"id__in": [product.id]},
                "filter_type": "api_filters",
                "category_id": category.id,
            },
            authentication=self.get_operator_token(product.application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Missing mandatory key"], "field": "action"}]},
        )
        self.assertIsNone(self.get_action())

    def test_with_invalid_action(self):
        product = self.create_products(1)[0]
        category = self.create_category(application=product.application)

        resp = self.api_client.post(
            "/v1/product/batch/{}/".format(self.action_name),
            data={
                "dry_run": False,
                "filters": {"id__in": [product.id]},
                "filter_type": "api_filters",
                "category_id": category.id,
                "action": "invalid",
            },
            authentication=self.get_operator_token(product.application),
        )

        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": ['Action should be "assign" or "remove"'],
                        "field": "action",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_with_no_category_id(self):
        product = self.create_products(1)[0]
        resp = self.api_client.post(
            "/v1/product/batch/{}/".format(self.action_name),
            data={
                "dry_run": False,
                "filters": {"id__in": [product.id]},
                "filter_type": "api_filters",
                "action": "assign",
            },
            authentication=self.get_operator_token(product.application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": [
                            "Missing mandatory key or invalid value,"
                            " only an integer is allowed"
                        ],
                        "field": "category_id",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_with_invalid_category_id(self):
        product = self.create_products(1)[0]
        resp = self.api_client.post(
            "/v1/product/batch/{}/".format(self.action_name),
            data={
                "dry_run": False,
                "filters": {"id__in": [product.id]},
                "filter_type": "api_filters",
                "category_id": "test",
                "action": "assign",
            },
            authentication=self.get_operator_token(product.application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": [
                            "Missing mandatory key or invalid value,"
                            " only an integer is allowed"
                        ],
                        "field": "category_id",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    @patch(
        "apps.products.algolia_items_retriever.AlgoliaItemsRetriever."
        "get_algolia_index"
    )
    @patch("apps.products.algolia_items_retriever.AlgoliaItemsRetriever._do_search")
    def test_dry_run_false_algolia_filters(self, mocked_search, mocked_get_index):
        product = self.create_products(1)[0]
        category = self.create_category(application=product.application)
        mocked_search.return_value = [{"id": product.id}]

        resp = self.do_algolia_filters_request(
            False, product, category_id=category.id, action="assign"
        )
        self.assertEqual(resp.status_code, 202)
        self.assertEqual(resp.content, b"")
        self.assertIn("/v1/async-action/", resp["Location"])

        action = self.get_action()
        data = action.get_as_dict()
        self.assertIsNotNone(data.pop("created_on"))
        self.assertEqual(
            data,
            {
                "status": "in_queue",
                "ended_on": None,
                "total_products": 1,
                "action_name": self.action_cls.action_name,
                "ignored_products": 0,
                "errored_products": 0,
                "modified_products": 0,
                "category_id": category.id,
                "action": "assign",
                "lock_action": True,
                "id": action.id,
                "result": {},
            },
        )

    @patch(
        "apps.products.algolia_items_retriever.AlgoliaItemsRetriever."
        "get_algolia_index"
    )
    @patch("apps.products.algolia_items_retriever.AlgoliaItemsRetriever._do_search")
    def test_dry_run_false_algolia_filters_diffent_number_found(
        self, mocked_search, mocked_get_index
    ):
        product = self.create_products(1)[0]
        category = self.create_category(application=product.application)
        mocked_search.return_value = []

        resp = self.do_algolia_filters_request(
            False, product, category_id=category.id, action="assign"
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": ["Expected count 1 does not match computed 0"],
                        "field": "__all__",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())


class BatchUpdateKeywordsTestCase(BaseResourceTestCase, BatchActionMixin):
    action_name = "update-keywords"
    action_cls = ProductBatchUpdateKeywordsAction

    def create_localized_products(self, products, **kwargs):
        return [
            LocalizedProductInfoFactory(product=product, **kwargs)
            for product in products
        ]

    def test_dry_run_false_api_filters_as_operateur(self):
        product = self.create_products(2)[0]
        localized_info = self.create_localized_products(products=[product])[0]

        resp = self.do_api_filters_request(
            False,
            [product],
            keywords=["keyword_1", "keyword_2"],
            language=localized_info.language,
            action="add",
        )

        self.assertEqual(resp.status_code, 202)
        self.assertEqual(resp.content, b"")
        self.assertIn("/v1/async-action/", resp["Location"])

        action = self.get_action()
        data = action.get_as_dict()
        self.assertIsNotNone(data.pop("created_on"))
        self.assertEqual(
            data,
            {
                "status": "in_queue",
                "ended_on": None,
                "total_products": 1,
                "ignored_products": 0,
                "errored_products": 0,
                "action_name": self.action_cls.action_name,
                "modified_products": 0,
                "value": ["keyword_1", "keyword_2"],
                "language": localized_info.language,
                "action": "add",
                "lock_action": True,
                "id": action.id,
                "result": {},
            },
        )

    def test_dry_run_false_api_filters_as_merchant(self):
        merchant = MerchantFactory()
        product = self.create_products(
            count=2, application=merchant.application, merchant_admin_id=merchant.id
        )[0]

        localized_info = self.create_localized_products(products=[product])[0]

        resp = self.do_api_filters_request(
            False,
            [product],
            merchant=merchant,
            keywords=["keyword_1", "keyword_2"],
            language=localized_info.language,
            action="add",
        )

        self.assertEqual(resp.status_code, 202)
        self.assertEqual(resp.content, b"")
        self.assertIn("/v1/async-action/", resp["Location"])

        action = self.get_action()
        data = action.get_as_dict()
        self.assertIsNotNone(data.pop("created_on"))
        self.assertEqual(
            data,
            {
                "status": "in_queue",
                "ended_on": None,
                "total_products": 1,
                "ignored_products": 0,
                "errored_products": 0,
                "action_name": self.action_cls.action_name,
                "modified_products": 0,
                "value": ["keyword_1", "keyword_2"],
                "language": localized_info.language,
                "action": "add",
                "lock_action": True,
                "id": action.id,
                "result": {},
            },
        )

    def test_dry_run_true(self):
        product = self.create_products(1)[0]
        localized_info = self.create_localized_products(products=[product])[0]

        resp = self.do_api_filters_request(
            True,
            [product],
            keywords=["keyword_1", "keyword_2"],
            language=localized_info.language,
            action="add",
        )
        self.assertHttpOK(resp)
        self.assertEqual(self.deserialize(resp), {"product_count": 1, "dry_run": True})
        self.assertIsNone(self.get_action())

    def test_dry_run_not_set_defaults_true(self):
        product = self.create_products(1)[0]
        localized_info = self.create_localized_products(products=[product])[0]

        resp = self.do_api_filters_request(
            True,
            [product],
            keywords=["keyword_1", "keyword_2"],
            language=localized_info.language,
            action="add",
        )

        self.assertHttpOK(resp)
        self.assertEqual(self.deserialize(resp), {"product_count": 1, "dry_run": True})
        self.assertIsNone(self.get_action())

    def test_as_merchant(self):
        merchant = MerchantFactory()
        product = self.create_products(
            count=1, application=merchant.application, merchant_admin_id=merchant.id
        )[0]

        localized_info = self.create_localized_products(products=[product])[0]

        resp = self.do_api_filters_request(
            True,
            [product],
            keywords=["keyword_1", "keyword_2"],
            language=localized_info.language,
            action="add",
        )

        self.assertHttpOK(resp)
        self.assertEqual(self.deserialize(resp), {"product_count": 1, "dry_run": True})
        self.assertIsNone(self.get_action())

    def test_as_izb_staff(self):
        merchant = MerchantFactory()
        product = self.create_products(
            1, application=merchant.application, merchant_admin_id=merchant.id
        )[0]
        localized_info = self.create_localized_products(products=[product])[0]

        resp = self.do_api_filters_request(
            False,
            [product],
            merchant=merchant,
            keywords=["keyword_1", "keyword_2"],
            language=localized_info.language,
            action="add",
            izb_staff=True,
        )

        self.assertEqual(resp.status_code, 202)

    def test_unfiltered_request_as_izberg_staff(self):
        product = self.create_products(1)[0]
        localized_info = self.create_localized_products(products=[product])[0]

        resp = self.api_client.post(
            "/v1/product/batch/{}/".format(self.action_name),
            data={
                "dry_run": False,
                "filters": {},
                "filter_type": "api_filters",
                "keywords": ["keyword_1", "keyword_2"],
                "language": localized_info.language,
                "action": "add",
            },
            authentication=self.get_staff_token(),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertIsNone(self.get_action())
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Missing mandatory parameter"], "field": "merchant"}]},
        )

    def test_as_customer(self):
        product = self.create_products(1)[0]
        localized_info = self.create_localized_products(products=[product])[0]

        resp = self.api_client.post(
            "/v1/product/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": [product.id]},
                "filter_type": "api_filters",
                "keywords": ["keyword_1", "keyword_2"],
                "language": localized_info.language,
                "action": "add",
            },
            authentication=self.get_app_user_token(application=product.application),
        )
        self.assertEqual(resp.status_code, 401)

    def test_with_empty_queryset(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/product/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": []},
                "filter_type": "api_filters",
                "keywords": ["keyword_1", "keyword_2"],
                "language": "fr",
                "action": "add",
            },
            authentication=self.get_operator_token(application=application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": [
                            "Trying to batch-update 0 item. Please check your "
                            "filters."
                        ],
                        "field": "__all__",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_invalid_api_filters(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/product/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"invalid": "value"},
                "filter_type": "api_filters",
                "keywords": ["keyword_1", "keyword_2"],
                "language": "fr",
                "action": "add",
            },
            authentication=self.get_operator_token(application=application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": ["The 'invalid' field does not allow filtering."],
                        "field": "__all__",
                    }
                ]
            },
        )

    def test_not_owner_of_ids(self):
        product = self.create_products(1)[0]
        localized_info = self.create_localized_products(products=[product])[0]

        application = ApplicationFactory()

        resp = self.do_api_filters_request(
            False,
            [product],
            keywords=["keyword_1", "keyword_2"],
            language=localized_info.language,
            action="add",
            application=application,
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": [
                            "Trying to batch-update 0 item. Please check your "
                            "filters."
                        ],
                        "field": "__all__",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_with_empty_body(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/product/batch/{}/".format(self.action_name),
            data={},
            authentication=self.get_operator_token(application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": ["Missing mandatory key"],
                        "field": "keywords",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_invalid_api_filter_value(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/product/batch/{}/".format(self.action_name),
            data={
                "dry_run": False,
                "filters": {"id__in": ["blablabla"]},
                "filter_type": "api_filters",
                "keywords": ["keyword_1", "keyword_2"],
                "language": "fr",
                "action": "add",
            },
            authentication=self.get_operator_token(application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Invalid filters provided"], "field": "filters"}]},
        )
        self.assertIsNone(self.get_action())

    def test_with_no_action(self):
        product = self.create_products(1)[0]
        localized_info = self.create_localized_products(products=[product])[0]

        resp = self.api_client.post(
            "/v1/product/batch/{}/".format(self.action_name),
            data={
                "dry_run": False,
                "filters": {"id__in": [product.id]},
                "filter_type": "api_filters",
                "keywords": ["keyword_1", "keyword_2"],
                "language": localized_info.language,
            },
            authentication=self.get_operator_token(product.application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Missing mandatory key"], "field": "action"}]},
        )
        self.assertIsNone(self.get_action())

    def test_with_invalid_action(self):
        product = self.create_products(1)[0]
        localized_info = self.create_localized_products(products=[product])[0]

        resp = self.api_client.post(
            "/v1/product/batch/{}/".format(self.action_name),
            data={
                "dry_run": False,
                "filters": {"id__in": [product.id]},
                "filter_type": "api_filters",
                "keywords": ["keyword_1", "keyword_2"],
                "language": localized_info.language,
                "action": "invalid",
            },
            authentication=self.get_operator_token(product.application),
        )

        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": ['Action should be "add" or "remove"'],
                        "field": "action",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_with_no_language(self):
        product = self.create_products(1)[0]

        resp = self.api_client.post(
            "/v1/product/batch/{}/".format(self.action_name),
            data={
                "dry_run": False,
                "filters": {"id__in": [product.id]},
                "filter_type": "api_filters",
                "keywords": ["keyword_1", "keyword_2"],
                "action": "add",
            },
            authentication=self.get_operator_token(product.application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": ["Missing mandatory key"],
                        "field": "language",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_with_no_keywords(self):
        product = self.create_products(1)[0]
        localized_product = self.create_localized_products(products=[product])[0]

        resp = self.api_client.post(
            "/v1/product/batch/{}/".format(self.action_name),
            data={
                "dry_run": False,
                "filters": {"id__in": [product.id]},
                "filter_type": "api_filters",
                "action": "add",
                "language": localized_product.language,
            },
            authentication=self.get_operator_token(product.application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": ["Missing mandatory key"],
                        "field": "keywords",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_with_invalid_language(self):
        product = self.create_products(1)[0]
        localized_product = self.create_localized_products(products=[product])[0]

        resp = self.api_client.post(
            "/v1/product/batch/{}/".format(self.action_name),
            data={
                "dry_run": False,
                "filters": {"id__in": [product.id]},
                "filter_type": "api_filters",
                "keywords": "keyword_1",
                "action": "add",
                "language": localized_product.language,
            },
            authentication=self.get_operator_token(product.application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": ["Keywords should be a list of string"],
                        "field": "keywords",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    @patch(
        "apps.products.algolia_items_retriever.AlgoliaItemsRetriever."
        "get_algolia_index"
    )
    @patch("apps.products.algolia_items_retriever.AlgoliaItemsRetriever._do_search")
    def test_dry_run_false_algolia_filters(self, mocked_search, mocked_get_index):
        product = self.create_products(1)[0]
        localized_product = self.create_localized_products(products=[product])[0]
        mocked_search.return_value = [{"id": product.id}]

        resp = self.do_algolia_filters_request(
            False,
            product,
            keywords=["keyword_1", "keyword_2"],
            language=localized_product.language,
            action="add",
        )
        self.assertEqual(resp.status_code, 202)
        self.assertEqual(resp.content, b"")
        self.assertIn("/v1/async-action/", resp["Location"])

        action = self.get_action()
        data = action.get_as_dict()

        self.assertIsNotNone(data.pop("created_on"))
        self.assertDictEqual(
            data,
            {
                "status": "in_queue",
                "ended_on": None,
                "action_name": self.action_cls.action_name,
                "ignored_products": 0,
                "errored_products": 0,
                "modified_products": 0,
                "value": ["keyword_1", "keyword_2"],
                "language": localized_product.language,
                "action": "add",
                "lock_action": True,
                "id": action.id,
                "result": {},
                "total_products": 1,
            },
        )

    @patch(
        "apps.products.algolia_items_retriever.AlgoliaItemsRetriever."
        "get_algolia_index"
    )
    @patch("apps.products.algolia_items_retriever.AlgoliaItemsRetriever._do_search")
    def test_dry_run_false_algolia_filters_diffent_number_found(
        self, mocked_search, mocked_get_index
    ):
        product = self.create_products(1)[0]
        localized_product = self.create_localized_products(products=[product])[0]
        mocked_search.return_value = []

        resp = self.do_algolia_filters_request(
            False,
            product,
            keywords=["keyword_1", "keyword_2"],
            language=localized_product.language,
            action="add",
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": ["Expected count 1 does not match computed 0"],
                        "field": "__all__",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())
