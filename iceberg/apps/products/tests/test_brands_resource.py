from apps.cart.tests.test_cart_setup_mixin import CartTestSetupMixin
from apps.ice_applications.tests.application_settings_tests_setup_mixin import (
    ApplicationSettingsTestsSetupMixin,
)
from apps.products.models import Brand
from apps.testing.factories import (
    ApplicationFactory,
    BrandFactory,
    MerchantFactory,
    PerItemCountAssignmentFactory,
    UserFactory,
)
from apps.user.tests.user_tests_setup_mixin import UserTestsSetupMixin
from django.db import IntegrityError
from ims.tests import BaseResourceTestCase
from ims.tests.jwt_tokens import PIM_TOKENS


class TestBrandResource(
    CartTestSetupMixin, ApplicationSettingsTestsSetupMixin, BaseResourceTestCase
):
    def test_anonymous_get_list_return_no_result(self):
        super(TestBrandResource, self).setUp()
        ApplicationSettingsTestsSetupMixin.set_up(self)
        CartTestSetupMixin.set_up(self)
        self.izberg_staff_user = self.create_izberg_user("izstaff", staff=True)
        self.create_access(self.application, self.izberg_staff_user, True)
        self.product = self.create_active_product_with_brand(self.brand)

        response = self.api_client.get("/v1/brand/")

        self.assertValidJSONResponse(response)
        data = self.deserialize(response)
        self.assertEqual(data["meta"]["total_count"], 0)

    def test_anonymous_get_detail_return_unauthorized(self):
        super(TestBrandResource, self).setUp()
        ApplicationSettingsTestsSetupMixin.set_up(self)
        CartTestSetupMixin.set_up(self)
        self.izberg_staff_user = self.create_izberg_user("izstaff", staff=True)
        self.create_access(self.application, self.izberg_staff_user, True)
        self.product = self.create_active_product_with_brand(self.brand)

        response = self.api_client.get("/v1/brand/%d/" % self.brand.id)

        self.assertHttpUnauthorized(response)

    def test_anonymous_update_brand(self):
        super(TestBrandResource, self).setUp()
        ApplicationSettingsTestsSetupMixin.set_up(self)
        CartTestSetupMixin.set_up(self)
        self.izberg_staff_user = self.create_izberg_user("izstaff", staff=True)
        self.create_access(self.application, self.izberg_staff_user, True)
        self.product = self.create_active_product_with_brand(self.brand)

        response = self.api_client.put(
            "/v1/brand/%d/" % self.brand.id, data={"name": "Mÿ braaand"}
        )

        self.assertHttpUnauthorized(response)

    def test_anonymous_delete_brand(self):
        super(TestBrandResource, self).setUp()
        ApplicationSettingsTestsSetupMixin.set_up(self)
        CartTestSetupMixin.set_up(self)
        self.izberg_staff_user = self.create_izberg_user("izstaff", staff=True)
        self.create_access(self.application, self.izberg_staff_user, True)
        self.product = self.create_active_product_with_brand(self.brand)

        response = self.api_client.delete("/v1/brand/%d/" % self.brand.id)

        self.assertHttpUnauthorized(response)

    def test_app_anonymous_can_get_app_brand(self):
        super(TestBrandResource, self).setUp()
        ApplicationSettingsTestsSetupMixin.set_up(self)
        CartTestSetupMixin.set_up(self)
        self.izberg_staff_user = self.create_izberg_user("izstaff", staff=True)
        self.create_access(self.application, self.izberg_staff_user, True)
        self.product = self.create_active_product_with_brand(self.brand)

        brand = BrandFactory()
        response = self.api_client.get(
            "/v1/brand/%d/" % brand.id,
            authentication=self.get_app_anonymous_token(brand.application),
        )

        self.assertValidJSONResponse(response)

    def test_app_anonymous_can_not_get_another_app_brand(self):
        super(TestBrandResource, self).setUp()
        ApplicationSettingsTestsSetupMixin.set_up(self)
        CartTestSetupMixin.set_up(self)
        self.izberg_staff_user = self.create_izberg_user("izstaff", staff=True)
        self.create_access(self.application, self.izberg_staff_user, True)
        self.product = self.create_active_product_with_brand(self.brand)

        brand = BrandFactory()
        response = self.api_client.get(
            "/v1/brand/%d/" % brand.id,
            authentication=self.get_app_anonymous_token(self.application),
        )

        self.assertHttpUnauthorized(response)

    def test_app_anonymous_can_list_app(self):
        super(TestBrandResource, self).setUp()
        ApplicationSettingsTestsSetupMixin.set_up(self)
        CartTestSetupMixin.set_up(self)
        self.izberg_staff_user = self.create_izberg_user("izstaff", staff=True)
        self.create_access(self.application, self.izberg_staff_user, True)
        self.product = self.create_active_product_with_brand(self.brand)

        response = self.api_client.get(
            "/v1/brand/", authentication=self.get_app_anonymous_token(self.application)
        )

        self.assertValidJSONResponse(response)
        data = self.deserialize(response)
        self.assertEqual(data["meta"]["total_count"], 1)

    def test_operator_update_app_brand(self):
        super(TestBrandResource, self).setUp()
        ApplicationSettingsTestsSetupMixin.set_up(self)
        CartTestSetupMixin.set_up(self)
        self.izberg_staff_user = self.create_izberg_user("izstaff", staff=True)
        self.create_access(self.application, self.izberg_staff_user, True)
        self.product = self.create_active_product_with_brand(self.brand)

        response = self.api_client.put(
            "/v1/brand/%d/" % self.brand.id,
            data={"name": "Mÿ braaand"},
            authentication=self.get_token(self.app_admin_user),
        )

        self.assertHttpAccepted(response)

    def test_operator_delete_brand(self):
        super(TestBrandResource, self).setUp()
        ApplicationSettingsTestsSetupMixin.set_up(self)
        CartTestSetupMixin.set_up(self)
        self.izberg_staff_user = self.create_izberg_user("izstaff", staff=True)
        self.create_access(self.application, self.izberg_staff_user, True)
        self.product = self.create_active_product_with_brand(self.brand)

        response = self.api_client.delete(
            "/v1/brand/%d/" % self.brand.id,
            authentication=self.get_token(self.app_admin_user),
        )

        self.assertHttpNoContent(response)

    def test_izberg_staff_delete_brand_with_app_token(self):
        super(TestBrandResource, self).setUp()
        ApplicationSettingsTestsSetupMixin.set_up(self)
        CartTestSetupMixin.set_up(self)
        self.izberg_staff_user = self.create_izberg_user("izstaff", staff=True)
        self.create_access(self.application, self.izberg_staff_user, True)
        self.product = self.create_active_product_with_brand(self.brand)

        response = self.api_client.delete(
            "/v1/brand/%d/" % self.brand.id,
            authentication=self.get_token(self.izberg_staff_user),
        )

        self.assertHttpNoContent(response)

    def test_izberg_staff_delete_brand_with_user_token(self):
        super(TestBrandResource, self).setUp()
        ApplicationSettingsTestsSetupMixin.set_up(self)
        CartTestSetupMixin.set_up(self)
        self.izberg_staff_user = self.create_izberg_user("izstaff", staff=True)
        self.create_access(self.application, self.izberg_staff_user, True)
        self.product = self.create_active_product_with_brand(self.brand)

        response = self.api_client.delete(
            "/v1/brand/%d/" % self.brand.id,
            authentication=self.get_token(self.izberg_staff_user, app_token=False),
        )

        self.assertHttpNoContent(response)

    def test_delete_brand_used_in_shipping_assignment(self):
        assignment = PerItemCountAssignmentFactory()

        resp = self.api_client.delete(
            "/v1/brand/{}/".format(assignment.brand.id),
            authentication=self.get_operator_token(assignment.application),
        )

        self.assertHttpForbidden(resp)
        self.assertEqual(
            self.deserialize(resp),
            {
                "error_description": (
                    "Cannot delete a brand with active shipping assignments"
                ),
                "error_code": "RESOURCE_PROTECTED",
                "error_context": {
                    "resource_uri": assignment.brand.get_resource_uri(),
                    "id": assignment.brand_id,
                },
            },
        )

    def test_can_delete_brand_used_in_shipping_assignment(self):
        assignment = PerItemCountAssignmentFactory()

        resp = self.api_client.get(
            "/v1/brand/{}/can-delete/".format(assignment.brand_id),
            authentication=self.get_operator_token(assignment.application),
        )

        self.assertHttpOK(resp)
        self.assertEqual(self.deserialize(resp), {"is_available": False})

    def test_can_delete_brand_not_used_in_shipping_assignment(self):
        brand = BrandFactory()

        resp = self.api_client.get(
            "/v1/brand/{}/can-delete/".format(brand.id),
            authentication=self.get_operator_token(brand.application),
        )

        self.assertHttpOK(resp)
        self.assertEqual(self.deserialize(resp), {"is_available": True})


class TestOperatorMerchantBrandResource(UserTestsSetupMixin, BaseResourceTestCase):
    def test_operator_create_brand(self):
        application = ApplicationFactory()
        user = UserFactory()
        operator_token = self.get_operator_token(application, user=user)

        response = self.api_client.post(
            "/v1/brand/",
            authentication=operator_token,
            data={
                "name": "name",
                "description": "description",
                "slug": "slug",
                "keywords": "test,brand",
                "application": "/v1/application/{}/".format(application.id),
            },
        )

        self.assertHttpCreated(response)
        brand = Brand.objects.get()
        self.assertEqual(brand.user, user)

    def test_merchant_create_brand(self):
        merchant = MerchantFactory()
        merchant_token = self.get_merchant_token(merchant=merchant)

        response = self.api_client.post(
            "/v1/brand/",
            authentication=merchant_token,
            data={
                "name": "name",
                "description": "description",
                "slug": "slug",
                "keywords": "test,brand",
                "merchant": "/v1/merchant/{}/".format(merchant.id),
            },
        )
        self.assertHttpCreated(response)
        brand = Brand.objects.get()
        self.assertEqual(brand.merchant, merchant)
        self.assertEqual(brand.application, merchant.application)

    def test_operator_get_list_brand(self):
        application = ApplicationFactory()
        operator_token = self.get_operator_token(application)
        BrandFactory(application=application)

        response = self.api_client.get(
            "/v1/brand/?application={}".format(application.id),
            authentication=operator_token,
        )
        self.assertHttpOK(response)
        data = self.deserialize(response)
        self.assertEqual(data.get("meta", {}).get("total_count"), 1)

    def test_merchant_get_list_brand(self):
        merchant = MerchantFactory()
        application = merchant.application
        merchant_token = self.get_merchant_token(merchant=merchant)
        BrandFactory(application=application)

        response = self.api_client.get(
            "/v1/brand/?application={}".format(application.id),
            authentication=merchant_token,
        )
        self.assertHttpOK(response)
        data = self.deserialize(response)
        self.assertEqual(data.get("meta", {}).get("total_count"), 1)

    def test_operator_get_list_brand_with_merchant_token(self):
        merchant = MerchantFactory()
        application = merchant.application
        operator_to_merchant_token = self.get_merchant_token(
            merchant=merchant, user=application.contact_user
        )
        BrandFactory(application=application)

        response = self.api_client.get(
            "/v1/brand/?application={}".format(application.id),
            authentication=operator_to_merchant_token,
        )
        self.assertHttpOK(response)
        data = self.deserialize(response)
        self.assertEqual(data.get("meta", {}).get("total_count"), 1)

    def test_create_band_with_identity_token(self):
        application = ApplicationFactory.create(id=1, namespace="application_1")
        bearer = "Bearer {}".format(PIM_TOKENS["APPLICATION_1_ADMIN"]["token"])
        resp = self.api_client.post(
            "/v1/brand/",
            data={
                "name": "My Brªnd",
            },
            authentication=bearer,
        )
        self.assertHttpCreated(resp)
        brand = Brand.objects.get()
        self.assertEqual(brand.application, application)


class TestStaffBrandResource(BaseResourceTestCase):
    def test_izberg_staff_can_create_app_brand(self):
        staff_token = self.get_staff_token()
        application = ApplicationFactory()

        response = self.api_client.post(
            "/v1/brand/",
            authentication=staff_token,
            data={
                "name": "name",
                "description": "description",
                "slug": "slug",
                "keywords": "test,brand",
                "application": application.get_resource_uri(),
            },
        )
        self.assertHttpCreated(response)
        brand = Brand.objects.get()
        self.assertEqual(brand.application, application)


class TestBrandNoDuplicateCreation(BaseResourceTestCase):
    def test_create_brand_twice_raises(self):
        application = ApplicationFactory.create(id=1, namespace="application_1")
        bearer = "Bearer {}".format(PIM_TOKENS["APPLICATION_1_ADMIN"]["token"])
        BrandFactory(name="dup", application=application)

        resp = self.api_client.post(
            "/v1/brand/",
            data={
                "name": "dup",
            },
            authentication=bearer,
        )
        self.assertHttpBadRequest(resp)

        data = self.deserialize(resp)
        self.assertEqual(
            data,
            {
                "errors": [
                    {
                        "field": "slug",
                        "msg": ["Brand 'dup' with slug dup already exists"],
                    }
                ]
            },
        )

    def test_create_brand_twice_concurrently_raises(self):
        application = ApplicationFactory.create(id=1, namespace="application_1")
        BrandFactory(name="a", application=application)
        BrandFactory(name="b", application=application)

        with self.assertRaises(IntegrityError):
            # simulate 2 tasks trying to create a brand with the same slug
            Brand.objects.update(slug="same_slug_for_all_brand")
