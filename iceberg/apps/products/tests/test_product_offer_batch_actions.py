# -*- coding: utf-8 -*-

from apps.ice_applications.app_conf_settings import AllowTrashStatus
from apps.products.algolia_items_retriever import AlgoliaItemsRetriever
from apps.tasking.models import (
    ProductOfferBatchActivateAction,
    ProductOfferBatchApplyDefaultTaxSettingsAction,
    ProductOfferBatchBanAction,
    ProductOfferBatchDeactivateAction,
    ProductOfferBatchRemoveAllManuallyManagedFieldsAction,
    ProductOfferBatchSubmitAction,
    ProductOfferBatchTrashAction,
    ProductOfferBatchUnBanAction,
    ProductOfferBatchUntrashAction,
    ProductOfferBatchUpdateStockFieldAction,
)
from apps.tasking.redis_client import get_client
from apps.testing.factories import (
    ApplicationFactory,
    MerchantFactory,
    ProductOfferFactory,
    TaxRateAssignmentFactory,
)
from django.test.utils import override_settings
from ims.tests import BaseResourceTestCase
from ims.tests.jwt_tokens import PIM_TOKENS
from mock import patch
from reference.status import (
    PRODUCT_OFFER_STATUS_ACTIVE,
    PRODUCT_OFFER_STATUS_BANNED,
    PRODUCT_OFFER_STATUS_INACTIVE,
    PRODUCT_OFFER_STATUS_TRASHED,
)


class BatchActionMixin:
    def create_offers(self, count, **kwargs):
        first_offer = ProductOfferFactory(**kwargs)
        if count > 1:
            offers = [
                ProductOfferFactory.create_for_merchant(first_offer.merchant, **kwargs)
                for _ in range(count - 1)
            ]
            offers = [first_offer] + offers
        else:
            offers = [first_offer]
        return offers

    def get_action(self):
        client = get_client()
        _, ids = client.scan(0, "*")
        if ids:
            full_id = ids[0].split(b":")[2]
            app_id, merchant_id, action_id = full_id.split(b"-", 2)
            action = self.action_cls(application_id=app_id, merchant_id=merchant_id)
            action.id = full_id
            return action
        return None

    def do_api_filters_request(
        self, dry_run, offers, token=None, application=None, merchant=None
    ):
        data = {
            "filters": {"id__in": [_offer.id for _offer in offers]},
            "filter_type": "api_filters",
        }
        if dry_run is not None:
            data["dry_run"] = dry_run
        if not application and offers:
            application = offers[0].application

        path = "/v1/productoffer/batch/{}/".format(self.action_name)
        if not token:
            if merchant:
                token = self.get_merchant_token(merchant=merchant)
                path += "?merchant_id={}".format(merchant.id)
            else:
                token = self.get_operator_token(application)
        return self.api_client.post(path, data=data, authentication=token)

    def do_algolia_filters_request(self, dry_run, offer, application=None):
        if not application:
            application = offer.application
        data = {
            "filters": {
                "channel_id": application.backoffice_channel.id,
                "requests": [
                    {
                        "index": "my:algolia:index",
                        "query": "",
                        "facets": ["merchant.group_keys", "price_with_vat", "status"],
                        "disjunctiveFacets": [
                            "merchant.name",
                            "product.brand.name",
                            "product.gender",
                            "product.keywords",
                            "availability",
                            "product.application_categories_dict.name",
                            "discounted",
                            "applied_discounts.operation_type",
                        ],
                        "hierarchicalFacets": [],
                        "facetsRefinements": {"status": ["active"]},
                        "facetsExcludes": {},
                        "disjunctiveFacetsRefinements": {},
                        "numericRefinements": {},
                        "tagRefinements": [],
                        "hierarchicalFacetsRefinements": {},
                        "typoTolerance": "false",
                        "length": 1,
                        "managedParameters": [
                            "index",
                            "facets",
                            "disjunctiveFacets",
                            "facetsRefinements",
                            "facetsExcludes",
                            "disjunctiveFacetsRefinements",
                            "numericRefinements",
                            "tagRefinements",
                            "hierarchicalFacets",
                            "hierarchicalFacetsRefinements",
                        ],
                        "params": "query=",
                        "indexName": "my:algolia:index:name",
                    }
                ],
            },
            "filter_type": "algolia",
        }

        if dry_run is not None:
            data["dry_run"] = dry_run
        return self.api_client.post(
            "/v1/productoffer/batch/{}/".format(self.action_name),
            data=data,
            authentication=self.get_operator_token(application),
        )


class BatchActivateTestCase(BaseResourceTestCase, BatchActionMixin):
    action_name = "activate"
    action_cls = ProductOfferBatchActivateAction

    def create_offers(self, count, **kwargs):
        return super(BatchActivateTestCase, self).create_offers(
            count, status=PRODUCT_OFFER_STATUS_INACTIVE, **kwargs
        )

    def test_dry_run_false_api_filters(self):
        offer = self.create_offers(2)[0]

        resp = self.do_api_filters_request(False, [offer])

        self.assertEqual(resp.status_code, 202)
        self.assertEqual(resp.content, b"")
        self.assertIn("/v1/async-action/", resp["Location"])

        action = self.get_action()
        data = action.get_as_dict()
        self.assertIsNotNone(data.pop("created_on"))
        self.assertEqual(
            data,
            {
                "status": "in_queue",
                "ended_on": None,
                "total_offers": 1,
                "ignored_offers": 0,
                "errored_offers": 0,
                "action_name": self.action_cls.action_name,
                "modified_offers": 0,
                "lock_action": True,
                "id": action.id,
                "result": {},
            },
        )

    def test_dry_run_false_api_filters_as_merchant_without_moderation(self):
        offer = self.create_offers(2)[0]

        resp = self.do_api_filters_request(False, [offer], merchant=offer.merchant)

        self.assertEqual(resp.status_code, 202)
        self.assertEqual(resp.content, b"")
        self.assertIn("/v1/async-action/", resp["Location"])

        action = self.get_action()
        data = action.get_as_dict()
        self.assertIsNotNone(data.pop("created_on"))
        self.assertEqual(
            data,
            {
                "status": "in_queue",
                "ended_on": None,
                "total_offers": 1,
                "ignored_offers": 0,
                "errored_offers": 0,
                "action_name": self.action_cls.action_name,
                "modified_offers": 0,
                "lock_action": True,
                "id": action.id,
                "result": {},
            },
        )

    def test_dry_run_false_api_filters_as_merchant_with_moderation(self):
        offer = self.create_offers(2)[0]
        offer.application.set_setting("product_offer_moderation_enabled", True)
        resp = self.do_api_filters_request(False, [offer], merchant=offer.merchant)

        self.assertEqual(resp.status_code, 202)
        self.assertEqual(resp.content, b"")
        self.assertIn("/v1/async-action/", resp["Location"])

        action = self.get_action()
        data = action.get_as_dict()
        self.assertIsNotNone(data.pop("created_on"))
        self.assertEqual(
            data,
            {
                "status": "in_queue",
                "ended_on": None,
                "total_offers": 1,
                "ignored_offers": 0,
                "errored_offers": 0,
                "action_name": ProductOfferBatchSubmitAction.action_name,
                "modified_offers": 0,
                "lock_action": True,
                "id": action.id,
                "result": {},
            },
        )

    @patch(
        "apps.products.algolia_items_retriever.AlgoliaItemsRetriever."
        "get_algolia_index"
    )
    @patch("apps.products.algolia_items_retriever.AlgoliaItemsRetriever._do_search")
    def test_dry_run_false_algolia_filters(self, mocked_search, mocked_get_index):
        offer = self.create_offers(1)[0]
        mocked_search.return_value = [{"id": offer.id}]

        resp = self.do_algolia_filters_request(False, offer)
        self.assertEqual(resp.status_code, 202)
        self.assertEqual(resp.content, b"")
        self.assertIn("/v1/async-action/", resp["Location"])

        action = self.get_action()
        data = action.get_as_dict()
        self.assertIsNotNone(data.pop("created_on"))
        self.assertEqual(
            data,
            {
                "status": "in_queue",
                "ended_on": None,
                "total_offers": 1,
                "action_name": self.action_cls.action_name,
                "ignored_offers": 0,
                "errored_offers": 0,
                "modified_offers": 0,
                "lock_action": True,
                "id": action.id,
                "result": {},
            },
        )

    @patch(
        "apps.products.algolia_items_retriever.AlgoliaItemsRetriever."
        "get_algolia_index"
    )
    @patch("apps.products.algolia_items_retriever.AlgoliaItemsRetriever._do_search")
    def test_dry_run_false_algolia_filters_diffent_number_found(
        self, mocked_search, mocked_get_index
    ):
        offer = self.create_offers(1)[0]
        mocked_search.return_value = []

        resp = self.do_algolia_filters_request(False, offer)
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": ["Expected count 1 does not match computed 0"],
                        "field": "__all__",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_dry_run_true(self):
        offer = self.create_offers(1)[0]

        resp = self.do_api_filters_request(True, [offer])
        self.assertHttpOK(resp)
        self.assertEqual(
            self.deserialize(resp), {"product_offer_count": 1, "dry_run": True}
        )
        self.assertIsNone(self.get_action())

    def test_dry_run_not_set_defaults_true(self):
        offer = self.create_offers(1)[0]

        resp = self.do_api_filters_request(None, [offer])
        self.assertHttpOK(resp)
        self.assertEqual(
            self.deserialize(resp), {"product_offer_count": 1, "dry_run": True}
        )
        self.assertIsNone(self.get_action())

    def test_as_merchant(self):
        offer = self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/?merchant_id={}".format(
                self.action_name, offer.merchant_id
            ),
            data={
                "dry_run": True,
                "filters": {"id__in": [offer.id]},
                "filter_type": "api_filters",
            },
            authentication=self.get_merchant_token(merchant=offer.merchant),
        )
        self.assertHttpOK(resp)
        self.assertEqual(
            self.deserialize(resp), {"product_offer_count": 1, "dry_run": True}
        )
        self.assertIsNone(self.get_action())

    def test_as_izb_staff(self):
        offer = self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/?merchant_id={}".format(
                self.action_name, offer.merchant_id
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": [offer.id]},
                "filter_type": "api_filters",
            },
            authentication=self.get_staff_token(),
        )
        self.assertEqual(resp.status_code, 202)

    def test_unfiltered_request_as_izberg_staff(self):
        self.create_offers(1)

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(self.action_name),
            data={"dry_run": False, "filters": {}, "filter_type": "api_filters"},
            authentication=self.get_staff_token(),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertIsNone(self.get_action())
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Missing mandatory parameter"], "field": "merchant"}]},
        )

    def test_unfiltered_request_as_izberg_staff_with_merchant_ctx(self):
        offer = self.create_offers(1)[0]
        self.create_offers(1)

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/?merchant_id={}".format(
                self.action_name, offer.merchant_id
            ),
            data={"dry_run": False, "filters": {}, "filter_type": "api_filters"},
            authentication=self.get_staff_token(),
        )
        self.assertEqual(resp.status_code, 202)
        action = self.get_action()
        self.assertEqual(action.get_meta("total_offers"), 1)
        self.assertEqual(int(action.pop_id()), offer.id)

    def test_as_customer(self):
        offer = self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": [offer.id]},
                "filter_type": "api_filters",
            },
            authentication=self.get_app_user_token(application=offer.application),
        )
        self.assertEqual(resp.status_code, 401)

    def test_with_empty_queryset(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": []},
                "filter_type": "api_filters",
            },
            authentication=self.get_operator_token(application=application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": [
                            "Trying to batch-update 0 item. Please check your "
                            "filters."
                        ],
                        "field": "__all__",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_invalid_api_filters(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"invalid": "value"},
                "filter_type": "api_filters",
            },
            authentication=self.get_operator_token(application=application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": ["The 'invalid' field does not allow filtering."],
                        "field": "__all__",
                    }
                ]
            },
        )

    def test_empty_queryset(self):
        application = ApplicationFactory()

        resp = self.do_api_filters_request(False, [], application=application)
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": [
                            "Trying to batch-update 0 item. Please check your "
                            "filters."
                        ],
                        "field": "__all__",
                    }
                ]
            },
        )

    def test_not_owner_of_ids(self):
        offer = self.create_offers(1)[0]
        application = ApplicationFactory()

        resp = self.do_api_filters_request(False, [offer], application=application)
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": [
                            "Trying to batch-update 0 item. Please check your "
                            "filters."
                        ],
                        "field": "__all__",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_with_empty_body(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(self.action_name),
            data={},
            authentication=self.get_operator_token(application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Missing mandatory key"], "field": "filter_type"}]},
        )
        self.assertIsNone(self.get_action())

    def test_invalid_api_filter_value(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(self.action_name),
            data={
                "dry_run": False,
                "filters": {"id__in": ["blablabla"]},
                "filter_type": "api_filters",
            },
            authentication=self.get_operator_token(application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Invalid filters provided"], "field": "filters"}]},
        )
        self.assertIsNone(self.get_action())

    def test_dry_run_true_as_pim_write(self):
        merchant2 = MerchantFactory(id=2, application__id=1)
        merchant3 = MerchantFactory(
            id=3,
            application=merchant2.application,
            default_currency=merchant2.default_currency,
        )
        offer2 = self.create_offers(
            1,
            merchant=merchant2,
            application=merchant2.application,
            currency=merchant2.default_currency,
        )[0]
        # other offer
        offer3 = self.create_offers(
            1,
            merchant=merchant3,
            application=merchant3.application,
            currency=merchant3.default_currency,
        )[0]

        resp = self.do_api_filters_request(
            dry_run=True,
            offers=[offer2, offer3],
            token="Bearer {}".format(PIM_TOKENS["APPLICATION_1_WRITE"]["token"]),
        )
        self.assertHttpOK(resp)
        self.assertEqual(
            self.deserialize(resp), {"product_offer_count": 2, "dry_run": True}
        )
        self.assertIsNone(self.get_action())

    def test_dry_run_true_as_merchant_pim_write_only_applies_on_its_offer(self):
        merchant2 = MerchantFactory(id=2, application__id=1)
        merchant3 = MerchantFactory(
            id=3,
            application=merchant2.application,
            default_currency=merchant2.default_currency,
        )
        offer2 = self.create_offers(
            1,
            merchant=merchant2,
            application=merchant2.application,
            currency=merchant2.default_currency,
        )[0]
        # other offer
        offer3 = self.create_offers(
            1,
            merchant=merchant3,
            application=merchant3.application,
            currency=merchant3.default_currency,
        )[0]

        resp = self.do_api_filters_request(
            dry_run=True,
            offers=[offer2, offer3],
            token="Bearer {}".format(
                PIM_TOKENS["APPLICATION_1_MERCHANT_2_WRITE"]["token"]
            ),
        )
        self.assertHttpOK(resp)
        self.assertEqual(
            self.deserialize(resp), {"product_offer_count": 1, "dry_run": True}
        )
        self.assertIsNone(self.get_action())


class BatchDeactivateTestCase(BaseResourceTestCase, BatchActionMixin):
    action_name = "deactivate"
    action_cls = ProductOfferBatchDeactivateAction

    def create_offers(self, count, **kwargs):
        return super(BatchDeactivateTestCase, self).create_offers(
            count, status=PRODUCT_OFFER_STATUS_ACTIVE, **kwargs
        )

    def test_dry_run_false_api_filters(self):
        offer = self.create_offers(2)[0]

        resp = self.do_api_filters_request(False, [offer])

        self.assertEqual(resp.status_code, 202)
        self.assertEqual(resp.content, b"")
        self.assertIn("/v1/async-action/", resp["Location"])

        action = self.get_action()
        data = action.get_as_dict()
        self.assertIsNotNone(data.pop("created_on"))
        self.assertEqual(
            data,
            {
                "status": "in_queue",
                "ended_on": None,
                "total_offers": 1,
                "ignored_offers": 0,
                "errored_offers": 0,
                "modified_offers": 0,
                "lock_action": True,
                "action_name": self.action_cls.action_name,
                "id": action.id,
                "result": {},
            },
        )

    @patch(
        "apps.products.algolia_items_retriever.AlgoliaItemsRetriever."
        "get_algolia_index"
    )
    @patch("apps.products.algolia_items_retriever.AlgoliaItemsRetriever._do_search")
    def test_dry_run_false_algolia_filters(self, mocked_search, mocked_get_index):
        offer = self.create_offers(1)[0]
        mocked_search.return_value = [{"id": offer.id}]

        resp = self.do_algolia_filters_request(False, offer)
        self.assertEqual(resp.status_code, 202)
        self.assertEqual(resp.content, b"")
        self.assertIn("/v1/async-action/", resp["Location"])

        action = self.get_action()
        data = action.get_as_dict()
        self.assertIsNotNone(data.pop("created_on"))
        self.assertEqual(
            data,
            {
                "status": "in_queue",
                "ended_on": None,
                "total_offers": 1,
                "ignored_offers": 0,
                "errored_offers": 0,
                "action_name": self.action_cls.action_name,
                "modified_offers": 0,
                "lock_action": True,
                "id": action.id,
                "result": {},
            },
        )

    @patch(
        "apps.products.algolia_items_retriever.AlgoliaItemsRetriever."
        "get_algolia_index"
    )
    @patch("apps.products.algolia_items_retriever.AlgoliaItemsRetriever._do_search")
    def test_dry_run_false_algolia_filters_diffent_number_found(
        self, mocked_search, mocked_get_index
    ):
        offer = self.create_offers(1)[0]
        mocked_search.return_value = []

        resp = self.do_algolia_filters_request(False, offer)
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": ["Expected count 1 does not match computed 0"],
                        "field": "__all__",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_dry_run_true(self):
        offer = self.create_offers(1)[0]

        resp = self.do_api_filters_request(True, [offer])
        self.assertHttpOK(resp)
        self.assertEqual(
            self.deserialize(resp), {"product_offer_count": 1, "dry_run": True}
        )
        self.assertIsNone(self.get_action())

    def test_dry_run_not_set_defaults_true(self):
        offer = self.create_offers(1)[0]

        resp = self.do_api_filters_request(None, [offer])
        self.assertHttpOK(resp)
        self.assertEqual(
            self.deserialize(resp), {"product_offer_count": 1, "dry_run": True}
        )
        self.assertIsNone(self.get_action())

    def test_as_merchant(self):
        offer = self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/?merchant_id={}".format(
                self.action_name, offer.merchant_id
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": [offer.id]},
                "filter_type": "api_filters",
            },
            authentication=self.get_merchant_token(merchant=offer.merchant),
        )
        self.assertEqual(resp.status_code, 202)

    def test_as_merchant_without_context(self):
        offer = self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(self.action_name),
            data={
                "dry_run": False,
                "filters": {"id__in": [offer.id]},
                "filter_type": "api_filters",
            },
            authentication=self.get_merchant_token(merchant=offer.merchant),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Missing mandatory parameter"], "field": "merchant"}]},
        )

    def test_as_izb_staff(self):
        offer = self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/?merchant_id={}".format(
                self.action_name, offer.merchant_id
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": [offer.id]},
                "filter_type": "api_filters",
            },
            authentication=self.get_staff_token(),
        )
        self.assertEqual(resp.status_code, 202)

    def test_as_customer(self):
        offer = self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": [offer.id]},
                "filter_type": "api_filters",
            },
            authentication=self.get_app_user_token(application=offer.application),
        )
        self.assertEqual(resp.status_code, 401)

    def test_invalid_api_filters(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"invalid": "value"},
                "filter_type": "api_filters",
            },
            authentication=self.get_operator_token(application=application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": ["The 'invalid' field does not allow filtering."],
                        "field": "__all__",
                    }
                ]
            },
        )

    def test_with_empty_queryset(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": []},
                "filter_type": "api_filters",
            },
            authentication=self.get_operator_token(application=application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": [
                            "Trying to batch-update 0 item. Please check your "
                            "filters."
                        ],
                        "field": "__all__",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_not_owner_of_ids(self):
        offer = self.create_offers(1)[0]
        application = ApplicationFactory()

        resp = self.do_api_filters_request(
            dry_run=False, offers=[offer], application=application
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": [
                            "Trying to batch-update 0 item. Please check your "
                            "filters."
                        ],
                        "field": "__all__",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_with_empty_body(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(self.action_name),
            data={},
            authentication=self.get_operator_token(application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Missing mandatory key"], "field": "filter_type"}]},
        )
        self.assertIsNone(self.get_action())

    def test_invalid_api_filter_value(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(self.action_name),
            data={
                "dry_run": False,
                "filters": {"id__in": ["blablabla"]},
                "filter_type": "api_filters",
            },
            authentication=self.get_operator_token(application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Invalid filters provided"], "field": "filters"}]},
        )
        self.assertIsNone(self.get_action())

    def test_unfiltered_request_as_izberg_staff(self):
        self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(self.action_name),
            data={"dry_run": False, "filters": {}, "filter_type": "api_filters"},
            authentication=self.get_staff_token(),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertIsNone(self.get_action())
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Missing mandatory parameter"], "field": "merchant"}]},
        )

    def test_unfiltered_request_as_izberg_staff_with_merchant_ctx(self):
        offer = self.create_offers(1)[0]
        self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/?merchant_id={}".format(
                self.action_name, offer.merchant_id
            ),
            data={"dry_run": False, "filters": {}, "filter_type": "api_filters"},
            authentication=self.get_staff_token(),
        )
        self.assertEqual(resp.status_code, 202)
        action = self.get_action()
        self.assertEqual(action.get_meta("total_offers"), 1)
        self.assertEqual(int(action.pop_id()), offer.id)


class BatchBanTestCase(BaseResourceTestCase, BatchActionMixin):
    action_name = "ban"
    action_cls = ProductOfferBatchBanAction

    def create_offers(self, count, **kwargs):
        return super(BatchBanTestCase, self).create_offers(
            count, status=PRODUCT_OFFER_STATUS_ACTIVE, **kwargs
        )

    def test_dry_run_false_api_filters(self):
        offer = self.create_offers(2)[0]

        resp = self.do_api_filters_request(False, [offer])

        self.assertEqual(resp.status_code, 202)
        self.assertEqual(resp.content, b"")
        self.assertIn("/v1/async-action/", resp["Location"])

        action = self.get_action()
        data = action.get_as_dict()
        self.assertIsNotNone(data.pop("created_on"))
        self.assertEqual(
            data,
            {
                "status": "in_queue",
                "ended_on": None,
                "total_offers": 1,
                "action_name": self.action_cls.action_name,
                "modified_offers": 0,
                "ignored_offers": 0,
                "errored_offers": 0,
                "lock_action": True,
                "id": action.id,
                "result": {},
            },
        )

    @patch(
        "apps.products.algolia_items_retriever.AlgoliaItemsRetriever."
        "get_algolia_index"
    )
    @patch("apps.products.algolia_items_retriever.AlgoliaItemsRetriever._do_search")
    def test_dry_run_false_algolia_filters(self, mocked_search, mocked_get_index):
        offer = self.create_offers(1)[0]
        mocked_search.return_value = [{"id": offer.id}]

        resp = self.do_algolia_filters_request(False, offer)
        self.assertEqual(resp.status_code, 202)
        self.assertEqual(resp.content, b"")
        self.assertIn("/v1/async-action/", resp["Location"])

        action = self.get_action()
        data = action.get_as_dict()
        self.assertIsNotNone(data.pop("created_on"))
        self.assertEqual(
            data,
            {
                "status": "in_queue",
                "ended_on": None,
                "total_offers": 1,
                "ignored_offers": 0,
                "errored_offers": 0,
                "action_name": self.action_cls.action_name,
                "modified_offers": 0,
                "lock_action": True,
                "id": action.id,
                "result": {},
            },
        )

    @patch(
        "apps.products.algolia_items_retriever.AlgoliaItemsRetriever."
        "get_algolia_index"
    )
    @patch("apps.products.algolia_items_retriever.AlgoliaItemsRetriever._do_search")
    def test_dry_run_false_algolia_filters_diffent_number_found(
        self, mocked_search, mocked_get_index
    ):
        offer = self.create_offers(1)[0]
        mocked_search.return_value = []

        resp = self.do_algolia_filters_request(False, offer)
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": ["Expected count 1 does not match computed 0"],
                        "field": "__all__",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_dry_run_true(self):
        offer = self.create_offers(1)[0]

        resp = self.do_api_filters_request(True, [offer])
        self.assertHttpOK(resp)
        self.assertEqual(
            self.deserialize(resp), {"product_offer_count": 1, "dry_run": True}
        )
        self.assertIsNone(self.get_action())

    def test_dry_run_not_set_defaults_true(self):
        offer = self.create_offers(1)[0]

        resp = self.do_api_filters_request(None, [offer])
        self.assertHttpOK(resp)
        self.assertEqual(
            self.deserialize(resp), {"product_offer_count": 1, "dry_run": True}
        )
        self.assertIsNone(self.get_action())

    def test_as_merchant(self):
        offer = self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/?merchant_id={}".format(
                self.action_name, offer.merchant_id
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": [offer.id]},
                "filter_type": "api_filters",
            },
            authentication=self.get_merchant_token(merchant=offer.merchant),
        )
        self.assertEqual(resp.status_code, 401)

    def test_as_izb_staff(self):
        offer = self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/?merchant_id={}".format(
                self.action_name, offer.merchant_id
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": [offer.id]},
                "filter_type": "api_filters",
            },
            authentication=self.get_staff_token(),
        )
        self.assertEqual(resp.status_code, 202)

    def test_as_customer(self):
        offer = self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": [offer.id]},
                "filter_type": "api_filters",
            },
            authentication=self.get_app_user_token(application=offer.application),
        )
        self.assertEqual(resp.status_code, 401)

    def test_invalid_api_filters(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"invalid": "value"},
                "filter_type": "api_filters",
            },
            authentication=self.get_operator_token(application=application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": ["The 'invalid' field does not allow filtering."],
                        "field": "__all__",
                    }
                ]
            },
        )

    def test_with_empty_queryset(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": []},
                "filter_type": "api_filters",
            },
            authentication=self.get_operator_token(application=application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": [
                            "Trying to batch-update 0 item. Please check your "
                            "filters."
                        ],
                        "field": "__all__",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_not_owner_of_ids(self):
        offer = self.create_offers(1)[0]
        application = ApplicationFactory()

        resp = self.do_api_filters_request(
            dry_run=False, offers=[offer], application=application
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": [
                            "Trying to batch-update 0 item. Please check your "
                            "filters."
                        ],
                        "field": "__all__",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_with_empty_body(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(self.action_name),
            data={},
            authentication=self.get_operator_token(application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Missing mandatory key"], "field": "filter_type"}]},
        )
        self.assertIsNone(self.get_action())

    def test_invalid_api_filter_value(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(self.action_name),
            data={
                "dry_run": False,
                "filters": {"id__in": ["blablabla"]},
                "filter_type": "api_filters",
            },
            authentication=self.get_operator_token(application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Invalid filters provided"], "field": "filters"}]},
        )
        self.assertIsNone(self.get_action())

    def test_unfiltered_request_as_izberg_staff(self):
        self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(self.action_name),
            data={"dry_run": False, "filters": {}, "filter_type": "api_filters"},
            authentication=self.get_staff_token(),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertIsNone(self.get_action())
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Missing mandatory parameter"], "field": "merchant"}]},
        )

    def test_unfiltered_request_as_izberg_staff_with_merchant_ctx(self):
        offer = self.create_offers(1)[0]
        self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/?merchant_id={}".format(
                self.action_name, offer.merchant_id
            ),
            data={"dry_run": False, "filters": {}, "filter_type": "api_filters"},
            authentication=self.get_staff_token(),
        )
        self.assertEqual(resp.status_code, 202)
        action = self.get_action()
        self.assertEqual(action.get_meta("total_offers"), 1)
        self.assertEqual(int(action.pop_id()), offer.id)


class BatchUnbanTestCase(BaseResourceTestCase, BatchActionMixin):
    action_name = "unban"
    action_cls = ProductOfferBatchUnBanAction

    def create_offers(self, count, **kwargs):
        return super(BatchUnbanTestCase, self).create_offers(
            count, status=PRODUCT_OFFER_STATUS_BANNED, **kwargs
        )

    def test_dry_run_false_api_filters(self):
        offer = self.create_offers(2)[0]

        resp = self.do_api_filters_request(False, [offer])

        self.assertEqual(resp.status_code, 202)
        self.assertEqual(resp.content, b"")
        self.assertIn("/v1/async-action/", resp["Location"])

        action = self.get_action()
        data = action.get_as_dict()
        self.assertIsNotNone(data.pop("created_on"))
        self.assertEqual(
            data,
            {
                "status": "in_queue",
                "ended_on": None,
                "total_offers": 1,
                "action_name": self.action_cls.action_name,
                "ignored_offers": 0,
                "errored_offers": 0,
                "modified_offers": 0,
                "lock_action": True,
                "id": action.id,
                "result": {},
            },
        )

    @patch(
        "apps.products.algolia_items_retriever.AlgoliaItemsRetriever."
        "get_algolia_index"
    )
    @patch("apps.products.algolia_items_retriever.AlgoliaItemsRetriever._do_search")
    def test_dry_run_false_algolia_filters(self, mocked_search, mocked_get_index):
        offer = self.create_offers(1)[0]
        mocked_search.return_value = [{"id": offer.id}]

        resp = self.do_algolia_filters_request(False, offer)
        self.assertEqual(resp.status_code, 202)
        self.assertEqual(resp.content, b"")
        self.assertIn("/v1/async-action/", resp["Location"])

        action = self.get_action()
        data = action.get_as_dict()
        self.assertIsNotNone(data.pop("created_on"))
        self.assertEqual(
            data,
            {
                "status": "in_queue",
                "ended_on": None,
                "total_offers": 1,
                "action_name": self.action_cls.action_name,
                "ignored_offers": 0,
                "errored_offers": 0,
                "modified_offers": 0,
                "lock_action": True,
                "id": action.id,
                "result": {},
            },
        )

    @patch(
        "apps.products.algolia_items_retriever.AlgoliaItemsRetriever."
        "get_algolia_index"
    )
    @patch("apps.products.algolia_items_retriever.AlgoliaItemsRetriever._do_search")
    def test_dry_run_false_algolia_filters_diffent_number_found(
        self, mocked_search, mocked_get_index
    ):
        offer = self.create_offers(1)[0]
        mocked_search.return_value = []

        resp = self.do_algolia_filters_request(False, offer)
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": ["Expected count 1 does not match computed 0"],
                        "field": "__all__",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_dry_run_true(self):
        offer = self.create_offers(1)[0]

        resp = self.do_api_filters_request(True, [offer])
        self.assertHttpOK(resp)
        self.assertEqual(
            self.deserialize(resp), {"product_offer_count": 1, "dry_run": True}
        )
        self.assertIsNone(self.get_action())

    def test_dry_run_not_set_defaults_true(self):
        offer = self.create_offers(1)[0]

        resp = self.do_api_filters_request(None, [offer])
        self.assertHttpOK(resp)
        self.assertEqual(
            self.deserialize(resp), {"product_offer_count": 1, "dry_run": True}
        )
        self.assertIsNone(self.get_action())

    def test_as_merchant(self):
        offer = self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/?merchant_id={}".format(
                self.action_name, offer.merchant_id
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": [offer.id]},
                "filter_type": "api_filters",
            },
            authentication=self.get_merchant_token(merchant=offer.merchant),
        )
        self.assertEqual(resp.status_code, 401)

    def test_as_izb_staff(self):
        offer = self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/?merchant_id={}".format(
                self.action_name, offer.merchant_id
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": [offer.id]},
                "filter_type": "api_filters",
            },
            authentication=self.get_staff_token(),
        )
        self.assertEqual(resp.status_code, 202)

    def test_as_customer(self):
        offer = self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": [offer.id]},
                "filter_type": "api_filters",
            },
            authentication=self.get_app_user_token(application=offer.application),
        )
        self.assertEqual(resp.status_code, 401)

    def test_invalid_api_filters(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"invalid": "value"},
                "filter_type": "api_filters",
            },
            authentication=self.get_operator_token(application=application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": ["The 'invalid' field does not allow filtering."],
                        "field": "__all__",
                    }
                ]
            },
        )

    def test_with_empty_queryset(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": []},
                "filter_type": "api_filters",
            },
            authentication=self.get_operator_token(application=application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": [
                            "Trying to batch-update 0 item. Please check your "
                            "filters."
                        ],
                        "field": "__all__",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_not_owner_of_ids(self):
        offer = self.create_offers(1)[0]
        application = ApplicationFactory()

        resp = self.do_api_filters_request(
            dry_run=False, offers=[offer], application=application
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": [
                            "Trying to batch-update 0 item. Please check your "
                            "filters."
                        ],
                        "field": "__all__",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_with_empty_body(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(self.action_name),
            data={},
            authentication=self.get_operator_token(application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Missing mandatory key"], "field": "filter_type"}]},
        )
        self.assertIsNone(self.get_action())

    def test_invalid_api_filter_value(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(self.action_name),
            data={
                "dry_run": False,
                "filters": {"id__in": ["blablabla"]},
                "filter_type": "api_filters",
            },
            authentication=self.get_operator_token(application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Invalid filters provided"], "field": "filters"}]},
        )
        self.assertIsNone(self.get_action())

    def test_unfiltered_request_as_izberg_staff(self):
        self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(self.action_name),
            data={"dry_run": False, "filters": {}, "filter_type": "api_filters"},
            authentication=self.get_staff_token(),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertIsNone(self.get_action())
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Missing mandatory parameter"], "field": "merchant"}]},
        )

    def test_unfiltered_request_as_izberg_staff_with_merchant_ctx(self):
        offer = self.create_offers(1)[0]
        self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/?merchant_id={}".format(
                self.action_name, offer.merchant_id
            ),
            data={"dry_run": False, "filters": {}, "filter_type": "api_filters"},
            authentication=self.get_staff_token(),
        )
        self.assertEqual(resp.status_code, 202)
        action = self.get_action()
        self.assertEqual(action.get_meta("total_offers"), 1)
        self.assertEqual(int(action.pop_id()), offer.id)


class BatchTrashTestCase(BaseResourceTestCase, BatchActionMixin):
    action_name = "trash"
    action_cls = ProductOfferBatchTrashAction

    def test_dry_run_false_api_filters(self):
        offer = self.create_offers(2)[0]

        resp = self.do_api_filters_request(False, [offer])

        self.assertEqual(resp.status_code, 202)
        self.assertEqual(resp.content, b"")
        self.assertIn("/v1/async-action/", resp["Location"])

        action = self.get_action()
        data = action.get_as_dict()
        self.assertIsNotNone(data.pop("created_on"))
        self.assertEqual(
            data,
            {
                "status": "in_queue",
                "ended_on": None,
                "total_offers": 1,
                "action_name": self.action_cls.action_name,
                "ignored_offers": 0,
                "errored_offers": 0,
                "modified_offers": 0,
                "lock_action": True,
                "id": action.id,
                "result": {},
            },
        )

    @patch(
        "apps.products.algolia_items_retriever.AlgoliaItemsRetriever."
        "get_algolia_index"
    )
    @patch("apps.products.algolia_items_retriever.AlgoliaItemsRetriever._do_search")
    def test_dry_run_false_algolia_filters(self, mocked_search, mocked_get_index):
        offer = self.create_offers(1)[0]
        mocked_search.return_value = [{"id": offer.id}]

        resp = self.do_algolia_filters_request(False, offer)
        self.assertEqual(resp.status_code, 202)
        self.assertEqual(resp.content, b"")
        self.assertIn("/v1/async-action/", resp["Location"])

        action = self.get_action()
        data = action.get_as_dict()
        self.assertIsNotNone(data.pop("created_on"))
        self.assertEqual(
            data,
            {
                "status": "in_queue",
                "ended_on": None,
                "total_offers": 1,
                "action_name": self.action_cls.action_name,
                "ignored_offers": 0,
                "errored_offers": 0,
                "modified_offers": 0,
                "lock_action": True,
                "id": action.id,
                "result": {},
            },
        )

    @patch(
        "apps.products.algolia_items_retriever.AlgoliaItemsRetriever."
        "get_algolia_index"
    )
    @patch("apps.products.algolia_items_retriever.AlgoliaItemsRetriever._do_search")
    def test_dry_run_false_algolia_filters_diffent_number_found(
        self, mocked_search, mocked_get_index
    ):
        offer = self.create_offers(1)[0]
        mocked_search.return_value = []

        resp = self.do_algolia_filters_request(False, offer)
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": ["Expected count 1 does not match computed 0"],
                        "field": "__all__",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_dry_run_true(self):
        offer = self.create_offers(1)[0]

        resp = self.do_api_filters_request(True, [offer])
        self.assertHttpOK(resp)
        self.assertEqual(
            self.deserialize(resp), {"product_offer_count": 1, "dry_run": True}
        )
        self.assertIsNone(self.get_action())

    def test_dry_run_not_set_defaults_true(self):
        offer = self.create_offers(1)[0]

        resp = self.do_api_filters_request(None, [offer])
        self.assertHttpOK(resp)
        self.assertEqual(
            self.deserialize(resp), {"product_offer_count": 1, "dry_run": True}
        )
        self.assertIsNone(self.get_action())

    def test_as_merchant(self):
        offer = self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/?merchant_id={}".format(
                self.action_name, offer.merchant_id
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": [offer.id]},
                "filter_type": "api_filters",
            },
            authentication=self.get_merchant_token(merchant=offer.merchant),
        )
        self.assertEqual(resp.status_code, 202)

    def test_as_izb_staff(self):
        offer = self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/?merchant_id={}".format(
                self.action_name, offer.merchant_id
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": [offer.id]},
                "filter_type": "api_filters",
            },
            authentication=self.get_staff_token(),
        )
        self.assertEqual(resp.status_code, 202)

    def test_unfiltered_request_as_izberg_staff(self):
        self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(self.action_name),
            data={"dry_run": False, "filters": {}, "filter_type": "api_filters"},
            authentication=self.get_staff_token(),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertIsNone(self.get_action())
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Missing mandatory parameter"], "field": "merchant"}]},
        )

    def test_unfiltered_request_as_izberg_staff_with_merchant_ctx(self):
        offer = self.create_offers(1)[0]
        self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/?merchant_id={}".format(
                self.action_name, offer.merchant_id
            ),
            data={"dry_run": False, "filters": {}, "filter_type": "api_filters"},
            authentication=self.get_staff_token(),
        )
        self.assertEqual(resp.status_code, 202)
        action = self.get_action()
        self.assertEqual(action.get_meta("total_offers"), 1)
        self.assertEqual(int(action.pop_id()), offer.id)

    def test_as_customer(self):
        offer = self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": [offer.id]},
                "filter_type": "api_filters",
            },
            authentication=self.get_app_user_token(application=offer.application),
        )
        self.assertEqual(resp.status_code, 401)

    def test_invalid_api_filters(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"invalid": "value"},
                "filter_type": "api_filters",
            },
            authentication=self.get_operator_token(application=application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": ["The 'invalid' field does not allow filtering."],
                        "field": "__all__",
                    }
                ]
            },
        )

    def test_with_empty_queryset_as_operator(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": []},
                "filter_type": "api_filters",
            },
            authentication=self.get_operator_token(application=application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": [
                            "Trying to batch-update 0 item. Please check your "
                            "filters."
                        ],
                        "field": "__all__",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_with_empty_queryset_as_merchant(self):
        merchant = MerchantFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": []},
                "filter_type": "api_filters",
            },
            authentication=self.get_merchant_token(merchant=merchant),
        )
        self.assertEqual(resp.status_code, 401)

    def test_not_owner_of_ids(self):
        offer = self.create_offers(1)[0]
        application = ApplicationFactory()

        resp = self.do_api_filters_request(
            dry_run=False, offers=[offer], application=application
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": [
                            "Trying to batch-update 0 item. Please check your "
                            "filters."
                        ],
                        "field": "__all__",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_with_empty_body(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(self.action_name),
            data={},
            authentication=self.get_operator_token(application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Missing mandatory key"], "field": "filter_type"}]},
        )
        self.assertIsNone(self.get_action())

    def test_invalid_api_filter_value(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(self.action_name),
            data={
                "dry_run": False,
                "filters": {"id__in": ["blablabla"]},
                "filter_type": "api_filters",
            },
            authentication=self.get_operator_token(application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Invalid filters provided"], "field": "filters"}]},
        )
        self.assertIsNone(self.get_action())

    def test_with_trash_app_setting_disabled_as_operator(self):
        offer = self.create_offers(1)[0]
        offer.application.set_setting(AllowTrashStatus, False)

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(self.action_name),
            data={"dry_run": False, "filters": {}, "filter_type": "api_filters"},
            authentication=self.get_operator_token(application=offer.application),
        )

        self.assertHttpForbidden(resp)
        self.assertEqual(self.deserialize(resp), {"msg": "Action not available"})

    def test_with_trash_app_setting_disabled_as_merchant(self):
        offer = self.create_offers(1)[0]
        offer.application.set_setting(AllowTrashStatus, False)

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/?merchant_id={}".format(
                self.action_name, offer.merchant_id
            ),
            data={"dry_run": False, "filters": {}, "filter_type": "api_filters"},
            authentication=self.get_merchant_token(merchant=offer.merchant),
        )

        self.assertHttpForbidden(resp)
        self.assertEqual(self.deserialize(resp), {"msg": "Action not available"})


class BatchUntrashTestCase(BaseResourceTestCase, BatchActionMixin):
    action_name = "untrash"
    action_cls = ProductOfferBatchUntrashAction

    def create_offers(self, count, **kwargs):
        return super(BatchUntrashTestCase, self).create_offers(
            count, status=PRODUCT_OFFER_STATUS_TRASHED, **kwargs
        )

    def test_dry_run_false_api_filters(self):
        offer = self.create_offers(2)[0]

        resp = self.do_api_filters_request(False, [offer])

        self.assertEqual(resp.status_code, 202)
        self.assertEqual(resp.content, b"")
        self.assertIn("/v1/async-action/", resp["Location"])

        action = self.get_action()
        data = action.get_as_dict()
        self.assertIsNotNone(data.pop("created_on"))
        self.assertEqual(
            data,
            {
                "status": "in_queue",
                "ended_on": None,
                "total_offers": 1,
                "action_name": self.action_cls.action_name,
                "ignored_offers": 0,
                "errored_offers": 0,
                "modified_offers": 0,
                "lock_action": True,
                "id": action.id,
                "result": {},
            },
        )

    @patch(
        "apps.products.algolia_items_retriever.AlgoliaItemsRetriever."
        "get_algolia_index"
    )
    @patch("apps.products.algolia_items_retriever.AlgoliaItemsRetriever._do_search")
    def test_dry_run_false_algolia_filters(self, mocked_search, mocked_get_index):
        offer = self.create_offers(1)[0]
        mocked_search.return_value = [{"id": offer.id}]

        resp = self.do_algolia_filters_request(False, offer)
        self.assertEqual(resp.status_code, 202)
        self.assertEqual(resp.content, b"")
        self.assertIn("/v1/async-action/", resp["Location"])

        action = self.get_action()
        data = action.get_as_dict()
        self.assertIsNotNone(data.pop("created_on"))
        self.assertEqual(
            data,
            {
                "status": "in_queue",
                "ended_on": None,
                "total_offers": 1,
                "action_name": self.action_cls.action_name,
                "ignored_offers": 0,
                "errored_offers": 0,
                "modified_offers": 0,
                "lock_action": True,
                "id": action.id,
                "result": {},
            },
        )

    @patch(
        "apps.products.algolia_items_retriever.AlgoliaItemsRetriever."
        "get_algolia_index"
    )
    @patch("apps.products.algolia_items_retriever.AlgoliaItemsRetriever._do_search")
    def test_dry_run_false_algolia_filters_diffent_number_found(
        self, mocked_search, mocked_get_index
    ):
        offer = self.create_offers(1)[0]
        mocked_search.return_value = []

        resp = self.do_algolia_filters_request(False, offer)
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": ["Expected count 1 does not match computed 0"],
                        "field": "__all__",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_dry_run_true(self):
        offer = self.create_offers(1)[0]

        resp = self.do_api_filters_request(True, [offer])
        self.assertHttpOK(resp)
        self.assertEqual(
            self.deserialize(resp), {"product_offer_count": 1, "dry_run": True}
        )
        self.assertIsNone(self.get_action())

    def test_dry_run_not_set_defaults_true(self):
        offer = self.create_offers(1)[0]

        resp = self.do_api_filters_request(None, [offer])
        self.assertHttpOK(resp)
        self.assertEqual(
            self.deserialize(resp), {"product_offer_count": 1, "dry_run": True}
        )
        self.assertIsNone(self.get_action())

    def test_as_merchant(self):
        offer = self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/?merchant_id={}".format(
                self.action_name, offer.merchant_id
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": [offer.id]},
                "filter_type": "api_filters",
            },
            authentication=self.get_merchant_token(merchant=offer.merchant),
        )
        self.assertEqual(resp.status_code, 202)

    def test_as_izb_staff(self):
        offer = self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/?merchant_id={}".format(
                self.action_name, offer.merchant_id
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": [offer.id]},
                "filter_type": "api_filters",
            },
            authentication=self.get_staff_token(),
        )
        self.assertEqual(resp.status_code, 202)

    def test_unfiltered_request_as_izberg_staff(self):
        self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(self.action_name),
            data={"dry_run": False, "filters": {}, "filter_type": "api_filters"},
            authentication=self.get_staff_token(),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertIsNone(self.get_action())
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Missing mandatory parameter"], "field": "merchant"}]},
        )

    def test_unfiltered_request_as_izberg_staff_with_merchant_ctx(self):
        offer = self.create_offers(1)[0]
        self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/?merchant_id={}".format(
                self.action_name, offer.merchant_id
            ),
            data={"dry_run": False, "filters": {}, "filter_type": "api_filters"},
            authentication=self.get_staff_token(),
        )
        self.assertEqual(resp.status_code, 202)
        action = self.get_action()
        self.assertEqual(action.get_meta("total_offers"), 1)
        self.assertEqual(int(action.pop_id()), offer.id)

    def test_as_customer(self):
        offer = self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": [offer.id]},
                "filter_type": "api_filters",
            },
            authentication=self.get_app_user_token(application=offer.application),
        )
        self.assertEqual(resp.status_code, 401)

    def test_invalid_api_filters(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"invalid": "value"},
                "filter_type": "api_filters",
            },
            authentication=self.get_operator_token(application=application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": ["The 'invalid' field does not allow filtering."],
                        "field": "__all__",
                    }
                ]
            },
        )

    def test_with_empty_queryset_as_operator(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": []},
                "filter_type": "api_filters",
            },
            authentication=self.get_operator_token(application=application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": [
                            "Trying to batch-update 0 item. Please check your "
                            "filters."
                        ],
                        "field": "__all__",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_with_empty_queryset_as_merchant(self):
        merchant = MerchantFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": []},
                "filter_type": "api_filters",
            },
            authentication=self.get_merchant_token(merchant=merchant),
        )
        self.assertEqual(resp.status_code, 401)

    def test_not_owner_of_ids(self):
        offer = self.create_offers(1)[0]
        application = ApplicationFactory()

        resp = self.do_api_filters_request(
            dry_run=False, offers=[offer], application=application
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": [
                            "Trying to batch-update 0 item. Please check your "
                            "filters."
                        ],
                        "field": "__all__",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_with_empty_body(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(self.action_name),
            data={},
            authentication=self.get_operator_token(application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Missing mandatory key"], "field": "filter_type"}]},
        )
        self.assertIsNone(self.get_action())

    def test_invalid_api_filter_value(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(self.action_name),
            data={
                "dry_run": False,
                "filters": {"id__in": ["blablabla"]},
                "filter_type": "api_filters",
            },
            authentication=self.get_operator_token(application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Invalid filters provided"], "field": "filters"}]},
        )
        self.assertIsNone(self.get_action())

    def test_with_trash_app_setting_disabled_as_operator(self):
        offer = self.create_offers(1)[0]
        offer.application.set_setting(AllowTrashStatus, False)

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(self.action_name),
            data={"dry_run": False, "filters": {}, "filter_type": "api_filters"},
            authentication=self.get_operator_token(application=offer.application),
        )

        self.assertHttpForbidden(resp)
        self.assertEqual(self.deserialize(resp), {"msg": "Action not available"})

    def test_with_trash_app_setting_disabled_as_merchant(self):
        offer = self.create_offers(1)[0]
        offer.application.set_setting(AllowTrashStatus, False)

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/?merchant_id={}".format(
                self.action_name, offer.merchant_id
            ),
            data={"dry_run": False, "filters": {}, "filter_type": "api_filters"},
            authentication=self.get_merchant_token(merchant=offer.merchant),
        )

        self.assertHttpForbidden(resp)
        self.assertEqual(self.deserialize(resp), {"msg": "Action not available"})


@override_settings(CELERY_TASK_ALWAYS_EAGER=True)
@override_settings(CELERY_TASK_EAGER_PROPAGATES_EXCEPTIONS=True)
class BatchApplyDefaultTaxSettingsTestCase(BaseResourceTestCase, BatchActionMixin):
    action_name = "apply-default-tax-settings"
    action_cls = ProductOfferBatchApplyDefaultTaxSettingsAction

    def test_dry_run_true(self):
        merchant = MerchantFactory(application__country__code="BR")
        po = ProductOfferFactory.create_for_merchant(
            merchant,
            shipped_from_eu_vat_zone=False,
            shipped_from_region="somewhere",
        )
        ass = TaxRateAssignmentFactory(
            application=merchant.application, country__code="FR"
        )
        merchant.tax_settings.enabled_tax_zones = [ass.tax_zone.zone_key]
        merchant.tax_settings.shipped_from_country = ass.tax_zone.country
        merchant.tax_settings.shipped_from_eu_vat_zone = True
        merchant.tax_settings.shipped_from_region = "bla"
        merchant.tax_settings.default_offer_tax_rates = [
            {"zone_key": ass.tax_zone.zone_key, "rate_key": ass.tax_rate.rate_key}
        ]
        merchant.tax_settings.save()

        resp = self.api_client.post(
            f"/v1/productoffer/batch/{self.action_name}/?"
            f"merchant_id={po.merchant_id}",
            data={
                "filter_type": "api_filters",
                "filters": {},
            },
            authentication=self.get_merchant_token(merchant=merchant),
        )

        self.assertHttpOK(resp)
        self.assertNotIn("Location", resp)
        po.refresh_from_db()
        self.assertEqual(po.shipped_from_country.code, "BR")
        self.assertFalse(po.shipped_from_eu_vat_zone)
        self.assertEqual(po.shipped_from_region, "somewhere")
        self.assertEqual(po.tax_rates, [])

    def test_dry_run_false(self):
        merchant = MerchantFactory(application__country__code="BR")
        po = ProductOfferFactory.create_for_merchant(
            merchant,
            shipped_from_eu_vat_zone=False,
            shipped_from_region="somewhere",
        )
        ass = TaxRateAssignmentFactory(
            application=merchant.application, country__code="FR"
        )
        merchant.tax_settings.enabled_tax_zones = [ass.tax_zone.zone_key]
        merchant.tax_settings.default_shipped_from_country = ass.tax_zone.country
        merchant.tax_settings.default_shipped_from_eu_vat_zone = True
        merchant.tax_settings.default_shipped_from_region = "bla"
        merchant.tax_settings.default_offer_tax_rates = [
            {"zone_key": ass.tax_zone.zone_key, "rate_key": ass.tax_rate.rate_key}
        ]
        merchant.tax_settings.save()

        resp = self.api_client.post(
            f"/v1/productoffer/batch/{self.action_name}/?"
            f"merchant_id={po.merchant_id}",
            data={
                "filter_type": "api_filters",
                "filters": {},
                "dry_run": False,
            },
            authentication=self.get_merchant_token(merchant=merchant),
        )

        self.assertHttpAccepted(resp)
        self.assertTrue("/v1/async-action/" in resp["Location"])
        po.refresh_from_db()
        self.assertEqual(po.shipped_from_country.code, "FR")
        self.assertTrue(po.shipped_from_eu_vat_zone)
        self.assertEqual(po.shipped_from_region, "bla")
        self.assertEqual(
            po.tax_rates,
            [{"zone_key": ass.tax_zone.zone_key, "rate_key": ass.tax_rate.rate_key}],
        )

    def test_with_valid_api_filters(self):
        merchant = MerchantFactory(application__country__code="BR")
        po = ProductOfferFactory.create_for_merchant(
            merchant,
            shipped_from_eu_vat_zone=False,
            shipped_from_region="somewhere",
            sku="sku1",
        )
        po_2 = ProductOfferFactory.create_for_merchant(
            merchant,
            shipped_from_eu_vat_zone=False,
            shipped_from_region="somewhere",
            sku="sku2",
        )
        ass = TaxRateAssignmentFactory(
            application=merchant.application, country__code="FR"
        )
        merchant.tax_settings.enabled_tax_zones = [ass.tax_zone.zone_key]
        merchant.tax_settings.default_shipped_from_country = ass.tax_zone.country
        merchant.tax_settings.default_shipped_from_eu_vat_zone = True
        merchant.tax_settings.default_shipped_from_region = "bla"
        merchant.tax_settings.default_offer_tax_rates = [
            {"zone_key": ass.tax_zone.zone_key, "rate_key": ass.tax_rate.rate_key}
        ]
        merchant.tax_settings.save()

        resp = self.api_client.post(
            f"/v1/productoffer/batch/{self.action_name}/?"
            f"merchant_id={po.merchant_id}",
            data={
                "filter_type": "api_filters",
                "filters": {"sku": "sku1"},
                "dry_run": False,
            },
            authentication=self.get_merchant_token(merchant=merchant),
        )

        self.assertHttpAccepted(resp)
        self.assertTrue("/v1/async-action/" in resp["Location"])
        # po was updated
        po.refresh_from_db()
        self.assertEqual(po.shipped_from_country.code, "FR")
        self.assertTrue(po.shipped_from_eu_vat_zone)
        self.assertEqual(po.shipped_from_region, "bla")
        self.assertEqual(
            po.tax_rates,
            [{"zone_key": ass.tax_zone.zone_key, "rate_key": ass.tax_rate.rate_key}],
        )
        # po_2 was not updated
        po_2.refresh_from_db()
        self.assertEqual(po_2.shipped_from_country.code, "BR")
        self.assertFalse(po_2.shipped_from_eu_vat_zone)
        self.assertEqual(po_2.shipped_from_region, "somewhere")
        self.assertEqual(po_2.tax_rates, [])

    def test_with_invalid_api_filters(self):
        po = ProductOfferFactory()

        resp = self.api_client.post(
            f"/v1/productoffer/batch/{self.action_name}/" f"?merchant={po.merchant.id}",
            data={
                "filter_type": "api_filters",
                "filters": {"invalid": "filter"},
                "dry_run": False,
            },
            authentication=self.get_merchant_token(merchant=po.merchant),
        )

        self.assertHttpBadRequest(resp)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "field": "__all__",
                        "msg": ["The 'invalid' field does not allow filtering."],
                    }
                ]
            },
        )

    @patch.object(
        AlgoliaItemsRetriever,
        "search_items_on_algolia",
        lambda _, __, item_ids_set: item_ids_set.add(1),
    )
    def test_with_valid_algolia_filters(self):
        po = ProductOfferFactory.create(
            shipped_from_eu_vat_zone=False,
            shipped_from_region="somewhere",
            sku="sku1",
            id=1,
            product__id=1,
            merchant__id=1,
            application__id=1,
        )
        po.application.backoffice_channel  # creates channel
        ass = TaxRateAssignmentFactory(
            application=po.merchant.application, country__code="FR"
        )
        po.merchant.tax_settings.enabled_tax_zones = [ass.tax_zone.zone_key]
        po.merchant.tax_settings.default_shipped_from_country = ass.tax_zone.country
        po.merchant.tax_settings.default_shipped_from_eu_vat_zone = True
        po.merchant.tax_settings.default_shipped_from_region = "bla"
        po.merchant.tax_settings.default_offer_tax_rates = [
            {"zone_key": ass.tax_zone.zone_key, "rate_key": ass.tax_rate.rate_key}
        ]
        po.merchant.tax_settings.save()

        resp = self.api_client.post(
            f"/v1/productoffer/batch/{self.action_name}/?"
            f"merchant_id={po.merchant_id}",
            data={
                "filter_type": "algolia",
                "filters": {
                    "channel_id": po.application.backoffice_channel.id,
                    "requests": [{"sample": "request"}],
                },
                "dry_run": False,
            },
            authentication=self.get_merchant_token(merchant=po.merchant),
        )

        self.assertHttpAccepted(resp)
        self.assertTrue("/v1/async-action/" in resp["Location"])
        # po was updated
        po.refresh_from_db()
        self.assertEqual(po.shipped_from_country.code, "FR")
        self.assertTrue(po.shipped_from_eu_vat_zone)
        self.assertEqual(po.shipped_from_region, "bla")
        self.assertEqual(
            po.tax_rates,
            [{"zone_key": ass.tax_zone.zone_key, "rate_key": ass.tax_rate.rate_key}],
        )

    def test_as_operator_without_merchant_ctx(self):
        merchant = MerchantFactory(application__country__code="BR")
        ProductOfferFactory.create_for_merchant(
            merchant,
            shipped_from_eu_vat_zone=False,
            shipped_from_region="somewhere",
        )
        ass = TaxRateAssignmentFactory(
            application=merchant.application, country__code="FR"
        )
        merchant.tax_settings.enabled_tax_zones = [ass.tax_zone.zone_key]
        merchant.tax_settings.default_shipped_from_country = ass.tax_zone.country
        merchant.tax_settings.default_shipped_from_eu_vat_zone = True
        merchant.tax_settings.default_shipped_from_region = "bla"
        merchant.tax_settings.default_offer_tax_rates = [
            {"zone_key": ass.tax_zone.zone_key, "rate_key": ass.tax_rate.rate_key}
        ]
        merchant.tax_settings.save()

        resp = self.api_client.post(
            f"/v1/productoffer/batch/{self.action_name}/",
            data={
                "filter_type": "api_filters",
                "filters": {},
                "dry_run": False,
            },
            authentication=self.get_operator_token(application=merchant.application),
        )

        self.assertHttpBadRequest(resp)
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"field": "merchant", "msg": ["Missing mandatory parameter"]}]},
        )

    def test_as_operator_with_merchant_ctx(self):
        merchant = MerchantFactory(application__country__code="BR")
        po_1 = ProductOfferFactory.create_for_merchant(
            merchant,
            shipped_from_eu_vat_zone=False,
            shipped_from_region="somewhere",
        )
        po_other_merchant = ProductOfferFactory.create_for_application(
            merchant.application,
            shipped_from_eu_vat_zone=False,
            shipped_from_region="somewhere",
        )
        ass = TaxRateAssignmentFactory(
            application=merchant.application, country__code="FR"
        )
        merchant.tax_settings.enabled_tax_zones = [ass.tax_zone.zone_key]
        merchant.tax_settings.default_shipped_from_country = ass.tax_zone.country
        merchant.tax_settings.default_shipped_from_eu_vat_zone = True
        merchant.tax_settings.default_shipped_from_region = "bla"
        merchant.tax_settings.default_offer_tax_rates = [
            {"zone_key": ass.tax_zone.zone_key, "rate_key": ass.tax_rate.rate_key}
        ]
        merchant.tax_settings.save()

        resp = self.api_client.post(
            f"/v1/productoffer/batch/{self.action_name}/",
            data={
                "filter_type": "api_filters",
                "filters": {},
                "dry_run": False,
                "merchant": f"/v1/merchant/{merchant.id}/",
            },
            authentication=self.get_operator_token(application=merchant.application),
        )

        self.assertHttpAccepted(resp)
        self.assertTrue("/v1/async-action/" in resp["Location"])
        po_1.refresh_from_db()
        self.assertEqual(po_1.shipped_from_country.code, "FR")
        self.assertTrue(po_1.shipped_from_eu_vat_zone)
        self.assertEqual(po_1.shipped_from_region, "bla")
        self.assertEqual(
            po_1.tax_rates,
            [{"zone_key": ass.tax_zone.zone_key, "rate_key": ass.tax_rate.rate_key}],
        )
        po_other_merchant.refresh_from_db()
        self.assertEqual(po_other_merchant.shipped_from_country.code, "BR")
        self.assertFalse(po_other_merchant.shipped_from_eu_vat_zone)
        self.assertEqual(po_other_merchant.shipped_from_region, "somewhere")
        self.assertEqual(po_other_merchant.tax_rates, [])

    def test_as_staff_without_merchant_ctx(self):
        merchant = MerchantFactory(application__country__code="BR")
        ProductOfferFactory.create_for_merchant(
            merchant,
            shipped_from_eu_vat_zone=False,
            shipped_from_region="somewhere",
        )
        ass = TaxRateAssignmentFactory(
            application=merchant.application, country__code="FR"
        )
        merchant.tax_settings.enabled_tax_zones = [ass.tax_zone.zone_key]
        merchant.tax_settings.default_shipped_from_country = ass.tax_zone.country
        merchant.tax_settings.default_shipped_from_eu_vat_zone = True
        merchant.tax_settings.default_shipped_from_region = "bla"
        merchant.tax_settings.default_offer_tax_rates = [
            {"zone_key": ass.tax_zone.zone_key, "rate_key": ass.tax_rate.rate_key}
        ]
        merchant.tax_settings.save()

        resp = self.api_client.post(
            f"/v1/productoffer/batch/{self.action_name}/",
            data={
                "filter_type": "api_filters",
                "filters": {},
                "dry_run": False,
            },
            authentication=self.get_staff_token(),
        )

        self.assertHttpBadRequest(resp)
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"field": "merchant", "msg": ["Missing mandatory parameter"]}]},
        )

    def test_as_staff_with_merchant_ctx(self):
        merchant = MerchantFactory(application__country__code="BR")
        po_1 = ProductOfferFactory.create_for_merchant(
            merchant,
            shipped_from_eu_vat_zone=False,
            shipped_from_region="somewhere",
        )
        po_other_merchant = ProductOfferFactory.create_for_application(
            merchant.application,
            shipped_from_eu_vat_zone=False,
            shipped_from_region="somewhere",
        )
        ass = TaxRateAssignmentFactory(
            application=merchant.application, country__code="FR"
        )
        merchant.tax_settings.enabled_tax_zones = [ass.tax_zone.zone_key]
        merchant.tax_settings.default_shipped_from_country = ass.tax_zone.country
        merchant.tax_settings.default_shipped_from_eu_vat_zone = True
        merchant.tax_settings.default_shipped_from_region = "bla"
        merchant.tax_settings.default_offer_tax_rates = [
            {"zone_key": ass.tax_zone.zone_key, "rate_key": ass.tax_rate.rate_key}
        ]
        merchant.tax_settings.save()

        resp = self.api_client.post(
            f"/v1/productoffer/batch/{self.action_name}/" f"?merchant_id={merchant.id}",
            data={
                "filter_type": "api_filters",
                "filters": {},
                "dry_run": False,
            },
            authentication=self.get_staff_token(),
        )

        self.assertHttpAccepted(resp)
        self.assertTrue("/v1/async-action/" in resp["Location"])
        po_1.refresh_from_db()
        self.assertEqual(po_1.shipped_from_country.code, "FR")
        self.assertTrue(po_1.shipped_from_eu_vat_zone)
        self.assertEqual(po_1.shipped_from_region, "bla")
        self.assertEqual(
            po_1.tax_rates,
            [{"zone_key": ass.tax_zone.zone_key, "rate_key": ass.tax_rate.rate_key}],
        )
        po_other_merchant.refresh_from_db()
        self.assertEqual(po_other_merchant.shipped_from_country.code, "BR")
        self.assertFalse(po_other_merchant.shipped_from_eu_vat_zone)
        self.assertEqual(po_other_merchant.shipped_from_region, "somewhere")
        self.assertEqual(po_other_merchant.tax_rates, [])

    def test_only_reset_shipped_from_country(self):
        merchant = MerchantFactory(application__country__code="BR")
        po = ProductOfferFactory.create_for_merchant(
            merchant,
            shipped_from_eu_vat_zone=False,
            shipped_from_region="somewhere",
        )
        ass = TaxRateAssignmentFactory(
            application=merchant.application, country__code="FR"
        )
        merchant.tax_settings.enabled_tax_zones = [ass.tax_zone.zone_key]
        merchant.tax_settings.default_shipped_from_country = ass.tax_zone.country
        merchant.tax_settings.default_shipped_from_eu_vat_zone = True
        merchant.tax_settings.default_shipped_from_region = "bla"
        merchant.tax_settings.default_offer_tax_rates = [
            {"zone_key": ass.tax_zone.zone_key, "rate_key": ass.tax_rate.rate_key}
        ]
        merchant.tax_settings.save()

        resp = self.api_client.post(
            f"/v1/productoffer/batch/{self.action_name}/?"
            f"merchant_id={po.merchant_id}",
            data={
                "filter_type": "api_filters",
                "filters": {},
                "dry_run": False,
                "only": ["shipped_from_country"],
            },
            authentication=self.get_merchant_token(merchant=merchant),
        )

        self.assertHttpAccepted(resp)
        self.assertTrue("/v1/async-action/" in resp["Location"])
        po.refresh_from_db()
        self.assertEqual(po.shipped_from_country.code, "FR")
        self.assertFalse(po.shipped_from_eu_vat_zone)
        self.assertEqual(po.shipped_from_region, "somewhere")
        self.assertEqual(po.tax_rates, [])

    def test_only_reset_shipped_from_eu_vat_zone(self):
        merchant = MerchantFactory(application__country__code="BR")
        po = ProductOfferFactory.create_for_merchant(
            merchant,
            shipped_from_eu_vat_zone=False,
            shipped_from_region="somewhere",
        )
        ass = TaxRateAssignmentFactory(
            application=merchant.application, country__code="FR"
        )
        merchant.tax_settings.enabled_tax_zones = [ass.tax_zone.zone_key]
        merchant.tax_settings.default_shipped_from_country = ass.tax_zone.country
        merchant.tax_settings.default_shipped_from_eu_vat_zone = True
        merchant.tax_settings.default_shipped_from_region = "bla"
        merchant.tax_settings.default_offer_tax_rates = [
            {"zone_key": ass.tax_zone.zone_key, "rate_key": ass.tax_rate.rate_key}
        ]
        merchant.tax_settings.save()

        resp = self.api_client.post(
            f"/v1/productoffer/batch/{self.action_name}/?"
            f"merchant_id={po.merchant_id}",
            data={
                "filter_type": "api_filters",
                "filters": {},
                "dry_run": False,
                "only": ["shipped_from_eu_vat_zone"],
            },
            authentication=self.get_merchant_token(merchant=merchant),
        )

        self.assertHttpAccepted(resp)
        self.assertTrue("/v1/async-action/" in resp["Location"])
        po.refresh_from_db()
        self.assertEqual(po.shipped_from_country.code, "BR")
        self.assertTrue(po.shipped_from_eu_vat_zone)
        self.assertEqual(po.shipped_from_region, "somewhere")
        self.assertEqual(po.tax_rates, [])

    def test_only_reset_shipped_from_region(self):
        merchant = MerchantFactory(application__country__code="BR")
        po = ProductOfferFactory.create_for_merchant(
            merchant,
            shipped_from_eu_vat_zone=False,
            shipped_from_region="somewhere",
        )
        ass = TaxRateAssignmentFactory(
            application=merchant.application, country__code="FR"
        )
        merchant.tax_settings.enabled_tax_zones = [ass.tax_zone.zone_key]
        merchant.tax_settings.default_shipped_from_country = ass.tax_zone.country
        merchant.tax_settings.default_shipped_from_eu_vat_zone = True
        merchant.tax_settings.default_shipped_from_region = "bla"
        merchant.tax_settings.default_offer_tax_rates = [
            {"zone_key": ass.tax_zone.zone_key, "rate_key": ass.tax_rate.rate_key}
        ]
        merchant.tax_settings.save()

        resp = self.api_client.post(
            f"/v1/productoffer/batch/{self.action_name}/?"
            f"merchant_id={po.merchant_id}",
            data={
                "filter_type": "api_filters",
                "filters": {},
                "dry_run": False,
                "only": ["shipped_from_region"],
            },
            authentication=self.get_merchant_token(merchant=merchant),
        )

        self.assertHttpAccepted(resp)
        self.assertTrue("/v1/async-action/" in resp["Location"])
        po.refresh_from_db()
        self.assertEqual(po.shipped_from_country.code, "BR")
        self.assertFalse(po.shipped_from_eu_vat_zone)
        self.assertEqual(po.shipped_from_region, "bla")
        self.assertEqual(po.tax_rates, [])

    def test_only_reset_tax_rates(self):
        merchant = MerchantFactory(application__country__code="BR")
        po = ProductOfferFactory.create_for_merchant(
            merchant,
            shipped_from_eu_vat_zone=False,
            shipped_from_region="somewhere",
        )
        ass = TaxRateAssignmentFactory(
            application=merchant.application, country__code="FR"
        )
        merchant.tax_settings.enabled_tax_zones = [ass.tax_zone.zone_key]
        merchant.tax_settings.default_shipped_from_country = ass.tax_zone.country
        merchant.tax_settings.default_shipped_from_eu_vat_zone = True
        merchant.tax_settings.default_shipped_from_region = "bla"
        merchant.tax_settings.default_offer_tax_rates = [
            {"zone_key": ass.tax_zone.zone_key, "rate_key": ass.tax_rate.rate_key}
        ]
        merchant.tax_settings.save()

        resp = self.api_client.post(
            f"/v1/productoffer/batch/{self.action_name}/?"
            f"merchant_id={po.merchant_id}",
            data={
                "filter_type": "api_filters",
                "filters": {},
                "dry_run": False,
                "only": ["tax_rates"],
            },
            authentication=self.get_merchant_token(merchant=merchant),
        )

        self.assertHttpAccepted(resp)
        self.assertTrue("/v1/async-action/" in resp["Location"])
        po.refresh_from_db()
        self.assertEqual(po.shipped_from_country.code, "BR")
        self.assertFalse(po.shipped_from_eu_vat_zone)
        self.assertEqual(po.shipped_from_region, "somewhere")
        self.assertEqual(
            po.tax_rates,
            [{"zone_key": ass.tax_zone.zone_key, "rate_key": ass.tax_rate.rate_key}],
        )

    def test_only_invalid_type(self):
        po = ProductOfferFactory()

        resp = self.api_client.post(
            f"/v1/productoffer/batch/{self.action_name}/?"
            f"merchant_id={po.merchant_id}",
            data={
                "filter_type": "api_filters",
                "filters": {},
                "dry_run": False,
                "only": "toto",
            },
            authentication=self.get_merchant_token(merchant=po.merchant),
        )

        self.assertHttpBadRequest(resp)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "field": "only",
                        "msg": ["Invalid parameter type. Expecting a string list"],
                    }
                ]
            },
        )

    def test_only_invalid_values(self):
        po = ProductOfferFactory()

        resp = self.api_client.post(
            f"/v1/productoffer/batch/{self.action_name}/?"
            f"merchant_id={po.merchant_id}",
            data={
                "filter_type": "api_filters",
                "filters": {},
                "dry_run": False,
                "only": ["value", "invalid"],
            },
            authentication=self.get_merchant_token(merchant=po.merchant),
        )

        self.assertHttpBadRequest(resp)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "field": "only",
                        "msg": [
                            "Invalid value received: invalid, value. "
                            "Allowed: shipped_from_country, "
                            "shipped_from_eu_vat_zone, "
                            "shipped_from_region, tax_rates"
                        ],
                    }
                ]
            },
        )

    @patch("apps.ice_applications.models.Application.trigger_on_active_channels")
    def test_reset_with_same_values_wont_save(self, channel_sync_mock):
        merchant = MerchantFactory(application__country__code="FR")
        ass = TaxRateAssignmentFactory(
            application=merchant.application, country__code="FR"
        )
        merchant.tax_settings.enabled_tax_zones = [ass.tax_zone.zone_key]
        merchant.tax_settings.default_shipped_from_country = ass.tax_zone.country
        merchant.tax_settings.default_shipped_from_eu_vat_zone = True
        merchant.tax_settings.default_shipped_from_region = "bla"
        merchant.tax_settings.default_offer_tax_rates = [
            {"zone_key": ass.tax_zone.zone_key, "rate_key": ass.tax_rate.rate_key}
        ]
        merchant.tax_settings.save()
        po = ProductOfferFactory.create_for_merchant(merchant)

        last_modified_before = po.last_modified
        resp = self.api_client.post(
            f"/v1/productoffer/batch/{self.action_name}/?"
            f"merchant_id={po.merchant_id}",
            data={
                "filter_type": "api_filters",
                "filters": {},
                "dry_run": False,
            },
            authentication=self.get_merchant_token(merchant=merchant),
        )

        self.assertHttpAccepted(resp)
        self.assertTrue("/v1/async-action/" in resp["Location"])
        po.refresh_from_db()
        self.assertEqual(po.last_modified, last_modified_before)
        channel_sync_mock.assert_not_called()

    @patch("apps.ice_applications.models.Application.trigger_on_active_channels")
    def test_channel_synced_on_save(self, channel_sync_mock):
        merchant = MerchantFactory(application__country__code="BR")
        po = ProductOfferFactory.create_for_merchant(
            merchant, shipped_from_eu_vat_zone=False
        )
        merchant.tax_settings.default_shipped_from_eu_vat_zone = True
        merchant.tax_settings.save()

        resp = self.api_client.post(
            f"/v1/productoffer/batch/{self.action_name}/?"
            f"merchant_id={po.merchant_id}",
            data={
                "filter_type": "api_filters",
                "filters": {},
                "dry_run": False,
            },
            authentication=self.get_merchant_token(merchant=merchant),
        )

        self.assertHttpAccepted(resp)
        channel_sync_mock.assert_called_once()


class BatchRemoveAllManuallyManagedFieldsTestCase(
    BaseResourceTestCase, BatchActionMixin
):
    action_name = "remove-manually-managed-fields"
    action_cls = ProductOfferBatchRemoveAllManuallyManagedFieldsAction

    def create_offers(self, count, **kwargs):
        return super(BatchRemoveAllManuallyManagedFieldsTestCase, self).create_offers(
            count, manually_managed_fields=["brand", "name"], **kwargs
        )

    def test_dry_run_false_api_filters_as_operateur(self):
        offer = self.create_offers(2)[0]

        resp = self.do_api_filters_request(False, [offer])

        self.assertEqual(resp.status_code, 202)
        self.assertEqual(resp.content, b"")
        self.assertIn("/v1/async-action/", resp["Location"])

        action = self.get_action()
        data = action.get_as_dict()
        self.assertIsNotNone(data.pop("created_on"))
        self.assertEqual(
            data,
            {
                "status": "in_queue",
                "ended_on": None,
                "total_offers": 1,
                "ignored_offers": 0,
                "errored_offers": 0,
                "action_name": self.action_cls.action_name,
                "modified_offers": 0,
                "lock_action": True,
                "id": action.id,
                "result": {},
            },
        )

    def test_dry_run_false_api_filters_as_merchant_without_moderation(self):
        offer = self.create_offers(2)[0]

        resp = self.do_api_filters_request(False, [offer], merchant=offer.merchant)

        self.assertEqual(resp.status_code, 202)
        self.assertEqual(resp.content, b"")
        self.assertIn("/v1/async-action/", resp["Location"])

        action = self.get_action()
        data = action.get_as_dict()
        self.assertIsNotNone(data.pop("created_on"))
        self.assertEqual(
            data,
            {
                "status": "in_queue",
                "ended_on": None,
                "total_offers": 1,
                "ignored_offers": 0,
                "errored_offers": 0,
                "action_name": self.action_cls.action_name,
                "modified_offers": 0,
                "lock_action": True,
                "id": action.id,
                "result": {},
            },
        )

    def test_dry_run_false_api_filters_as_merchant_with_moderation(self):
        offer = self.create_offers(2)[0]
        offer.application.set_setting("product_offer_moderation_enabled", True)
        resp = self.do_api_filters_request(False, [offer], merchant=offer.merchant)

        self.assertEqual(resp.status_code, 202)
        self.assertEqual(resp.content, b"")
        self.assertIn("/v1/async-action/", resp["Location"])

        action = self.get_action()
        data = action.get_as_dict()
        self.assertIsNotNone(data.pop("created_on"))
        self.assertEqual(
            data,
            {
                "status": "in_queue",
                "ended_on": None,
                "total_offers": 1,
                "ignored_offers": 0,
                "errored_offers": 0,
                "action_name": self.action_cls.action_name,
                "modified_offers": 0,
                "lock_action": True,
                "id": action.id,
                "result": {},
            },
        )

    def test_dry_run_true(self):
        offer = self.create_offers(1)[0]

        resp = self.do_api_filters_request(True, [offer])
        self.assertHttpOK(resp)
        self.assertEqual(
            self.deserialize(resp), {"product_offer_count": 1, "dry_run": True}
        )
        self.assertIsNone(self.get_action())

    def test_dry_run_not_set_defaults_true(self):
        offer = self.create_offers(1)[0]

        resp = self.do_api_filters_request(None, [offer])
        self.assertHttpOK(resp)
        self.assertEqual(
            self.deserialize(resp), {"product_offer_count": 1, "dry_run": True}
        )
        self.assertIsNone(self.get_action())

    def test_as_merchant(self):
        offer = self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/?merchant_id={}".format(
                self.action_name, offer.merchant_id
            ),
            data={
                "dry_run": True,
                "filters": {"id__in": [offer.id]},
                "filter_type": "api_filters",
            },
            authentication=self.get_merchant_token(merchant=offer.merchant),
        )
        self.assertHttpOK(resp)
        self.assertEqual(
            self.deserialize(resp), {"product_offer_count": 1, "dry_run": True}
        )
        self.assertIsNone(self.get_action())

    def test_as_izb_staff(self):
        offer = self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/?merchant_id={}".format(
                self.action_name, offer.merchant_id
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": [offer.id]},
                "filter_type": "api_filters",
            },
            authentication=self.get_staff_token(),
        )
        self.assertEqual(resp.status_code, 202)

    def test_unfiltered_request_as_izberg_staff(self):
        self.create_offers(1)

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(self.action_name),
            data={"dry_run": False, "filters": {}, "filter_type": "api_filters"},
            authentication=self.get_staff_token(),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertIsNone(self.get_action())
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Missing mandatory parameter"], "field": "merchant"}]},
        )

    def test_unfiltered_request_as_izberg_staff_with_merchant_ctx(self):
        offer = self.create_offers(1)[0]
        self.create_offers(1)

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/?merchant_id={}".format(
                self.action_name, offer.merchant_id
            ),
            data={"dry_run": False, "filters": {}, "filter_type": "api_filters"},
            authentication=self.get_staff_token(),
        )
        self.assertEqual(resp.status_code, 202)
        action = self.get_action()
        self.assertEqual(action.get_meta("total_offers"), 1)
        self.assertEqual(int(action.pop_id()), offer.id)

    def test_as_customer(self):
        offer = self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": [offer.id]},
                "filter_type": "api_filters",
            },
            authentication=self.get_app_user_token(application=offer.application),
        )
        self.assertEqual(resp.status_code, 401)

    def test_with_empty_queryset(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": []},
                "filter_type": "api_filters",
            },
            authentication=self.get_operator_token(application=application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": [
                            "Trying to batch-update 0 item. Please check your "
                            "filters."
                        ],
                        "field": "__all__",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_invalid_api_filters(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"invalid": "value"},
                "filter_type": "api_filters",
            },
            authentication=self.get_operator_token(application=application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": ["The 'invalid' field does not allow filtering."],
                        "field": "__all__",
                    }
                ]
            },
        )

    def test_empty_queryset(self):
        application = ApplicationFactory()

        resp = self.do_api_filters_request(False, [], application=application)
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": [
                            "Trying to batch-update 0 item. Please check your "
                            "filters."
                        ],
                        "field": "__all__",
                    }
                ]
            },
        )

    def test_not_owner_of_ids(self):
        offer = self.create_offers(1)[0]
        application = ApplicationFactory()

        resp = self.do_api_filters_request(False, [offer], application=application)
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": [
                            "Trying to batch-update 0 item. Please check your "
                            "filters."
                        ],
                        "field": "__all__",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_with_empty_body(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(self.action_name),
            data={},
            authentication=self.get_operator_token(application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Missing mandatory key"], "field": "filter_type"}]},
        )
        self.assertIsNone(self.get_action())

    def test_invalid_api_filter_value(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(self.action_name),
            data={
                "dry_run": False,
                "filters": {"id__in": ["blablabla"]},
                "filter_type": "api_filters",
            },
            authentication=self.get_operator_token(application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Invalid filters provided"], "field": "filters"}]},
        )
        self.assertIsNone(self.get_action())

    def test_dry_run_true_as_pim_write(self):
        merchant2 = MerchantFactory(id=2, application__id=1)
        merchant3 = MerchantFactory(
            id=3,
            application=merchant2.application,
            default_currency=merchant2.default_currency,
        )
        offer2 = self.create_offers(
            1,
            merchant=merchant2,
            application=merchant2.application,
            currency=merchant2.default_currency,
        )[0]
        # other offer
        offer3 = self.create_offers(
            1,
            merchant=merchant3,
            application=merchant3.application,
            currency=merchant3.default_currency,
        )[0]

        resp = self.do_api_filters_request(
            dry_run=True,
            offers=[offer2, offer3],
            token="Bearer {}".format(PIM_TOKENS["APPLICATION_1_WRITE"]["token"]),
        )
        self.assertHttpOK(resp)
        self.assertEqual(
            self.deserialize(resp), {"product_offer_count": 2, "dry_run": True}
        )
        self.assertIsNone(self.get_action())

    def test_dry_run_true_as_merchant_pim_write_only_applies_on_its_offer(self):
        merchant2 = MerchantFactory(id=2, application__id=1)
        merchant3 = MerchantFactory(
            id=3,
            application=merchant2.application,
            default_currency=merchant2.default_currency,
        )
        offer2 = self.create_offers(
            1,
            merchant=merchant2,
            application=merchant2.application,
            currency=merchant2.default_currency,
        )[0]
        # other offer
        offer3 = self.create_offers(
            1,
            merchant=merchant3,
            application=merchant3.application,
            currency=merchant3.default_currency,
        )[0]

        resp = self.do_api_filters_request(
            dry_run=True,
            offers=[offer2, offer3],
            token="Bearer {}".format(
                PIM_TOKENS["APPLICATION_1_MERCHANT_2_WRITE"]["token"]
            ),
        )
        self.assertHttpOK(resp)
        self.assertEqual(
            self.deserialize(resp), {"product_offer_count": 1, "dry_run": True}
        )
        self.assertIsNone(self.get_action())


class BatchUpdateStockFieldTestCase(BaseResourceTestCase, BatchActionMixin):
    action_name = "update-stock"
    action_cls = ProductOfferBatchUpdateStockFieldAction

    def create_offers(self, count, **kwargs):
        return super(BatchUpdateStockFieldTestCase, self).create_offers(
            count, stock=10, **kwargs
        )

    def api_filters_request(
        self,
        offers,
        new_stock,
        dry_run=None,
        token=None,
        application=None,
        merchant=None,
    ):
        data = {
            "filters": {"id__in": [_offer.id for _offer in offers]},
            "filter_type": "api_filters",
            "new_stock": new_stock,
        }
        if dry_run is not None:
            data["dry_run"] = dry_run
        if not application and offers:
            application = offers[0].application

        path = "/v1/productoffer/batch/{}/".format(self.action_name)
        if not token:
            if merchant:
                token = self.get_merchant_token(merchant=merchant)
                path += "?merchant_id={}".format(merchant.id)
            else:
                token = self.get_operator_token(application)
        return self.api_client.post(path, data=data, authentication=token)

    def test_dry_run_false_api_filters_as_operateur(self):
        offer = self.create_offers(2)[0]

        resp = self.api_filters_request([offer], 20, dry_run=False)

        self.assertEqual(resp.status_code, 202)
        self.assertEqual(resp.content, b"")
        self.assertIn("/v1/async-action/", resp["Location"])

        action = self.get_action()
        data = action.get_as_dict()
        self.assertIsNotNone(data.pop("created_on"))
        self.assertEqual(
            data,
            {
                "status": "in_queue",
                "ended_on": None,
                "total_offers": 1,
                "ignored_offers": 0,
                "errored_offers": 0,
                "action_name": self.action_cls.action_name,
                "modified_offers": 0,
                "lock_action": True,
                "id": action.id,
                "result": {},
                "new_stock": 20,
            },
        )

    def test_dry_run_false_api_filters_as_merchant_without_moderation(self):
        offer = self.create_offers(2)[0]

        resp = self.api_filters_request(
            [offer], 20, dry_run=False, merchant=offer.merchant
        )

        self.assertEqual(resp.status_code, 202)
        self.assertEqual(resp.content, b"")
        self.assertIn("/v1/async-action/", resp["Location"])

        action = self.get_action()
        data = action.get_as_dict()
        self.assertIsNotNone(data.pop("created_on"))
        self.assertEqual(
            data,
            {
                "status": "in_queue",
                "ended_on": None,
                "total_offers": 1,
                "ignored_offers": 0,
                "errored_offers": 0,
                "action_name": self.action_cls.action_name,
                "modified_offers": 0,
                "lock_action": True,
                "id": action.id,
                "result": {},
                "new_stock": 20,
            },
        )

    def test_dry_run_false_api_filters_as_merchant_with_moderation(self):
        offer = self.create_offers(2)[0]
        offer.application.set_setting("product_offer_moderation_enabled", True)
        resp = self.api_filters_request(
            [offer], 20, dry_run=False, merchant=offer.merchant
        )

        self.assertEqual(resp.status_code, 202)
        self.assertEqual(resp.content, b"")
        self.assertIn("/v1/async-action/", resp["Location"])

        action = self.get_action()
        data = action.get_as_dict()
        self.assertIsNotNone(data.pop("created_on"))
        self.assertEqual(
            data,
            {
                "status": "in_queue",
                "ended_on": None,
                "total_offers": 1,
                "ignored_offers": 0,
                "errored_offers": 0,
                "action_name": self.action_cls.action_name,
                "modified_offers": 0,
                "lock_action": True,
                "id": action.id,
                "result": {},
                "new_stock": 20,
            },
        )

    def test_dry_run_true(self):
        offer = self.create_offers(1)[0]

        resp = self.api_filters_request([offer], 20, dry_run=True)
        self.assertHttpOK(resp)
        self.assertEqual(
            self.deserialize(resp), {"product_offer_count": 1, "dry_run": True}
        )
        self.assertIsNone(self.get_action())

    def test_dry_run_not_set_defaults_true(self):
        offer = self.create_offers(1)[0]

        resp = self.api_filters_request([offer], 20)
        self.assertHttpOK(resp)
        self.assertEqual(
            self.deserialize(resp), {"product_offer_count": 1, "dry_run": True}
        )
        self.assertIsNone(self.get_action())

    def test_as_merchant(self):
        offer = self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/?merchant_id={}".format(
                self.action_name, offer.merchant_id
            ),
            data={
                "dry_run": True,
                "filters": {"id__in": [offer.id]},
                "filter_type": "api_filters",
                "new_stock": 20,
            },
            authentication=self.get_merchant_token(merchant=offer.merchant),
        )
        self.assertHttpOK(resp)
        self.assertEqual(
            self.deserialize(resp), {"product_offer_count": 1, "dry_run": True}
        )
        self.assertIsNone(self.get_action())

    def test_as_izb_staff(self):
        offer = self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/?merchant_id={}".format(
                self.action_name, offer.merchant_id
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": [offer.id]},
                "filter_type": "api_filters",
                "new_stock": 20,
            },
            authentication=self.get_staff_token(),
        )
        self.assertEqual(resp.status_code, 202)

    def test_unfiltered_request_as_izberg_staff(self):
        self.create_offers(1)

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(self.action_name),
            data={
                "dry_run": False,
                "filters": {},
                "filter_type": "api_filters",
                "new_stock": 20,
            },
            authentication=self.get_staff_token(),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertIsNone(self.get_action())
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Missing mandatory parameter"], "field": "merchant"}]},
        )

    def test_unfiltered_request_as_izberg_staff_with_merchant_ctx(self):
        offer = self.create_offers(1)[0]
        self.create_offers(1)

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/?merchant_id={}".format(
                self.action_name, offer.merchant_id
            ),
            data={
                "dry_run": False,
                "filters": {},
                "filter_type": "api_filters",
                "new_stock": 20,
            },
            authentication=self.get_staff_token(),
        )
        self.assertEqual(resp.status_code, 202)
        action = self.get_action()
        self.assertEqual(action.get_meta("total_offers"), 1)
        self.assertEqual(int(action.pop_id()), offer.id)

    def test_as_customer(self):
        offer = self.create_offers(1)[0]

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"id__in": [offer.id]},
                "filter_type": "api_filters",
                "new_stock": 20,
            },
            authentication=self.get_app_user_token(application=offer.application),
        )
        self.assertEqual(resp.status_code, 401)

    def test_invalid_api_filters(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(
                self.action_name,
            ),
            data={
                "dry_run": False,
                "filters": {"invalid": "value"},
                "filter_type": "api_filters",
                "new_stock": 20,
            },
            authentication=self.get_operator_token(application=application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": ["The 'invalid' field does not allow filtering."],
                        "field": "__all__",
                    }
                ]
            },
        )

    def test_empty_queryset(self):
        application = ApplicationFactory()

        resp = self.api_filters_request([], 20, dry_run=False, application=application)
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": [
                            "Trying to batch-update 0 item. Please check your "
                            "filters."
                        ],
                        "field": "__all__",
                    }
                ]
            },
        )

    def test_not_owner_of_ids(self):
        offer = self.create_offers(1)[0]
        application = ApplicationFactory()

        resp = self.api_filters_request(
            [offer], 20, dry_run=False, application=application
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "msg": [
                            "Trying to batch-update 0 item. Please check your "
                            "filters."
                        ],
                        "field": "__all__",
                    }
                ]
            },
        )
        self.assertIsNone(self.get_action())

    def test_with_empty_body(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(self.action_name),
            data={},
            authentication=self.get_operator_token(application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Missing mandatory key"], "field": "new_stock"}]},
        )
        self.assertIsNone(self.get_action())

    def test_with_empty_new_stock(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(self.action_name),
            data={},
            authentication=self.get_operator_token(application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Missing mandatory key"], "field": "new_stock"}]},
        )
        self.assertIsNone(self.get_action())

    def test_invalid_api_filter_value(self):
        application = ApplicationFactory()

        resp = self.api_client.post(
            "/v1/productoffer/batch/{}/".format(self.action_name),
            data={
                "dry_run": False,
                "filters": {"id__in": ["blablabla"]},
                "filter_type": "api_filters",
                "new_stock": 20,
            },
            authentication=self.get_operator_token(application),
        )
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            self.deserialize(resp),
            {"errors": [{"msg": ["Invalid filters provided"], "field": "filters"}]},
        )
        self.assertIsNone(self.get_action())

    def test_dry_run_true_as_pim_write(self):
        merchant2 = MerchantFactory(id=2, application__id=1)
        merchant3 = MerchantFactory(
            id=3,
            application=merchant2.application,
            default_currency=merchant2.default_currency,
        )
        offer2 = self.create_offers(
            1,
            merchant=merchant2,
            application=merchant2.application,
            currency=merchant2.default_currency,
        )[0]
        # other offer
        offer3 = self.create_offers(
            1,
            merchant=merchant3,
            application=merchant3.application,
            currency=merchant3.default_currency,
        )[0]

        resp = self.api_filters_request(
            dry_run=True,
            offers=[offer2, offer3],
            new_stock=20,
            token="Bearer {}".format(PIM_TOKENS["APPLICATION_1_WRITE"]["token"]),
        )
        self.assertHttpOK(resp)
        self.assertEqual(
            self.deserialize(resp), {"product_offer_count": 2, "dry_run": True}
        )
        self.assertIsNone(self.get_action())

    def test_dry_run_true_as_merchant_pim_write_only_applies_on_its_offer(self):
        merchant2 = MerchantFactory(id=2, application__id=1)
        merchant3 = MerchantFactory(
            id=3,
            application=merchant2.application,
            default_currency=merchant2.default_currency,
        )
        offer2 = self.create_offers(
            1,
            merchant=merchant2,
            application=merchant2.application,
            currency=merchant2.default_currency,
        )[0]
        # other offer
        offer3 = self.create_offers(
            1,
            merchant=merchant3,
            application=merchant3.application,
            currency=merchant3.default_currency,
        )[0]

        resp = self.api_filters_request(
            dry_run=True,
            offers=[offer2, offer3],
            new_stock=20,
            token="Bearer {}".format(
                PIM_TOKENS["APPLICATION_1_MERCHANT_2_WRITE"]["token"]
            ),
        )
        self.assertHttpOK(resp)
        self.assertEqual(
            self.deserialize(resp), {"product_offer_count": 1, "dry_run": True}
        )
        self.assertIsNone(self.get_action())
