# -*- coding: utf-8 -*-

import logging
from decimal import Decimal

import xworkflows
from apps.cart_notifications.models.mixins import CartNotificationPromotionMixin
from apps.currencies.models import Currency
from apps.ice_applications.app_conf_settings import EnableDiscountEngine
from apps.promotions.tasks import update_discount_use_count
from apps.tax.exceptions import NoTaxRateApplicable
from constance import config as live_config
from django.conf import settings
from django.core.cache import cache
from django.core.exceptions import ValidationError
from django.core.validators import MaxV<PERSON>ueValidator, MinValueValidator
from django.db import models
from django.db.models import Q
from django.utils import timezone
from django.utils.encoding import force_str
from django.utils.translation import gettext_lazy as _
from django_xworkflows import models as xwf_models
from ims.fields import IzbergForeignKey
from ims.models.mixin import ChangedFieldLookupMixin, SanitizeCharFieldMixin
from ims.workflow import IzbergWorkflow
from lib.models import ExternalIDModelMixin, IzbergQueryset, PostgresIndex
from redis import OutOfMemoryError

from ..reference import DISCOUNT_MANAGEMENT_IZBERG

__all__ = ("Discount", "DiscountUse")

logger = logging.getLogger(__name__)

User = settings.AUTH_USER_MODEL


class DiscountQueryset(IzbergQueryset):
    def actives(self, **kwargs):
        """
        Active discounts (ACTIVE or MODIFIED) at current time
        """
        now = timezone.now()
        return (
            self.filter(status__in=[Discount.ACTIVE, Discount.MODIFIED])
            .filter_or_none(start_date__lt=now)
            .filter_or_none(end_date__gt=now)
            .filter(**kwargs)
        )

    def filter_upcoming(self, **kwargs):
        return self.filter(
            start_date__gt=timezone.now(),
            status__in=[Discount.ACTIVE, Discount.MODIFIED],
        ).filter(**kwargs)

    def merchant_or_none(self, merchant):
        """
        Filter discounts with 'merchant' set to given merchant or set to None
        """
        return self.filter(Q(merchant=merchant) | Q(merchant=None))

    def filter_no_code(self, **kwargs):
        return self.exclude(discount_code__gt="", **kwargs)

    def filter_with_code(self, **kwargs):
        return self.filter(discount_code__gt="", **kwargs)

    def actives_no_code(self, **kwargs):
        """
        [DEPRECATED] Use CurrentlyActiveDiscountManager instead
        Active discounts (ACTIVE or MODIFIED) at current time that
        dont require a discount_code to be applied
        """
        return self.actives(**kwargs).exclude(discount_code__gt="")

    def exclude_terminated_deleted(self):
        """Excludes TERMINATED and DELETED discounts from queryset"""
        return self.exclude(status__in=[Discount.TERMINATED, Discount.DELETED])

    def exclude_deleted(self):
        """Excludes DELETED discounts from queryset"""
        return self.exclude(status=Discount.DELETED)


DiscountManager = DiscountQueryset.as_manager


class DiscountTransitionLog(xwf_models.GenericTransitionLog):
    discount = IzbergForeignKey(
        "promotions.Discount", db_index=False, on_delete=models.CASCADE
    )
    user = IzbergForeignKey(
        settings.AUTH_USER_MODEL,
        blank=True,
        null=True,
        db_index=False,
        on_delete=models.CASCADE,
    )
    comment = models.CharField(max_length=255, blank=True, null=True)

    MODIFIED_OBJECT_FIELD = "discount"
    EXTRA_LOG_ATTRIBUTES = (("user", "user", None), ("comment", "comment", None))


class DiscountWorkflow(IzbergWorkflow):
    log_model = "promotions.DiscountTransitionLog"
    log_model_class = DiscountTransitionLog

    DRAFT = "draft"
    ACTIVE = "active"
    MODIFIED = "modified"
    PAUSED = "paused"
    DELETED = "deleted"
    TERMINATED = "terminated"

    states = (
        (DRAFT, _("Draft")),
        (ACTIVE, _("Active")),
        (MODIFIED, _("Modified")),
        (TERMINATED, _("Terminated")),
        (PAUSED, _("Paused")),
        (DELETED, _("Deleted")),
    )

    transitions = (
        ("activate", [DRAFT, MODIFIED, PAUSED], ACTIVE),
        ("modified", [ACTIVE], MODIFIED),
        ("pause", [ACTIVE, MODIFIED], PAUSED),
        ("terminate", [ACTIVE, MODIFIED, PAUSED], TERMINATED),
        ("delete_action", [DRAFT, PAUSED, TERMINATED], DELETED),
    )

    initial_state = DRAFT


class Discount(
    xwf_models.WorkflowEnabled,
    CartNotificationPromotionMixin,
    SanitizeCharFieldMixin,
    ChangedFieldLookupMixin,
    ExternalIDModelMixin,
):
    """Discount model creatable by an operator or a merchant"""

    objects = DiscountManager()

    merchant = models.ForeignKey(
        "stores.Merchant",
        null=True,
        blank=True,
        related_name="discounts",
        on_delete=models.CASCADE,
        db_index=False,
    )

    application = models.ForeignKey(
        "ice_applications.Application",
        null=True,
        blank=False,
        related_name="discounts",
        on_delete=models.CASCADE,
        db_index=False,
    )

    name = models.CharField(max_length=255, help_text=_("the name of the discount"))

    start_date = models.DateTimeField(
        help_text=_("the date the discount starts"),
        null=True,
        blank=True,
    )
    end_date = models.DateTimeField(
        help_text=_("the date the discount ends"),
        null=True,
        blank=True,
    )

    status = xwf_models.StateField(DiscountWorkflow)

    currency = models.ForeignKey(
        Currency,
        default="EUR",
        related_name="discounts",
        help_text=_("which type of currency the discount is using"),
        on_delete=models.CASCADE,
        db_index=False,
    )

    min_quantity = models.PositiveIntegerField(
        default=1,
        help_text=_(
            "the minimum number of items that must "
            "be purchased for the discount to apply"
        ),
    )
    max_quantity = models.PositiveIntegerField(
        blank=True,
        null=True,
        help_text=_(
            "the maximum number of items that must "
            "be purchased for the discount to apply"
        ),
    )

    PERCENTAGE = 0
    AMOUNT = 10
    FIXED_PRICE = 20
    FREE_SHIPPING = 30
    VOUCHER = 40
    REDUCTION_TYPE_CHOICES = (
        (PERCENTAGE, _("In Percentage based on original product price")),
        (AMOUNT, _("In Amount based on original product price (VAT included)")),
        (
            FIXED_PRICE,
            _("The given value is the discounted product price (VAT included)"),
        ),
        (FREE_SHIPPING, _("Shipping will be free")),
        (VOUCHER, _("Voucher applied to a global cart/order")),
    )
    # these types of reduction are applied to a product offer
    PRODUCT_OFFER_BASED = [PERCENTAGE, AMOUNT, FIXED_PRICE]
    # these types of reduction are applied to a cart / merchant order
    MERCHANT_ORDER_BASED = [FREE_SHIPPING, VOUCHER]

    DECIMAL_VALUE_TYPES = [AMOUNT, FIXED_PRICE, VOUCHER]
    PERCENTAGE_VALUE_TYPES = [PERCENTAGE]
    reduction_type = models.PositiveSmallIntegerField(
        _("Reduction type"),
        choices=REDUCTION_TYPE_CHOICES,
    )

    reduction_value = models.CharField(
        _("Reduction value"),
        max_length=255,
        help_text=_(
            "The percentage/exact amount/fixed price "
            "(depending on the reduction_type) - Unused for free shipping "
            "discounts"
        ),
        blank=True,
        null=True,
        default=None,
    )

    SEASONAL_SALE = "seasonal_sale"
    FLASH_SALE = "flash_sale"
    PRIVATE_SALE = "private_sale"
    MARKETING = "marketing"
    OPERATION_TYPE_CHOICES = (
        (SEASONAL_SALE, _("Seasonal Sale")),
        (FLASH_SALE, _("Flash Sale")),
        (PRIVATE_SALE, _("Private Sale")),
        (MARKETING, _("Marketing operation")),
    )

    operation_type = models.CharField(
        _("Operation type"),
        max_length=32,
        blank=True,
        null=True,
        choices=OPERATION_TYPE_CHOICES,
    )

    product_families = models.ManyToManyField(
        "products.ProductFamily", related_name="discounts", blank=True
    )

    # CONTEXT CONDITIONS
    eligible_customer_tax_groups = models.ManyToManyField(
        "tax.CustomerTaxGroup",
        related_name="discounts",
        blank=True,
        help_text=_("[DEPRECATED] Limit to customer tax groups"),
    )

    zone = models.ForeignKey(
        "address.Zone",
        help_text=_("what zone the discount applies to"),
        null=True,
        blank=True,
        related_name="discounts",
        on_delete=models.CASCADE,
        db_index=False,
    )
    country = models.ForeignKey(
        "address.Country",
        help_text=_("what country the discount applies to"),
        null=True,
        blank=True,
        related_name="discounts",
        on_delete=models.CASCADE,
        db_index=False,
    )

    eligible_users = models.ManyToManyField(
        settings.AUTH_USER_MODEL, related_name="eligible_discounts", blank=True
    )

    minimum_price = models.DecimalField(
        max_digits=20,
        decimal_places=6,
        null=True,
        blank=True,
        validators=[MinValueValidator(Decimal("0.00"))],
    )
    maximum_price = models.DecimalField(
        max_digits=20,
        decimal_places=6,
        null=True,
        blank=True,
        validators=[MinValueValidator(Decimal("0.00"))],
    )

    financed_by_application = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal("0.0"),
        validators=[
            MinValueValidator(Decimal("0.00")),
            MaxValueValidator(Decimal("100.00")),
        ],
        help_text=_(
            "Percentage of the discount taken on application "
            "commission (0 = all on merchant, 100 = all on application)"
        ),
    )

    internal_note = models.CharField(
        _("Internal note"), max_length=512, blank=True, null=True
    )

    # Code options
    discount_code = models.CharField(
        _("If set, discount code to enter to apply the discount"),
        max_length=255,
        blank=True,
        null=True,
        default="",
    )

    COMBINABLE, EXCLUSIVE = (0, 10)
    COMBINABILITY_CHOICES = ((EXCLUSIVE, _("Exclusive (not combinable)")),)
    combinability = models.PositiveSmallIntegerField(
        choices=COMBINABILITY_CHOICES,
        default=EXCLUSIVE,
        help_text=_("Combinability of the discount"),
    )

    use_count = models.PositiveIntegerField(default=0)
    max_use_count = models.PositiveIntegerField(blank=True, null=True)
    max_use_count_per_user = models.PositiveIntegerField(blank=True, null=True)

    priority = models.PositiveSmallIntegerField(
        _("The applied discount is the one with the highest priority"),
        default=100,
    )

    shipping_providers = models.ManyToManyField(
        "shipping2.ShippingProvider", related_name="discounts", blank=True
    )
    USE_COUNT_BASED_ON_ORDER_ITEMS = "order_items"
    USE_COUNT_BASED_ON_MERCHANT_ORDERS = "merchant_orders"
    USE_COUNT_BASED_ON_ORDERS = "orders"
    USE_COUNT_BASED_ON_CHOICES = (
        (USE_COUNT_BASED_ON_ORDER_ITEMS, _("Items")),
        (USE_COUNT_BASED_ON_MERCHANT_ORDERS, _("Merchant Orders")),
        (USE_COUNT_BASED_ON_ORDERS, _("Orders")),
    )
    use_count_based_on = models.CharField(
        help_text=_("On which entities the use count is based"),
        max_length=32,
        choices=USE_COUNT_BASED_ON_CHOICES,
        default=USE_COUNT_BASED_ON_ORDERS,
    )

    """ Statuses and field lists """
    DRAFT = DiscountWorkflow.DRAFT
    ACTIVE = DiscountWorkflow.ACTIVE
    MODIFIED = DiscountWorkflow.MODIFIED
    PAUSED = DiscountWorkflow.PAUSED
    DELETED = DiscountWorkflow.DELETED
    TERMINATED = DiscountWorkflow.TERMINATED

    CREATABLE_FIELDS = ["merchant", "application", "currency"]
    EDITABLE_FIELDS = [
        "name",
        "start_date",
        "end_date",
        "min_quantity",
        "max_quantity",
        "reduction_type",
        "reduction_value",
        "external_id",
        "zone",
        "country",
        "eligible_customer_tax_groups",
        "product_families",
        "maximum_price",
        "minimum_price",
        "internal_note",
        "discount_code",
        "combinability",
        "max_use_count",
        "max_use_count_per_user",
        "financed_by_application",  # but only for app
        "priority",
        "use_count_based_on",
        "eligible_users",
        "operation_type",
        "shipping_providers",
    ]

    class Meta:
        indexes = [
            PostgresIndex(
                fields=["operation_type"],
                name="promotions_discount_operation_type_oidx",
                opclasses=["varchar_pattern_ops"],
            ),
            PostgresIndex(
                fields=["application_id", "currency_id", "start_date", "end_date"],
                name="promotions_discount_a_id__c_id__sd__ed_idx",
            ),
        ]

    # """ Overwritten functions """

    def __str__(self):
        return f"Discount[{self.id}]{' ' + self.name if self.name else ''}"

    def _clean_reduction_value(self):
        """
        clean reduction_value based on reduction_type
        """
        try:
            under_100_validator = MaxValueValidator(Decimal("100.00"))
            over_0_validator = MinValueValidator(Decimal("0.00"))
            if self.reduction_type == self.FREE_SHIPPING:
                self.reduction_value = None
            elif self.reduction_type in self.DECIMAL_VALUE_TYPES:
                self.reduction_value = models.DecimalField(
                    validators=[over_0_validator]
                ).clean(self.reduction_value, self)
            elif self.reduction_type in self.PERCENTAGE_VALUE_TYPES:
                self.reduction_value = models.DecimalField(
                    validators=[over_0_validator, under_100_validator]
                ).clean(self.reduction_value, self)
        except ValidationError as err:
            raise ValidationError({"reduction_value": err.messages}) from err

    def _clean_voucher_type(self):
        """Specific checks for VOUCHER type"""
        if not self.discount_code:
            raise ValidationError(
                {
                    "discount_code": [
                        _("A discount code is required for the reduction type %s")
                        % self.get_reduction_type_display()
                    ]
                }
            )
        if self.financed_by_application != Decimal("100"):
            raise ValidationError(
                {
                    "financed_by_application": [
                        _("Voucher can only be financed operator")
                    ]
                }
            )
        if self.merchant_id is not None:
            raise ValidationError(
                {"merchant": [_("Voucher cannot belong to a merchant")]}
            )
        if self.use_count_based_on != self.USE_COUNT_BASED_ON_ORDERS:
            raise ValidationError(
                {
                    "use_count_based_on": [
                        _("Voucher use count can only be done on orders")
                    ]
                }
            )

    def _clean_discount_code(self):
        """
        discount_code should be unique for a specific app
        and a specific priority (excluding terminated/deleted discounts)
        """
        if self.discount_code in ["", None]:
            return
        if self.status in [Discount.TERMINATED, Discount.DELETED]:
            # no conflict check on done/deleted discounts
            return
        conflicting_discounts_exists = (
            Discount.objects.exclude_terminated_deleted()
            .filter(
                application=self.application,
                discount_code=self.discount_code,
                priority=self.priority,
            )
            .exclude(id=self.id)
            .exists()
        )
        if conflicting_discounts_exists:
            raise ValidationError(
                {
                    "discount_code": [
                        _(
                            "A discount with code '%s' and priority %s"
                            " already exists for application %s"
                        )
                        % (
                            force_str(self.discount_code),
                            self.priority,
                            self.application_id,
                        )
                    ]
                }
            )

    def _clean_dates(self):
        """checks start_date is anterior to end_date if set"""
        invalid_dates = (
            self.start_date and self.end_date and self.start_date >= self.end_date
        )
        if invalid_dates:
            raise ValidationError({"end_date": _("Should be posterior to start date")})

    def _clean_operation_type(self):
        if self.operation_type in [self.SEASONAL_SALE, self.FLASH_SALE]:
            self._clean_mandatory_dates()

    def _clean_mandatory_dates(self):
        """check that start_date and end_date are set"""
        errors = {}
        if not self.start_date:
            errors["start_date"] = _("Empty value")
        if not self.end_date:
            errors["end_date"] = _("Empty value")
        if errors:
            raise ValidationError(errors)

    def clean(self):
        self._clean_reduction_value()
        self._clean_dates()
        self._clean_discount_code()
        self._clean_operation_type()
        if self.reduction_type == self.VOUCHER:
            self._clean_voucher_type()
        # TODO if self.FREE_SHIPPING check if reduction_value in standard,...
        self._update_use_count()

    def save(self, *args, **kwargs):
        self.full_clean()
        if self.id is None:
            self._check_discount_engine_is_enabled()
        super(Discount, self).save(*args, **kwargs)

    """ Properties """

    @property
    def is_combinable(self):
        return self.combinability == self.COMBINABLE

    @property
    def is_active(self):
        # TODO: rename this confusing property
        return self.status in [self.ACTIVE, self.MODIFIED]

    """ Private Functions """

    def _check_discount_engine_is_enabled(self):
        if not self.application.get_setting(EnableDiscountEngine):
            raise ValidationError(
                {"application": _("Discounts are not enabled on your marketplace.")}
            )

    def _compute_use_count_based_on_merchant_orders(
        self, user=None, merchant_order=None, cart=None, merchant=None, **extra_context
    ):
        """
        Use count is based on non canceled merchant order based DicountUses.
        If user and cart in context and no merchant_order, will check the cart_based
        discount_uses and count the distinct tuples (cart, merchant)
        (because that corresponds to the number of future merchant_orders)
        """
        if user and cart and not merchant_order:
            """Looking at cart based discount_uses"""
            discount_uses = self.discount_uses.cart_based(user=user).exclude(
                status=DiscountUseWorkflow.CANCELLED
            )
            if merchant:  # exclude discount_uses linked to merchant
                discount_uses = discount_uses.exclude(
                    Q(merchant=merchant, cart=cart)
                    | Q(cart_item__merchant=merchant, cart_item__cart=cart)
                )
            else:
                discount_uses = discount_uses.exclude(
                    Q(cart=cart) | Q(cart_item__cart=cart)
                )
            if self.is_merchant_order_based():
                return (
                    discount_uses.exclude(cart=None)
                    .values("cart", "merchant")
                    .distinct()
                    .count()
                )
            elif self.is_product_offer_based():
                return (
                    discount_uses.exclude(cart_item=None)
                    .values("cart_item__cart", "cart_item__merchant")
                    .distinct()
                    .count()
                )
            else:
                raise Exception("Cant count uses for %s" % self)
        else:
            """Looking at order based discount_uses"""
            discount_uses = self.discount_uses.order_based().exclude(
                status=DiscountUseWorkflow.CANCELLED
            )
            if user:  # limiting count for given user
                discount_uses = discount_uses.filter(user=user)
            if merchant_order:  # exclude discount_uses linked to merchant_order
                discount_uses = discount_uses.exclude(
                    Q(merchant_order=merchant_order)
                    | Q(order_item__merchant_order=merchant_order)
                )
            if self.is_merchant_order_based():
                return (
                    discount_uses.exclude(merchant_order=None)
                    .values("merchant_order")
                    .distinct()
                    .count()
                )
            elif self.is_product_offer_based():
                return (
                    discount_uses.exclude(order_item__merchant_order=None)
                    .values("order_item__merchant_order")
                    .distinct()
                    .count()
                )
            else:
                raise Exception("Cant count uses for %s" % self)

    def _compute_use_count_based_on_orders(
        self, user=None, order=None, **extra_context
    ):
        """
        Use count is based on non canceled order based DicountUses (not counting the
        one linked on Cart/CartItem)
        NB: count the number of distinct orders, not the number order items
        """
        if self.reduction_type == self.VOUCHER:
            # special count for voucher
            return self._compute_voucher_use_count_based_on_orders(
                user=user, order=order, **extra_context
            )
        discount_uses = self.discount_uses.order_based().exclude(
            status=DiscountUseWorkflow.CANCELLED
        )
        if user:  # limiting count for given user
            discount_uses = discount_uses.filter(user=user)
        if order:  # exclude discount_uses linked to order
            discount_uses = discount_uses.exclude(
                Q(merchant_order__order=order)
                | Q(order_item__merchant_order__order=order)
            )
        if self.is_merchant_order_based():
            return (
                discount_uses.exclude(merchant_order__order=None)
                .values("merchant_order__order")
                .distinct()
                .count()
            )
        elif self.is_product_offer_based():
            return (
                discount_uses.exclude(order_item__merchant_order__order=None)
                .values("order_item__merchant_order__order")
                .distinct()
                .count()
            )
        else:
            raise Exception("Cant count uses for %s" % self)

    def _compute_use_count_based_on_order_items(
        self, user=None, order_item=None, cart_item=None, **extra_context
    ):
        """
        Use count is based on non canceled order based DicountUses (not counting the
        one linked on Cart/CartItem)
        """
        if user and cart_item and not order_item:
            discount_uses = self.discount_uses.cart_based(user=user).exclude(
                status=DiscountUseWorkflow.CANCELLED
            )
            discount_uses = discount_uses.exclude(cart_item=cart_item)
            if self.is_merchant_order_based():
                return (
                    discount_uses.exclude(merchant_order__order_items=None)
                    .values("merchant_order__order_items")
                    .count()
                )
            elif self.is_product_offer_based():
                return discount_uses.exclude(cart_item=None).count()
            else:
                raise Exception("Cant count uses for %s" % self)
        else:
            discount_uses = self.discount_uses.order_based().exclude(
                status=DiscountUseWorkflow.CANCELLED
            )
            if user:  # limiting count for given user
                discount_uses = discount_uses.filter(user=user)
            if order_item:  # exclude discount_uses linked to order_item
                discount_uses = discount_uses.exclude(Q(order_item=order_item))
            if self.is_merchant_order_based():
                return (
                    discount_uses.exclude(merchant_order__order_items=None)
                    .values("merchant_order__order_items")
                    .count()
                )
            elif self.is_product_offer_based():
                return discount_uses.exclude(order_item=None).count()
            else:
                raise Exception("Cant count uses for %s" % self)

    def _compute_voucher_use_count_based_on_orders(
        self, user=None, order=None, cart=None, exclude_cart_uses=False, **extra_context
    ):
        """
        use_count specific to voucher.
        voucher is always based on order (see _clean_voucher_type)
        """
        discount_uses = self.discount_uses.exclude(status=DiscountUseWorkflow.CANCELLED)
        if user:  # limiting count for given user
            discount_uses = discount_uses.filter(user=user)
        if order:  # exclude discount_uses linked to order
            discount_uses = discount_uses.exclude(order=order)
        if cart and exclude_cart_uses:
            discount_uses = discount_uses.exclude(cart=cart)
        return (discount_uses.values("order").distinct()).count()

    def _update_use_count(self, save_if_changed=False):
        """Updates the use_count and save it if changed"""
        old_count = self.use_count
        self.use_count = self.get_use_count()
        if self.use_count != old_count:
            logger.info(
                "Discount[%s].use_count updated from %s to %s",
                self.id,
                old_count,
                self.use_count,
            )
            if save_if_changed:
                self.save()
        return self.use_count

    """ Public Functions """

    def to_dict(self):
        data = {
            "id": self.id,
            "reduction_type": self.reduction_type,
            "reduction_value": self.reduction_value,
            "discount_code": self.discount_code,
            "name": self.name,
            "start_date": self.start_date,
            "end_date": self.end_date,
            "operation_type": self.operation_type,
            "discount_management": DISCOUNT_MANAGEMENT_IZBERG,
        }
        if self.operation_type == self.FLASH_SALE:
            data["max_use_count"] = self.max_use_count
            data["use_count"] = self.use_count
        return data

    def is_applicable_now(self, **extra_context):
        """Returns True if discount is applicable at current time"""
        return (
            self.is_active
            and not self.is_too_much_applied(**extra_context)
            and not self.is_outdated()
        )

    def is_outdated(self):
        if self.start_date and self.start_date > timezone.now():
            return True

        if self.end_date and self.end_date <= timezone.now():
            return True

        return False

    def is_too_much_applied(self, **extra_context):
        return (
            self.max_use_count
            and self.get_use_count(**extra_context) >= self.max_use_count
        ) or (
            self.max_use_count_per_user
            and "user" in extra_context
            and (self.get_use_count(**extra_context) >= self.max_use_count_per_user)
        )

    def is_upcoming(self):
        return self.is_active and self.start_date and self.start_date > timezone.now()

    def is_merchant_order_based(self):
        return self.reduction_type in self.MERCHANT_ORDER_BASED

    def is_product_offer_based(self):
        return self.reduction_type in self.PRODUCT_OFFER_BASED

    def is_user_specific(self, force_recache=False):
        cache_key = "promotions:discount:%s:is_user_specific" % self.id
        cached_result = cache.get(cache_key) if not force_recache else None
        if cached_result is not None:
            return cached_result
        result = self.eligible_users.exists()
        # cache it 1 minute so we don't need to invalidate when adding/removing
        # users and still avoid many DB queries
        try:
            cache.set(cache_key, result, 60)
        except OutOfMemoryError as e:
            logger.error(
                "Out of memory error while trying to set {} cache key: {}".format(
                    cache_key, e
                )
            )
        return result

    def get_use_count(self, user=None, **extra_context):
        """
        Returns the (non-canceled order-based) use count for given user,
        optionally excluding the given order
        NB: the count depends on the use_count_based_on conf attribute
        """
        if self.use_count_based_on == self.USE_COUNT_BASED_ON_MERCHANT_ORDERS:
            count = self._compute_use_count_based_on_merchant_orders(
                user=user, **extra_context
            )
        elif self.use_count_based_on == self.USE_COUNT_BASED_ON_ORDERS:
            count = self._compute_use_count_based_on_orders(user=user, **extra_context)
        elif self.use_count_based_on == self.USE_COUNT_BASED_ON_ORDER_ITEMS:
            count = self._compute_use_count_based_on_order_items(
                user=user, **extra_context
            )
        else:
            raise NotImplementedError(
                "Unknown use_count_based_on %s" % self.use_count_based_on
            )
        logger.debug("User %s has used %s times the discount %s" % (user, count, self))
        return count

    def is_applicable_for_context(
        self,
        customer_tax_group=None,
        address=None,
        user=None,
        cart=None,
        **extra_context,
    ):
        """
        Checks the context and returns True if Discount is applicable
        """
        logger.debug(
            "%s.is_applicable_for_context(customer_tax_group=%s,"
            " address=%s, user=%s, cart=%s)"
            % (self, customer_tax_group, address, user, cart)
        )
        if cart and cart.selected_payment_type == cart.TERM_PAYMENT:
            return False
        user_failed_check = self.is_user_specific() and (
            not user or not self.eligible_users.filter(id=user.id).exists()
        )
        if user_failed_check:
            logger.debug("user %s not in eligible_users of %s" % (user, self))
            return False

        if self.zone_id and (not address or address not in self.zone):
            logger.debug("address %s not in zone %s" % (address, self.zone))
            return False

        if self.country_id and (not address or address.country_id != self.country_id):
            logger.debug("address %s not in country %s", address, self.country_id)
            return False

        if user and self.max_use_count_per_user:
            user_use_count = self.get_use_count(user=user, cart=cart, **extra_context)
            if user_use_count >= self.max_use_count_per_user:
                logger.warning(
                    "user has reached max_use_count_per_user %s (used %s)"
                    % (self.max_use_count_per_user, user_use_count)
                )
                return False
            else:
                logger.debug(
                    "user hasn't reached max_use_count_per_user %s (used only %s)"
                    % (self.max_use_count_per_user, user_use_count)
                )
        logger.debug(
            "%s.is_applicable_for_context"
            "(customer_tax_group=%s, address=%s, user=%s)=True"
            % (self, customer_tax_group, address, user)
        )
        return True

    def is_applicable_for_product_offer(self, product_offer, quantity=1):
        """
        Is the discount applicable to the given product_offer and quantity ?
        """
        if self.currency_id != product_offer.currency_id:
            logger.debug(
                "product_offer %s doesn't satisfy %s (currency)" % (product_offer, self)
            )
            return False

        if not self.is_product_offer_based():
            logger.debug(
                "product_offer %s doesn't satisfy %s "
                "(is_product_offer_based)" % (product_offer, self)
            )
            return False

        if self.merchant_id and product_offer.merchant_id != self.merchant_id:
            logger.debug(
                "product_offer %s doesn't satisfy %s (merchant)" % (product_offer, self)
            )
            return False

        if self.min_quantity and quantity < self.min_quantity:
            logger.debug(
                "product_offer %s doesn't satisfy %s (min_quantity)"
                % (product_offer, self)
            )
            return False

        if self.max_quantity and quantity > self.max_quantity:
            logger.debug(
                "product_offer %s doesn't satisfy %s (max_quantity)"
                % (product_offer, self)
            )
            return False

        if (
            self.max_use_count_per_user
            and self.use_count_based_on == self.USE_COUNT_BASED_ON_ORDER_ITEMS
            and quantity > self.max_use_count_per_user
        ):
            # TODO  handle this in is_applicable_for_context with use_count ?
            logger.debug(
                "product_offer %s doesnt satisfy %s (quantity > "
                "max_use_count_per_user)" % (product_offer, self)
            )
            return False

        if (
            self.minimum_price
            and (quantity * product_offer.price_with_vat) < self.minimum_price
        ):
            logger.debug(
                "product_offer %s doesnt satisfy %s (minimum_price)"
                % (product_offer, self)
            )
            return False

        if (
            self.reduction_type == self.AMOUNT
            and self.reduction_value
            and product_offer.price_with_vat
            and Decimal(self.reduction_value) > Decimal(product_offer.price_with_vat)
        ):
            logger.debug("reduction value greater than product_offer price")
            return False

        if (
            self.maximum_price
            and (quantity * product_offer.price_with_vat) > self.maximum_price
        ):
            logger.debug(
                "product_offer %s doesnt satisfy %s (maximum_price)"
                % (product_offer, self)
            )
            return False

        for product_family in self.product_families.all():
            if product_family.contains_offer_id(product_offer.id):
                logger.debug(
                    "offer %s is in product_family %s"
                    % (product_offer.id, product_family.id)
                )
                return True

        logger.debug(
            "discount %s is not applicable for product offer %s"
            % (self.id, product_offer.id)
        )
        return False

    def is_applicable_for_cart_item(self, cart_item):
        """
        Is the discount applicable to the given cart_item ?
        """
        return self.is_applicable_for_product_offer(
            cart_item.product_offer, quantity=cart_item.quantity
        )

    def get_product_queryset(self):
        from apps.products.models import Product

        logger.debug("Discount[%s].get_product_queryset" % self.id)
        filters = excludes = None
        if not self.product_families.exists():
            return Product.objects.none()
        for pf in self.product_families.all():
            pf_filters, pf_excludes = pf.get_product_filters()
            if isinstance(pf_filters, dict):
                pf_filters = Q(**pf_filters)
            if isinstance(pf_excludes, dict):
                pf_excludes = Q(**pf_excludes)
            excludes = excludes & pf_excludes if excludes else pf_excludes
            filters = filters | pf_filters if filters else pf_filters
        if not filters:
            return Product.objects.none()
        qs = Product.objects.exclude_deleted().filter(filters)
        if excludes:
            qs = qs.exclude(excludes)

        # apply filters set on discount level
        qs = qs.filter(product_offers__currency=self.currency_id)
        if self.merchant_id:
            qs = qs.filter(product_offers__merchant=self.merchant_id)

        return qs

    def get_offer_queryset(self):
        from apps.products.models import ProductOffer

        logger.debug("Discount[%s].get_offer_queryset" % self.id)
        filters = None
        excludes = []
        for pf in self.product_families.all():
            pf_filters, pf_excludes = pf.get_offer_filters()
            excludes += pf_excludes
            if isinstance(pf_filters, dict):
                pf_filters = Q(**pf_filters)
            if filters is None:
                filters = pf_filters
            else:
                filters = filters | pf_filters
        if filters is None:
            return ProductOffer.objects.none()
        queryset = ProductOffer.objects.exclude_deleted().filter(filters)
        for exclude in excludes:
            queryset = queryset.exclude(**exclude)

        # apply filters set on discount level
        queryset = queryset.filter(currency=self.currency_id)
        if self.merchant_id:
            queryset = queryset.filter(merchant=self.merchant_id)

        return queryset

    def get_product_ids(self):
        """
        Returns the product ids of the channel based on the product_queryset
        """
        product_ids = set(self.get_product_queryset().values_list("id", flat=True))
        logger.debug(
            "Discount[%s].get_product_ids() returned %s ids"
            % (self.id, len(product_ids))
        )
        return product_ids

    def get_product_offer_ids(self):
        """
        Returns the product offer ids of the channel based on the offer_queryset
        """
        offer_ids = set(self.get_offer_queryset().values_list("id", flat=True))
        logger.debug(
            "Discount[%s].get_product_offer_ids() returned %s ids"
            % (self.id, len(offer_ids))
        )
        return offer_ids

    def is_applicable_for_cart(self, cart):
        """
        Is the discount applicable to the given cart ?
        NB: If discount is merchant specific, we check the amount_subtotal of
        this merchant only else the global amount_subtotal
        """
        if self.currency_id != cart.currency_id:
            return False

        # should we do cart_based discounts ?
        if not self.is_merchant_order_based():
            return False

        if self.discount_code:
            if (
                not cart.entered_discount_codes
                or self.discount_code not in cart.entered_discount_codes
            ):
                return False

        if self.min_quantity and cart.cart_items_count < self.min_quantity:
            return False

        if self.max_quantity and cart.cart_items_count > self.max_quantity:
            return False

        if self.merchant_id and self.merchant_id not in cart.cartitems.values_list(
            "merchant", flat=True
        ):
            return False

        amount_subtotal = cart.get_amount_subtotal(merchant=self.merchant_id)

        if self.minimum_price and amount_subtotal < self.minimum_price:
            logger.info(
                "cart %s doesnt satisfy %s (%s < minimum_price %s)"
                % (cart, self, amount_subtotal, self.minimum_price)
            )
            return False

        if self.maximum_price and amount_subtotal > self.maximum_price:
            logger.info(
                "cart %s doesnt satisfy %s (%s > maximum_price %s)"
                % (cart, self, amount_subtotal, self.maximum_price)
            )
            return False

        return True

    def get_discounted_price_dict_for_product_offer(
        self, product_offer, customer_tax_group=None, address=None, silent=True
    ):
        from apps.products.models import ProductOffer

        original_price_dict = product_offer.get_original_price_dict(
            address=address,
            customer_tax_group=customer_tax_group,
            silent=silent,
        )
        if self.reduction_type not in self.PRODUCT_OFFER_BASED:
            return original_price_dict

        _cast_reduction_value = self.reduction_type in [
            self.PERCENTAGE,
            self.AMOUNT,
            self.FIXED_PRICE,
        ]
        if _cast_reduction_value:
            self.reduction_value = Decimal(self.reduction_value)

        price_dict = {
            "discount": self,
            "original_price_dict": original_price_dict,
            "currency": product_offer.currency,
            "tax_rate": original_price_dict["tax_rate"],
            "tax_rate_key": original_price_dict["tax_rate_key"],
        }
        fake_offer = ProductOffer(
            application=product_offer.application,
            merchant=product_offer.merchant,
            product_tax_group=product_offer.product_tax_group,
            tax_rates=product_offer.tax_rates,
        )
        if self.reduction_type == self.PERCENTAGE:
            # price is original_price * (100 - reduction_value/100)
            fake_offer.price = Decimal(
                product_offer.price
                * (Decimal("100") - self.reduction_value)
                / Decimal("100")
            )
            fake_offer.price = fake_offer.price.quantize(Decimal("0.000001"))

            price_dict["price"] = fake_offer.price
            price_dict["price_with_vat"] = fake_offer.get_price_with_vat(
                address=address,
                customer_tax_group=customer_tax_group,
                silent=silent,
            )
            price_dict["price_without_vat"] = fake_offer.price_without_vat

        elif self.reduction_type == self.AMOUNT:
            original_price_with_vat = product_offer.get_price_with_vat(
                address=address,
                customer_tax_group=customer_tax_group,
                silent=silent,
            )
            # vat incl. price is original_price - reduction_value
            if original_price_with_vat is not None:
                discounted_price_with_vat = Decimal(
                    original_price_with_vat - self.reduction_value
                ).quantize(Decimal("0.01"))
                fake_offer.set_price_with_vat(discounted_price_with_vat, address)

            price_dict["price"] = fake_offer.price
            price_dict["price_with_vat"] = fake_offer.get_price_with_vat(
                address=address,
                customer_tax_group=customer_tax_group,
                silent=silent,
            )
            price_dict["price_without_vat"] = fake_offer.price_without_vat

        elif self.reduction_type == self.FIXED_PRICE:
            # vat incl. price is directly reduction_value
            try:
                fake_offer.price_with_vat = Decimal(self.reduction_value).quantize(
                    Decimal("0.01")
                )
            except NoTaxRateApplicable:
                pass
            price_dict["price"] = fake_offer.price
            price_dict["price_with_vat"] = fake_offer.get_price_with_vat(
                address=address,
                customer_tax_group=customer_tax_group,
                silent=silent,
            )
            price_dict["price_without_vat"] = fake_offer.price_without_vat

        elif self.reduction_type == self.FREE_SHIPPING:
            # original price
            price_dict.update(
                **product_offer.get_original_price_dict(
                    address=address,
                    customer_tax_group=customer_tax_group,
                    silent=silent,
                )
            )
        else:
            raise Exception(f"Unknown reduction type {self.reduction_type}")

        invalid_price_with_vat = (
            price_dict["price_with_vat"] is not None
            and price_dict["price_with_vat"] < 0
        )
        if invalid_price_with_vat:
            price_dict.update(
                **product_offer.get_original_price_dict(
                    address=address,
                    customer_tax_group=customer_tax_group,
                )
            )

        price_dict["reduction_type"] = self.reduction_type
        price_dict["reduction_value"] = self.reduction_value
        price_dict["start_date"] = self.start_date
        price_dict["end_date"] = self.end_date
        price_dict["is_active"] = self.is_active

        if price_dict["price_without_vat"] is not None:
            price_dict["price_without_vat"] = Decimal(
                price_dict["price_without_vat"]
            ).quantize(Decimal("0.000001"))

        price_dict["previous_price_without_vat"] = (
            product_offer.previous_price_without_vat or product_offer.price_without_vat
        )
        price_dict[
            "previous_price_with_vat"
        ] = product_offer.get_previous_price_with_vat(
            address=address,
            customer_tax_group=customer_tax_group,
            silent=silent,
        ) or product_offer.get_price_with_vat(
            address=address,
            customer_tax_group=customer_tax_group,
            silent=silent,
        )
        return price_dict

    def get_discounted_price_dict_for_product_variation(
        self, product_variation, customer_tax_group=None, address=None, silent=True
    ):
        from apps.products.models import ProductOffer

        original_price_dict = product_variation.get_original_price_dict(
            address=address,
            customer_tax_group=customer_tax_group,
            silent=silent,
        )
        if self.reduction_type not in self.PRODUCT_OFFER_BASED:
            return original_price_dict

        _cast_reduction_value = self.reduction_type in [
            self.PERCENTAGE,
            self.AMOUNT,
            self.FIXED_PRICE,
        ]
        if _cast_reduction_value:
            self.reduction_value = Decimal(self.reduction_value)

        price_dict = {
            "discount": self,
            "original_price_dict": original_price_dict,
            "tax_rate": original_price_dict["tax_rate"],
            "tax_rate_key": original_price_dict["tax_rate_key"],
        }
        fake_offer = ProductOffer(
            application=product_variation.product_offer.application,
            merchant=product_variation.product_offer.merchant,
            product_tax_group=product_variation.product_offer.product_tax_group,
            tax_rates=product_variation.product_offer.tax_rates,
        )
        if self.reduction_type == self.PERCENTAGE:
            # price is original_price * (100 - reduction_value/100)
            variation_price = (
                product_variation.price or product_variation.product_offer.price
            )
            fake_offer.price = Decimal(
                variation_price
                * (Decimal("100") - self.reduction_value)
                / Decimal("100")
            )
            fake_offer.price = fake_offer.price.quantize(Decimal("0.000001"))

            price_dict["price"] = fake_offer.price
            price_dict["price_with_vat"] = fake_offer.get_price_with_vat(
                address=address,
                customer_tax_group=customer_tax_group,
                silent=silent,
            )
            price_dict["price_without_vat"] = fake_offer.price_without_vat

        elif self.reduction_type == self.AMOUNT:
            if product_variation.price_without_vat:
                original_price_with_vat = product_variation.get_price_with_vat(
                    address=address,
                    customer_tax_group=customer_tax_group,
                    silent=silent,
                )
            else:
                original_price_with_vat = (
                    product_variation.product_offer.get_price_with_vat(
                        address=address,
                        customer_tax_group=customer_tax_group,
                        silent=silent,
                    )
                )
            # vat incl. price is original_price - reduction_value
            if original_price_with_vat is not None:
                discounted_price_with_vat = Decimal(
                    original_price_with_vat - self.reduction_value
                ).quantize(Decimal("0.01"))
                fake_offer.set_price_with_vat(discounted_price_with_vat, address)

            price_dict["price"] = fake_offer.price
            price_dict["price_with_vat"] = fake_offer.get_price_with_vat(
                address=address,
                customer_tax_group=customer_tax_group,
                silent=silent,
            )
            price_dict["price_without_vat"] = fake_offer.price_without_vat

        elif self.reduction_type == self.FIXED_PRICE:
            # vat incl. price is directly reduction_value
            try:
                fake_offer.price_with_vat = Decimal(self.reduction_value).quantize(
                    Decimal("0.01")
                )
            except NoTaxRateApplicable:
                pass
            price_dict["price"] = fake_offer.price
            price_dict["price_with_vat"] = fake_offer.get_price_with_vat(
                address=address,
                customer_tax_group=customer_tax_group,
                silent=silent,
            )
            price_dict["price_without_vat"] = fake_offer.price_without_vat

        elif self.reduction_type == self.FREE_SHIPPING:
            # original price
            price_dict.update(
                **product_variation.get_original_price_dict(
                    address=address,
                    customer_tax_group=customer_tax_group,
                    silent=silent,
                )
            )
        else:
            raise Exception("Unknown reduction type %s" % self.reduction_type)

        invalid_price_with_vat = (
            price_dict["price_with_vat"] is not None
            and price_dict["price_with_vat"] < 0
        )
        if invalid_price_with_vat:
            price_dict.update(
                **product_variation.get_original_price_dict(
                    address=address, customer_tax_group=customer_tax_group
                )
            )

        price_dict["reduction_type"] = self.reduction_type
        price_dict["reduction_value"] = self.reduction_value
        price_dict["start_date"] = self.start_date
        price_dict["end_date"] = self.end_date
        price_dict["is_active"] = self.is_active

        if price_dict["price_without_vat"] is not None:
            price_dict["price_without_vat"] = Decimal(
                price_dict["price_without_vat"]
            ).quantize(Decimal("0.000001"))

        # previous price of variation or offer or price of variation or price
        # or offer
        price_dict["previous_price_without_vat"] = (
            product_variation.previous_price_without_vat
            or product_variation.product_offer.previous_price_without_vat
            or product_variation.price_without_vat
            or product_variation.product_offer.price_without_vat
        )

        price_dict["previous_price_with_vat"] = (
            product_variation.get_previous_price_with_vat(
                address=address, customer_tax_group=customer_tax_group, silent=silent
            )
            or product_variation.product_offer.get_previous_price_with_vat(
                address=address,
                customer_tax_group=customer_tax_group,
                silent=silent,
            )
            or product_variation.get_price_with_vat(
                address=address,
                customer_tax_group=customer_tax_group,
                silent=silent,
            )
            or product_variation.product_offer.get_price_with_vat(
                address=address,
                customer_tax_group=customer_tax_group,
                silent=silent,
            )
        )
        return price_dict

    """ Discount xworkflow hooks """

    @xworkflows.after_transition("activate")
    def after_transition_activate(self, *args, **kwargs):
        self.handle_beginning_of_discount()

    def should_update_channels_on_discount_start(self):
        """
        Return True if the channels should be updated on discount start.
        Conditions (or):
        - currently applicable discount with no code
        - currently applicable discount with code with
            INCLUDE_APPLICABLE_DISCOUNTS channel option enabled
        - upcoming discount with
            INCLUDE_UPCOMING_DISCOUNTS channel option enabled
        TODO: check if any of active channel has INCLUDE_APPLICABLE_DISCOUNTS/
                INCLUDE_UPCOMING_DISCOUNTS options
        """
        from apps.channels.models import ProductChannel

        return (
            (not self.discount_code and self.is_applicable_now())
            or (
                self.operation_type in [self.MARKETING, self.FLASH_SALE]
                and self.is_applicable_now()
                and self.application.active_products_channel
                and self.application.active_products_channel.get_option(
                    ProductChannel.INCLUDE_APPLICABLE_DISCOUNTS
                )
            )
            or (
                self.operation_type in [self.MARKETING, self.FLASH_SALE]
                and self.is_upcoming()
                and self.application.active_products_channel
                and self.application.active_products_channel.get_option(
                    ProductChannel.INCLUDE_UPCOMING_DISCOUNTS
                )
            )
        )

    def should_update_channels_on_discount_end(self):
        """
        Return True if the channels should be updated on discount end.
        Conditions (or):
        - was discount with no code
        - INCLUDE_APPLICABLE_DISCOUNTS channel option enabled
        - INCLUDE_UPCOMING_DISCOUNTS channel option enabled
        """
        from apps.channels.models import ProductChannel

        return not self.discount_code or (
            self.application.active_products_channel
            and (
                self.application.active_products_channel.get_option(
                    ProductChannel.INCLUDE_APPLICABLE_DISCOUNTS
                )
                or self.application.active_products_channel.get_option(
                    ProductChannel.INCLUDE_UPCOMING_DISCOUNTS
                )
            )
        )

    def should_invalidate_items_cache(self):
        # If this live setting is true, it means we will not have any discount
        # computation for product_offer and product resources.
        # We can keep the cache.
        if live_config.DISABLE_DISCOUNT_IN_PAYLOAD_RESPONSE:
            return False
        return not self.discount_code

    def handle_beginning_of_discount(self):
        """
        FIXME: When a product family is removed from a discount,
        its products are not in the get_product_ids anymore, so
        their prices are not udpated.
        """

        if self.should_update_channels_on_discount_start():
            channel_event = {
                "event": "discount_started",
                "discount_id": self.id,
                "priority": "high",
            }
            self._trigger_event_on_channels(channel_event)

        if self.should_invalidate_items_cache():
            self.invalidate_items_cache()

    def handle_refresh_of_discount(self, offer_ids=None, product_ids=None):
        if self.should_update_channels_on_discount_start():
            channel_event = {
                "event": "discount_refresh",
                "discount_id": self.id,
                "priority": "high",
            }
            if offer_ids is not None:
                channel_event["product_offer_ids"] = offer_ids
            if product_ids is not None:
                channel_event["product_ids"] = product_ids
            self._trigger_event_on_channels(channel_event)

        if self.should_invalidate_items_cache():
            self.invalidate_items_cache(offer_ids=offer_ids)

    @xworkflows.after_transition("pause")
    def after_transition_pause(self, *args, **kwargs):
        """
        Call handle_end_of_discount when the discount is paused
        """
        self.handle_end_of_discount()

    def handle_end_of_discount(self):
        if self.should_update_channels_on_discount_end():
            channel_event = {
                "event": "discount_ended",
                "discount_id": self.id,
                "priority": "high",
            }
            self._trigger_event_on_channels(channel_event)

        if self.should_invalidate_items_cache():
            self.invalidate_items_cache()

    def _trigger_event_on_channels(self, channel_event: dict):
        self.application.trigger_on_active_channels(
            channel_event, currency=self.currency
        )

    def invalidate_items_cache(self, offer_ids=None):
        """
        Invalidate products cache
        TODO: use class method and IDs to avoid fetching objects
        """
        offers = self.get_offer_queryset()
        if offer_ids is not None:
            offers = offers.filter(id__in=offer_ids)
        logger.info(
            "%s.invalidate_items_cache starts (%s offers)", self, offers.count()
        )
        for offer in offers.chunk_iterate():
            # NOTE: also invalidate product cache
            offer.invalidate_cache(languages="auto")
            offer.discount_manager.invalidate_cache(self)
        logger.info("%s.invalidate_items_cache done", self)


class DiscountUseManager(models.Manager):
    def not_cancelled(self, **kwargs):
        return self.exclude(status=DiscountUseWorkflow.CANCELLED).filter(**kwargs)

    def order_based(self, **kwargs):
        return self.filter(
            Q(order_item__isnull=False) | Q(merchant_order__isnull=False)
        ).filter(**kwargs)

    def cart_based(self, **kwargs):
        return self.filter(Q(cart_item__isnull=False) | Q(cart__isnull=False)).filter(
            **kwargs
        )

    def filter_voucher(self):
        return self.filter(discount__reduction_type=Discount.VOUCHER)

    def create_order_discount_use(self, discount_use, merchant_order):
        return DiscountUse.objects.create(
            discount=discount_use.discount,
            merchant_order=merchant_order,
            user=merchant_order.order.user,
            application=merchant_order.order.application,
            discount_amount=discount_use.discount_amount,
            discount_vat_amount=discount_use.discount_vat_amount,
        )


class DiscountUseWorkflow(IzbergWorkflow):
    INITIAL = "initial"
    CONFIRMED = "confirmed"
    CANCELLED = "cancelled"

    states = (
        (INITIAL, _("Initial")),
        (CONFIRMED, _("Confirmed")),
        (CANCELLED, _("Cancelled")),
    )

    transitions = (
        ("confirm", [INITIAL], CONFIRMED),
        ("cancel", [INITIAL], CANCELLED),
    )

    initial_state = INITIAL


def set_null_for_not_done_carts_else_cascade(collector, field, sub_objs, using):
    to_null = []
    to_delete = []

    for discount_use in sub_objs:
        is_bound_to_an_order = (
            discount_use.order_id
            or discount_use.merchant_order_id
            or discount_use.order_item_id
        ) is not None
        if is_bound_to_an_order:
            to_null.append(discount_use)
        else:
            to_delete.append(discount_use)

    models.SET_NULL(collector, field, to_null, using)
    models.CASCADE(collector, field, to_delete, using)


class DiscountUse(xwf_models.WorkflowEnabled, models.Model):
    """Store the usage of a discount for an item, merchant order or an order"""

    objects = DiscountUseManager()

    discount = IzbergForeignKey(
        Discount, related_name="discount_uses", db_index=False, on_delete=models.CASCADE
    )

    cart_item = IzbergForeignKey(
        "orders.CartItem",
        blank=True,
        null=True,
        related_name="discount_uses",
        on_delete=set_null_for_not_done_carts_else_cascade,
        db_index=False,
    )
    cart = IzbergForeignKey(
        "orders.Cart",
        blank=True,
        null=True,
        related_name="discount_uses",
        on_delete=set_null_for_not_done_carts_else_cascade,
        db_index=False,
    )
    merchant = IzbergForeignKey(
        "stores.Merchant",
        blank=True,
        null=True,
        related_name="discount_uses",
        on_delete=models.CASCADE,
        db_index=False,
    )
    order_item = IzbergForeignKey(
        "orders.OrderItem",
        blank=True,
        null=True,
        related_name="discount_uses",
        on_delete=models.CASCADE,
        db_index=False,
    )
    merchant_order = IzbergForeignKey(
        "orders.MerchantOrder",
        blank=True,
        null=True,
        related_name="discount_uses",
        on_delete=models.CASCADE,
        db_index=False,
    )
    order = IzbergForeignKey(
        "orders.Order",
        blank=True,
        null=True,
        related_name="discount_uses",
        on_delete=models.CASCADE,
        db_index=False,
    )
    # total amounts
    discount_amount = models.DecimalField(
        max_digits=20,
        decimal_places=6,
        null=True,
        blank=True,
        default=Decimal("0.00"),
        validators=[MinValueValidator(Decimal("0.00"))],
    )
    discount_vat_amount = models.DecimalField(
        max_digits=20,
        decimal_places=6,
        null=True,
        blank=True,
        default=Decimal("0.00"),
        validators=[MinValueValidator(Decimal("0.00"))],
    )
    discount_amount_financed_by_application = models.DecimalField(
        max_digits=20,
        decimal_places=6,
        null=True,
        blank=True,
        default=Decimal("0.00"),
        validators=[MinValueValidator(Decimal("0.00"))],
    )
    discount_vat_amount_financed_by_application = models.DecimalField(
        max_digits=20,
        decimal_places=6,
        null=True,
        blank=True,
        default=Decimal("0.00"),
        validators=[MinValueValidator(Decimal("0.00"))],
    )
    # unit amounts
    discount_unit_amount = models.DecimalField(
        max_digits=20,
        decimal_places=6,
        null=True,
        blank=True,
        default=Decimal("0.00"),
        validators=[MinValueValidator(Decimal("0.00"))],
    )
    discount_vat_unit_amount = models.DecimalField(
        max_digits=20,
        decimal_places=6,
        null=True,
        blank=True,
        default=Decimal("0.00"),
        validators=[MinValueValidator(Decimal("0.00"))],
    )
    discount_unit_amount_financed_by_application = models.DecimalField(
        max_digits=20,
        decimal_places=6,
        null=True,
        blank=True,
        default=Decimal("0.00"),
        validators=[MinValueValidator(Decimal("0.00"))],
    )
    discount_vat_unit_amount_financed_by_application = models.DecimalField(
        max_digits=20,
        decimal_places=6,
        null=True,
        blank=True,
        default=Decimal("0.00"),
        validators=[MinValueValidator(Decimal("0.00"))],
    )
    status = xwf_models.StateField(DiscountUseWorkflow)

    # denormalization
    quantity = models.IntegerField(
        _("Quantity"),
        default=1,
        help_text=_("Quantity of item"),
    )
    user = IzbergForeignKey(
        settings.AUTH_USER_MODEL,
        blank=True,
        null=True,
        related_name="discount_uses",
        on_delete=models.CASCADE,
        db_index=False,
    )
    application = IzbergForeignKey(
        "ice_applications.Application",
        blank=True,
        null=True,
        related_name="discount_uses",
        on_delete=models.CASCADE,
        db_index=False,
    )

    cart_shipping_choice = IzbergForeignKey(
        "shipping2.CartShippingChoice",
        null=True,
        blank=True,
        related_name="discount_uses",
        on_delete=models.CASCADE,
        db_index=False,
    )
    order_shipping_choice = IzbergForeignKey(
        "shipping2.OrderShippingChoice",
        null=True,
        blank=True,
        related_name="discount_uses",
        on_delete=models.CASCADE,
        db_index=False,
    )

    class Meta:
        indexes = [
            PostgresIndex(
                fields=["discount"],
                name="promotions_discountuse_discount_id_idx",
            ),
            PostgresIndex(
                fields=["cart_item"],
                name="promotions_discountuse_cart_item_id_idx",
            ),
            PostgresIndex(
                fields=["cart"],
                name="promotions_discountuse_cart_id_idx",
            ),
            PostgresIndex(
                fields=["merchant"],
                name="promotions_discountuse_merchant_id_idx",
            ),
            PostgresIndex(
                fields=["order"],
                name="promotions_discountuse__idx",
            ),
            PostgresIndex(
                fields=["user"],
                name="promotions_discountuse_order_id_idx",
            ),
            PostgresIndex(
                fields=["application"],
                name="promotions_discountuse_application_id_idx",
            ),
            PostgresIndex(
                fields=["cart_shipping_choice"],
                name="promotions_discountuse_cart_shipping_choice_id_idx",
            ),
            PostgresIndex(
                fields=["order_shipping_choice"],
                name="promotions_discountuse_order_shipping_choice_id_idx",
            ),
            PostgresIndex(
                fields=["order_item_id"],
                name="promotions_discountuse_order_item_id_idx",
            ),
            PostgresIndex(
                fields=["status"],
                name="promotions_discountuse_status_pidx",
                condition=~Q(status=DiscountUseWorkflow.CANCELLED),
            ),
            PostgresIndex(
                fields=["merchant_order_id", "status"],
                name="promotions_discountuse_merchant_order_id__status_pidx",
                condition=~Q(status=DiscountUseWorkflow.CANCELLED),
            ),
        ]

    def __str__(self):
        return "DiscountUse[{pk}|{status}]".format(pk=self.id, status=self.status.name)

    def _update_financed_by_application_amounts(self):
        app_rate = self.discount.financed_by_application / Decimal("100")
        self.discount_amount_financed_by_application = self.discount_amount * app_rate
        self.discount_vat_amount_financed_by_application = (
            self.discount_vat_amount * app_rate
        )
        self.discount_unit_amount_financed_by_application = (
            self.discount_unit_amount * app_rate
        )
        self.discount_vat_unit_amount_financed_by_application = (
            self.discount_vat_unit_amount * app_rate
        )

    def clean(self):
        creation = self.id is None
        nb_of_linked_items = (
            bool(self.cart_item)
            + bool(self.cart)
            + bool(self.order_item)
            + bool(self.merchant_order)
            + bool(self.cart_shipping_choice)
        )
        if nb_of_linked_items != 1:
            if nb_of_linked_items == 2 and self.cart and self.cart_shipping_choice:
                pass
            else:
                raise ValidationError(
                    _(
                        "DiscountUse must have one and only one linked object "
                        "(among cart, cart_item, order_item, merchant_order and "
                        "cart_shipping_choice)"
                    )
                )
        if (
            creation
            and not self.discount_amount_financed_by_application
            and self.discount.financed_by_application
        ):
            self._update_financed_by_application_amounts()

            logger.info(
                "DiscountUse discount_amount= %s, %s > financed_by_application=%s >"
                " discount_amount_financed_by_application=%s, %s"
                % (
                    self.discount_amount,
                    self.discount_vat_amount,
                    self.discount.financed_by_application,
                    self.discount_amount_financed_by_application,
                    self.discount_vat_amount_financed_by_application,
                )
            )

    def save(self, *args, **kwargs):
        self.full_clean()
        super(DiscountUse, self).save(*args, **kwargs)
        update_discount_use_count.delay(
            self.discount_id, application_id=self.application_id, save_if_changed=True
        )

    def delete(self, *args, **kwargs):
        """Update discount use_count on delete."""
        super(DiscountUse, self).delete(*args, **kwargs)
        update_discount_use_count.delay(
            self.discount_id, application_id=self.application_id, save_if_changed=True
        )

    @property
    def discount_amount_vat_included(self):
        return self.discount_amount + self.discount_vat_amount

    @property
    def app_discount_amount_vat_incl(self):
        return (
            self.discount_amount_financed_by_application
            + self.discount_vat_amount_financed_by_application
        )

    @property
    def discount_unit_amount_vat_included(self):
        return self.discount_unit_amount + self.discount_vat_unit_amount

    @property
    def discount_unit_amount_financed_by_app_vat_included(self):
        return (
            self.discount_unit_amount_financed_by_application
            + self.discount_vat_unit_amount_financed_by_application
        )

    @xworkflows.after_transition("confirm")
    def create_transactions_on_voucher_use_confirmation(self, *args, **kwargs):
        from apps.promotions.models import Discount

        if self.discount.reduction_type != Discount.VOUCHER:
            # only dealing with voucher here
            return
        from apps.transactions.tasks import create_voucher_use

        if settings.DEBUG or settings.TESTING:
            create_voucher_use(self.id, self.application_id)
        else:
            create_voucher_use.delay(self.id, self.application_id)
