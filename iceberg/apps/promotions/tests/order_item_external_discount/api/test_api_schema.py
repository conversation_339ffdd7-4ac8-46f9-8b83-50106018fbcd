# -*- coding: utf-8 -*-
from unittest.mock import patch

from drf_izberg.tests.base import BaseAPITestCase


class OrderItemExternalDiscountViewsSchemaTestCase(BaseAPITestCase):
    API_VERSION = "v2"
    # This attribute controls the maximum length of diffs output by assert methods
    # that report diffs on failure.
    maxDiff = None

    # We have to mock <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> because /schema/ is a special endpoint
    # with his own override of permissions.
    # see IzbergSchemaMixin in drf_izberg.views.schema_mixins
    @patch("rest_framework.permissions.IsAuthenticated.has_permission", lambda *_: True)
    def test_schema(self):
        resp = self.api_client.get(
            f"/{self.API_VERSION}/order-item-external-discount/schema/"
        )

        self.assertHttpOK(resp)
        data = self.deserialize(resp)

        # The expected schema is slightly different from the actual Tastypie schema.
        # - Most listed fields have their attributes blank and default changed.
        #   DRF will use the value of the model by default OR the value set on the
        #   serializer. Tastypie seems to be different and are sometimes wrong.
        #   Example: id with blank: True -> Wrong
        # - We are adding the attribute "required" that defined if the field is required
        #   by our serializer to pass validation.
        # - in methods, for each 'action' listed, we also add the allowed_http_methods
        #
        # Therefore, we cannot directly use our schema.json, we need to change
        # those values. You must be careful when changing the value, do not simply use
        # what our DRF schema generator is giving, but check on the model and
        # serializer that the information is correct
        expected_data = {
            "allowed_detail_http_methods": ["get"],
            "allowed_list_http_methods": ["get"],
            "default_format": "application/json",
            "default_limit": 20,
            "doc": "",
            "fields": {
                "applicating_order": {
                    "blank": False,
                    "creatable": False,
                    "default": "",
                    "editable": False,
                    "help_text": "Order of discounts application",
                    "name": "Applicating order",
                    "nullable": True,
                    "readonly": True,
                    "required": False,
                    "type": "integer",
                    "unique": False,
                },
                "application": {
                    "blank": False,
                    "creatable": False,
                    "default": "",
                    "editable": False,
                    "help_text": "",
                    "name": "Application",
                    "nullable": False,
                    "readonly": True,
                    "related_type": "to_one",
                    "required": False,
                    "type": "related",
                    "unique": False,
                },
                "discount_amount": {
                    "blank": False,
                    "creatable": False,
                    "default": "",
                    "editable": False,
                    "help_text": "",
                    "name": "Discount amount",
                    "nullable": False,
                    "readonly": True,
                    "required": False,
                    "type": "decimal",
                    "unique": False,
                },
                "discount_code": {
                    "blank": False,
                    "creatable": False,
                    "default": "",
                    "editable": False,
                    "help_text": "",
                    "max_length": 255,
                    "name": "Discount code",
                    "nullable": True,
                    "readonly": True,
                    "required": False,
                    "type": "string",
                    "unique": False,
                },
                "discount_unit_amount": {
                    "blank": False,
                    "creatable": False,
                    "default": "",
                    "editable": False,
                    "help_text": "",
                    "name": "Discount unit amount",
                    "nullable": False,
                    "readonly": True,
                    "required": False,
                    "type": "decimal",
                    "unique": False,
                },
                "discount_vat_amount": {
                    "blank": False,
                    "creatable": False,
                    "default": "",
                    "editable": False,
                    "help_text": "",
                    "name": "Discount vat amount",
                    "nullable": False,
                    "readonly": True,
                    "required": False,
                    "type": "decimal",
                    "unique": False,
                },
                "discount_vat_unit_amount": {
                    "blank": False,
                    "creatable": False,
                    "default": "",
                    "editable": False,
                    "help_text": "",
                    "name": "Discount vat unit amount",
                    "nullable": False,
                    "readonly": True,
                    "required": False,
                    "type": "decimal",
                    "unique": False,
                },
                "end_date": {
                    "blank": False,
                    "creatable": False,
                    "default": "",
                    "editable": False,
                    "help_text": "the date the discount ends",
                    "name": "End date",
                    "nullable": True,
                    "readonly": True,
                    "required": False,
                    "type": "datetime",
                    "unique": False,
                },
                "external_id": {
                    "blank": False,
                    "creatable": False,
                    "default": "",
                    "editable": False,
                    "help_text": "Use it to store external matching id",
                    "max_length": 255,
                    "name": "External id",
                    "nullable": True,
                    "readonly": True,
                    "required": False,
                    "type": "string",
                    "unique": False,
                },
                "id": {
                    "blank": False,
                    "creatable": False,
                    "default": "",
                    "editable": False,
                    "help_text": "",
                    "name": "Id",
                    "nullable": False,
                    "readonly": True,
                    "required": False,
                    "type": "integer",
                    "unique": True,
                },
                "name": {
                    "blank": False,
                    "creatable": False,
                    "default": "",
                    "editable": False,
                    "help_text": "the name of the discount",
                    "max_length": 255,
                    "name": "Name",
                    "nullable": False,
                    "readonly": True,
                    "required": False,
                    "type": "string",
                    "unique": False,
                },
                "order_item": {
                    "blank": False,
                    "creatable": False,
                    "default": "",
                    "editable": False,
                    "help_text": "",
                    "name": "Order item",
                    "nullable": False,
                    "readonly": True,
                    "related_type": "to_one",
                    "required": False,
                    "type": "related",
                    "unique": False,
                },
                "quantity": {
                    "blank": False,
                    "creatable": False,
                    "default": "",
                    "editable": False,
                    "help_text": "Quantity of item",
                    "name": "Quantity",
                    "nullable": False,
                    "readonly": True,
                    "required": False,
                    "type": "integer",
                    "unique": False,
                },
                "reduction_type": {
                    "blank": False,
                    "choices": [
                        {
                            "value": 0,
                            "help_text": "In Percentage based on original product price",
                        },
                        {
                            "value": 10,
                            "help_text": "In Amount based on original product price",
                        },
                        {
                            "value": 20,
                            "help_text": "The given value is the discounted product price",
                        },
                    ],
                    "choices_to_hide": [],
                    "creatable": False,
                    "default": "",
                    "editable": False,
                    "help_text": "",
                    "name": "Reduction type",
                    "nullable": True,
                    "readonly": True,
                    "required": False,
                    "type": "integer",
                    "unique": False,
                    "virtual_choices": [],
                },
                "reduction_type_localized": {
                    "blank": False,
                    "creatable": False,
                    "default": "",
                    "editable": False,
                    "help_text": "",
                    "max_length": None,
                    "name": "Reduction type localized",
                    "nullable": False,
                    "readonly": True,
                    "required": False,
                    "type": "string",
                    "unique": False,
                },
                "reduction_value": {
                    "blank": False,
                    "creatable": False,
                    "default": "",
                    "editable": False,
                    "help_text": "The percentage/exact amount/fixed price (depending on the reduction_type)",
                    "max_length": 255,
                    "name": "Reduction value",
                    "nullable": True,
                    "readonly": True,
                    "required": False,
                    "type": "string",
                    "unique": False,
                },
                "resource_uri": {
                    "blank": False,
                    "creatable": False,
                    "default": "",
                    "editable": False,
                    "help_text": "",
                    "max_length": None,
                    "name": "Resource uri",
                    "nullable": False,
                    "readonly": True,
                    "required": False,
                    "type": "string",
                    "unique": False,
                },
                "start_date": {
                    "blank": False,
                    "creatable": False,
                    "default": "",
                    "editable": False,
                    "help_text": "the date the discount starts",
                    "name": "Start date",
                    "nullable": True,
                    "readonly": True,
                    "required": False,
                    "type": "datetime",
                    "unique": False,
                },
            },
            "filtering": {
                "id": 1,
                "order_item": 1,
                "external_id": 1,
                "discount_code": 1,
            },
            "methods": [],
            "ordering": ["order_item", "external_id", "discount_code"],
        }

        self.assertEqual(data, expected_data)
