from apps.payment.api.payment_term.views import PaymentTermViewSet
from drf_izberg.permissions.constants.roles import ADMIN_ROLE, READ_ROLE, WRITE_ROLE
from drf_izberg.permissions.constants.scopes import OMS_SCOPE, PIM_SCOPE
from drf_izberg.tests.base import BasePermissionsAPITestCase
from ims.api.const import BUYER_OWNER, INTERNAL_OWNER, MERCHANT_OWNER, OPERATOR_OWNER


class PaymentTermPermissionsTestCase(BasePermissionsAPITestCase):
    def setUp(self):
        super().setUp()
        self.view = PaymentTermViewSet()
        self.view.action = None

    def test_internal_oms_read(self):
        self.request.owner_type = INTERNAL_OWNER
        self.request.scopes = [f"{OMS_SCOPE}:{READ_ROLE}"]

        self._should_raise_with_action("list")
        self._should_raise_with_action("retrieve")
        self._should_raise_with_action("create")
        self._should_raise_with_action("update")
        self._should_raise_with_action("destroy")
        self._should_raise_with_action("activate_action")
        self._should_raise_with_action("activate_action_post")
        self._should_raise_with_action("deactivate_action")
        self._should_raise_with_action("deactivate_action_post")

    def test_internal_oms_write(self):
        self.request.owner_type = INTERNAL_OWNER
        self.request.scopes = [f"{OMS_SCOPE}:{WRITE_ROLE}"]

        self._should_raise_with_action("list")
        self._should_raise_with_action("retrieve")
        self._should_raise_with_action("create")
        self._should_raise_with_action("update")
        self._should_raise_with_action("destroy")
        self._should_raise_with_action("activate_action")
        self._should_raise_with_action("activate_action_post")
        self._should_raise_with_action("deactivate_action")
        self._should_raise_with_action("deactivate_action_post")

    def test_operator_oms_read(self):
        self.request.owner_type = OPERATOR_OWNER
        self.request.scopes = [f"{OMS_SCOPE}:{READ_ROLE}"]

        self._should_pass_permissions_with_action("list")
        self._should_pass_permissions_with_action("retrieve")
        self._should_pass_permissions_with_action("activate_action")
        self._should_pass_permissions_with_action("deactivate_action")

        self._should_raise_with_action("create")
        self._should_raise_with_action("update")
        self._should_raise_with_action("destroy")
        self._should_raise_with_action("activate_action_post")
        self._should_raise_with_action("deactivate_action_post")

    def test_operator_oms_write(self):
        self.request.owner_type = OPERATOR_OWNER
        self.request.scopes = [f"{OMS_SCOPE}:{WRITE_ROLE}"]

        self._should_pass_permissions_with_action("list")
        self._should_pass_permissions_with_action("retrieve")
        self._should_pass_permissions_with_action("activate_action")
        self._should_pass_permissions_with_action("deactivate_action")

        self._should_raise_with_action("create")
        self._should_raise_with_action("update")
        self._should_raise_with_action("destroy")
        self._should_raise_with_action("activate_action_post")
        self._should_raise_with_action("deactivate_action_post")

    def test_operator_oms_admin(self):
        self.request.owner_type = OPERATOR_OWNER
        self.request.scopes = [f"{OMS_SCOPE}:{ADMIN_ROLE}"]

        self._should_pass_permissions_with_action("list")
        self._should_pass_permissions_with_action("retrieve")
        self._should_pass_permissions_with_action("create")
        self._should_pass_permissions_with_action("update")
        self._should_pass_permissions_with_action("destroy")
        self._should_pass_permissions_with_action("activate_action")
        self._should_pass_permissions_with_action("activate_action_post")
        self._should_pass_permissions_with_action("deactivate_action")
        self._should_pass_permissions_with_action("deactivate_action_post")

    def test_operator_pim_admin(self):
        self.request.owner_type = OPERATOR_OWNER
        self.request.scopes = [f"{PIM_SCOPE}:{ADMIN_ROLE}"]

        self._should_raise_with_action("list")
        self._should_raise_with_action("retrieve")
        self._should_raise_with_action("create")
        self._should_raise_with_action("update")
        self._should_raise_with_action("destroy")
        self._should_raise_with_action("activate_action")
        self._should_raise_with_action("activate_action_post")
        self._should_raise_with_action("deactivate_action")
        self._should_raise_with_action("deactivate_action_post")

    def test_merchant_oms_read(self):
        self.request.owner_type = MERCHANT_OWNER
        self.request.scopes = [f"{OMS_SCOPE}:{READ_ROLE}"]

        self._should_pass_permissions_with_action("list")
        self._should_pass_permissions_with_action("retrieve")

        self._should_raise_with_action("create")
        self._should_raise_with_action("update")
        self._should_raise_with_action("destroy")
        self._should_raise_with_action("activate_action")
        self._should_raise_with_action("activate_action_post")
        self._should_raise_with_action("deactivate_action")
        self._should_raise_with_action("deactivate_action_post")

    def test_merchant_oms_write(self):
        self.request.owner_type = MERCHANT_OWNER
        self.request.scopes = [f"{OMS_SCOPE}:{WRITE_ROLE}"]

        self._should_pass_permissions_with_action("list")
        self._should_pass_permissions_with_action("retrieve")

        self._should_raise_with_action("create")
        self._should_raise_with_action("update")
        self._should_raise_with_action("destroy")
        self._should_raise_with_action("activate_action")
        self._should_raise_with_action("activate_action_post")
        self._should_raise_with_action("deactivate_action")
        self._should_raise_with_action("deactivate_action_post")

    def test_merchant_pim_write(self):
        self.request.owner_type = MERCHANT_OWNER
        self.request.scopes = [f"{PIM_SCOPE}:{WRITE_ROLE}"]

        self._should_raise_with_action("list")
        self._should_raise_with_action("retrieve")
        self._should_raise_with_action("create")
        self._should_raise_with_action("update")
        self._should_raise_with_action("destroy")
        self._should_raise_with_action("activate_action")
        self._should_raise_with_action("activate_action_post")
        self._should_raise_with_action("deactivate_action")
        self._should_raise_with_action("deactivate_action_post")

    def test_buyer_oms_read(self):
        self.request.owner_type = BUYER_OWNER
        self.request.scopes = [f"{OMS_SCOPE}:{READ_ROLE}"]

        self._should_pass_permissions_with_action("list")
        self._should_pass_permissions_with_action("retrieve")

        self._should_raise_with_action("create")
        self._should_raise_with_action("update")
        self._should_raise_with_action("destroy")
        self._should_raise_with_action("activate_action")
        self._should_raise_with_action("activate_action_post")
        self._should_raise_with_action("deactivate_action")
        self._should_raise_with_action("deactivate_action_post")

    def test_buyer_oms_write(self):
        self.request.owner_type = BUYER_OWNER
        self.request.scopes = [f"{OMS_SCOPE}:{WRITE_ROLE}"]

        self._should_pass_permissions_with_action("list")
        self._should_pass_permissions_with_action("retrieve")

        self._should_raise_with_action("create")
        self._should_raise_with_action("update")
        self._should_raise_with_action("destroy")
        self._should_raise_with_action("activate_action")
        self._should_raise_with_action("activate_action_post")
        self._should_raise_with_action("deactivate_action")
        self._should_raise_with_action("deactivate_action_post")

    def test_buyer_pim_write(self):
        self.request.owner_type = BUYER_OWNER
        self.request.scopes = [f"{PIM_SCOPE}:{WRITE_ROLE}"]

        self._should_raise_with_action("list")
        self._should_raise_with_action("retrieve")
        self._should_raise_with_action("create")
        self._should_raise_with_action("update")
        self._should_raise_with_action("destroy")
        self._should_raise_with_action("activate_action")
        self._should_raise_with_action("activate_action_post")
        self._should_raise_with_action("deactivate_action")
        self._should_raise_with_action("deactivate_action_post")
