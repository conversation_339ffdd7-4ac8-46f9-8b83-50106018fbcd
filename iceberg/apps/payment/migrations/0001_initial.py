# Generated by Django 3.2.20 on 2023-08-24 09:28

from decimal import Decimal
import django.contrib.postgres.fields
import django.core.serializers.json
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import django_xworkflows.models
import ims.api.mixins
import ims.models.mixin
import mp_utils.model_mixins.decimal_field_check_mixin
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("contenttypes", "0002_remove_content_type_name"),
    ]

    operations = [
        migrations.CreateModel(
            name="OrderItemPaymentTerm",
            fields=[
                (
                    "created_on",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created on"),
                ),
                (
                    "last_modified",
                    models.DateTimeField(auto_now=True, verbose_name="Last modified"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(default=None, max_length=255)),
                ("label_on_invoice", models.Char<PERSON>ield(default=None, max_length=255)),
                (
                    "lines",
                    models.JSONField(
                        blank=True,
                        db_column="lines_new",
                        default=dict,
                        encoder=django.core.serializers.json.DjangoJSONEncoder,
                        help_text="Payment term lines",
                    ),
                ),
                (
                    "lines_tmp",
                    models.JSONField(
                        blank=True,
                        db_column="lines",
                        default=dict,
                        encoder=django.core.serializers.json.DjangoJSONEncoder,
                        help_text="Payment term lines",
                        null=True,
                    ),
                ),
            ],
            bases=(
                ims.api.mixins.ApiUtilsMixin,
                ims.models.mixin.SanitizeCharFieldMixin,
                ims.models.mixin.ChangedFieldLookupMixin,
                mp_utils.model_mixins.decimal_field_check_mixin.DecimalFieldCheckMixin,
                models.Model,
            ),
        ),
        migrations.CreateModel(
            name="Payment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "status",
                    django_xworkflows.models.StateField(
                        max_length=16,
                        workflow=django_xworkflows.models._SerializedWorkflow(
                            initial_state="0",
                            name="PaymentWorkflow",
                            states=[
                                "0",
                                "77",
                                "60",
                                "61",
                                "80",
                                "85",
                                "90",
                                "100",
                                "2000",
                                "1000",
                            ],
                        ),
                    ),
                ),
                (
                    "consistency_status",
                    django_xworkflows.models.StateField(
                        max_length=19,
                        verbose_name="Consistency status",
                        workflow=django_xworkflows.models._SerializedWorkflow(
                            initial_state="consistency_unknown",
                            name="PaymentConsistencyWorkflow",
                            states=[
                                "consistency_unknown",
                                "inconsistent",
                                "consistent",
                            ],
                        ),
                    ),
                ),
                ("consistency_last_check", models.DateTimeField(blank=True, null=True)),
                (
                    "payment_backend",
                    models.CharField(
                        choices=[
                            ("external", "External"),
                            ("hipay_tpp", "Hipay"),
                            ("psp_gateway", "PSP Gateway"),
                            ("be2bill", "Be-2-Bill"),
                            ("ingenico", "Ingenico"),
                        ],
                        default="external",
                        max_length=255,
                    ),
                ),
                (
                    "external_id",
                    models.CharField(
                        blank=True,
                        help_text="IZBERG relies on this field to capture / refund on your behalf when authorization is made on external modules.",
                        max_length=255,
                        null=True,
                        verbose_name="Remote PSP transactionID / order-id",
                    ),
                ),
                (
                    "pre_auth_backend",
                    models.CharField(
                        blank=True,
                        help_text="From what payment backend has this payment preauth have been saved",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "pre_auth_id",
                    models.CharField(
                        blank=True,
                        help_text="ID of backend attached pre auth",
                        max_length=255,
                        null=True,
                    ),
                ),
                ("created_on", models.DateTimeField(auto_now_add=True, null=True)),
                ("last_updated", models.DateTimeField(auto_now=True, null=True)),
                (
                    "authorized_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Authorized amount.",
                        max_digits=20,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00"))
                        ],
                    ),
                ),
                (
                    "collected_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="Amount already collected by a payment gateway.",
                        max_digits=20,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00"))
                        ],
                    ),
                ),
                (
                    "refunded_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="Amount from this payment gateway that has already been refunded.",
                        max_digits=20,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00"))
                        ],
                    ),
                ),
                (
                    "voucher_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="Amount paid via voucher(s).",
                        max_digits=20,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00"))
                        ],
                    ),
                ),
                (
                    "refunded_voucher_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="Voucher amount that has already been refunded.",
                        max_digits=20,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00"))
                        ],
                    ),
                ),
                (
                    "disputed_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Disputed amount.",
                        max_digits=20,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00"))
                        ],
                    ),
                ),
                (
                    "payment_type",
                    models.CharField(
                        choices=[
                            ("full", "Full payment"),
                            ("progress", "Progress payment"),
                            ("deposit", "Deposit payment"),
                            ("application_fees", "Operator fees payment"),
                        ],
                        default="full",
                        max_length=32,
                    ),
                ),
                (
                    "payment_name",
                    models.CharField(
                        blank=True,
                        help_text="Optional name of the payment",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "specific_to_collect_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Specific amount to collect (mandatory for non-full payments)",
                        max_digits=20,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00"))
                        ],
                    ),
                ),
                (
                    "specific_commission_rate",
                    models.DecimalField(
                        blank=True,
                        decimal_places=17,
                        help_text="Payment's commission rate. This overrides application's behaviour.",
                        max_digits=20,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00")),
                            django.core.validators.MaxValueValidator(Decimal("100.00")),
                        ],
                    ),
                ),
                (
                    "_collect_detail",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.BigIntegerField(blank=True, null=True),
                        blank=True,
                        db_column="_collect_detail",
                        default=list,
                        null=True,
                        size=None,
                    ),
                ),
            ],
            bases=(
                django_xworkflows.models.BaseWorkflowEnabled,
                ims.api.mixins.ApiUtilsMixin,
                ims.models.mixin.SanitizeCharFieldMixin,
                ims.models.mixin.ChangedFieldLookupMixin,
                mp_utils.model_mixins.decimal_field_check_mixin.DecimalFieldCheckMixin,
                models.Model,
            ),
        ),
        migrations.CreateModel(
            name="PaymentBackendMigration",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_on",
                    models.DateTimeField(
                        auto_now_add=True, null=True, verbose_name="Created on"
                    ),
                ),
                (
                    "last_modified",
                    models.DateTimeField(
                        auto_now=True, null=True, verbose_name="Last modified"
                    ),
                ),
                ("kyc_start_date", models.DateTimeField(blank=True)),
                ("start_date", models.DateTimeField()),
                ("end_date", models.DateTimeField()),
                ("from_psp_object_id", models.PositiveIntegerField()),
                ("to_psp_object_id", models.PositiveIntegerField()),
            ],
            options={
                "abstract": False,
            },
            bases=(
                ims.api.mixins.ApiUtilsMixin,
                ims.models.mixin.SanitizeCharFieldMixin,
                ims.models.mixin.ChangedFieldLookupMixin,
                mp_utils.model_mixins.decimal_field_check_mixin.DecimalFieldCheckMixin,
                models.Model,
            ),
        ),
        migrations.CreateModel(
            name="PaymentConsistencyStatusLog",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "transition",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="transition"
                    ),
                ),
                (
                    "from_state",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="from state"
                    ),
                ),
                (
                    "to_state",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="to state"
                    ),
                ),
                (
                    "timestamp",
                    models.DateTimeField(
                        db_index=True,
                        default=django.utils.timezone.now,
                        verbose_name="performed at",
                    ),
                ),
                ("comment", models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="PaymentInconsistency",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_on",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created on"),
                ),
                (
                    "last_modified",
                    models.DateTimeField(auto_now=True, verbose_name="Last modified"),
                ),
                (
                    "status",
                    django_xworkflows.models.StateField(
                        max_length=16,
                        verbose_name="Status",
                        workflow=django_xworkflows.models._SerializedWorkflow(
                            initial_state="open",
                            name="PaymentInconsistencyWorkflow",
                            states=["open", "resolved", "ignored"],
                        ),
                    ),
                ),
                (
                    "inconsistent_field",
                    models.CharField(
                        choices=[
                            ("requested_amount", "Requested amount"),
                            ("authorized_amount", "Authorized amount"),
                            ("collected_amount", "Collected amount"),
                            ("refunded_amount", "Refunded amount"),
                            ("refundable_amount", "Refundable amount"),
                            ("disputed_amount", "Disputed amount"),
                            ("on_escrow_amount", "On escrow amount"),
                            ("ventilated_amount", "Ventilated amount"),
                            (
                                "authorization_expiration_date",
                                "Authorization expiration date",
                            ),
                        ],
                        max_length=64,
                    ),
                ),
                ("received_value", models.CharField(blank=True, max_length=100)),
                ("expected_value", models.CharField(blank=True, max_length=100)),
            ],
            options={
                "verbose_name_plural": "Payment inconsistencies",
                "ordering": ["-id"],
            },
            bases=(
                django_xworkflows.models.BaseWorkflowEnabled,
                ims.api.mixins.ApiUtilsMixin,
                ims.models.mixin.SanitizeCharFieldMixin,
                ims.models.mixin.ChangedFieldLookupMixin,
                mp_utils.model_mixins.decimal_field_check_mixin.DecimalFieldCheckMixin,
                models.Model,
            ),
        ),
        migrations.CreateModel(
            name="PaymentInconsistencyTransitionLog",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "transition",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="transition"
                    ),
                ),
                (
                    "from_state",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="from state"
                    ),
                ),
                (
                    "to_state",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="to state"
                    ),
                ),
                (
                    "timestamp",
                    models.DateTimeField(
                        db_index=True,
                        default=django.utils.timezone.now,
                        verbose_name="performed at",
                    ),
                ),
                ("comment", models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="PaymentMethod",
            fields=[
                ("id", models.IntegerField(auto_created=True, unique=True)),
                (
                    "name",
                    models.CharField(
                        help_text="Method's name", max_length=255, verbose_name="Name"
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        choices=[
                            ("cheque", "Cheque"),
                            ("visa", "VISA"),
                            ("mastercard", "Mastercard"),
                            ("cb", "CB"),
                            ("amex", "American express"),
                            ("multibanco", "Multi banco"),
                            ("bcmc", "Bancontact Mister Cash"),
                            ("giropay", "Giropay"),
                            ("ideal", "iDeal"),
                            ("klarna", "Klarna Invoice"),
                            ("p24", "Przelewy 24"),
                            ("paypal", "PayPal"),
                            ("sofort", "Sofort"),
                            ("sdd", "Sepa direct debit"),
                            ("bankwire", "Bank wire"),
                        ],
                        default="visa",
                        help_text="payment_method_codes",
                        max_length=15,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "method_kind",
                    models.CharField(
                        choices=[
                            ("debit-card", "Debit card"),
                            ("credit-card", "Credit card"),
                            ("e-wallet", "E Wallet"),
                            ("physical", "Physical payment mode"),
                            ("realtime-banking", "Real time banking"),
                            ("bankwire", "Bank wire"),
                            ("direct-transfer", "Direct transfer"),
                        ],
                        default="debit-card",
                        help_text="Payment method kind - implies a specific behaviour on PSP",
                        max_length=20,
                    ),
                ),
                (
                    "max_collect_time",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="How long will you wait for payment (in days)?",
                        max_digits=5,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0"))
                        ],
                        verbose_name="Max collect time",
                    ),
                ),
                (
                    "payment_behaviour",
                    models.CharField(
                        choices=[
                            ("authorization_capture", "Authorization-capture"),
                            ("direct_collect", "direct_collect"),
                            ("passive", "Passive"),
                        ],
                        help_text="Payment behaviour type.",
                        max_length=50,
                        verbose_name="Payment behaviour",
                    ),
                ),
            ],
            bases=(ims.api.mixins.ApiUtilsMixin, models.Model),
        ),
        migrations.CreateModel(
            name="PaymentTerm",
            fields=[
                (
                    "created_on",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created on"),
                ),
                (
                    "last_modified",
                    models.DateTimeField(auto_now=True, verbose_name="Last modified"),
                ),
                (
                    "external_id",
                    models.CharField(
                        blank=True,
                        help_text="Use it to store external matching id",
                        max_length=255,
                        null=True,
                        verbose_name="External ID",
                    ),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=None, editable=False, primary_key=True, serialize=False
                    ),
                ),
                (
                    "status",
                    django_xworkflows.models.StateField(
                        max_length=16,
                        verbose_name="status",
                        workflow=django_xworkflows.models._SerializedWorkflow(
                            initial_state="initial",
                            name="PaymentTermWorkflow",
                            states=["initial", "active", "deleted"],
                        ),
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                (
                    "label_on_invoice",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(
                django_xworkflows.models.BaseWorkflowEnabled,
                ims.api.mixins.ApiUtilsMixin,
                ims.models.mixin.SanitizeCharFieldMixin,
                ims.models.mixin.ChangedFieldLookupMixin,
                mp_utils.model_mixins.decimal_field_check_mixin.DecimalFieldCheckMixin,
                models.Model,
            ),
        ),
        migrations.CreateModel(
            name="PaymentTermLine",
            fields=[
                (
                    "created_on",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created on"),
                ),
                (
                    "last_modified",
                    models.DateTimeField(auto_now=True, verbose_name="Last modified"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=None, editable=False, primary_key=True, serialize=False
                    ),
                ),
                (
                    "due_type",
                    models.CharField(
                        choices=[("balance", "Balance")],
                        default="balance",
                        max_length=15,
                    ),
                ),
                (
                    "value",
                    models.PositiveSmallIntegerField(
                        blank=True,
                        default=None,
                        help_text="Leave blank for balance",
                        null=True,
                    ),
                ),
                ("number_of_days", models.PositiveSmallIntegerField()),
                (
                    "day_of_the_month",
                    models.PositiveSmallIntegerField(
                        default=1,
                        help_text="Day of the month for day_after_defined_delay term type",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(28),
                        ],
                    ),
                ),
                (
                    "term_type",
                    models.CharField(
                        choices=[
                            ("days_after_invoice_date", "Days after invoice date"),
                            ("net_eom", "Net EOM (End of month)"),
                            ("day_after_defined_delay", "Day after defined delay"),
                        ],
                        default=None,
                        max_length=30,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(
                ims.api.mixins.ApiUtilsMixin,
                ims.models.mixin.SanitizeCharFieldMixin,
                ims.models.mixin.ChangedFieldLookupMixin,
                mp_utils.model_mixins.decimal_field_check_mixin.DecimalFieldCheckMixin,
                models.Model,
            ),
        ),
        migrations.CreateModel(
            name="PaymentTermTransitionLog",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "transition",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="transition"
                    ),
                ),
                (
                    "from_state",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="from state"
                    ),
                ),
                (
                    "to_state",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="to state"
                    ),
                ),
                (
                    "timestamp",
                    models.DateTimeField(
                        db_index=True,
                        default=django.utils.timezone.now,
                        verbose_name="performed at",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="PaymentTransitionLog",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "transition",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="transition"
                    ),
                ),
                (
                    "from_state",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="from state"
                    ),
                ),
                (
                    "to_state",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="to state"
                    ),
                ),
                (
                    "timestamp",
                    models.DateTimeField(
                        db_index=True,
                        default=django.utils.timezone.now,
                        verbose_name="performed at",
                    ),
                ),
                (
                    "content_id",
                    models.PositiveIntegerField(
                        blank=True, db_index=True, null=True, verbose_name="Content id"
                    ),
                ),
                ("comment", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "content_type",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contenttypes.contenttype",
                        verbose_name="Content type",
                    ),
                ),
                (
                    "payment",
                    models.ForeignKey(
                        db_index=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="payment.payment",
                    ),
                ),
            ],
        ),
    ]
