# -*- coding: utf-8 -*-

from django.contrib import admin, messages
from django.http import HttpResponseRedirect
from django.template.response import TemplateResponse
from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from django_object_actions.utils import DjangoObjectActions, takes_instance_or_queryset
from mp_utils.admin import RawIDModelAdmin

from .models import (
    AlgoliaChannelOutput,
    CacheStorageBackend,
    ChannelQueueManager,
    DBLoggingBackend,
    DynDBBackend,
    ElasticsearchChannelOutput,
    ProductChannel,
    ProductChannelLogEvent,
    ProductChannelTransitionLog,
)


class ProductChannelAdmin(RawIDModelAdmin):
    get_latest_by = "id"
    ordering = ["-id"]
    search_fields = (
        "name",
        "label",
        "description",
        "application__id",
        "application__name",
        "application__namespace",
    )
    list_display = (
        "smart_name",
        "id",
        "status",
        "label",
        "application",
        "language",
        "currency",
        "created_on",
    )
    list_filter = (
        "created_on",
        "last_modified",
        "status",
        "storage_backend_name",
        "language",
        "channel_type",
        "item_type",
    )
    actions = [
        "trigger_low_priority_full_sync",
        "trigger_normal_priority_full_sync",
        "trigger_high_priority_full_sync",
        "create_elasticsearch_main_ouput",
        "update_elasticsearch_mapping_and_reindex",
        "create_algolia_output",
    ]

    def trigger_low_priority_full_sync(self, request, queryset):
        return self._trigger_full_sync(request=request, queryset=queryset)

    def trigger_normal_priority_full_sync(self, request, queryset):
        return self._trigger_full_sync(
            request=request, queryset=queryset, priority="normal"
        )

    def trigger_high_priority_full_sync(self, request, queryset):
        return self._trigger_full_sync(
            request=request, queryset=queryset, priority="high"
        )

    trigger_low_priority_full_sync.short_description = "Trigger low priority full sync"
    trigger_normal_priority_full_sync.short_description = (
        "Trigger normal priority full sync"
    )
    trigger_high_priority_full_sync.short_description = (
        "Trigger high priority full sync"
    )

    def _trigger_full_sync(self, request, queryset, priority=None):
        """Ask for full sync."""
        channel_ids = queryset.values_list("id", flat=True)
        data = {"event": "full_sync"}
        if priority:
            data["priority"] = priority
        for channel_id in channel_ids:
            ProductChannel.cls_incoming_event(channel_id=channel_id, event_data=data)
        self.message_user(
            request,
            "%s full_sync event(s) sent to queue: %s."
            % (len(channel_ids), ", ".join(map(str, channel_ids))),
        )

    def create_elasticsearch_main_ouput(self, request, queryset):
        for channel in queryset:
            channel.actions.handle_elasticsearch_creation()

        self.message_user(
            request,
            "ElasticSearch ouput have been created for the selected channels",
        )

    create_elasticsearch_main_ouput.short_description = (
        "Create elastic search main ouput"
    )

    def update_elasticsearch_mapping_and_reindex(self, request, queryset):
        """
        Action pour mettre à jour le mapping Elasticsearch et réindexer.
        """
        for channel in queryset:
            # Récupérer tous les ElasticsearchChannelOutput associés
            es_outputs = ElasticsearchChannelOutput.objects.filter(channel=channel)
            if not es_outputs.exists():
                self.message_user(
                    request,
                    f"Aucun ElasticsearchChannelOutput associé au canal {channel}.",
                    messages.WARNING,
                )
                continue
            for es_output in es_outputs:
                try:
                    es_output.update_mapping_and_reindex()
                    self.message_user(
                        request,
                        f"Le mapping Elasticsearch a été mis à jour et la réindexation a été effectuée pour le canal {channel}, output {es_output.id}.",
                        messages.SUCCESS,
                    )
                except Exception as e:
                    self.message_user(
                        request,
                        f"Erreur lors de la mise à jour du mapping et de la réindexation pour le canal {channel}, output {es_output.id}: {e}",
                        messages.ERROR,
                    )

    update_elasticsearch_mapping_and_reindex.short_description = (
        "Mettre à jour le mapping Elasticsearch et réindexer"
    )

    def create_algolia_output(self, request, queryset):
        if queryset.count() != 1:
            self.message_user(
                request, "Please select exactly one channel.", messages.ERROR
            )
            return HttpResponseRedirect(request.get_full_path())

        channel = queryset.first()
        url = (
            reverse("admin:channels_algoliachanneloutput_add")
            + f"?channel={channel.id}"
        )
        return HttpResponseRedirect(url)

    create_algolia_output.short_description = (
        "Create Algolia Output for selected channel"
    )


class ProductChannelLogEventAdmin(RawIDModelAdmin):
    get_latest_by = "id"
    ordering = ["-id"]
    search_fields = (
        "channel__id",
        "merchant__id",
        "merchant__application__id",
        "merchant__application__name",
        "merchant__application__namespace",
    )
    list_display = (
        "timestamp",
        "log_level",
        "channel",
        "message",
        "other_channel",
        "event",
        "merchant",
        "outgoing",
        "action",
        "log_type",
    )
    list_filter = (
        "timestamp",
        "log_level",
        "event",
        "action",
        "log_type",
        "generic_msg",
    )


class DBLoggingBackendAdmin(RawIDModelAdmin):
    search_fields = (
        "channel__application__namespace",
        "channel__merchant__slug",
    )
    get_latest_by = "id"
    ordering = ["-id"]
    raw_id_fields = ("channel",)
    list_display = ("log_level", "channel")
    list_filter = ("log_level",)


class AlgoliaChannelOutputAdmin(DjangoObjectActions, RawIDModelAdmin):
    get_latest_by = "id"
    ordering = ["-id"]
    list_display = (
        "id",
        "status",
        "channel",
        "application",
        "algolia_application_id",
        "last_full_generation",
        "last_updated",
    )
    list_filter = ("status", "last_full_generation", "last_updated")
    search_fields = (
        "id",
        "channel__id",
        "channel__application__id",
        "channel__application__namespace",
        "algolia_application_id",
    )
    readonly_fields = (
        "last_full_generation",
        "last_updated",
    )
    _actions = [
        "full_sync_outputs",
        "activate",
        "pause",
        "delete_action",
        "set_as_favorite",
    ]
    change_actions = _actions[:]
    actions = _actions[:]

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        if not obj and "channel" in request.GET:
            channel_id = request.GET.get("channel")
            form.base_fields["channel"].initial = channel_id
            form.base_fields["channel"].widget.attrs["readonly"] = True

        form.base_fields["status"].initial = "ACTIF"
        return form

    def save_model(self, request, obj, form, change):

        super().save_model(request, obj, form, change)
        obj.save()

        self.message_user(
            request,
            f"Algolia index created and indexed successfully for {obj.channel}.",
            messages.SUCCESS,
        )

    def application(self, obj):
        return obj.channel.application.namespace

    application.short_description = "Application"
    application.admin_order_field = "channel__application__namespace"

    @takes_instance_or_queryset
    def full_sync_outputs(self, request, queryset):
        # breaks if imported at module level as require a database to be up
        from .tasks import async_generate_output

        for obj in queryset:
            async_generate_output.delay(obj.id, "algolia", obj.channel.application_id)
        messages.success(request, _("Algolia outputs will be generated asynchronously"))

    full_sync_outputs.label = _("Full sync output")
    full_sync_outputs.short_description = _("Full sync the selected outputs")

    def _run_transition(self, request, queryset, transition_name):
        processed = 0
        success = 0
        for obj in queryset:
            method = getattr(obj, transition_name)
            if method.is_available():
                method()
                success += 1
            processed += 1
        has_error = processed != success
        method = messages.error if has_error else messages.success
        method(
            request,
            _("Successfully processed {} objects out of {}".format(success, processed)),
        )

    @takes_instance_or_queryset
    def activate(self, request, queryset):
        self._run_transition(request, queryset, "activate")

    activate.label = _("Activate")
    activate.short_description = _("Activate the selected outputs")

    @takes_instance_or_queryset
    def pause(self, request, queryset):
        self._run_transition(request, queryset, "pause")

    pause.label = _("Pause")
    pause.short_description = _("Pause the selected outputs")

    @takes_instance_or_queryset
    def delete_action(self, request, queryset):
        self._run_transition(request, queryset, "delete_action")

    delete_action.label = _("Soft delete")
    delete_action.short_description = _("Soft delete the selected outputs")

    def get_actions(self, request):
        """Disable bulk hard deletion"""
        actions = super(AlgoliaChannelOutputAdmin, self).get_actions(request)
        if "delete_selected" in actions:
            del actions["delete_selected"]
        return actions

    def set_as_favorite(self, request, queryset):
        if queryset.count() != 1:
            self.message_user(
                request,
                "Veuillez sélectionner un seul output à la fois.",
                messages.ERROR,
            )
            return HttpResponseRedirect(request.get_full_path())

        algolia_output = queryset.first()

        all_outputs = AlgoliaChannelOutput.objects.filter(
            channel=algolia_output.channel
        )
        all_outputs.update(is_favorite=False)

        algolia_output.is_favorite = True
        algolia_output.save()

        self.message_user(
            request,
            f"L'output {algolia_output} a été défini comme favori.",
            messages.SUCCESS,
        )

    set_as_favorite.short_description = "Définir comme favori"


class ProductChannelTransitionLogAdmin(RawIDModelAdmin):
    get_latest_by = "id"
    ordering = ["-id"]
    list_display = (
        "timestamp",
        "channel",
        "transition",
        "from_state",
        "to_state",
        "user",
        "comment",
    )
    list_filter = (
        "timestamp",
        "transition",
        "from_state",
        "to_state",
    )
    search_fields = ["channel__id"]


class CacheStorageBackendAdmin(RawIDModelAdmin):
    get_latest_by = "channel"
    ordering = ["-channel"]
    list_display = ("channel", "last_full_sync")
    list_filter = ("last_full_sync",)


class DynDBBackendAdmin(RawIDModelAdmin):
    search_fields = (
        "channel__application__namespace",
        "channel__merchant__slug",
    )
    get_latest_by = "channel"
    ordering = ["-channel"]
    list_display = ("channel", "last_full_sync")
    list_filter = ("last_full_sync",)


class ElasticsearchChannelOutputAdmin(DjangoObjectActions, RawIDModelAdmin):
    get_latest_by = "id"
    ordering = ["-id"]
    list_display = (
        "id",
        "status",
        "channel",
        "application",
        "elasticsearch_alias",
        "last_full_generation",
        "last_updated",
    )
    list_filter = ("status", "last_full_generation", "last_updated")
    search_fields = (
        "id",
        "channel__id",
        "channel__application__id",
        "channel__application__namespace",
        "elasticsearch_alias",
    )
    readonly_fields = (
        "last_full_generation",
        "last_updated",
    )
    _actions = ["full_sync_outputs", "activate", "pause", "delete_action"]
    change_actions = _actions[:]
    actions = _actions[:]

    def application(self, obj):
        return obj.channel.application.namespace

    application.short_description = "Application"
    application.admin_order_field = "channel__application__namespace"

    @takes_instance_or_queryset
    def full_sync_outputs(self, request, queryset):
        # breaks if imported at module level as require a database to be up
        from .tasks import async_generate_output

        for obj in queryset:
            async_generate_output.delay(
                obj.id, "elasticsearch", obj.channel.application_id
            )
        messages.success(
            request, _("Elasticsearch outputs will be generated asynchronously")
        )

    full_sync_outputs.label = _("Full sync output")
    full_sync_outputs.short_description = _("Full sync the selected outputs")

    def _run_transition(self, request, queryset, transition_name):
        processed = 0
        success = 0
        for obj in queryset:
            method = getattr(obj, transition_name)
            if method.is_available():
                method()
                success += 1
            processed += 1
        has_error = processed != success
        method = messages.error if has_error else messages.success
        method(
            request,
            _("Successfully processed {} objects out of {}".format(success, processed)),
        )

    @takes_instance_or_queryset
    def activate(self, request, queryset):
        self._run_transition(request, queryset, "activate")

    activate.label = _("Activate")
    activate.short_description = _("Activate the selected outputs")

    @takes_instance_or_queryset
    def pause(self, request, queryset):
        self._run_transition(request, queryset, "pause")

    pause.label = _("Pause")
    pause.short_description = _("Pause the selected outputs")

    @takes_instance_or_queryset
    def delete_action(self, request, queryset):
        self._run_transition(request, queryset, "delete_action")

    delete_action.label = _("Soft delete")
    delete_action.short_description = _("Soft delete the selected outputs")

    def get_actions(self, request):
        """Disable bulk hard deletion"""
        actions = super(ElasticsearchChannelOutputAdmin, self).get_actions(request)
        if "delete_selected" in actions:
            del actions["delete_selected"]
        return actions


class ChannelQueueManagerAdmin(admin.ModelAdmin):
    """
    Custom html page to display info about the channels indexes
    """

    change_list_template = "admin/channel_manager.html"

    def changelist_view(self, request, extra_context=None):
        extra_context = {
            "app_label": "channel_manager",
        }
        return TemplateResponse(request, self.change_list_template, extra_context)


admin.site.register(ProductChannel, ProductChannelAdmin)
admin.site.register(ProductChannelLogEvent, ProductChannelLogEventAdmin)
admin.site.register(DBLoggingBackend, DBLoggingBackendAdmin)
admin.site.register(AlgoliaChannelOutput, AlgoliaChannelOutputAdmin)
admin.site.register(ProductChannelTransitionLog, ProductChannelTransitionLogAdmin)
admin.site.register(CacheStorageBackend, CacheStorageBackendAdmin)
admin.site.register(DynDBBackend, DynDBBackendAdmin)
admin.site.register(ElasticsearchChannelOutput, ElasticsearchChannelOutputAdmin)
admin.site.register(ChannelQueueManager, ChannelQueueManagerAdmin)
