# -*- coding: utf-8 -*-
import logging
from decimal import Decimal

import mock
from apps.channels.models import AlgoliaChannelOutput
from apps.ice_applications.app_conf_settings import (
    AlgoliaAdminApiKeyId,
    AlgoliaApplicationId,
)
from apps.testing.factories import ApplicationFactory
from django.test.utils import override_settings
from ims.tests import BaseTestCase

logger = logging.getLogger(__name__)


class AlgoliaChannelOutputTestCase(BaseTestCase):
    @mock.patch("apps.channels.models.AlgoliaChannelOutput.client")
    def setUp(self, mocked_client):
        super(AlgoliaChannelOutputTestCase, self).setUp()
        self.application = ApplicationFactory()
        self.channel = self.application.channel_manager.product_offer_channel
        self.output = AlgoliaChannelOutput.objects.create(channel=self.channel)

    def test_algolia_output_remove_variations_if_101(self):
        # given
        item_data = {"id": 123, "variations": 101 * [{"foo": "bar"}]}

        # when
        result = self.output._process_item_data(item_data)
        # then
        self.assertEqual(
            result,
            {
                "id": 123,
                "last_updated": mock.ANY,
                "objectID": 123,
                "partial_data": True,
                "variation_count": 101,
            },
        )

    def test_algolia_output_truncates_long_description(self):
        # given
        item_data = {"id": 123, "description": "a" * 300}

        # when
        result = self.output._process_item_data(item_data)

        # then
        self.assertTrue(result["description_truncated"])
        self.assertEqual(result["description"], "a" * 256)

    def test_algolia_output_truncates_long_details(self):
        # given
        item_data = {"id": 123, "details": "a" * 10}

        # when
        result = self.output._process_item_data(item_data)

        # then
        self.assertTrue(result["details_truncated"])
        self.assertEqual(result["details"], "")

    def test_algolia_output_truncates_long_legal_disclaimer(self):
        # given
        item_data = {"id": 123, "legal_disclaimer": "a" * 10}

        # when
        result = self.output._process_item_data(item_data)

        # then
        self.assertTrue(result["legal_disclaimer_truncated"])
        self.assertEqual(result["legal_disclaimer"], "")

    def test_algolia_output_truncates_long_safety_warning(self):
        # given
        item_data = {"id": 123, "safety_warning": "a" * 10}

        # when
        result = self.output._process_item_data(item_data)

        # then
        self.assertTrue(result["safety_warning_truncated"])
        self.assertEqual(result["safety_warning"], "")

    def test_algolia_output_doesnt_truncate_short_description(self):
        # given
        item_data = {"id": 123, "description": "a" * 200}

        # when
        result = self.output._process_item_data(item_data)

        # then
        self.assertNotIn("description_truncated", result)
        self.assertEqual(result["description"], "a" * 200)

    def test_algolia_output_truncates_long_description_in_best_offer(self):
        # given
        item_data = {"id": 123, "best_offer": {"description": "a" * 300}}

        # when
        result = self.output._process_item_data(item_data)

        # then
        self.assertTrue(result["best_offer"]["description_truncated"])
        self.assertEqual(result["best_offer"]["description"], "a" * 256)

    def test_algolia_output_truncates_long_details_in_best_offer(self):
        # given
        item_data = {"id": 123, "best_offer": {"details": "a" * 10}}

        # when
        result = self.output._process_item_data(item_data)

        # then
        self.assertTrue(result["best_offer"]["details_truncated"])
        self.assertEqual(result["best_offer"]["details"], "")

    def test_algolia_output_truncates_long_legal_disclaimer_in_best_offer(self):
        # given
        item_data = {"id": 123, "best_offer": {"legal_disclaimer": "a" * 10}}

        # when
        result = self.output._process_item_data(item_data)

        # then
        self.assertTrue(result["best_offer"]["legal_disclaimer_truncated"])
        self.assertEqual(result["best_offer"]["legal_disclaimer"], "")

    def test_algolia_output_truncates_long_safety_warning_in_best_offer(self):
        # given
        item_data = {"id": 123, "best_offer": {"safety_warning": "a" * 10}}

        # when
        result = self.output._process_item_data(item_data)

        # then
        self.assertTrue(result["best_offer"]["safety_warning_truncated"])
        self.assertEqual(result["best_offer"]["safety_warning"], "")

    def test_algolia_output_truncates_long_description_in_product(self):
        # given
        item_data = {"id": 123, "product": {"description": "a" * 300}}

        # when
        result = self.output._process_item_data(item_data)

        # then
        self.assertTrue(result["product"]["description_truncated"])
        self.assertEqual(result["product"]["description"], "a" * 256)

    def test_algolia_output_truncates_long_details_in_product(self):
        # given
        item_data = {"id": 123, "product": {"details": "a" * 10}}

        # when
        result = self.output._process_item_data(item_data)

        # then
        self.assertTrue(result["product"]["details_truncated"])
        self.assertEqual(result["product"]["details"], "")

    def test_algolia_output_truncates_long_description_in_other_offers(self):
        # given
        item_data = {
            "id": 123,
            "other_offers": [
                {"description": "a" * 300},
                {"description": "a" * 100},
                {"description": "a" * 300},
            ],
        }

        # when
        result = self.output._process_item_data(item_data)

        # then
        self.assertEqual(
            result["other_offers"],
            [
                {"description": "a" * 256, "description_truncated": True},
                {"description": "a" * 100},
                {"description": "a" * 256, "description_truncated": True},
            ],
        )

    def test_algolia_output_truncates_long_details_in_other_offers(self):
        # given
        item_data = {
            "id": 123,
            "other_offers": [
                {"details": "a" * 10},
                {"details": "a" * 10},
            ],
        }

        # when
        result = self.output._process_item_data(item_data)

        # then
        self.assertEqual(
            result["other_offers"],
            [
                {"details": "", "details_truncated": True},
                {"details": "", "details_truncated": True},
            ],
        )

    def test_algolia_output_truncates_long_legal_disclaimer_in_other_offers(self):
        # given
        item_data = {
            "id": 123,
            "other_offers": [
                {"legal_disclaimer": "a" * 10},
                {"legal_disclaimer": "a" * 10},
            ],
        }

        # when
        result = self.output._process_item_data(item_data)

        # then
        self.assertEqual(
            result["other_offers"],
            [
                {"legal_disclaimer": "", "legal_disclaimer_truncated": True},
                {"legal_disclaimer": "", "legal_disclaimer_truncated": True},
            ],
        )

    def test_algolia_output_truncates_long_safety_warnings_in_other_offers(self):
        # given
        item_data = {
            "id": 123,
            "other_offers": [
                {"safety_warning": "a" * 10},
                {"safety_warning": "a" * 10},
            ],
        }

        # when
        result = self.output._process_item_data(item_data)

        # then
        self.assertEqual(
            result["other_offers"],
            [
                {"safety_warning": "", "safety_warning_truncated": True},
                {"safety_warning": "", "safety_warning_truncated": True},
            ],
        )

    def test_algolia_output_disabled_description_truncating(self):
        # given
        item_data = {"id": 123, "description": "a" * 300}
        self.output.options[self.output.MAX_SIZE_DESCRIPTION_OPTION] = None

        # when
        result = self.output._process_item_data(item_data)

        # then
        self.assertNotIn("description_truncated", result)
        self.assertEqual(result["description"], "a" * 300)

    def test_algolia_output_disabled_details_truncating(self):
        # given
        item_data = {"id": 123, "details": "a" * 10}
        self.output.options[self.output.MAX_SIZE_DETAILS_OPTION] = None

        # when
        result = self.output._process_item_data(item_data)

        # then
        self.assertNotIn("details_truncated", result)
        self.assertEqual(result["details"], "a" * 10)

    def test_algolia_output_disabled_legal_disclaimer_truncating(self):
        # given
        item_data = {"id": 123, "legal_disclaimer": "a" * 10}
        self.output.options[self.output.MAX_SIZE_LEGAL_DISCLAIMER_OPTION] = None

        # when
        result = self.output._process_item_data(item_data)

        # then
        self.assertNotIn("legal_disclaimer_truncated", result)
        self.assertEqual(result["legal_disclaimer"], "a" * 10)

    def test_algolia_output_disabled_safety_warning_truncating(self):
        # given
        item_data = {"id": 123, "safety_warning": "a" * 10}
        self.output.options[self.output.MAX_SIZE_SAFETY_WARNING_OPTION] = None

        # when
        result = self.output._process_item_data(item_data)

        # then
        self.assertNotIn("safety_warning_truncated", result)
        self.assertEqual(result["safety_warning"], "a" * 10)

    def test_algolia_output_modify_description_truncating_limit(self):
        # given
        item_data = {"id": 123, "description": "a" * 300}
        self.output.options[self.output.MAX_SIZE_DESCRIPTION_OPTION] = 200

        # when
        result = self.output._process_item_data(item_data)

        # then
        self.assertTrue(result["description_truncated"])
        self.assertEqual(result["description"], "a" * 200, "")

    def test_algolia_output_modify_details_truncating_limit(self):
        # given
        item_data = {"id": 123, "details": "a" * 300}
        self.output.options[self.output.MAX_SIZE_DETAILS_OPTION] = 200

        # when
        result = self.output._process_item_data(item_data)

        # then
        self.assertTrue(result["details_truncated"])
        self.assertEqual(result["details"], "a" * 200)

    def test_algolia_output_modify_legal_disclaimer_truncating_limit(self):
        # given
        item_data = {"id": 123, "legal_disclaimer": "a" * 300}
        self.output.options[self.output.MAX_SIZE_LEGAL_DISCLAIMER_OPTION] = 200

        # when
        result = self.output._process_item_data(item_data)

        # then
        self.assertTrue(result["legal_disclaimer_truncated"])
        self.assertEqual(result["legal_disclaimer"], "a" * 200)

    def test_algolia_output_modify_safety_warning_truncating_limit(self):
        # given
        item_data = {"id": 123, "safety_warning": "a" * 300}
        self.output.options[self.output.MAX_SIZE_SAFETY_WARNING_OPTION] = 200

        # when
        result = self.output._process_item_data(item_data)

        # then
        self.assertTrue(result["safety_warning_truncated"])
        self.assertEqual(result["safety_warning"], "a" * 200)

    def test_null_fields_are_removed_by_default(self):
        item_data = {
            "id": 123,
            "null_should_be_dropped": None,
            "false_value": False,
            "true_value": True,
            "empty_list": [],
            "empty_dict": {},
        }

        # when
        result = self.output._process_item_data(item_data)

        # then
        self.assertEqual(
            result,
            {
                "id": 123,
                "objectID": 123,
                "last_updated": mock.ANY,
                "partial_data": False,
                "false_value": False,
                "true_value": True,
                "empty_list": [],
                "empty_dict": {},
            },
        )

    def test_null_fields_are_kept_if_keep_option_is_enabled(self):
        item_data = {
            "id": 123,
            "null_should_not_be_dropped": None,
            "false_value": False,
            "true_value": True,
            "empty_list": [],
            "empty_dict": {},
        }
        self.output.options[AlgoliaChannelOutput.KEEP_NULL_VALUES] = True

        # when
        result = self.output._process_item_data(item_data)

        # then
        self.assertEqual(
            result,
            {
                "id": 123,
                "objectID": 123,
                "last_updated": mock.ANY,
                "partial_data": False,
                "null_should_not_be_dropped": None,
                "false_value": False,
                "true_value": True,
                "empty_list": [],
                "empty_dict": {},
            },
        )

    @mock.patch(
        "apps.channels.models.AlgoliaChannelOutput._generate_algolia_index",
        lambda _: None,
    )
    def test_algolia_output_slims_down_variations_for_product_channel(
        self,
    ):
        product_data = {
            "id": 123,
            "best_offer": {"variations": 21 * [{"foo": "bar", "id": 1234}]},
            "other_offers": [
                {"variations": 19 * [{"foo": "bar", "id": 12345}]},
                {"variations": 25 * [{"foo": "bar", "id": 123456}]},
                {"variations": 102 * [{"foo": "bar", "id": 1234567}]},
            ],
        }
        product_channel_output = AlgoliaChannelOutput.objects.create(
            channel=self.application.channel_manager.backoffice_channel
        )
        result = product_channel_output._process_item_data(product_data)
        self.maxDiff = None
        self.assertEqual(
            result,
            {
                "id": 123,
                "best_offer": {
                    # IDs only
                    "partial_data": True,
                    "variations": 21 * [{"id": 1234}],
                    "variation_count": 21,
                },
                "other_offers": [
                    {
                        # Whole data
                        "variations": 19 * [{"foo": "bar", "id": 12345}],
                        "variation_count": 19,
                    },
                    {
                        # IDs only
                        "partial_data": True,
                        "variations": 25 * [{"id": 123456}],
                        "variation_count": 25,
                    },
                    {
                        # No variations at all
                        "partial_data": True,
                        "variation_count": 102,
                    },
                ],
                "partial_data": True,
                "objectID": 123,
                "last_updated": mock.ANY,
            },
        )

    def test_algolia_output_slims_down_variations_if_more_than_20_for_offer_channel(
        self,
    ):
        item_data = {"id": 123, "variations": 21 * [{"foo": "bar", "id": 1234}]}

        result = self.output._process_item_data(item_data)

        self.assertEqual(
            result,
            {
                "id": 123,
                # IDs only
                "variations": 21 * [{"id": 1234}],
                "objectID": 123,
                "last_updated": mock.ANY,
                "partial_data": True,
                "variation_count": 21,
            },
        )

    def test_algolia_output_keeps_only_1_assigned_images_when_less_than_20_var(
        self,
    ):
        item_data = {
            "id": 123,
            "variations": 20
            * [
                {
                    "foo": "bar",
                    "id": 1234,
                    "assigned_images": 5
                    * [{"display_url": "my_url", "title": None, "image_width": 600}],
                }
            ],
        }

        result = self.output._process_item_data(item_data)

        self.assertEqual(
            result,
            {
                "id": 123,
                "variations": 20
                * [
                    {
                        "foo": "bar",
                        "id": 1234,
                        "assigned_images": [
                            # only 1 image expected
                            {
                                "display_url": "my_url",
                                "image_width": 600,
                            }
                        ],
                    }
                ],
                "objectID": 123,
                "last_updated": mock.ANY,
                "partial_data": False,
                "variation_count": 20,
            },
        )

    def test_algolia_output_disable_variation_slimming_option_keeps_all_variation_data(
        self,
    ):
        item_data = {
            "id": 123,
            "variations": 21
            * [
                {
                    "foo": "bar",
                    "id": 1234,
                    "assigned_images": 5
                    * [{"display_url": "my_url", "title": None, "image_width": 600}],
                }
            ],
        }
        self.output.options[AlgoliaChannelOutput.DISABLE_VARIATION_SLIMMING] = True
        result = self.output._process_item_data(item_data)

        self.assertEqual(
            result,
            {
                "id": 123,
                "variations": 21
                * [
                    {
                        "foo": "bar",
                        "id": 1234,
                        # all images are kept, but still removing None values
                        "assigned_images": 5
                        * [{"display_url": "my_url", "image_width": 600}],
                    }
                ],
                "objectID": 123,
                "last_updated": mock.ANY,
                "partial_data": False,
                "variation_count": 21,
            },
        )

    def test_algolia_output_doesnt_return_geoloc_nor_tags_when_empty(self):
        # given
        item_data = {
            "id": 123,
        }
        # when
        result = self.output._process_item_data(item_data)
        # then
        expected = {
            # given fields
            "id": 123,
            # processed fields
            "last_updated": mock.ANY,
            "objectID": 123,
            "partial_data": False,
        }
        self.assertEqual(expected, result)

    def test_algolia_output_uses_given_location_infos(self):
        # given
        item_data = {
            "id": 123,
            "offer_longitude": Decimal("2.31024254"),
            "offer_latitude": Decimal("10.31024239"),
        }

        # when
        result = self.output._process_item_data(item_data)

        # then
        expected = {
            # given fields
            "id": 123,
            "offer_latitude": Decimal("10.31024239"),
            "offer_longitude": Decimal("2.31024254"),
            # processed fields
            "last_updated": mock.ANY,
            "objectID": 123,
            "partial_data": False,
            "_geoloc": [{"lat": Decimal("10.31024239"), "lng": Decimal("2.31024254")}],
        }
        self.assertEqual(expected, result)

    def test_algolia_output_uses_given_tags(self):
        # given
        item_data = {
            "id": 123,
            "tags": ["foo", "bar"],
        }

        # when
        result = self.output._process_item_data(item_data)
        # then
        expected = {
            # given fields
            "id": 123,
            "tags": ["foo", "bar"],
            # processed fields
            "last_updated": mock.ANY,
            "objectID": 123,
            "partial_data": False,
            "_tags": ["foo", "bar"],
        }
        self.assertEqual(expected, result)

    def test_algolia_deduplicates_item_ids_before_sending_request(self):
        # sending an item multiple times would return a
        # "400 Provided list of item keys contains duplicates"
        mocked_retrieve_list = mock.Mock()
        mocked_retrieve_list.return_value = []
        self.output.channel.storage_backend.retrieve_list = mocked_retrieve_list
        self.output.generate_output(item_ids=[3, 1, 2, 1, 3])
        mocked_retrieve_list.assert_called_once_with(item_ids=[3, 1, 2])


@mock.patch(
    "apps.channels.models.AlgoliaChannelOutput._generate_algolia_index",
    lambda _: None,
)
class AlgoliaChannelOutputCredentialsSelectionTestCase(BaseTestCase):
    def setUp(self):
        super(AlgoliaChannelOutputCredentialsSelectionTestCase, self).setUp()
        self.application = ApplicationFactory()
        self.channel = self.application.channel_manager.product_offer_channel

    def test_output_specific_credentials_used_if_set(self):
        output = AlgoliaChannelOutput.objects.create(
            channel=self.channel,
            algolia_application_id="my app id",
            algolia_admin_api_key="my api key",
        )

        self.assertEqual(output.algolia_credentials, ("my app id", "my api key"))

    def test_app_settings_credientials_used_and_stored_if_set_no_output_specific(self):
        self.application.set_setting(AlgoliaApplicationId, "42")
        self.application.set_setting(AlgoliaAdminApiKeyId, "api key")
        output = AlgoliaChannelOutput.objects.create(channel=self.channel)

        self.assertEqual(output.algolia_credentials, ("42", "api key"))
        self.assertEqual(output.algolia_application_id, "42")
        self.assertEqual(output.algolia_admin_api_key, "api key")

    def test_app_settings_credientials_unused_if_set_with_output_specific(self):
        self.application.set_setting(AlgoliaApplicationId, "42")
        self.application.set_setting(AlgoliaAdminApiKeyId, "api key")
        output = AlgoliaChannelOutput.objects.create(
            channel=self.channel,
            algolia_application_id="my app id",
            algolia_admin_api_key="my api key",
        )

        self.assertEqual(output.algolia_credentials, ("my app id", "my api key"))

    @override_settings(ALGOLIA_APPLICATION_ID="defaultappid")
    @override_settings(ALGOLIA_API_KEY="defaultapikey")
    def test_default_credentials_used_and_stored_if_nothing_else_set(self):
        output = AlgoliaChannelOutput.objects.create(channel=self.channel)

        self.assertEqual(output.algolia_credentials, ("defaultappid", "defaultapikey"))
        self.assertEqual(output.algolia_application_id, "defaultappid")
        self.assertEqual(output.algolia_admin_api_key, "defaultapikey")
