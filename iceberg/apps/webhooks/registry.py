# -*- coding: utf-8 -*-

import logging

from django.conf import settings

__all__ = ("events", "register", "unregister")

logger = logging.getLogger(__name__)


class AlreadyRegistered(Exception):
    pass


class NotRegistered(Exception):
    pass


class Events(dict):
    def register(self, event, handler):
        if event in self:
            raise AlreadyRegistered(
                "handler {0} already registered for event {1}".format(handler, event)
            )
        self[event] = handler

    def unregister(self, event):
        if event not in self:
            raise NotRegistered("event {0} not registered".format(event))
        del self[event]


events = Events()
register = events.register
unregister = events.unregister

# Autodiscover apps for webhook handlers. Modules can import webhooks and
# and do webhooks.register.


def hook_builder(signal_name):
    def trigger_func(sender, **kwargs):
        from apps.webhooks.utils import trigger

        try:
            logger.debug(
                "generic_hook forwarding signal %s for sender %s (kwargs %s)"
                % (signal_name, sender, kwargs)
            )
        except Exception as err:
            logger.debug("in log: %s", repr(err))
        kwargs["_async"] = getattr(settings, "WEBHOOK_ASYNC", True)
        trigger(signal_name, sender=sender, **kwargs)

    return trigger_func
