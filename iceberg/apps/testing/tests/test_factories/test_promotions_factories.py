# -*- coding: utf-8 -*-

from apps.cart.models import Cart, CartItem
from apps.ice_applications.models import Application
from apps.orders.models import MerchantOrder, OrderItem
from apps.products.models import Brand
from apps.promotions.models import Discount, DiscountUseWorkflow, DiscountWorkflow
from apps.promotions.models.external_discount import (
    CartItemExternalDiscountUse,
    OrderItemExternalDiscountUse,
)
from apps.stores.models import Merchant
from apps.testing.factories import (
    AmountDiscountFactory,
    ApplicationFactory,
    AssignedFreeShippingDiscountUseFactory,
    CartItemExternalDiscountUseFactory,
    FixedPriceDiscountFactory,
    FixedVoucherFactory,
    FreeShippingDiscountFactory,
    MerchantFactory,
    OrderItemExternalDiscountUseFactory,
    PercentageDiscountFactory,
)
from apps.user.models import User
from django.utils import timezone
from ims.tests import BaseTestCase
from reference.status import (
    CART_ITEM_STATUS_ACTIVE,
    CART_STATUS_VALID,
    MERCHANT_STATUS_ACTIVE,
)
from shipping2.models import Carrier, ShippingProvider, ShippingProviderAssignment
from zone.models import Zone


class PromotionsFactoriesTestCase(BaseTestCase):
    def test_build_free_shipping_discount(self):
        discount = FreeShippingDiscountFactory.build()

        self.assertEqual(discount.status, DiscountWorkflow.DRAFT)
        self.assertEqual(discount.name, "Frëe shipping")
        self.assertEqual(discount.reduction_type, Discount.FREE_SHIPPING)
        self.assertIsNone(discount.reduction_value)
        self.assertTrue(len(discount.external_id) > 1)
        self.assertIsNotNone(discount.currency)
        self.assertGreaterEqual(discount.min_quantity, 1)
        self.assertIsNone(discount.max_quantity)
        self.assertEqual(discount.discount_code, "")
        self.assertEqual(discount.combinability, Discount.EXCLUSIVE)
        self.assertIsNone(discount.max_use_count)
        self.assertIsNone(discount.max_use_count)
        self.assertEqual(discount.priority, 100)
        self.assertTrue(0 <= discount.financed_by_application <= 100)
        self.assertIsNone(discount.zone)
        self.assertIsNone(discount.country)
        self.assertIsNone(discount.minimum_price)
        self.assertIsNone(discount.maximum_price)
        self.assertTrue(discount.start_date < timezone.now())
        self.assertTrue(discount.end_date > timezone.now())
        self.assertIsNotNone(discount.operation_type)
        self.assertIsNotNone(discount.application)
        self.assertIsNotNone(discount.merchant)
        self.assertEqual(discount.currency, discount.application.default_currency)
        self.assertEqual(discount.currency, discount.merchant.default_currency)
        self.assertEqual(discount.application, discount.merchant.application)
        self.assertEqual(
            discount.application.language, discount.merchant.prefered_language
        )
        self.assertEqual(discount.merchant.status, MERCHANT_STATUS_ACTIVE)

    def test_create_free_shipping_discount(self):
        discount = FreeShippingDiscountFactory.create()

        self.assertEqual(discount.status, DiscountWorkflow.DRAFT)
        self.assertEqual(discount.name, "Frëe shipping")
        self.assertEqual(discount.reduction_type, Discount.FREE_SHIPPING)
        self.assertIsNone(discount.reduction_value)
        self.assertTrue(len(discount.external_id) > 1)
        self.assertIsNotNone(discount.currency)
        self.assertGreaterEqual(discount.min_quantity, 1)
        self.assertIsNone(discount.max_quantity)
        self.assertEqual(discount.discount_code, "")
        self.assertEqual(discount.combinability, Discount.EXCLUSIVE)
        self.assertIsNone(discount.max_use_count)
        self.assertIsNone(discount.max_use_count)
        self.assertEqual(discount.priority, 100)
        self.assertTrue(0 <= discount.financed_by_application <= 100)
        self.assertIsNone(discount.zone)
        self.assertIsNone(discount.country)
        self.assertIsNone(discount.minimum_price)
        self.assertIsNone(discount.maximum_price)
        self.assertTrue(discount.start_date < timezone.now())
        self.assertTrue(discount.end_date > timezone.now())
        self.assertIsNotNone(discount.operation_type)
        self.assertIsNotNone(discount.application)
        self.assertIsNotNone(discount.merchant)
        self.assertEqual(discount.currency, discount.application.default_currency)
        self.assertEqual(discount.currency, discount.merchant.default_currency)
        self.assertEqual(discount.application, discount.merchant.application)
        self.assertEqual(
            discount.application.language, discount.merchant.prefered_language
        )
        self.assertEqual(discount.merchant.status, MERCHANT_STATUS_ACTIVE)
        self.assertEqual(Merchant.objects.count(), 1)
        self.assertEqual(Application.objects.count(), 1)
        self.assertEqual(ShippingProvider.objects.count(), 0)
        self.assertEqual(Carrier.objects.count(), 0)
        self.assertEqual(Brand.objects.count(), 0)
        self.assertEqual(Zone.objects.count(), 0)

    def test_build_for_application_free_shipping_discount(self):
        application = ApplicationFactory.build()
        discount = FreeShippingDiscountFactory.build_for_application(application)

        self.assertEqual(discount.status, DiscountWorkflow.DRAFT)
        self.assertEqual(discount.name, "Frëe shipping")
        self.assertEqual(discount.reduction_type, Discount.FREE_SHIPPING)
        self.assertIsNone(discount.reduction_value)
        self.assertTrue(len(discount.external_id) > 1)
        self.assertIsNotNone(discount.currency)
        self.assertGreaterEqual(discount.min_quantity, 1)
        self.assertIsNone(discount.max_quantity)
        self.assertEqual(discount.discount_code, "")
        self.assertEqual(discount.combinability, Discount.EXCLUSIVE)
        self.assertIsNone(discount.max_use_count)
        self.assertIsNone(discount.max_use_count)
        self.assertEqual(discount.priority, 100)
        self.assertTrue(0 <= discount.financed_by_application <= 100)
        self.assertIsNone(discount.zone)
        self.assertIsNone(discount.country)
        self.assertIsNone(discount.minimum_price)
        self.assertIsNone(discount.maximum_price)
        self.assertTrue(discount.start_date < timezone.now())
        self.assertTrue(discount.end_date > timezone.now())
        self.assertIsNotNone(discount.operation_type)
        self.assertEqual(discount.application, application)
        self.assertIsNotNone(discount.merchant)
        self.assertEqual(discount.currency, application.default_currency)
        self.assertEqual(discount.currency, discount.merchant.default_currency)
        self.assertEqual(discount.application, discount.merchant.application)
        self.assertEqual(
            discount.application.language, discount.merchant.prefered_language
        )
        self.assertEqual(discount.merchant.status, MERCHANT_STATUS_ACTIVE)

    def test_create_for_application_free_shipping_discount(self):
        application = ApplicationFactory.create()
        discount = FreeShippingDiscountFactory.create_for_application(application)

        self.assertEqual(discount.status, DiscountWorkflow.DRAFT)
        self.assertEqual(discount.name, "Frëe shipping")
        self.assertEqual(discount.reduction_type, Discount.FREE_SHIPPING)
        self.assertIsNone(discount.reduction_value)
        self.assertTrue(len(discount.external_id) > 1)
        self.assertIsNotNone(discount.currency)
        self.assertGreaterEqual(discount.min_quantity, 1)
        self.assertIsNone(discount.max_quantity)
        self.assertEqual(discount.discount_code, "")
        self.assertEqual(discount.combinability, Discount.EXCLUSIVE)
        self.assertIsNone(discount.max_use_count)
        self.assertIsNone(discount.max_use_count)
        self.assertEqual(discount.priority, 100)
        self.assertTrue(0 <= discount.financed_by_application <= 100)
        self.assertIsNone(discount.zone)
        self.assertIsNone(discount.country)
        self.assertIsNone(discount.minimum_price)
        self.assertIsNone(discount.maximum_price)
        self.assertTrue(discount.start_date < timezone.now())
        self.assertTrue(discount.end_date > timezone.now())
        self.assertIsNotNone(discount.operation_type)
        self.assertEqual(discount.application, application)
        self.assertIsNotNone(discount.merchant)
        self.assertEqual(discount.currency, application.default_currency)
        self.assertEqual(discount.currency, discount.merchant.default_currency)
        self.assertEqual(discount.application, discount.merchant.application)
        self.assertEqual(
            discount.application.language, discount.merchant.prefered_language
        )
        self.assertEqual(discount.merchant.status, MERCHANT_STATUS_ACTIVE)
        self.assertEqual(Merchant.objects.count(), 1)
        self.assertEqual(Application.objects.count(), 1)
        self.assertEqual(ShippingProvider.objects.count(), 0)
        self.assertEqual(Carrier.objects.count(), 0)
        self.assertEqual(Brand.objects.count(), 0)
        self.assertEqual(Zone.objects.count(), 0)

    def test_build_for_merchant_free_shipping_discount(self):
        merchant = MerchantFactory.build()
        discount = FreeShippingDiscountFactory.build_for_merchant(merchant)

        self.assertEqual(discount.status, DiscountWorkflow.DRAFT)
        self.assertEqual(discount.name, "Frëe shipping")
        self.assertEqual(discount.reduction_type, Discount.FREE_SHIPPING)
        self.assertIsNone(discount.reduction_value)
        self.assertTrue(len(discount.external_id) > 1)
        self.assertIsNotNone(discount.currency)
        self.assertGreaterEqual(discount.min_quantity, 1)
        self.assertIsNone(discount.max_quantity)
        self.assertEqual(discount.discount_code, "")
        self.assertEqual(discount.combinability, Discount.EXCLUSIVE)
        self.assertIsNone(discount.max_use_count)
        self.assertIsNone(discount.max_use_count)
        self.assertEqual(discount.priority, 100)
        self.assertTrue(0 <= discount.financed_by_application <= 100)
        self.assertIsNone(discount.zone)
        self.assertIsNone(discount.country)
        self.assertIsNone(discount.minimum_price)
        self.assertIsNone(discount.maximum_price)
        self.assertTrue(discount.start_date < timezone.now())
        self.assertTrue(discount.end_date > timezone.now())
        self.assertIsNotNone(discount.operation_type)
        self.assertEqual(discount.application, merchant.application)
        self.assertEqual(discount.merchant, merchant)
        self.assertEqual(discount.currency, merchant.default_currency)
        self.assertEqual(discount.currency, discount.merchant.default_currency)
        self.assertEqual(discount.application, discount.merchant.application)
        self.assertEqual(
            discount.application.language, discount.merchant.prefered_language
        )
        self.assertEqual(discount.merchant.status, merchant.status)

    def test_create_for_merchant_free_shipping_discount(self):
        merchant = MerchantFactory.create()
        discount = FreeShippingDiscountFactory.create_for_merchant(merchant)

        self.assertEqual(discount.status, DiscountWorkflow.DRAFT)
        self.assertEqual(discount.name, "Frëe shipping")
        self.assertEqual(discount.reduction_type, Discount.FREE_SHIPPING)
        self.assertIsNone(discount.reduction_value)
        self.assertTrue(len(discount.external_id) > 1)
        self.assertIsNotNone(discount.currency)
        self.assertGreaterEqual(discount.min_quantity, 1)
        self.assertIsNone(discount.max_quantity)
        self.assertEqual(discount.discount_code, "")
        self.assertEqual(discount.combinability, Discount.EXCLUSIVE)
        self.assertIsNone(discount.max_use_count)
        self.assertIsNone(discount.max_use_count)
        self.assertEqual(discount.priority, 100)
        self.assertTrue(0 <= discount.financed_by_application <= 100)
        self.assertIsNone(discount.zone)
        self.assertIsNone(discount.country)
        self.assertIsNone(discount.minimum_price)
        self.assertIsNone(discount.maximum_price)
        self.assertTrue(discount.start_date < timezone.now())
        self.assertTrue(discount.end_date > timezone.now())
        self.assertIsNotNone(discount.operation_type)
        self.assertEqual(discount.application, merchant.application)
        self.assertEqual(discount.merchant, merchant)
        self.assertEqual(discount.currency, merchant.default_currency)
        self.assertEqual(discount.currency, discount.merchant.default_currency)
        self.assertEqual(discount.application, discount.merchant.application)
        self.assertEqual(
            discount.application.language, discount.merchant.prefered_language
        )
        self.assertEqual(discount.merchant.status, merchant.status)
        self.assertEqual(Merchant.objects.count(), 1)
        self.assertEqual(Application.objects.count(), 1)
        self.assertEqual(ShippingProvider.objects.count(), 0)
        self.assertEqual(Carrier.objects.count(), 0)
        self.assertEqual(Brand.objects.count(), 0)
        self.assertEqual(Zone.objects.count(), 0)

    def test_build_assigned_free_shipping_discount_use(self):
        discount_use = AssignedFreeShippingDiscountUseFactory.build()

        self.assertEqual(discount_use.status, DiscountUseWorkflow.INITIAL)
        self.assertIsNotNone(discount_use.application)
        self.assertIsNotNone(discount_use.merchant)
        self.assertIsNotNone(discount_use.user)
        self.assertIsNotNone(discount_use.discount)
        self.assertIsNotNone(discount_use.cart)
        self.assertIsNotNone(discount_use.cart_shipping_choice)
        self.assertEqual(
            discount_use.application.language, discount_use.merchant.prefered_language
        )
        self.assertEqual(
            discount_use.discount.currency, discount_use.merchant.default_currency
        )
        self.assertEqual(discount_use.discount.currency, discount_use.cart.currency)
        self.assertEqual(discount_use.discount.currency, discount_use.cart.currency)
        self.assertEqual(
            discount_use.discount.currency, discount_use.cart_shipping_choice.currency
        )
        self.assertEqual(discount_use.application, discount_use.discount.application)
        self.assertEqual(discount_use.application, discount_use.merchant.application)
        self.assertEqual(discount_use.application, discount_use.cart.application)
        self.assertEqual(discount_use.user, discount_use.cart.user)
        self.assertEqual(discount_use.merchant, discount_use.discount.merchant)
        self.assertEqual(
            discount_use.merchant, discount_use.cart_shipping_choice.merchant
        )
        self.assertEqual(discount_use.cart, discount_use.cart_shipping_choice.cart)
        self.assertEqual(
            discount_use.cart_shipping_choice.carrier,
            discount_use.cart_shipping_choice.assignment.carrier,
        )

    def test_create_assigned_free_shipping_discount_use(self):
        discount_use = AssignedFreeShippingDiscountUseFactory.create()

        self.assertEqual(discount_use.status, DiscountUseWorkflow.INITIAL)
        self.assertIsNotNone(discount_use.application)
        self.assertIsNotNone(discount_use.merchant)
        self.assertIsNotNone(discount_use.user)
        self.assertIsNotNone(discount_use.discount)
        self.assertIsNotNone(discount_use.cart)
        self.assertIsNotNone(discount_use.cart_shipping_choice)
        self.assertEqual(
            discount_use.application.language, discount_use.merchant.prefered_language
        )
        self.assertEqual(
            discount_use.discount.currency, discount_use.merchant.default_currency
        )
        self.assertEqual(discount_use.discount.currency, discount_use.cart.currency)
        self.assertEqual(discount_use.discount.currency, discount_use.cart.currency)
        self.assertEqual(
            discount_use.discount.currency, discount_use.cart_shipping_choice.currency
        )
        self.assertEqual(discount_use.application, discount_use.discount.application)
        self.assertEqual(discount_use.application, discount_use.merchant.application)
        self.assertEqual(discount_use.application, discount_use.cart.application)
        self.assertEqual(discount_use.user, discount_use.cart.user)
        self.assertEqual(discount_use.merchant, discount_use.discount.merchant)
        self.assertEqual(
            discount_use.merchant, discount_use.cart_shipping_choice.merchant
        )
        self.assertEqual(discount_use.cart, discount_use.cart_shipping_choice.cart)
        self.assertEqual(
            discount_use.cart_shipping_choice.carrier,
            discount_use.cart_shipping_choice.assignment.carrier,
        )
        self.assertEqual(Merchant.objects.count(), 1)
        self.assertEqual(Application.objects.count(), 1)
        self.assertEqual(ShippingProvider.objects.count(), 1)
        self.assertEqual(ShippingProviderAssignment.objects.count(), 1)
        self.assertEqual(Carrier.objects.count(), 1)
        self.assertEqual(Brand.objects.count(), 0)
        self.assertEqual(Zone.objects.count(), 1)
        self.assertEqual(Cart.objects.count(), 1)
        self.assertEqual(Discount.objects.count(), 1)
        self.assertEqual(User.objects.count(), 3)

    def test_build_percentage_discount(self):
        discount = PercentageDiscountFactory.build()

        self.assertIsNotNone(discount.name)
        self.assertIsNotNone(discount.application)
        self.assertIsNotNone(discount.merchant)
        self.assertIsNotNone(discount.currency)
        self.assertGreater(discount.end_date, discount.start_date)
        self.assertEqual(discount.currency, discount.application.default_currency)
        self.assertEqual(discount.currency, discount.merchant.default_currency)
        self.assertEqual(discount.application, discount.merchant.application)
        self.assertTrue(10 <= discount.reduction_value <= 90)
        self.assertEqual(discount.status, DiscountWorkflow.DRAFT)

    def test_create_percentage_discount(self):
        discount = PercentageDiscountFactory.create()

        self.assertIsNotNone(discount.name)
        self.assertIsNotNone(discount.application)
        self.assertIsNotNone(discount.merchant)
        self.assertIsNotNone(discount.currency)
        self.assertGreater(discount.end_date, discount.start_date)
        self.assertEqual(discount.currency, discount.application.default_currency)
        self.assertEqual(discount.currency, discount.merchant.default_currency)
        self.assertEqual(discount.application, discount.merchant.application)
        self.assertTrue(10 <= discount.reduction_value <= 90)
        self.assertEqual(discount.status, DiscountWorkflow.DRAFT)

        self.assertEqual(Discount.objects.count(), 1)
        self.assertEqual(Application.objects.count(), 1)
        self.assertEqual(Merchant.objects.count(), 1)

    def test_build_for_application_percentage_discount(self):
        application = ApplicationFactory.build()
        discount = PercentageDiscountFactory.build_for_application(application)

        self.assertIsNotNone(discount.name)
        self.assertEqual(discount.application, application)
        self.assertIsNotNone(discount.merchant)
        self.assertIsNotNone(discount.currency)
        self.assertGreater(discount.end_date, discount.start_date)
        self.assertEqual(discount.currency, application.default_currency)
        self.assertEqual(
            discount.merchant.default_currency, application.default_currency
        )
        self.assertEqual(discount.merchant.application, application)
        self.assertTrue(10 <= discount.reduction_value <= 90)
        self.assertEqual(discount.status, DiscountWorkflow.DRAFT)

    def test_create_for_application_percentage_discount(self):
        application = ApplicationFactory.create()
        discount = PercentageDiscountFactory.create_for_application(application)

        self.assertIsNotNone(discount.name)
        self.assertEqual(discount.application, application)
        self.assertIsNotNone(discount.merchant)
        self.assertIsNotNone(discount.currency)
        self.assertGreater(discount.end_date, discount.start_date)
        self.assertEqual(discount.currency, application.default_currency)
        self.assertEqual(
            discount.merchant.default_currency, application.default_currency
        )
        self.assertEqual(discount.merchant.application, application)
        self.assertTrue(10 <= discount.reduction_value <= 90)
        self.assertEqual(discount.status, DiscountWorkflow.DRAFT)

        self.assertEqual(Discount.objects.count(), 1)
        self.assertEqual(Application.objects.count(), 1)
        self.assertEqual(Merchant.objects.count(), 1)

    def test_build_for_merchant_percentage_discount(self):
        merchant = MerchantFactory.build()
        discount = PercentageDiscountFactory.build_for_merchant(merchant)

        self.assertIsNotNone(discount.name)
        self.assertEqual(discount.application, merchant.application)
        self.assertEqual(discount.merchant, merchant)
        self.assertGreater(discount.end_date, discount.start_date)
        self.assertEqual(discount.currency, merchant.default_currency)
        self.assertEqual(discount.merchant.application, merchant.application)
        self.assertTrue(10 <= discount.reduction_value <= 90)
        self.assertEqual(discount.status, DiscountWorkflow.DRAFT)

    def test_create_for_merchant_percentage_discount(self):
        merchant = MerchantFactory.create()
        discount = PercentageDiscountFactory.create_for_merchant(merchant)

        self.assertIsNotNone(discount.name)
        self.assertEqual(discount.application, merchant.application)
        self.assertEqual(discount.merchant, merchant)
        self.assertGreater(discount.end_date, discount.start_date)
        self.assertEqual(discount.currency, merchant.default_currency)
        self.assertEqual(discount.merchant.application, merchant.application)
        self.assertTrue(10 <= discount.reduction_value <= 90)
        self.assertEqual(discount.status, DiscountWorkflow.DRAFT)

        self.assertEqual(Discount.objects.count(), 1)
        self.assertEqual(Application.objects.count(), 1)
        self.assertEqual(Merchant.objects.count(), 1)

    def test_build_fixed_price_discount(self):
        discount = FixedPriceDiscountFactory.build()

        self.assertIsNotNone(discount.name)
        self.assertIsNotNone(discount.application)
        self.assertIsNotNone(discount.merchant)
        self.assertIsNotNone(discount.currency)
        self.assertGreater(discount.end_date, discount.start_date)
        self.assertEqual(discount.currency, discount.application.default_currency)
        self.assertEqual(discount.currency, discount.merchant.default_currency)
        self.assertEqual(discount.application, discount.merchant.application)
        self.assertTrue(10 <= discount.reduction_value <= 90)
        self.assertEqual(discount.status, DiscountWorkflow.DRAFT)
        self.assertEqual(discount.reduction_type, Discount.FIXED_PRICE)

    def test_create_fixed_price_discount(self):
        discount = FixedPriceDiscountFactory.create()

        self.assertIsNotNone(discount.name)
        self.assertIsNotNone(discount.application)
        self.assertIsNotNone(discount.merchant)
        self.assertIsNotNone(discount.currency)
        self.assertGreater(discount.end_date, discount.start_date)
        self.assertEqual(discount.currency, discount.application.default_currency)
        self.assertEqual(discount.currency, discount.merchant.default_currency)
        self.assertEqual(discount.application, discount.merchant.application)
        self.assertTrue(10 <= discount.reduction_value <= 90)
        self.assertEqual(discount.status, DiscountWorkflow.DRAFT)
        self.assertEqual(discount.reduction_type, Discount.FIXED_PRICE)

        self.assertEqual(Discount.objects.count(), 1)
        self.assertEqual(Application.objects.count(), 1)
        self.assertEqual(Merchant.objects.count(), 1)

    def test_build_for_application_fixed_price_discount(self):
        application = ApplicationFactory.build()
        discount = FixedPriceDiscountFactory.build_for_application(application)

        self.assertIsNotNone(discount.name)
        self.assertEqual(discount.application, application)
        self.assertIsNotNone(discount.merchant)
        self.assertIsNotNone(discount.currency)
        self.assertGreater(discount.end_date, discount.start_date)
        self.assertEqual(discount.currency, application.default_currency)
        self.assertEqual(
            discount.merchant.default_currency, application.default_currency
        )
        self.assertEqual(discount.merchant.application, application)
        self.assertTrue(10 <= discount.reduction_value <= 90)
        self.assertEqual(discount.status, DiscountWorkflow.DRAFT)
        self.assertEqual(discount.reduction_type, Discount.FIXED_PRICE)

    def test_create_for_application_fixed_price_discount(self):
        application = ApplicationFactory.create()
        discount = FixedPriceDiscountFactory.create_for_application(application)

        self.assertIsNotNone(discount.name)
        self.assertEqual(discount.application, application)
        self.assertIsNotNone(discount.merchant)
        self.assertIsNotNone(discount.currency)
        self.assertGreater(discount.end_date, discount.start_date)
        self.assertEqual(discount.currency, application.default_currency)
        self.assertEqual(
            discount.merchant.default_currency, application.default_currency
        )
        self.assertEqual(discount.merchant.application, application)
        self.assertTrue(10 <= discount.reduction_value <= 90)
        self.assertEqual(discount.status, DiscountWorkflow.DRAFT)
        self.assertEqual(discount.reduction_type, Discount.FIXED_PRICE)

        self.assertEqual(Discount.objects.count(), 1)
        self.assertEqual(Application.objects.count(), 1)
        self.assertEqual(Merchant.objects.count(), 1)

    def test_build_for_merchant_fixed_price_discount(self):
        merchant = MerchantFactory.build()
        discount = FixedPriceDiscountFactory.build_for_merchant(merchant)

        self.assertIsNotNone(discount.name)
        self.assertEqual(discount.application, merchant.application)
        self.assertEqual(discount.merchant, merchant)
        self.assertGreater(discount.end_date, discount.start_date)
        self.assertEqual(discount.currency, merchant.default_currency)
        self.assertEqual(discount.merchant.application, merchant.application)
        self.assertTrue(10 <= discount.reduction_value <= 90)
        self.assertEqual(discount.status, DiscountWorkflow.DRAFT)
        self.assertEqual(discount.reduction_type, Discount.FIXED_PRICE)

    def test_create_for_merchant_fixed_price_discount(self):
        merchant = MerchantFactory.create()
        discount = FixedPriceDiscountFactory.create_for_merchant(merchant)

        self.assertIsNotNone(discount.name)
        self.assertEqual(discount.application, merchant.application)
        self.assertEqual(discount.merchant, merchant)
        self.assertGreater(discount.end_date, discount.start_date)
        self.assertEqual(discount.currency, merchant.default_currency)
        self.assertEqual(discount.merchant.application, merchant.application)
        self.assertTrue(10 <= discount.reduction_value <= 90)
        self.assertEqual(discount.status, DiscountWorkflow.DRAFT)
        self.assertEqual(discount.reduction_type, Discount.FIXED_PRICE)

        self.assertEqual(Discount.objects.count(), 1)
        self.assertEqual(Application.objects.count(), 1)
        self.assertEqual(Merchant.objects.count(), 1)

    def test_build_amount_discount(self):
        discount = AmountDiscountFactory.build()

        self.assertEqual(discount.reduction_type, Discount.AMOUNT)
        self.assertEqual(
            discount.use_count_based_on, Discount.USE_COUNT_BASED_ON_ORDERS
        )
        self.assertEqual(discount.discount_code, "CODE123")
        self.assertIsNotNone(discount.name)
        self.assertIsNotNone(discount.application)
        self.assertIsNotNone(discount.merchant)
        self.assertIsNotNone(discount.currency)
        self.assertGreater(discount.end_date, discount.start_date)
        self.assertEqual(discount.currency, discount.application.default_currency)
        self.assertEqual(discount.currency, discount.merchant.default_currency)
        self.assertEqual(discount.application, discount.merchant.application)
        self.assertTrue(10 <= discount.reduction_value <= 90)
        self.assertEqual(discount.status, DiscountWorkflow.DRAFT)

    def test_create_amount_discount(self):
        discount = AmountDiscountFactory.create()

        self.assertEqual(discount.reduction_type, Discount.AMOUNT)
        self.assertEqual(
            discount.use_count_based_on, Discount.USE_COUNT_BASED_ON_ORDERS
        )
        self.assertEqual(discount.discount_code, "CODE123")
        self.assertIsNotNone(discount.name)
        self.assertIsNotNone(discount.application)
        self.assertIsNotNone(discount.merchant)
        self.assertIsNotNone(discount.currency)
        self.assertGreater(discount.end_date, discount.start_date)
        self.assertEqual(discount.currency, discount.application.default_currency)
        self.assertEqual(discount.currency, discount.merchant.default_currency)
        self.assertEqual(discount.application, discount.merchant.application)
        self.assertTrue(10 <= discount.reduction_value <= 90)
        self.assertEqual(discount.status, DiscountWorkflow.DRAFT)

        self.assertEqual(Discount.objects.count(), 1)
        self.assertEqual(Application.objects.count(), 1)
        self.assertEqual(Merchant.objects.count(), 1)

    def test_build_for_application_amount_discount(self):
        application = ApplicationFactory.build()
        discount = AmountDiscountFactory.build_for_application(application)

        self.assertEqual(discount.reduction_type, Discount.AMOUNT)
        self.assertEqual(
            discount.use_count_based_on, Discount.USE_COUNT_BASED_ON_ORDERS
        )
        self.assertEqual(discount.discount_code, "CODE123")
        self.assertIsNotNone(discount.name)
        self.assertEqual(discount.application, application)
        self.assertIsNotNone(discount.merchant)
        self.assertEqual(discount.currency, application.default_currency)
        self.assertGreater(discount.end_date, discount.start_date)
        self.assertEqual(discount.merchant.default_currency, discount.currency)
        self.assertEqual(discount.application, discount.merchant.application)
        self.assertTrue(10 <= discount.reduction_value <= 90)
        self.assertEqual(discount.status, DiscountWorkflow.DRAFT)

    def test_create_for_application_amount_discount(self):
        application = ApplicationFactory.create()
        discount = AmountDiscountFactory.create_for_application(application)

        self.assertEqual(discount.reduction_type, Discount.AMOUNT)
        self.assertEqual(
            discount.use_count_based_on, Discount.USE_COUNT_BASED_ON_ORDERS
        )
        self.assertEqual(discount.discount_code, "CODE123")
        self.assertIsNotNone(discount.name)
        self.assertEqual(discount.application, application)
        self.assertIsNotNone(discount.merchant)
        self.assertEqual(discount.currency, application.default_currency)
        self.assertGreater(discount.end_date, discount.start_date)
        self.assertEqual(discount.merchant.default_currency, discount.currency)
        self.assertEqual(discount.application, discount.merchant.application)
        self.assertTrue(10 <= discount.reduction_value <= 90)
        self.assertEqual(discount.status, DiscountWorkflow.DRAFT)

        self.assertEqual(Discount.objects.count(), 1)
        self.assertEqual(Application.objects.count(), 1)
        self.assertEqual(Merchant.objects.count(), 1)

    def test_build_for_merchant_amount_discount(self):
        merchant = MerchantFactory.build()
        discount = AmountDiscountFactory.build_for_merchant(merchant)

        self.assertEqual(discount.reduction_type, Discount.AMOUNT)
        self.assertEqual(
            discount.use_count_based_on, Discount.USE_COUNT_BASED_ON_ORDERS
        )
        self.assertEqual(discount.discount_code, "CODE123")
        self.assertIsNotNone(discount.name)
        self.assertEqual(discount.application, merchant.application)
        self.assertIsNotNone(discount.merchant)
        self.assertEqual(discount.currency, merchant.default_currency)
        self.assertGreater(discount.end_date, discount.start_date)
        self.assertEqual(discount.merchant.default_currency, discount.currency)
        self.assertTrue(10 <= discount.reduction_value <= 90)
        self.assertEqual(discount.status, DiscountWorkflow.DRAFT)

    def test_create_for_merchant_amount_discount(self):
        merchant = MerchantFactory.create()
        discount = AmountDiscountFactory.create_for_merchant(merchant)

        self.assertEqual(discount.reduction_type, Discount.AMOUNT)
        self.assertEqual(
            discount.use_count_based_on, Discount.USE_COUNT_BASED_ON_ORDERS
        )
        self.assertEqual(discount.discount_code, "CODE123")
        self.assertIsNotNone(discount.name)
        self.assertEqual(discount.application, merchant.application)
        self.assertIsNotNone(discount.merchant)
        self.assertEqual(discount.currency, merchant.default_currency)
        self.assertGreater(discount.end_date, discount.start_date)
        self.assertEqual(discount.merchant.default_currency, discount.currency)
        self.assertTrue(10 <= discount.reduction_value <= 90)
        self.assertEqual(discount.status, DiscountWorkflow.DRAFT)

        self.assertEqual(Discount.objects.count(), 1)
        self.assertEqual(Application.objects.count(), 1)
        self.assertEqual(Merchant.objects.count(), 1)

    def test_build_fixed_voucher(self):
        discount = FixedVoucherFactory.build()

        self.assertEqual(discount.reduction_type, Discount.VOUCHER)
        self.assertEqual(
            discount.use_count_based_on, Discount.USE_COUNT_BASED_ON_ORDERS
        )
        self.assertEqual(discount.discount_code, "CODE123")
        self.assertIsNotNone(discount.name)
        self.assertIsNotNone(discount.application)
        self.assertIsNone(discount.merchant)
        self.assertIsNotNone(discount.currency)
        self.assertGreater(discount.end_date, discount.start_date)
        self.assertEqual(discount.currency, discount.application.default_currency)
        self.assertEqual(discount.reduction_value, 100)
        self.assertEqual(discount.financed_by_application, 100)
        self.assertEqual(discount.min_quantity, 1)
        self.assertEqual(discount.status, DiscountWorkflow.DRAFT)

    def test_create_fixed_voucher(self):
        discount = FixedVoucherFactory.create()

        self.assertEqual(discount.reduction_type, Discount.VOUCHER)
        self.assertEqual(
            discount.use_count_based_on, Discount.USE_COUNT_BASED_ON_ORDERS
        )
        self.assertEqual(discount.discount_code, "CODE123")
        self.assertIsNotNone(discount.name)
        self.assertIsNotNone(discount.application)
        self.assertIsNone(discount.merchant)
        self.assertIsNotNone(discount.currency)
        self.assertGreater(discount.end_date, discount.start_date)
        self.assertEqual(discount.currency, discount.application.default_currency)
        self.assertEqual(discount.reduction_value, 100)
        self.assertEqual(discount.financed_by_application, 100)
        self.assertEqual(discount.min_quantity, 1)
        self.assertEqual(discount.status, DiscountWorkflow.DRAFT)

        self.assertEqual(Discount.objects.count(), 1)
        self.assertEqual(Application.objects.count(), 1)
        self.assertEqual(Merchant.objects.count(), 0)

    def test_build_for_application_fixed_voucher(self):
        application = ApplicationFactory.build()
        discount = FixedVoucherFactory.build_for_application(application)

        self.assertEqual(discount.reduction_type, Discount.VOUCHER)
        self.assertEqual(
            discount.use_count_based_on, Discount.USE_COUNT_BASED_ON_ORDERS
        )
        self.assertEqual(discount.discount_code, "CODE123")
        self.assertIsNotNone(discount.name)
        self.assertEqual(discount.application, application)
        self.assertIsNone(discount.merchant)
        self.assertEqual(discount.currency, application.default_currency)
        self.assertGreater(discount.end_date, discount.start_date)
        self.assertEqual(discount.currency, application.default_currency)
        self.assertEqual(discount.reduction_value, 100)
        self.assertEqual(discount.financed_by_application, 100)
        self.assertEqual(discount.min_quantity, 1)
        self.assertEqual(discount.status, DiscountWorkflow.DRAFT)

    def test_create_for_application_fixed_voucher(self):
        application = ApplicationFactory.create()
        discount = FixedVoucherFactory.create_for_application(application)

        self.assertEqual(discount.reduction_type, Discount.VOUCHER)
        self.assertEqual(
            discount.use_count_based_on, Discount.USE_COUNT_BASED_ON_ORDERS
        )
        self.assertEqual(discount.discount_code, "CODE123")
        self.assertIsNotNone(discount.name)
        self.assertEqual(discount.application, application)
        self.assertIsNone(discount.merchant)
        self.assertEqual(discount.currency, application.default_currency)
        self.assertGreater(discount.end_date, discount.start_date)
        self.assertEqual(discount.currency, application.default_currency)
        self.assertEqual(discount.reduction_value, 100)
        self.assertEqual(discount.financed_by_application, 100)
        self.assertEqual(discount.min_quantity, 1)
        self.assertEqual(discount.status, DiscountWorkflow.DRAFT)

        self.assertEqual(Discount.objects.count(), 1)
        self.assertEqual(Application.objects.count(), 1)
        self.assertEqual(Merchant.objects.count(), 0)

    def _check_cart_item_external_discount_use(self, discount_use):
        self.assertIsNotNone(discount_use.application)
        self.assertIsNotNone(discount_use.cart_item)
        self.assertIsNotNone(discount_use.reduction_type)
        self.assertIsNotNone(discount_use.discount_code)
        self.assertEqual(discount_use.name, "External Dïscount")
        self.assertGreater(discount_use.end_date, discount_use.start_date)
        self.assertIsNotNone(discount_use.reduction_value)
        self.assertIsNotNone(discount_use.external_id)
        self.assertIsNotNone(discount_use.quantity)
        self.assertIsNotNone(discount_use.discount_unit_amount)

        self.assertEqual(
            discount_use.application, discount_use.cart_item.cart.application
        )
        self.assertEqual(discount_use.quantity, discount_use.cart_item.quantity)
        self.assertEqual(discount_use.cart_item.status, CART_ITEM_STATUS_ACTIVE)
        self.assertEqual(discount_use.cart_item.cart.status, CART_STATUS_VALID)

    def test_create_cart_item_external_discount_use(self):
        discount_use = CartItemExternalDiscountUseFactory.create()

        self._check_cart_item_external_discount_use(discount_use)

        self.assertIsNotNone(discount_use.discount_vat_unit_amount)
        self.assertIsNotNone(discount_use.discount_amount)
        self.assertIsNotNone(discount_use.discount_vat_amount)

        self.assertEqual(CartItemExternalDiscountUse.objects.count(), 1)
        self.assertEqual(CartItem.objects.count(), 1)
        self.assertEqual(Cart.objects.count(), 1)
        self.assertEqual(Application.objects.count(), 1)

    def test_build_cart_item_external_discount_use(self):
        discount_use = CartItemExternalDiscountUseFactory.build()

        self._check_cart_item_external_discount_use(discount_use)

        self.assertIsNone(discount_use.discount_vat_unit_amount)
        self.assertIsNone(discount_use.discount_amount)
        self.assertIsNone(discount_use.discount_vat_amount)

    def _check_order_item_external_discount_use(self, discount_use):
        self.assertIsNotNone(discount_use.application)
        self.assertIsNotNone(discount_use.order_item)
        self.assertIsNotNone(discount_use.reduction_type)
        self.assertIsNotNone(discount_use.discount_code)
        self.assertEqual(discount_use.name, "External Dïscount")
        self.assertGreater(discount_use.end_date, discount_use.start_date)
        self.assertIsNotNone(discount_use.reduction_value)
        self.assertIsNotNone(discount_use.external_id)
        self.assertIsNotNone(discount_use.quantity)
        self.assertIsNotNone(discount_use.discount_unit_amount)
        self.assertIsNotNone(discount_use.discount_vat_unit_amount)
        self.assertIsNotNone(discount_use.discount_amount)
        self.assertIsNotNone(discount_use.discount_vat_amount)

        self.assertEqual(
            discount_use.application,
            discount_use.order_item.application,
        )
        self.assertEqual(discount_use.quantity, discount_use.order_item.quantity)
        self.assertEqual(
            discount_use.quantity, discount_use.order_item.cart_item.quantity
        )

    def test_create_order_item_external_discount_use(self):
        discount_use = OrderItemExternalDiscountUseFactory.create()

        self._check_order_item_external_discount_use(discount_use)

        self.assertEqual(OrderItemExternalDiscountUse.objects.count(), 1)
        self.assertEqual(OrderItem.objects.count(), 1)
        self.assertEqual(MerchantOrder.objects.count(), 1)
        self.assertEqual(CartItem.objects.count(), 1)
        self.assertEqual(Cart.objects.count(), 1)
        self.assertEqual(Application.objects.count(), 1)

    def test_build_order_item_external_discount_use(self):
        discount_use = OrderItemExternalDiscountUseFactory.build()

        self._check_order_item_external_discount_use(discount_use)
