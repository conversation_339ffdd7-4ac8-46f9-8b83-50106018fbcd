# -*- coding: utf-8 -*-

from decimal import ROUND_UP, Decimal

from apps.cart.models import Cart
from apps.ice_applications.app_conf_settings import OrderWorkflowV2
from apps.ice_applications.models import Application
from apps.orders.models import OrderItem
from apps.orders.models.workflows import (
    MerchantOrderWorkflow,
    OrderItemWorkflow,
    OrderWorkflow,
)
from apps.orders.models.workflows.configuration import IZBERG_V1, IZBERG_V2
from apps.payment.backends import HIPAYTPP
from apps.payment.models import OrderItemPaymentTerm, PaymentTerm, PaymentTermLine
from apps.products.models import Product, ProductOffer, ProductVariation
from apps.stores.models import Merchant
from apps.testing.factories import (
    MerchantOrderFactory,
    OrderFactory,
    OrderWorkflowMigrationFactory,
    ProductOfferOrderItemFactory,
    ProductOfferVariationOrderItemFactory,
    TermPaymentProductOfferOrderItemFactory,
)
from apps.user.models import User
from ims.tests import BaseTestCase
from reference.status import (
    CART_ITEM_STATUS_ACTIVE,
    CART_STATUS_VALID,
    MERCHANT_ORDER_STATUS_INITIAL,
    MERCHANT_STATUS_ACTIVE,
    ORDER_ITEM_STATUS_AUTH_SUCCESS,
    ORDER_ITEM_STATUS_CONFIRMED,
    ORDER_ITEM_STATUS_INITIAL,
    ORDER_STATUS_INITIAL,
    PAYMENT_STATUS_AUTH_SUCCESS,
    PAYMENT_STATUS_COLLECTED,
    PAYMENT_STATUS_INITIAL,
    PRODUCT_OFFER_STATUS_ACTIVE,
    PRODUCT_STATUS_ACTIVE,
    PRODUCT_VARIATION_STATUS_ACTIVE,
)


class OrdersFactoriesTestCase(BaseTestCase):
    def assert_decimals_almost_equal(self, decimal_1, decimal_2):
        # make sure decimal_1 == decimal_2,
        # allowing some minor rounding issue, due to the faker rounding's
        # internal implem not always rounding the same as us.
        computed = decimal_1.quantize(Decimal("1.00"), rounding=ROUND_UP)
        if abs(computed - decimal_2) > 0.1:
            raise AssertionError(
                "Too much difference between {} and {} (> 0.1).".format(
                    computed, decimal_2
                )
            )

    def test_build_order(self):
        order = OrderFactory.build()

        self.assertIsNotNone(order.currency)
        self.assertIsNotNone(order.application)
        self.assertIsNotNone(order.user)
        self.assertIsNotNone(order.shipping_address)
        self.assertIsNotNone(order.billing_address)
        self.assertIsNotNone(order.cart)
        self.assertEqual(order.status, OrderWorkflow.initial_state)
        self.assertEqual(order.currency, order.application.default_currency)
        self.assertEqual(order.shipping_address, order.billing_address)
        self.assertEqual(order.user, order.shipping_address.user)
        self.assertEqual(order.application, order.user.application)
        self.assertEqual(order.application, order.shipping_address.application)
        self.assertEqual(order.application, order.cart.application)
        self.assertEqual(order.user, order.cart.user)
        self.assertEqual(order.shipping_address, order.cart.shipping_address)
        self.assertEqual(order.billing_address, order.cart.billing_address)
        self.assertEqual(order.currency, order.cart.currency)
        self.assertEqual(order.shipping_address.country, order.cart.country)
        self.assertEqual(order.cart.status, CART_STATUS_VALID)
        self.assertEqual(order.status, ORDER_STATUS_INITIAL)
        self.assertIsNotNone(order.price)
        self.assertIsNotNone(order.shipping)
        self.assertIsNotNone(order.eco_tax)
        self.assertIsNotNone(order.vat_on_products)
        self.assertIsNotNone(order.vat_on_eco_tax)
        self.assertIsNotNone(order.vat_on_shipping)
        self.assertEqual(
            order.vat,
            order.vat_on_products + order.vat_on_eco_tax + order.vat_on_shipping,
        )
        self.assertEqual(order.workflow, IZBERG_V2)

    def test_create_order(self):
        order = OrderFactory.create()

        self.assertEqual(order.cart.status, CART_STATUS_VALID)
        self.assertEqual(order.status, ORDER_STATUS_INITIAL)
        self.assertEqual(order.currency, order.application.default_currency)
        self.assertEqual(order.shipping_address, order.billing_address)
        self.assertEqual(order.user, order.shipping_address.user)
        self.assertEqual(order.application, order.user.application)
        self.assertEqual(order.application, order.shipping_address.application)
        self.assertEqual(order.application, order.cart.application)
        self.assertEqual(order.user, order.cart.user)
        self.assertEqual(order.shipping_address, order.cart.shipping_address)
        self.assertEqual(order.billing_address, order.cart.billing_address)
        self.assertEqual(order.currency, order.cart.currency)
        self.assertEqual(order.shipping_address.country, order.cart.country)
        self.assertEqual(order.workflow, IZBERG_V2)
        self.assertEqual(Application.objects.count(), 1)
        self.assertEqual(Merchant.objects.count(), 0)
        self.assertEqual(Product.objects.count(), 0)
        self.assertEqual(ProductOffer.objects.count(), 0)
        self.assertEqual(User.objects.count(), 3)

    def test_build_merchant_order(self):
        mo = MerchantOrderFactory.build()

        self.assertEqual(mo.status, MerchantOrderWorkflow.initial_state)
        self.assertIsNotNone(mo.application)
        self.assertIsNotNone(mo.user)
        self.assertIsNotNone(mo.merchant)
        self.assertIsNotNone(mo.order)
        self.assertIsNotNone(mo.customer_tax_group)
        self.assertEqual(mo.user, mo.order.user)
        self.assertEqual(mo.application, mo.user.application)
        self.assertEqual(mo.application, mo.merchant.application)
        self.assertEqual(mo.application, mo.order.application)
        self.assertEqual(mo.application.default_currency, mo.merchant.default_currency)
        self.assertEqual(mo.application.default_currency, mo.order.currency)
        self.assertEqual(mo.application.language, mo.merchant.prefered_language)
        self.assertIsNotNone(mo.price)
        self.assertIsNotNone(mo.shipping)
        self.assertIsNotNone(mo.eco_tax)
        self.assertIsNotNone(mo.vat_on_products)
        self.assertIsNotNone(mo.vat_on_eco_tax)
        self.assertIsNotNone(mo.vat_rate_on_shipping)
        self.assertIsNotNone(mo.vat_on_shipping)
        self.assertEqual(mo.price, mo.order.price)
        self.assertEqual(mo.vat_on_products, mo.order.vat_on_products)
        self.assertEqual(mo.vat_on_eco_tax, mo.order.vat_on_eco_tax)
        self.assert_decimals_almost_equal(
            mo.vat, mo.vat_on_products + mo.vat_on_eco_tax + mo.vat_on_shipping
        )
        self.assertEqual(mo.shipping, mo.order.shipping)
        self.assertEqual(mo.eco_tax, mo.order.eco_tax)
        self.assert_decimals_almost_equal(
            mo.vat_on_shipping, mo.shipping * mo.vat_rate_on_shipping / 100
        )
        self.assertEqual(mo.status, MERCHANT_ORDER_STATUS_INITIAL)
        self.assertEqual(mo.merchant.status, MERCHANT_STATUS_ACTIVE)
        self.assertEqual(mo.workflow, IZBERG_V2)
        self.assertEqual(mo.workflow, mo.order.workflow)

    def test_create_merchant_order(self):
        mo = MerchantOrderFactory.create()

        self.assertEqual(mo.user, mo.order.user)
        self.assertEqual(mo.application, mo.user.application)
        self.assertEqual(mo.application, mo.merchant.application)
        self.assertEqual(mo.application, mo.order.application)
        self.assertEqual(mo.application.default_currency, mo.merchant.default_currency)
        self.assertEqual(mo.application.default_currency, mo.order.currency)
        self.assertEqual(mo.application.language, mo.merchant.prefered_language)
        self.assertEqual(mo.status, MERCHANT_ORDER_STATUS_INITIAL)
        self.assertEqual(mo.merchant.status, MERCHANT_STATUS_ACTIVE)
        self.assertEqual(mo.workflow, IZBERG_V2)
        self.assertEqual(mo.workflow, mo.order.workflow)
        self.assertEqual(Application.objects.count(), 1)
        self.assertEqual(Merchant.objects.count(), 1)
        self.assertEqual(Product.objects.count(), 0)
        self.assertEqual(ProductOffer.objects.count(), 0)
        self.assertEqual(User.objects.count(), 3)

    def test_build_product_offer_order_item(self):
        oi = ProductOfferOrderItemFactory.build()

        self.assertEqual(oi.status, OrderItemWorkflow.initial_state)
        self.assertEqual(oi.invoiced_quantity, 0)
        self.assertEqual(oi.invoiceable_quantity, 0)
        self.assertIsNotNone(oi.quantity)
        self.assertIsNotNone(oi.invoiceable_quantity)
        self.assertIsNotNone(oi.price)
        self.assertIsNotNone(oi.vat)
        self.assertIsNotNone(oi.tax_rate)
        self.assertIsNotNone(oi.cart)
        self.assertIsNotNone(oi.merchant_order)
        self.assertIsNotNone(oi.product_offer)
        self.assertIsNotNone(oi.cart_item)
        self.assertIsNotNone(oi.product_tax_group)
        self.assertIsNone(oi.product_offer_variation)
        self.assertEqual(oi.quantity, oi.product_offer.stock)
        self.assertEqual(oi.cart.user, oi.merchant_order.user)
        self.assertEqual(oi.cart.application, oi.cart.user.application)
        self.assertEqual(oi.cart.application, oi.merchant_order.application)
        self.assertEqual(oi.product_offer.status, PRODUCT_OFFER_STATUS_ACTIVE)
        self.assertEqual(oi.product_offer.product.status, PRODUCT_STATUS_ACTIVE)
        self.assertEqual(
            oi.product_offer.currency, oi.cart.application.default_currency
        )
        self.assertEqual(oi.product_offer.application, oi.cart.application)
        self.assertEqual(oi.cart.application, oi.product_tax_group.application)
        self.assertEqual(oi.product_offer.merchant, oi.merchant_order.merchant)
        self.assertEqual(oi.cart_item.quantity, oi.quantity)
        self.assertEqual(oi.cart_item.tax_rate, oi.tax_rate)
        self.assertEqual(oi.cart_item.cart, oi.cart)
        self.assertEqual(oi.cart_item.product_offer, oi.product_offer)
        self.assertEqual(oi.cart_item.merchant, oi.merchant_order.merchant)
        self.assertEqual(oi.item_type, OrderItem.PRODUCT_TYPE)
        self.assertEqual(oi.status, ORDER_ITEM_STATUS_INITIAL)
        self.assertEqual(oi.cart.status, CART_STATUS_VALID)
        self.assertEqual(oi.cart_item.status, CART_ITEM_STATUS_ACTIVE)
        self.assertIsNotNone(oi.price)
        self.assertEqual(oi.price, oi.cart_item.unit_price)
        self.assertEqual(oi.price, oi.product_offer.price)
        self.assertEqual(oi.price * oi.quantity, oi.merchant_order.price)
        self.assertEqual(oi.price * oi.quantity, oi.merchant_order.order.price)
        self.assert_decimals_almost_equal(
            oi.merchant_order.vat_on_products, oi.vat * oi.quantity
        )
        self.assertIsNotNone(oi.vat)
        self.assert_decimals_almost_equal(
            oi.merchant_order.vat_on_shipping,
            oi.merchant_order.shipping * oi.merchant_order.vat_rate_on_shipping / 100,
        )

    def test_create_product_offer_order_item(self):
        oi = ProductOfferOrderItemFactory.create()

        self.assertEqual(oi.quantity, oi.product_offer.stock)
        self.assertEqual(oi.cart.user, oi.merchant_order.user)
        self.assertEqual(oi.cart.application, oi.cart.user.application)
        self.assertEqual(oi.cart.application, oi.merchant_order.application)
        self.assertEqual(oi.product_offer.status, PRODUCT_OFFER_STATUS_ACTIVE)
        self.assertEqual(oi.status, ORDER_ITEM_STATUS_INITIAL)
        self.assertEqual(oi.cart.status, CART_STATUS_VALID)
        self.assertEqual(oi.cart_item.status, CART_ITEM_STATUS_ACTIVE)
        self.assertEqual(oi.price, oi.cart_item.unit_price)
        self.assertEqual(oi.price * oi.quantity, oi.merchant_order.price)
        self.assertEqual(oi.price * oi.quantity, oi.merchant_order.order.price)
        self.assertEqual(oi.price, oi.product_offer.price)
        self.assertEqual(
            oi.product_offer.currency, oi.cart.application.default_currency
        )
        self.assertEqual(oi.product_offer.application, oi.cart.application)
        self.assertEqual(oi.cart.application, oi.product_tax_group.application)
        self.assertEqual(oi.product_offer.merchant, oi.merchant_order.merchant)
        self.assertEqual(oi.cart_item.quantity, oi.quantity)
        self.assertEqual(oi.cart_item.tax_rate, oi.tax_rate)
        self.assertEqual(oi.cart_item.cart, oi.cart)
        self.assertEqual(oi.cart_item.product_offer, oi.product_offer)
        self.assertEqual(oi.cart_item.merchant, oi.merchant_order.merchant)
        self.assertEqual(Application.objects.count(), 1)
        self.assertEqual(Merchant.objects.count(), 1)
        self.assertEqual(Product.objects.count(), 1)
        self.assertEqual(ProductOffer.objects.count(), 1)
        self.assertEqual(ProductVariation.objects.count(), 0)
        self.assertEqual(User.objects.count(), 3)

    def test_build_product_offer_variation_order_item(self):
        oi = ProductOfferVariationOrderItemFactory.build()

        self.assertEqual(oi.status, OrderItemWorkflow.initial_state)
        self.assertEqual(oi.invoiced_quantity, 0)
        self.assertEqual(oi.invoiceable_quantity, 0)
        self.assertIsNotNone(oi.quantity)
        self.assertIsNotNone(oi.price)
        self.assertIsNotNone(oi.vat)
        self.assertIsNotNone(oi.tax_rate)
        self.assertIsNotNone(oi.cart)
        self.assertIsNotNone(oi.merchant_order)
        self.assertIsNotNone(oi.product_offer)
        self.assertIsNotNone(oi.product_offer_variation)
        self.assertIsNotNone(oi.cart_item)
        self.assertIsNotNone(oi.product_tax_group)
        self.assertEqual(oi.quantity, oi.product_offer.stock)
        self.assertEqual(oi.cart.user, oi.merchant_order.user)
        self.assertEqual(oi.cart.application, oi.cart.user.application)
        self.assertEqual(oi.cart.application, oi.merchant_order.application)
        self.assertEqual(oi.cart.application, oi.product_tax_group.application)
        self.assertEqual(oi.product_offer.status, PRODUCT_OFFER_STATUS_ACTIVE)
        self.assertEqual(oi.product_offer.product.status, PRODUCT_STATUS_ACTIVE)
        self.assertEqual(
            oi.product_offer_variation.status, PRODUCT_VARIATION_STATUS_ACTIVE
        )
        self.assertEqual(oi.status, ORDER_ITEM_STATUS_INITIAL)
        self.assertEqual(oi.cart.status, CART_STATUS_VALID)
        self.assertEqual(oi.cart_item.status, CART_ITEM_STATUS_ACTIVE)
        self.assertEqual(
            oi.product_offer.currency, oi.cart.application.default_currency
        )
        self.assertEqual(oi.product_offer.application, oi.cart.application)
        self.assertEqual(oi.product_offer.merchant, oi.merchant_order.merchant)
        self.assertEqual(oi.cart_item.quantity, oi.quantity)
        self.assertEqual(oi.cart_item.tax_rate, oi.tax_rate)
        self.assertEqual(oi.cart_item.cart, oi.cart)
        self.assertEqual(oi.cart_item.product_offer, oi.product_offer)
        self.assertEqual(oi.cart_item.merchant, oi.merchant_order.merchant)
        self.assertEqual(oi.item_type, OrderItem.PRODUCT_TYPE)
        self.assertEqual(oi.product_offer, oi.product_offer_variation.product_offer)
        self.assertEqual(oi.product_offer_variation.price, oi.price)
        self.assertEqual(oi.product_offer_variation.stock, oi.quantity)
        self.assertEqual(
            oi.product_offer_variation.merchant, oi.merchant_order.merchant
        )
        # price
        self.assertIsNotNone(oi.price)
        self.assertEqual(oi.price, oi.product_offer.price)
        self.assertEqual(oi.price * oi.quantity, oi.merchant_order.price)
        self.assertEqual(oi.price * oi.quantity, oi.merchant_order.order.price)
        self.assertEqual(oi.price, oi.cart_item.unit_price)
        self.assert_decimals_almost_equal(
            oi.merchant_order.vat_on_products, oi.vat * oi.quantity
        )
        self.assertIsNotNone(oi.vat)

    def test_create_product_offer_variation_order_item(self):
        oi = ProductOfferVariationOrderItemFactory.create()

        self.assertEqual(oi.quantity, oi.product_offer.stock)
        self.assertEqual(oi.cart.user, oi.merchant_order.user)
        self.assertEqual(oi.cart.application, oi.merchant_order.application)
        self.assertEqual(oi.cart.application, oi.product_tax_group.application)
        self.assertEqual(oi.product_offer.status, PRODUCT_OFFER_STATUS_ACTIVE)
        self.assertEqual(oi.product_offer.product.status, PRODUCT_STATUS_ACTIVE)
        self.assertEqual(
            oi.product_offer_variation.status, PRODUCT_VARIATION_STATUS_ACTIVE
        )
        self.assertEqual(oi.status, ORDER_ITEM_STATUS_INITIAL)
        self.assertEqual(oi.cart.status, CART_STATUS_VALID)
        self.assertEqual(oi.cart_item.status, CART_ITEM_STATUS_ACTIVE)
        self.assertEqual(
            oi.product_offer.currency, oi.cart.application.default_currency
        )
        self.assertEqual(oi.cart.application, oi.cart.user.application)
        self.assertEqual(oi.product_offer.application, oi.cart.application)
        self.assertEqual(oi.product_offer.merchant, oi.merchant_order.merchant)
        self.assertEqual(oi.cart_item.quantity, oi.quantity)
        self.assertEqual(oi.cart_item.tax_rate, oi.tax_rate)
        self.assertEqual(oi.cart_item.cart, oi.cart)
        self.assertEqual(oi.cart_item.product_offer, oi.product_offer)
        self.assertEqual(oi.cart_item.merchant, oi.merchant_order.merchant)
        self.assertEqual(oi.item_type, OrderItem.PRODUCT_TYPE)
        self.assertEqual(oi.product_offer, oi.product_offer_variation.product_offer)
        self.assertEqual(oi.price, oi.product_offer.price)
        self.assertEqual(oi.price * oi.quantity, oi.merchant_order.price)
        self.assertEqual(oi.price * oi.quantity, oi.merchant_order.order.price)
        self.assertEqual(oi.price, oi.cart_item.unit_price)
        self.assertEqual(oi.product_offer_variation.stock, oi.quantity)
        self.assertEqual(
            oi.product_offer_variation.merchant, oi.merchant_order.merchant
        )
        self.assertEqual(Application.objects.count(), 1)
        self.assertEqual(Merchant.objects.count(), 1)
        self.assertEqual(Product.objects.count(), 1)
        self.assertEqual(ProductOffer.objects.count(), 1)
        self.assertEqual(ProductVariation.objects.count(), 1)
        self.assertEqual(User.objects.count(), 3)

    def test_build_term_payment_product_offer_order_item(self):
        oi = TermPaymentProductOfferOrderItemFactory.build()

        self.assertEqual(oi.status, OrderItemWorkflow.initial_state)
        self.assertEqual(oi.invoiced_quantity, 0)
        self.assertEqual(oi.invoiceable_quantity, 0)
        self.assertIsNotNone(oi.quantity)
        self.assertIsNotNone(oi.invoiceable_quantity)
        self.assertIsNotNone(oi.price)
        self.assertIsNotNone(oi.vat)
        self.assertIsNotNone(oi.tax_rate)
        self.assertIsNotNone(oi.cart)
        self.assertIsNotNone(oi.merchant_order)
        self.assertIsNotNone(oi.product_offer)
        self.assertIsNotNone(oi.cart_item)
        self.assertIsNone(oi.product_offer_variation)
        self.assertIsNotNone(oi.product_tax_group)
        self.assertEqual(oi.quantity, oi.product_offer.stock)
        self.assertEqual(oi.cart.user, oi.merchant_order.user)
        self.assertEqual(oi.cart.application, oi.cart.user.application)
        self.assertEqual(oi.cart.application, oi.merchant_order.application)
        self.assertEqual(oi.cart.application, oi.product_tax_group.application)
        self.assertEqual(oi.product_offer.status, PRODUCT_OFFER_STATUS_ACTIVE)
        self.assertEqual(oi.product_offer.product.status, PRODUCT_STATUS_ACTIVE)
        self.assertEqual(
            oi.product_offer.currency, oi.cart.application.default_currency
        )
        self.assertEqual(oi.product_offer.application, oi.cart.application)
        self.assertEqual(oi.product_offer.merchant, oi.merchant_order.merchant)
        self.assertEqual(oi.cart_item.quantity, oi.quantity)
        self.assertEqual(oi.cart_item.tax_rate, oi.tax_rate)
        self.assertEqual(oi.cart_item.cart, oi.cart)
        self.assertEqual(oi.cart_item.product_offer, oi.product_offer)
        self.assertEqual(oi.cart_item.merchant, oi.merchant_order.merchant)
        self.assertEqual(oi.item_type, OrderItem.PRODUCT_TYPE)
        self.assertEqual(oi.status, ORDER_ITEM_STATUS_INITIAL)
        self.assertEqual(oi.cart.status, CART_STATUS_VALID)
        self.assertEqual(oi.cart_item.status, CART_ITEM_STATUS_ACTIVE)
        self.assertIsNotNone(oi.price)
        self.assertEqual(oi.price, oi.cart_item.unit_price)
        self.assertEqual(oi.product_offer.price, oi.price)
        self.assertEqual(oi.price * oi.quantity, oi.merchant_order.price)
        self.assertEqual(oi.price * oi.quantity, oi.merchant_order.order.price)
        self.assert_decimals_almost_equal(
            oi.merchant_order.vat_on_products, oi.vat * oi.quantity
        )
        self.assertIsNotNone(oi.vat)
        self.assert_decimals_almost_equal(
            oi.merchant_order.vat_on_shipping,
            oi.merchant_order.shipping * oi.merchant_order.vat_rate_on_shipping / 100,
        )
        self.assertEqual(oi.cart_item.cart.selected_payment_type, Cart.TERM_PAYMENT)

    def test_create_term_payment_product_offer_order_item(self):
        oi = TermPaymentProductOfferOrderItemFactory.create()

        self.assertEqual(oi.quantity, oi.product_offer.stock)
        self.assertEqual(oi.cart.user, oi.merchant_order.user)
        self.assertEqual(oi.cart.application, oi.merchant_order.application)
        self.assertEqual(oi.product_offer.status, PRODUCT_OFFER_STATUS_ACTIVE)
        self.assertEqual(oi.status, ORDER_ITEM_STATUS_INITIAL)
        self.assertEqual(oi.cart.status, CART_STATUS_VALID)
        self.assertEqual(oi.cart_item.status, CART_ITEM_STATUS_ACTIVE)
        self.assertEqual(
            oi.product_offer.currency, oi.cart.application.default_currency
        )
        self.assertEqual(oi.product_offer.application, oi.cart.application)
        self.assertEqual(oi.cart.application, oi.product_tax_group.application)
        self.assertEqual(oi.product_offer.merchant, oi.merchant_order.merchant)
        self.assertEqual(oi.cart.application, oi.cart.user.application)
        self.assertEqual(oi.cart_item.quantity, oi.quantity)
        self.assertEqual(oi.cart_item.tax_rate, oi.tax_rate)
        self.assertEqual(oi.cart_item.cart, oi.cart)
        self.assertEqual(oi.cart_item.product_offer, oi.product_offer)
        self.assertEqual(oi.cart_item.merchant, oi.merchant_order.merchant)
        self.assertEqual(oi.cart_item.cart.selected_payment_type, Cart.TERM_PAYMENT)
        self.assertEqual(oi.price, oi.cart_item.unit_price)
        self.assertEqual(oi.price, oi.product_offer.price)
        self.assertEqual(oi.price * oi.quantity, oi.merchant_order.price)
        self.assertEqual(oi.price * oi.quantity, oi.merchant_order.order.price)
        self.assertEqual(Application.objects.count(), 1)
        self.assertEqual(Merchant.objects.count(), 1)
        self.assertEqual(Product.objects.count(), 1)
        self.assertEqual(ProductOffer.objects.count(), 1)
        self.assertEqual(ProductVariation.objects.count(), 0)
        self.assertEqual(User.objects.count(), 3)
        self.assertEqual(PaymentTerm.objects.count(), 1)
        self.assertEqual(PaymentTermLine.objects.count(), 1)
        self.assertEqual(OrderItemPaymentTerm.objects.count(), 0)

    def test_build_order_workflow_migration(self):
        migration = OrderWorkflowMigrationFactory.build()

        self.assertIsNotNone(migration.application)
        self.assertTrue(migration.announcement < migration.migration_start)
        self.assertTrue(migration.migration_start < migration.migration_end)
        self.assertTrue(migration.migration_end < migration.old_orders_readonly)
        self.assertEqual(migration.from_workflow, IZBERG_V1)
        self.assertEqual(migration.to_workflow, IZBERG_V2)
        self.assertFalse(migration.application.get_setting(OrderWorkflowV2))

    def test_create_order_workflow_migration(self):
        migration = OrderWorkflowMigrationFactory.create()

        self.assertIsNotNone(migration.application)
        self.assertTrue(migration.announcement < migration.migration_start)
        self.assertTrue(migration.migration_start < migration.migration_end)
        self.assertTrue(migration.migration_end < migration.old_orders_readonly)
        self.assertEqual(migration.from_workflow, IZBERG_V1)
        self.assertEqual(migration.to_workflow, IZBERG_V2)
        self.assertFalse(migration.application.get_setting(OrderWorkflowV2))
        self.assertEqual(Application.objects.count(), 1)

    def test_create_full_order_with_2_merchant_orders_with_recompute(self):
        order_item_1 = ProductOfferOrderItemFactory.create()
        order_item_2 = ProductOfferOrderItemFactory.create(
            application=order_item_1.merchant_order.application,
            user=order_item_1.merchant_order.order.user,
            cart=order_item_1.merchant_order.order.cart,
            merchant_order__order=order_item_1.merchant_order.order,
            recompute_amounts=True,
            currency=order_item_1.merchant_order.order.currency,
        )

        mo_1 = order_item_1.merchant_order
        mo_2 = order_item_2.merchant_order
        mo_1.refresh_from_db()
        order = mo_1.order

        self.assertEqual(mo_1.order, mo_2.order)
        # price
        self.assert_decimals_almost_equal(
            mo_1.price, order_item_1.price * order_item_1.quantity
        )
        self.assert_decimals_almost_equal(
            mo_2.price, order_item_2.price * order_item_2.quantity
        )
        self.assert_decimals_almost_equal(
            order.price,
            order_item_1.price * order_item_1.quantity
            + order_item_2.price * order_item_2.quantity,
        )
        # vat_on_products
        self.assert_decimals_almost_equal(
            mo_1.vat_on_products, order_item_1.vat * order_item_1.quantity
        )
        self.assert_decimals_almost_equal(
            mo_2.vat_on_products, order_item_2.vat * order_item_2.quantity
        )
        self.assert_decimals_almost_equal(
            order.vat_on_products,
            order_item_1.vat * order_item_1.quantity
            + order_item_2.vat * order_item_2.quantity,
        )
        # vat_on_shipping
        self.assertEqual(order.vat_on_shipping, 0)
        self.assertEqual(mo_1.vat_on_shipping, 0)
        self.assertEqual(mo_2.vat_on_shipping, 0)

        # vat_on_eco_tax
        self.assert_decimals_almost_equal(
            order.vat_on_eco_tax, mo_1.vat_on_eco_tax + mo_2.vat_on_eco_tax
        )
        # shipping
        self.assertEqual(order.shipping, 0)
        self.assertEqual(mo_1.shipping, 0)
        self.assertEqual(mo_2.shipping, 0)

        # eco_tax
        self.assert_decimals_almost_equal(order.eco_tax, mo_1.eco_tax + mo_2.eco_tax)
        self.assertIsNone(order.payment)

    def test_create_full_order_with_payment(self):
        order_item = ProductOfferOrderItemFactory.create(
            merchant_order__order__auto_create_payment=True
        )

        self.assertIsNotNone(order_item.merchant_order.order.payment)

    def test_create_order_with_auto_created_payment_and_payment_backend(self):
        order = OrderFactory.create(
            auto_create_payment=True, application__payment_backend=HIPAYTPP
        )

        self.assertEqual(order.payment.payment_backend, HIPAYTPP)
        self.assertEqual(order.application.payment_settings.payment_backend, HIPAYTPP)

    def test_payment_synchro_with_initial_order(self):
        order_item = ProductOfferOrderItemFactory.create(
            recompute_amounts=True,
            merchant_order__order__auto_create_payment=True,
            status=ORDER_ITEM_STATUS_INITIAL,
        )

        order = order_item.merchant_order.order
        payment = order.payment
        self.assertEqual(payment.authorized_amount, None)
        self.assertEqual(payment.collected_amount, 0)
        self.assertEqual(payment.status, PAYMENT_STATUS_INITIAL)

    def test_payment_synchro_with_authorized_order(self):
        order_item = ProductOfferOrderItemFactory.create(
            recompute_amounts=True,
            merchant_order__order__auto_create_payment=True,
            status=ORDER_ITEM_STATUS_AUTH_SUCCESS,
        )

        order = order_item.merchant_order.order
        payment = order.payment
        self.assertEqual(payment.authorized_amount, order.amount_vat_included)
        self.assertEqual(payment.collected_amount, 0)
        self.assertEqual(payment.status, PAYMENT_STATUS_AUTH_SUCCESS)

    def test_payment_synchro_with_confirmed_order(self):
        order_item = ProductOfferOrderItemFactory.create(
            recompute_amounts=True,
            merchant_order__order__auto_create_payment=True,
            status=ORDER_ITEM_STATUS_CONFIRMED,
        )

        order = order_item.merchant_order.order
        payment = order.payment
        self.assertEqual(payment.authorized_amount, order.amount_vat_included)
        self.assertEqual(payment.collected_amount, order.amount_vat_included)
        self.assertEqual(payment.status, PAYMENT_STATUS_COLLECTED)

    def test_payment_synchro_with_2_items(self):
        order_item_1 = ProductOfferOrderItemFactory.create(
            status=ORDER_ITEM_STATUS_CONFIRMED,
            merchant_order__order__auto_create_payment=True,
        )
        ProductOfferOrderItemFactory.create(
            application=order_item_1.merchant_order.application,
            user=order_item_1.merchant_order.order.user,
            cart=order_item_1.merchant_order.order.cart,
            merchant_order__order=order_item_1.merchant_order.order,
            recompute_amounts=True,
            currency=order_item_1.merchant_order.order.currency,
            status=ORDER_ITEM_STATUS_CONFIRMED,
        )

        order = order_item_1.merchant_order.order
        payment = order.payment
        self.assertEqual(payment.authorized_amount, order.amount_vat_included)
        self.assertEqual(payment.collected_amount, order.amount_vat_included)
        self.assertEqual(payment.status, PAYMENT_STATUS_COLLECTED)
