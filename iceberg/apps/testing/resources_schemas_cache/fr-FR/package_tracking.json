{"allowed_detail_http_methods": ["get", "post", "put", "delete", "patch"], "allowed_list_http_methods": ["get", "post"], "default_format": "application/json", "default_limit": 20, "doc": "Package tracking information linked to merchant_order and/or order_items.", "fields": {"application": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "A single related resource. Can be either a URI or set of nested resource data.", "name": "Application", "nullable": false, "readonly": true, "related_type": "to_one", "type": "related", "unique": false}, "carrier": {"blank": false, "choices": [{"help_text": "Chronopost", "value": "chronopost"}, {"help_text": "La Poste (courrier suivi)", "value": "laposte-courrier-suivi"}, {"help_text": "<PERSON><PERSON><PERSON>", "value": "chronorelais"}, {"help_text": "Colissimo", "value": "colissi<PERSON>"}, {"help_text": "UPS", "value": "ups"}, {"help_text": "FedEx", "value": "fedex"}, {"help_text": "DHL", "value": "dhl"}, {"help_text": "USPS", "value": "usps"}, {"help_text": "TNT", "value": "tnt"}, {"help_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "parcelforce"}, {"help_text": "EXAPAQ", "value": "exapaq"}, {"help_text": "GLS", "value": "gls"}, {"help_text": "Mondial Relay Point Relais", "value": "mondial-relay-point-relais"}, {"help_text": "Mondial Relay Home Confort", "value": "mondial-relay-home-confort"}, {"help_text": "Mondial Relay Home Premium", "value": "mondial-relay-home-premium"}, {"help_text": "<PERSON><PERSON>", "value": "kiala"}, {"help_text": "Bpost", "value": "bpost"}, {"help_text": "Correos Spain", "value": "correos-spain"}, {"help_text": "Poste Italiane", "value": "poste-italiane"}, {"help_text": "CTT", "value": "ctt"}, {"help_text": "PostNord", "value": "<PERSON>nord"}, {"help_text": "Royal Mail", "value": "royal-mail"}, {"help_text": "Swiss Post", "value": "swiss-post"}, {"help_text": "<PERSON><PERSON>", "value": "seur"}, {"help_text": "Dpd", "value": "dpd"}, {"help_text": "Envialia", "value": "envialia"}, {"help_text": "Correos Express", "value": "correos-express"}, {"help_text": "Mail Boxes", "value": "mail-boxes"}, {"help_text": "Nacex", "value": "nacex"}, {"help_text": "<PERSON><PERSON><PERSON>", "value": "z<PERSON><PERSON>"}, {"help_text": "<PERSON><PERSON>", "value": "autre"}, {"help_text": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"help_text": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"help_text": "FATTON", "value": "fatton"}, {"help_text": "France colis express", "value": "france-colis-express"}, {"help_text": "<PERSON><PERSON><PERSON>", "value": "geodis"}, {"help_text": "<PERSON><PERSON><PERSON>", "value": "calberson"}, {"help_text": "GEFCO", "value": "gefco"}, {"help_text": "<PERSON><PERSON>", "value": "colis-prive"}, {"help_text": "Italmond<PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"help_text": "<PERSON><PERSON><PERSON>", "value": "relais-colis"}, {"help_text": "<PERSON><PERSON><PERSON>", "value": "<PERSON>ie<PERSON>"}, {"help_text": "Happy-post", "value": "happy-post"}, {"help_text": "Deliver.ee", "value": "deliveree"}, {"help_text": "Sodexi", "value": "sodexi"}, {"help_text": "Poczta Polska", "value": "poczta-polska"}, {"help_text": "An Post", "value": "an-post"}, {"help_text": "PostNL", "value": "postnl"}, {"help_text": "CTT", "value": "ctt"}, {"help_text": "Bpost", "value": "bpost"}, {"help_text": "P&T Luxembourg", "value": "pt-luxembourg"}, {"help_text": "Post Danmark", "value": "post-danmark"}, {"help_text": "Austrian Post", "value": "austrian-post"}, {"help_text": "Swedish Post", "value": "swedish-post"}, {"help_text": "Finland Post", "value": "finland-post"}, {"help_text": "Deutsche Post", "value": "deutsche-post"}], "choices_to_hide": [], "creatable": true, "default": "No default provided.", "editable": true, "help_text": "", "max_length": 300, "name": "Carrier", "nullable": false, "readonly": false, "type": "string", "unique": false, "virtual_choices": []}, "carrier_localized": {"blank": true, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Carrier localized", "nullable": true, "readonly": false, "type": "string", "unique": false}, "carrier_url": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "URL de suivi de colis du transporteur", "max_length": 400, "name": "URL du transporteur", "nullable": true, "readonly": false, "type": "string", "unique": false}, "comment": {"blank": false, "creatable": true, "default": "", "editable": true, "help_text": "Information pour l'utilisateur", "max_length": null, "name": "<PERSON>e d'aide", "nullable": true, "readonly": false, "type": "string", "unique": false}, "created_on": {"blank": true, "creatable": true, "default": true, "editable": true, "help_text": "", "name": "Created on", "nullable": false, "readonly": false, "type": "datetime", "unique": false}, "estimate_arrival": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "Temps de livraison estimé du colis", "name": "<PERSON><PERSON><PERSON>", "nullable": true, "readonly": false, "type": "datetime", "unique": false}, "id": {"blank": true, "creatable": false, "default": "", "editable": false, "help_text": "", "name": "ID", "nullable": false, "readonly": true, "type": "integer", "unique": true}, "merchant_order": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "", "name": "Merchant order", "nullable": false, "readonly": false, "related_type": "to_one", "type": "related", "unique": false}, "order": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Order", "nullable": false, "readonly": true, "related_type": "to_one", "type": "related", "unique": false}, "order_items": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "Utilisé en cas de validation partielle", "name": "Order items", "nullable": true, "readonly": false, "related_type": "to_many", "type": "related", "unique": false}, "overall_status": {"blank": false, "choices": [{"help_text": "<PERSON><PERSON> les d<PERSON>lai<PERSON>", "value": 1}, {"help_text": "En retard", "value": 2}, {"help_text": "Problème", "value": 3}], "choices_to_hide": [], "creatable": true, "default": "No default provided.", "editable": true, "help_text": "Suivre l'historique de livraison", "name": "Statut ", "nullable": true, "readonly": false, "type": "integer", "unique": false, "virtual_choices": []}, "resource_uri": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Resource uri", "nullable": false, "readonly": true, "type": "string", "unique": false}, "shipping_date": {"blank": true, "creatable": true, "default": true, "editable": true, "help_text": "Quand le colis a été expédié", "name": "Shipping date", "nullable": false, "readonly": false, "type": "datetime", "unique": false}, "status": {"blank": false, "choices": [{"help_text": "Initial", "value": 1}, {"help_text": "En transit", "value": 2}, {"help_text": "Complété(e)", "value": 3}, {"help_text": "Perdu", "value": 4}, {"help_text": "<PERSON><PERSON><PERSON> d'ad<PERSON>e", "value": 5}, {"help_text": "Colis inconnu", "value": 6}], "choices_to_hide": [], "creatable": true, "default": 1, "editable": true, "help_text": "", "name": "Statut ", "nullable": false, "readonly": false, "type": "integer", "unique": false, "virtual_choices": []}, "status_localized": {"blank": true, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Status localized", "nullable": true, "readonly": false, "type": "string", "unique": false}, "tracking_number": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "", "max_length": 300, "name": "Tracking number", "nullable": true, "readonly": false, "type": "string", "unique": false}, "tracking_url": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Tracking url", "nullable": true, "readonly": true, "type": "string", "unique": false}, "weight": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "", "name": "Weight", "nullable": true, "readonly": false, "type": "decimal", "unique": false}}, "filtering": {"application": "exact", "created_on": 1, "id": 1, "merchant_order": 1, "order": 2}, "methods": [], "ordering": ["application", "created_on", "id", "merchant_order", "order"]}