{"allowed_detail_http_methods": ["get", "put", "patch"], "allowed_list_http_methods": ["get", "post"], "default_format": "application/json", "default_limit": 100, "doc": "ProductAttributeGroup Resource", "fields": {"application": {"blank": false, "creatable": true, "default": "No default provided.", "editable": false, "help_text": "", "name": "Application", "nullable": false, "readonly": true, "related_type": "to_one", "type": "related", "unique": false}, "application_categories": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "Disponibilité limitée à ces catégories d'application si défini", "name": "Application categories", "nullable": true, "readonly": false, "related_type": "to_many", "type": "related", "unique": false}, "created_on": {"blank": true, "creatable": false, "default": true, "editable": false, "help_text": "", "name": "<PERSON><PERSON><PERSON>", "nullable": false, "readonly": true, "type": "datetime", "unique": false}, "entity_type": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Entity type", "nullable": false, "readonly": false, "type": "string", "unique": false}, "external_id": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "A utiliser pour stocker l'ID de matching externe", "max_length": 255, "name": "ID Externe", "nullable": true, "readonly": true, "type": "string", "unique": false}, "group_assignments": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Attribute_group_assignments", "nullable": true, "readonly": true, "related_type": "to_many", "type": "related", "unique": false}, "group_key": {"blank": true, "creatable": true, "default": "", "editable": false, "help_text": "", "max_length": 255, "name": "Clé unique du groupe", "nullable": false, "readonly": true, "type": "string", "unique": false}, "id": {"blank": true, "creatable": false, "default": "", "editable": false, "help_text": "", "name": "ID", "nullable": false, "readonly": true, "type": "integer", "unique": true}, "last_modified": {"blank": true, "creatable": false, "default": true, "editable": false, "help_text": "", "name": "Dernière modification", "nullable": false, "readonly": true, "type": "datetime", "unique": false}, "moderation_policy": {"blank": false, "choices": [{"help_text": "Non modéré", "value": 0}], "choices_to_hide": [], "creatable": false, "default": 0, "editable": false, "help_text": "", "name": "Moderation policy", "nullable": false, "readonly": true, "type": "integer", "unique": false, "virtual_choices": []}, "priority": {"blank": false, "creatable": true, "default": 100, "editable": true, "help_text": "", "name": "Priority", "nullable": false, "readonly": false, "type": "integer", "unique": false}, "resource_uri": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Resource uri", "nullable": false, "readonly": true, "type": "string", "unique": false}}, "filtering": {"application": 2, "application_categories": 2, "entity_type": 1, "group_key": 1, "id": 1}, "methods": [{"doc_html": "", "doc_text": "", "regex": "/v1/product_attribute_group/:id/assign_attribute/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/product_attribute_group/:id/unassign_attribute/"}], "ordering": ["application", "application_categories", "entity_type", "group_key", "id"]}