{"allowed_detail_http_methods": ["get", "post", "put", "delete", "patch"], "allowed_list_http_methods": ["get", "post"], "default_format": "application/json", "default_limit": 100, "doc": "MerchantAttribute Resource", "editable_for_statuses": [], "fields": {"application": {"blank": false, "creatable": true, "default": "No default provided.", "editable": false, "help_text": "", "name": "Application", "nullable": false, "readonly": true, "related_type": "to_one", "type": "related", "unique": false}, "attribute_kind": {"blank": false, "choices": [{"help_text": "<PERSON><PERSON><PERSON>", "value": "pricing"}, {"help_text": "<PERSON><PERSON><PERSON>", "value": "shipping"}, {"help_text": "Commission", "value": "commission"}, {"help_text": "Marchand", "value": "merchant"}, {"help_text": "Client", "value": "customer"}, {"help_text": "Facturation", "value": "invoicing"}, {"help_text": "Paiement", "value": "payment"}, {"help_text": "Légal", "value": "legal"}, {"help_text": "Réservation", "value": "booking"}, {"help_text": "<PERSON><PERSON><PERSON>", "value": "calendar"}, {"help_text": "Emplacement", "value": "location"}], "choices_to_hide": [], "creatable": true, "default": null, "editable": true, "help_text": "", "max_length": 20, "name": "Sorte d'attribut", "nullable": true, "readonly": false, "type": "string", "unique": false, "virtual_choices": []}, "creatable": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Boolean data. Ex: True", "name": "Creatable", "nullable": false, "readonly": true, "type": "boolean", "unique": false}, "created_on": {"blank": true, "creatable": false, "default": true, "editable": false, "help_text": "", "name": "<PERSON><PERSON><PERSON>", "nullable": false, "readonly": true, "type": "datetime", "unique": false}, "description": {"blank": true, "default": "", "help_text": "description", "localized": true, "name": "description", "nullable": true, "readonly": false, "type": "string", "unique": false}, "editable": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Boolean data. Ex: True", "name": "Editable", "nullable": false, "readonly": true, "type": "boolean", "unique": false}, "external_id": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "A utiliser pour stocker l'ID de matching externe", "max_length": 255, "name": "ID Externe", "nullable": true, "readonly": true, "type": "string", "unique": false}, "id": {"blank": true, "creatable": false, "default": "", "editable": false, "help_text": "", "name": "ID", "nullable": false, "readonly": true, "type": "integer", "unique": true}, "key": {"blank": false, "creatable": true, "default": "No default provided.", "editable": false, "help_text": "", "max_length": 255, "name": "Clé unique de l'attribut", "nullable": false, "readonly": true, "type": "string", "unique": false}, "label": {"blank": true, "default": "", "help_text": "label", "localized": true, "name": "label", "nullable": true, "readonly": false, "type": "string", "unique": false}, "languages": {"blank": true, "choices": [{"help_text": "Afrikaans", "value": "af"}, {"help_text": "Arabic", "value": "ar"}, {"help_text": "Algerian Arabic", "value": "ar-dz"}, {"help_text": "Asturian", "value": "ast"}, {"help_text": "Azerbaijani", "value": "az"}, {"help_text": "Bulgarian", "value": "bg"}, {"help_text": "Belarusian", "value": "be"}, {"help_text": "Bengali", "value": "bn"}, {"help_text": "Breton", "value": "br"}, {"help_text": "Bosnian", "value": "bs"}, {"help_text": "Catalan", "value": "ca"}, {"help_text": "Czech", "value": "cs"}, {"help_text": "Welsh", "value": "cy"}, {"help_text": "Danish", "value": "da"}, {"help_text": "German", "value": "de"}, {"help_text": "Lower Sorbian", "value": "dsb"}, {"help_text": "Greek", "value": "el"}, {"help_text": "English", "value": "en"}, {"help_text": "Australian English", "value": "en-au"}, {"help_text": "British English", "value": "en-gb"}, {"help_text": "Esperanto", "value": "eo"}, {"help_text": "Spanish", "value": "es"}, {"help_text": "Argentinian Spanish", "value": "es-ar"}, {"help_text": "Colombian Spanish", "value": "es-co"}, {"help_text": "Mexican Spanish", "value": "es-mx"}, {"help_text": "Nicaraguan Spanish", "value": "es-ni"}, {"help_text": "Venezuelan Spanish", "value": "es-ve"}, {"help_text": "Estonian", "value": "et"}, {"help_text": "Basque", "value": "eu"}, {"help_text": "Persian", "value": "fa"}, {"help_text": "Finnish", "value": "fi"}, {"help_text": "French", "value": "fr"}, {"help_text": "Frisian", "value": "fy"}, {"help_text": "Irish", "value": "ga"}, {"help_text": "Scottish Gaelic", "value": "gd"}, {"help_text": "Galician", "value": "gl"}, {"help_text": "Hebrew", "value": "he"}, {"help_text": "Hindi", "value": "hi"}, {"help_text": "Croatian", "value": "hr"}, {"help_text": "Upper Sorbian", "value": "hsb"}, {"help_text": "Hungarian", "value": "hu"}, {"help_text": "Armenian", "value": "hy"}, {"help_text": "Interlingua", "value": "ia"}, {"help_text": "Indonesian", "value": "id"}, {"help_text": "Igbo", "value": "ig"}, {"help_text": "Ido", "value": "io"}, {"help_text": "Icelandic", "value": "is"}, {"help_text": "Italian", "value": "it"}, {"help_text": "Japanese", "value": "ja"}, {"help_text": "Georgian", "value": "ka"}, {"help_text": "Ka<PERSON>le", "value": "kab"}, {"help_text": "Kazakh", "value": "kk"}, {"help_text": "Khmer", "value": "km"}, {"help_text": "Kannada", "value": "kn"}, {"help_text": "Korean", "value": "ko"}, {"help_text": "Kyrgyz", "value": "ky"}, {"help_text": "Luxembourgish", "value": "lb"}, {"help_text": "Lithuanian", "value": "lt"}, {"help_text": "Latvian", "value": "lv"}, {"help_text": "Macedonian", "value": "mk"}, {"help_text": "Malayalam", "value": "ml"}, {"help_text": "Mongolian", "value": "mn"}, {"help_text": "Marathi", "value": "mr"}, {"help_text": "Malay", "value": "ms"}, {"help_text": "Burmese", "value": "my"}, {"help_text": "Norwegian Bokmål", "value": "nb"}, {"help_text": "Nepali", "value": "ne"}, {"help_text": "Dutch", "value": "nl"}, {"help_text": "Norwegian Nynorsk", "value": "nn"}, {"help_text": "Ossetic", "value": "os"}, {"help_text": "Punjabi", "value": "pa"}, {"help_text": "Polish", "value": "pl"}, {"help_text": "Portuguese", "value": "pt"}, {"help_text": "Brazilian Portuguese", "value": "pt-br"}, {"help_text": "Romanian", "value": "ro"}, {"help_text": "Russian", "value": "ru"}, {"help_text": "Slovak", "value": "sk"}, {"help_text": "Slovenian", "value": "sl"}, {"help_text": "Albanian", "value": "sq"}, {"help_text": "Serbian", "value": "sr"}, {"help_text": "Serbian Latin", "value": "sr-latn"}, {"help_text": "Swedish", "value": "sv"}, {"help_text": "Swahili", "value": "sw"}, {"help_text": "Tamil", "value": "ta"}, {"help_text": "Telugu", "value": "te"}, {"help_text": "Tajik", "value": "tg"}, {"help_text": "Thai", "value": "th"}, {"help_text": "Turkmen", "value": "tk"}, {"help_text": "Turkish", "value": "tr"}, {"help_text": "Tatar", "value": "tt"}, {"help_text": "Udmurt", "value": "udm"}, {"help_text": "Ukrainian", "value": "uk"}, {"help_text": "Urdu", "value": "ur"}, {"help_text": "Uzbek", "value": "uz"}, {"help_text": "Vietnamese", "value": "vi"}, {"help_text": "Simplified Chinese", "value": "zh-hans"}, {"help_text": "Traditional Chinese", "value": "zh-hant"}], "choices_to_hide": [], "creatable": true, "default": "No default provided.", "editable": true, "help_text": "Langue si seulement disponible pour une sélection de langues", "name": "Languages", "nullable": false, "readonly": false, "type": "list", "unique": false, "virtual_choices": []}, "last_modified": {"blank": true, "creatable": false, "default": true, "editable": false, "help_text": "", "name": "Dernière modification", "nullable": false, "readonly": true, "type": "datetime", "unique": false}, "localized_infos": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Localized_infos", "nullable": true, "readonly": true, "related_type": "to_many", "type": "related", "unique": false}, "merchant": {"blank": false, "creatable": true, "default": "No default provided.", "editable": false, "help_text": "", "name": "Merchant", "nullable": true, "readonly": true, "related_type": "to_one", "type": "related", "unique": false}, "name": {"blank": true, "default": "", "help_text": "name", "localized": true, "name": "name", "nullable": true, "readonly": false, "type": "string", "unique": false}, "position": {"blank": false, "creatable": false, "default": 100, "editable": false, "help_text": "", "name": "Position", "nullable": false, "readonly": true, "type": "integer", "unique": false}, "readonly": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Boolean data. Ex: True", "name": "<PERSON><PERSON><PERSON>", "nullable": false, "readonly": true, "type": "boolean", "unique": false}, "resource_uri": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Resource uri", "nullable": false, "readonly": true, "type": "string", "unique": false}, "status": {"blank": false, "choices": [{"help_text": "Visible", "value": "visible"}, {"help_text": "Supprimé ", "value": "deleted"}], "choices_to_hide": [], "creatable": false, "default": "visible", "editable": false, "help_text": "", "max_length": 16, "name": "Status", "nullable": false, "readonly": true, "transitions": {"delete_action": {"from_states": ["visible"], "to_state": "deleted"}}, "type": "string", "unique": false, "virtual_choices": []}, "validation_rules": {"blank": true, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "", "name": "Validation rules", "nullable": false, "readonly": false, "type": "json", "unique": false}, "value_choices": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Value_choices", "nullable": true, "readonly": true, "related_type": "to_many", "type": "related", "unique": false}, "value_type": {"blank": false, "choices": [{"help_text": "<PERSON><PERSON>ne de caractères", "value": "str"}, {"help_text": "Texte", "value": "text"}, {"help_text": "Nombre entier", "value": "int"}, {"help_text": "Nombre décimal", "value": "decimal"}, {"help_text": "Prix", "value": "price"}, {"help_text": "Booléen", "value": "bool"}, {"help_text": "Date et heure", "value": "datetime"}, {"help_text": "Date", "value": "date"}, {"help_text": "<PERSON><PERSON>", "value": "time"}, {"help_text": "Liste de chaînes de caractères", "value": "str_list"}, {"help_text": "Liste d'entiers", "value": "int_list"}, {"help_text": "Json", "value": "json"}, {"help_text": "<PERSON><PERSON><PERSON>", "value": "file"}, {"help_text": "Entité", "value": "entity"}], "choices_to_hide": [], "creatable": true, "default": "str", "editable": true, "help_text": "", "max_length": 20, "name": "Value type", "nullable": false, "readonly": false, "type": "string", "unique": false, "virtual_choices": []}, "values_visibility": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "JSON object. Can be a dictionary or a list", "name": "Values visibility", "nullable": false, "readonly": false, "type": "json", "unique": false}}, "filtering": {"application": 2, "attribute_kind": 1, "id": 1, "key": 1, "localized_infos": 2, "merchant": 2, "name": 1, "status": 1, "value_type": 1}, "methods": [{"doc_html": "", "doc_text": "", "regex": "/v1/merchant_attribute/:id/can-delete/"}], "ordering": ["application", "merchant", "key", "status", "attribute_kind", "value_type"]}