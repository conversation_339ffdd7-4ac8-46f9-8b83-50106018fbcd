{"allowed_detail_http_methods": ["get", "post", "put", "delete", "patch"], "allowed_list_http_methods": ["get", "post"], "default_format": "application/json", "default_limit": 20, "editable_for_statuses": [], "fields": {"application": {"blank": false, "creatable": true, "default": "No default provided.", "editable": false, "help_text": "", "name": "Application", "nullable": false, "readonly": true, "related_type": "to_one", "type": "related", "unique": false}, "created_on": {"blank": true, "creatable": false, "default": true, "editable": false, "help_text": "", "name": "<PERSON><PERSON><PERSON>", "nullable": false, "readonly": true, "type": "datetime", "unique": false}, "external_id": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "A utiliser pour stocker l'ID de matching externe", "max_length": 255, "name": "ID Externe", "nullable": true, "readonly": true, "type": "string", "unique": false}, "id": {"blank": false, "creatable": false, "default": "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa", "editable": false, "help_text": "", "max_length": 32, "name": "Id", "nullable": false, "readonly": true, "type": "string", "unique": false}, "label_on_invoice": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "", "max_length": 255, "name": "Label on invoice", "nullable": true, "readonly": false, "type": "string", "unique": false}, "last_modified": {"blank": true, "creatable": false, "default": true, "editable": false, "help_text": "", "name": "Dernière modification", "nullable": false, "readonly": true, "type": "datetime", "unique": false}, "name": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "", "max_length": 255, "name": "Name", "nullable": false, "readonly": false, "type": "string", "unique": false}, "resource_uri": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Resource uri", "nullable": false, "readonly": true, "type": "string", "unique": false}, "status": {"blank": false, "choices": [{"help_text": "Initial", "value": "initial"}, {"help_text": "Actif", "value": "active"}, {"help_text": "Supprimé ", "value": "deleted"}], "choices_to_hide": [], "creatable": false, "default": "initial", "editable": false, "help_text": "", "max_length": 16, "name": "Statut", "nullable": false, "readonly": true, "transitions": {"activate": {"from_states": ["initial"], "to_state": "active"}, "deactivate": {"from_states": ["active"], "to_state": "initial"}, "delete_action": {"from_states": ["initial"], "to_state": "deleted"}}, "type": "string", "unique": false, "virtual_choices": []}}, "filtering": {"application": 2, "external_id": 1, "id": 1, "name": 1, "status": 1}, "methods": [{"doc_html": "Activate the payment term<br>@Allowed Methods: [POST]<br>@Return: Activated payment term", "doc_text": "Activate the payment term\n@Allowed Methods: [POST]\n@Return: Activated payment term", "regex": "/v1/payment_term/:id/activate/"}, {"doc_html": "Deactivate the payment term<br>@Allowed Methods: [POST]<br>@Return: Deactivated payment term", "doc_text": "Deactivate the payment term\n@Allowed Methods: [POST]\n@Return: Deactivated payment term", "regex": "/v1/payment_term/:id/deactivate/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/payment_term/:id/lines/"}], "ordering": ["application", "external_id", "id", "name", "status"]}