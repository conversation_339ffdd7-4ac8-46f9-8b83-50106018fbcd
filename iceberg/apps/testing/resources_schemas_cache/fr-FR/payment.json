{"allowed_detail_http_methods": ["get", "post", "put", "delete", "patch"], "allowed_list_http_methods": ["get", "post"], "default_format": "application/json", "default_limit": 20, "doc": "Gather Payment information for an Order.", "editable_for_statuses": {"0": ["external_id", "specific_to_collect_amount", "specific_commission_rate", "payment_name"], "100": ["external_id", "payment_name"], "1000": ["external_id", "payment_name"], "2000": ["external_id", "payment_name"], "60": ["external_id", "specific_to_collect_amount", "specific_commission_rate", "payment_name"], "61": ["external_id", "payment_name"], "77": ["external_id", "payment_name"], "80": ["external_id", "payment_name"], "85": ["external_id", "payment_name"], "90": ["external_id", "payment_name"]}, "fields": {"_collect_detail": {"blank": false, "creatable": false, "default": [], "editable": false, "help_text": "", "name": " collect detail", "nullable": true, "readonly": true, "type": "list", "unique": false}, "application": {"blank": false, "creatable": true, "default": "No default provided.", "editable": false, "help_text": "Application à laquelle appartient ce paiement.", "name": "Application", "nullable": false, "readonly": true, "related_type": "to_one", "type": "related", "unique": false}, "authorized_amount": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Montant auto<PERSON>", "name": "Authorized amount", "nullable": true, "readonly": true, "type": "decimal", "unique": false}, "collected_amount": {"blank": false, "creatable": false, "default": "0.00", "editable": false, "help_text": "Montant déjà collecté par une passerelle de paiement", "name": "Collected amount", "nullable": false, "readonly": true, "type": "decimal", "unique": false}, "consistency_last_check": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Consistency last check", "nullable": true, "readonly": true, "type": "datetime", "unique": false}, "consistency_status": {"blank": false, "choices": [{"help_text": "Co<PERSON><PERSON><PERSON>ce inconnue", "value": "consistency_unknown"}, {"help_text": "<PERSON><PERSON><PERSON><PERSON>", "value": "inconsistent"}, {"help_text": "<PERSON><PERSON><PERSON><PERSON>", "value": "consistent"}], "choices_to_hide": [], "creatable": false, "default": "consistency_unknown", "editable": false, "help_text": "", "max_length": 19, "name": "Statut de cohérence", "nullable": false, "readonly": true, "type": "string", "unique": false, "virtual_choices": []}, "created_on": {"blank": false, "creatable": false, "default": true, "editable": false, "help_text": "", "name": "Created on", "nullable": true, "readonly": true, "type": "datetime", "unique": false}, "currency": {"blank": false, "creatable": true, "default": "No default provided.", "editable": false, "help_text": "<PERSON><PERSON> de paiement (devise par défaut des articles payés)", "name": "<PERSON><PERSON><PERSON><PERSON>", "nullable": false, "readonly": true, "related_type": "to_one", "type": "related", "unique": false}, "customer_invoice": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Customer invoice", "nullable": true, "readonly": true, "related_type": "to_one", "type": "related", "unique": false}, "disputed_amount": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "<PERSON><PERSON> contesté", "name": "Disputed amount", "nullable": true, "readonly": true, "type": "decimal", "unique": false}, "external_id": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "IZBERG se base sur ce champ pour lancer la capture/le remboursement en votre nom quand l'autorisation est faite sur un module externe.", "max_length": 255, "name": "TransactionID / order-id du PSP distant", "nullable": true, "readonly": false, "type": "string", "unique": false}, "extra_data": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "JSON object. Can be a dictionary or a list", "name": "Extra data", "nullable": true, "readonly": true, "type": "json", "unique": false}, "id": {"blank": true, "creatable": false, "default": "", "editable": false, "help_text": "", "name": "ID", "nullable": false, "readonly": true, "type": "integer", "unique": true}, "last_updated": {"blank": false, "creatable": false, "default": true, "editable": false, "help_text": "", "name": "Last updated", "nullable": true, "readonly": true, "type": "datetime", "unique": false}, "merchant_orders": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Many related resources. Can be either a list of URIs or list of individually nested resource data.", "name": "Merchant orders", "nullable": true, "readonly": true, "related_type": "to_many", "type": "related", "unique": false}, "order": {"blank": false, "creatable": true, "default": "No default provided.", "editable": false, "help_text": "", "name": "Order", "nullable": true, "readonly": true, "related_type": "to_one", "type": "related", "unique": false}, "payment_backend": {"blank": false, "choices": [{"help_text": "Externe", "value": "external"}, {"help_text": "<PERSON><PERSON>", "value": "hipay_tpp"}, {"help_text": "PSP Gateway", "value": "psp_gateway"}, {"help_text": "Be-2-<PERSON>", "value": "be2bill"}, {"help_text": "Ingenico", "value": "ingenico"}], "choices_to_hide": [], "creatable": true, "default": "external", "editable": false, "help_text": "", "max_length": 255, "name": "Payment backend", "nullable": false, "readonly": true, "type": "string", "unique": false, "virtual_choices": []}, "payment_method": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Méthode de paiement utilisée", "name": "Méthode utilisée", "nullable": true, "readonly": true, "related_type": "to_one", "type": "related", "unique": false}, "payment_name": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "Nom optionnel du paiement", "max_length": 255, "name": "Payment name", "nullable": true, "readonly": false, "type": "string", "unique": false}, "payment_type": {"blank": false, "choices": [{"help_text": "Paiement complet", "value": "full"}, {"help_text": "Paiement échelonné", "value": "progress"}, {"help_text": "Paiement par d<PERSON><PERSON><PERSON>", "value": "deposit"}, {"help_text": "Paiement des frais opérateur", "value": "application_fees"}], "choices_to_hide": [], "creatable": true, "default": "full", "editable": false, "help_text": "", "max_length": 32, "name": "Payment type", "nullable": false, "readonly": true, "type": "string", "unique": false, "virtual_choices": []}, "payment_type_display": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Payment type display", "nullable": true, "readonly": true, "type": "string", "unique": false}, "pre_auth_backend": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Depuis quel backend de paiement cette pré-autorisation a été sauvegardée", "max_length": 255, "name": "Pre auth backend", "nullable": true, "readonly": true, "type": "string", "unique": false}, "pre_auth_id": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "ID de le pré-autorisation attachée au backend", "max_length": 255, "name": "Pre auth id", "nullable": true, "readonly": true, "type": "string", "unique": false}, "refunded_amount": {"blank": false, "creatable": false, "default": "0.00", "editable": false, "help_text": "<PERSON><PERSON> dé<PERSON><PERSON> remboursée depuis cette passerelle de paiement", "name": "Refunded amount", "nullable": false, "readonly": true, "type": "decimal", "unique": false}, "refunded_voucher_amount": {"blank": false, "creatable": false, "default": "0.00", "editable": false, "help_text": "Le montant du bon de réduction a déjà été remboursé", "name": "Refunded voucher amount", "nullable": false, "readonly": true, "type": "decimal", "unique": false}, "resource_uri": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Resource uri", "nullable": false, "readonly": true, "type": "string", "unique": false}, "specific_commission_rate": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "Taux de commission du paiement. Le comportement par défaut de l'application sera ignoré.", "name": "Specific commission rate", "nullable": true, "readonly": false, "type": "decimal", "unique": false}, "specific_to_collect_amount": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "Montant spécifique à collecter (obligatoire pour les paiement partiels)", "name": "Specific to collect amount", "nullable": true, "readonly": false, "type": "decimal", "unique": false}, "status": {"blank": false, "choices": [{"help_text": "Initial", "value": "0"}, {"help_text": "En attente", "value": "77"}, {"help_text": "Paiement Autorisé", "value": "60"}, {"help_text": "Paiement en attente d'autorisation", "value": "61"}, {"help_text": "Paiement collecté", "value": "80"}, {"help_text": "Paiement partiellement collecté", "value": "85"}, {"help_text": "Remboursée", "value": "90"}, {"help_text": "Partiellement rembo<PERSON>", "value": "100"}, {"help_text": "<PERSON><PERSON><PERSON>", "value": "2000"}, {"help_text": "Echec de collecte de paiement", "value": "1000"}], "choices_to_hide": [], "creatable": false, "default": "0", "editable": false, "help_text": "", "max_length": 16, "name": "Status", "nullable": false, "readonly": true, "transitions": {"authorize": {"from_states": ["0", "61"], "to_state": "60"}, "cancel": {"from_states": ["0", "61", "60"], "to_state": "2000"}, "collect": {"from_states": ["60", "85", "100"], "to_state": "80"}, "fail": {"from_states": ["0", "61", "60"], "to_state": "1000"}, "partially_collected": {"from_states": ["60", "85", "100"], "to_state": "85"}, "partially_refund": {"from_states": ["80", "85", "100"], "to_state": "100"}, "pending_authorization": {"from_states": ["0", "61"], "to_state": "61"}, "refund": {"from_states": ["80", "85", "100"], "to_state": "90"}, "reinit": {"from_states": ["1000"], "to_state": "0"}}, "type": "string", "unique": false, "virtual_choices": []}, "status_display": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Status display", "nullable": true, "readonly": true, "type": "string", "unique": false}, "to_collect_amount": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Fixed precision numeric data. Ex: 26.73", "name": "To collect amount", "nullable": true, "readonly": true, "type": "decimal", "unique": false}, "transactions": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Transactions", "nullable": true, "readonly": true, "related_type": "to_many", "type": "related", "unique": false}, "voucher_amount": {"blank": false, "creatable": false, "default": "0.00", "editable": false, "help_text": "Montant payé par bon(s) de réduction", "name": "Voucher amount", "nullable": false, "readonly": true, "type": "decimal", "unique": false}}, "filtering": {"application": 2, "consistency_last_check": 1, "consistency_status": 1, "created_on": 1, "customer_invoice": 2, "external_id": "exact", "id": 1, "order": 2, "payment_method": 2, "status": 1}, "methods": [{"doc_html": "", "doc_text": "", "regex": "/v1/payment/:id/pending_authorization/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/payment/:id/authorize/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/payment/:id/cancel/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/payment/:id/backend_form_data/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/payment/:id/collect/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/payment/:id/backend_raw_data/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/payment/:id/backend_summary/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/payment/:id/summary/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/payment/:id/check_backend_consistency/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/payment/:id/create_transactions_if_needed/"}], "ordering": ["application", "consistency_last_check", "consistency_status", "created_on", "customer_invoice", "external_id", "id", "order", "payment_method", "status"]}