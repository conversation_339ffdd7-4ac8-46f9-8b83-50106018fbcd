{"allowed_detail_http_methods": ["get", "post", "put", "delete", "patch"], "allowed_list_http_methods": ["get", "post"], "default_format": "application/json", "default_limit": 20, "fields": {"application": {"blank": false, "creatable": true, "default": "No default provided.", "editable": false, "help_text": "", "name": "Application", "nullable": true, "readonly": true, "related_type": "to_one", "type": "related", "unique": false}, "created_on": {"blank": false, "creatable": false, "default": true, "editable": false, "help_text": "", "name": "Created on", "nullable": true, "readonly": true, "type": "datetime", "unique": false}, "description": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "", "max_length": 512, "name": "Description", "nullable": true, "readonly": false, "type": "string", "unique": false}, "id": {"blank": true, "creatable": false, "default": "", "editable": false, "help_text": "", "name": "ID", "nullable": false, "readonly": true, "type": "integer", "unique": true}, "label": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "", "max_length": 128, "name": "Label", "nullable": true, "readonly": false, "type": "string", "unique": false}, "last_modified": {"blank": false, "creatable": false, "default": true, "editable": false, "help_text": "", "name": "Last modified", "nullable": true, "readonly": true, "type": "datetime", "unique": false}, "merchant": {"blank": false, "creatable": true, "default": "No default provided.", "editable": false, "help_text": "", "name": "Merchant", "nullable": true, "readonly": true, "related_type": "to_one", "type": "related", "unique": false}, "name": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "", "max_length": 255, "name": "Name", "nullable": true, "readonly": false, "type": "string", "unique": false}, "resource_uri": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Resource uri", "nullable": false, "readonly": true, "type": "string", "unique": false}, "selector_type": {"blank": false, "choices": [{"help_text": "Satisfait au moins un sélecteur", "value": "or"}, {"help_text": "Satisfait tous les sélecteurs", "value": "and"}], "choices_to_hide": [], "creatable": true, "default": "or", "editable": false, "help_text": "Les offres de la famille doivent-elles correspondre à tous les sélecteurs ou juste à au moins un ?", "max_length": 10, "name": "Selector type", "nullable": false, "readonly": true, "type": "string", "unique": false, "virtual_choices": []}, "selector_type_localized": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Selector type localized", "nullable": true, "readonly": true, "type": "string", "unique": false}, "selectors": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "", "name": "Selectors", "nullable": true, "readonly": false, "related_type": "to_many", "type": "related", "unique": false}}, "filtering": {"application": "exact", "created_on": 1, "id": 1, "last_modified": 1, "merchant": 1, "name": 1}, "methods": [{"doc_html": "", "doc_text": "", "regex": "/v1/product_family/:id/selectors/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/product_family/:id/products/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/product_family/:id/can-delete/"}], "ordering": ["application", "created_on", "id", "last_modified", "merchant", "name"]}