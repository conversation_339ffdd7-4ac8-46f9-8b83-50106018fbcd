{"allowed_detail_http_methods": ["get"], "allowed_list_http_methods": ["get"], "default_format": "application/json", "default_limit": 20, "fields": {"configuration": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "A single related resource. Can be either a URI or set of nested resource data.", "name": "Configuration", "nullable": false, "readonly": true, "related_type": "to_one", "type": "related", "unique": false}, "created_on": {"blank": true, "creatable": false, "default": true, "editable": false, "help_text": "", "name": "Created on", "nullable": false, "readonly": true, "type": "datetime", "unique": false}, "description": {"blank": false, "creatable": false, "default": "", "editable": false, "help_text": "Log description", "max_length": null, "name": "Description", "nullable": true, "readonly": true, "type": "string", "unique": false}, "error_code": {"blank": false, "choices": [{"help_text": "Skipped by filters", "value": "MAPPER.SKIP_ITEM"}, {"help_text": "This expression is invalid", "value": "MAPPER.INVALID_EXPRESSION"}, {"help_text": "The url is not a valid Google sheets link", "value": "MAPPER.GOOGLE_SHEET_EXCEPTION"}, {"help_text": "Parser must be defined", "value": "MAPPER.PARSER_MUST_BE_DEFINED"}, {"help_text": "Item list could not be found for this path", "value": "MAPPER.PARSER_CANNOT_YIELD_ITEM"}, {"help_text": "Xpath must be defined", "value": "MAPPER.INVALID_XPATH"}, {"help_text": "Mapper must be defined", "value": "MAPPER.MAPPER_MUST_BE_DEFINED"}, {"help_text": "Invalid protocol", "value": "MAPPER.INVALID_PROTOCOL"}, {"help_text": "Pass credentials as arguments or in url but not both", "value": "MAPPER.POSSIBLE_CREDENTIAL_CONFLICTS"}, {"help_text": "Pass port as argument or in url but not both", "value": "MAPPER.POSSIBLE_PORT_CONFLICTS"}, {"help_text": "Missing mandatory path of the file to download", "value": "MAPPER.MISSING_FTP_FILE_PATH"}, {"help_text": "Pass file path as arguments or in url but not both", "value": "MAPPER.FTP_FILE_PATH_CONFLICTS"}, {"help_text": "An error occurred during action processing", "value": "MAPPER.ACTION_ERROR"}, {"help_text": "An error occurred during moderation", "value": "MAPPER.MODERATION_ERROR"}, {"help_text": "Invalid type for mapping", "value": "MAPPER.INVALID_TYPE_FOR_MAPPING"}, {"help_text": "Nested mapping is invalid", "value": "MAPPER.INVALID_MULTIPLE_NESTED_MAPPING"}, {"help_text": "Invalid action", "value": "MAPPER.INVALID_ACTION"}, {"help_text": "Invalid cleaner", "value": "MAPPER.INVALID_CLEANER"}, {"help_text": "Invalid loader", "value": "MAPPER.INVALID_LOADER"}, {"help_text": "Invalid parser", "value": "MAPPER.INVALID_PARSER"}, {"help_text": "Missing mandatory parameter", "value": "MAPPER.MISSING_MANDATORY_PARAMETER"}, {"help_text": "Unknown parameter", "value": "MAPPER.UNKNOWN_PARAMETER"}, {"help_text": "The message has a problem", "value": "MAPPER.MESSAGE_EXCEPTION"}, {"help_text": "Configuration is already being processed", "value": "MAPPER.ALREADY_PROCESSING"}, {"help_text": "Image download error", "value": "MAPPER.IMAGE_DOWNLOAD_ERROR"}, {"help_text": "Feed download error", "value": "MAPPER.FEED_DOWNLOAD_ERROR"}, {"help_text": "Maximum data size exceeded for item", "value": "MAPPER.ITEM_TOO_BIG_FOR_BROKER"}, {"help_text": "Ambiguous order for images", "value": "MAPPER.AMBIGUOUS_IMAGE_ORDER"}, {"help_text": "Category not found", "value": "MAPPER.CATEGORY_NOT_FOUND"}, {"help_text": "Merchant not found", "value": "MAPPER.MERCHANT_NOT_FOUND"}, {"help_text": "Product not found", "value": "MAPPER.PRODUCT_NOT_FOUND"}, {"help_text": "Product offer not found", "value": "MAPPER.PRODUCT_OFFER_NOT_FOUND"}, {"help_text": "Product offer update already in progress", "value": "MAPPER.PRODUCT_OFFER_UPDATE_IN_PROGRESS"}, {"help_text": "Country not found", "value": "MAPPER.COUNTRY_NOT_FOUND"}, {"help_text": "Currency not found", "value": "MAPPER.CURRENCY_NOT_FOUND"}, {"help_text": "Product tax group not found", "value": "MAPPER.PRODUCT_TAX_GROUP_NOT_FOUND"}, {"help_text": "Ambiguous product lookup", "value": "MAPPER.AMBIGUOUS_PRODUCT_LOOKUP"}, {"help_text": "Ambiguous product offer lookup", "value": "MAPPER.AMBIGUOUS_PRODUCT_OFFER_LOOKUP"}, {"help_text": "Aggregation duplicate error", "value": "MAPPER.AGGREGATION_DUPLICATE_ERROR"}, {"help_text": "Incompatible values found", "value": "MAPPER.INCOMPATIBLE_VALUES"}, {"help_text": "Missing mandatory field", "value": "MAPPER.MISSING_MANDATORY_FIELD"}, {"help_text": "Missing nested field", "value": "MAPPER.MISSING_NESTED"}, {"help_text": "Unavailable language", "value": "MAPPER.UNAVAILABLE_LANGUAGE"}, {"help_text": "Ambiguous or unknown tax rate", "value": "MAPPER.AMBIGUOUS_OR_UNKNOWN_TAX_RATE"}, {"help_text": "Invalid variation type", "value": "MAPPER.INVALID_VARIATION_TYPE"}, {"help_text": "Missing VAT rule", "value": "MAPPER.MISSING_VAT_RULE"}, {"help_text": "No price defined", "value": "MAPPER.NO_PRICE_DEFINED"}, {"help_text": "Parent category not found", "value": "MAPPER.PARENT_CATEGORY_NOT_FOUND"}, {"help_text": "Field max length exceeded", "value": "MAPPER.FIELD_MAX_LENGTH_EXCEEDED"}, {"help_text": "Cannot create in \"Only update\" mode", "value": "MAPPER.CREATION_NOT_ALLOWED"}, {"help_text": "Cannot update in \"Only create\" mode", "value": "MAPPER.UPDATE_NOT_ALLOWED"}, {"help_text": "No analysis terminated successfully within the last few hours", "value": "MAPPER.ANALYSIS_NOT_FOUND"}, {"help_text": "Configuration is not initialized", "value": "MAPPER.UNINITIALIZED_CONFIGURATION"}, {"help_text": "Identifier Mismatch", "value": "MAPPER.IDENTIFIER_MISMATCH"}, {"help_text": "Encoding error", "value": "MAPPER.ENCODING_ERROR"}, {"help_text": "Decoding error", "value": "MAPPER.DECODING_ERROR"}, {"help_text": "Invalid format", "value": "MAPPER.INVALID_FORMAT"}, {"help_text": "Invalid URL format", "value": "MAPPER.INVALID_URL_FORMAT"}, {"help_text": "Missing timezone", "value": "MAPPER.MISSING_TIMEZONE"}, {"help_text": "Merchant is missing", "value": "MAPPER.MISSING_MERCHANT"}, {"help_text": "NULL byte found in file. Clean your imported file or try using UTF-16 encoding.", "value": "MAPPER.NULL_BYTE_IN_FILE"}, {"help_text": "Number of columns exceeds limit", "value": "MAPPER.EXCEED_MAX_COLUMNS"}, {"help_text": "Content exceeds max size limit", "value": "MAPPER.EXCEED_MAX_CONTENT_SIZE"}, {"help_text": "Limit reached", "value": "MAPPER.LIMIT_REACHED"}, {"help_text": "Attribute value is invalid", "value": "MAPPER.INVALID_ATTRIBUTE_VALUE"}, {"help_text": "Unknown error", "value": "MAPPER.UNKNOWN_ERROR"}, {"help_text": "Validation error", "value": "MAPPER.UNKNOWN_VALIDATION_ERROR"}], "choices_to_hide": [], "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "max_length": 50, "name": "Error code", "nullable": true, "readonly": true, "type": "string", "unique": false, "virtual_choices": []}, "error_code_verbose": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Error code verbose", "nullable": false, "readonly": true, "type": "string", "unique": false}, "id": {"blank": true, "creatable": false, "default": "", "editable": false, "help_text": "", "name": "Id", "nullable": false, "readonly": true, "type": "integer", "unique": true}, "item": {"blank": false, "creatable": false, "default": {}, "editable": false, "help_text": "JSON representation of the item", "name": "<PERSON><PERSON>", "nullable": true, "readonly": true, "type": "dict", "unique": false}, "item_external_id": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "max_length": 255, "name": "Item external ID", "nullable": true, "readonly": true, "type": "string", "unique": false}, "item_identifier_key": {"blank": false, "choices": [{"help_text": "GTIN", "value": "gtin"}, {"help_text": "SKU", "value": "sku"}], "choices_to_hide": [], "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "max_length": 10, "name": "Item identifier key", "nullable": true, "readonly": true, "type": "string", "unique": false, "virtual_choices": []}, "item_identifier_value": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "max_length": 255, "name": "Item identifier value", "nullable": true, "readonly": true, "type": "string", "unique": false}, "kind": {"blank": false, "choices": [{"help_text": "<PERSON><PERSON>", "value": "valid"}, {"help_text": "Invalid", "value": "invalid"}, {"help_text": "Skipped", "value": "skipped"}, {"help_text": "Warning", "value": "warning"}, {"help_text": "Error", "value": "error"}], "choices_to_hide": [], "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "max_length": 10, "name": "Kind", "nullable": false, "readonly": true, "type": "string", "unique": false, "virtual_choices": []}, "resource_uri": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Resource uri", "nullable": false, "readonly": true, "type": "string", "unique": false}, "transformation_log": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Transformation log", "nullable": false, "readonly": true, "related_type": "to_one", "type": "related", "unique": false}, "trigger_description": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Exception description forwarder", "max_length": 255, "name": "Trigger description", "nullable": true, "readonly": true, "type": "string", "unique": false}, "trigger_resource_uri": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "URI of the exception trigger. Ex: mapper, cleaner, matcher uris", "max_length": 255, "name": "Trigger resource uri", "nullable": true, "readonly": true, "type": "string", "unique": false}}, "filtering": {"created_on": 1, "error_code": 1, "id": 1, "item_external_id": 1, "item_identifier_key": 1, "item_identifier_value": 1, "kind": 1, "transformation_log": 2}, "methods": [{"doc_html": "", "doc_text": "", "regex": "/v1/mapper_transformation_log_detail/export/"}], "ordering": ["id"]}