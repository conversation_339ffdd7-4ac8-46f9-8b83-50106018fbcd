{"allowed_detail_http_methods": ["get", "put", "patch", "delete"], "allowed_list_http_methods": ["get", "post"], "default_format": "application/json", "default_limit": 20, "doc": "A Merchant object", "editable_for_statuses": [], "fields": {"addresses": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Many related resources. Can be either a list of URIs or list of individually nested resource data.", "name": "Addresses", "nullable": true, "readonly": true, "related_type": "to_many", "type": "related", "unique": false}, "admin_comments": {"blank": true, "creatable": false, "default": "", "editable": false, "help_text": "comments for admin only", "max_length": null, "name": "Admin comments", "nullable": false, "readonly": true, "type": "string", "unique": false}, "api_key": {"blank": false, "creatable": false, "default": "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa", "editable": false, "help_text": "", "max_length": 128, "name": "API key", "nullable": false, "readonly": true, "type": "string", "unique": false}, "application": {"blank": false, "creatable": true, "default": "No default provided.", "editable": false, "help_text": "Who has brought the Merchant. An application admin can manage a Merchant he has created", "name": "Application", "nullable": true, "readonly": true, "related_type": "to_one", "type": "related", "unique": false}, "book_before_delay": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Default waiting period before being able to book an offer in seconds", "name": "Book before delay", "nullable": true, "readonly": true, "type": "integer", "unique": false}, "book_before_hour": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Default hour of the day after which an order can't be made", "name": "Book before hour", "nullable": true, "readonly": true, "type": "time", "unique": false}, "company": {"blank": true, "creatable": true, "default": "No default provided.", "editable": false, "help_text": "", "name": "Company", "nullable": false, "readonly": true, "related_type": "to_one", "type": "related", "unique": false}, "cover_image": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "A dictionary of data. Ex: {'price': 26.73, 'name': '<PERSON>'}", "name": "Cover image", "nullable": false, "readonly": true, "type": "dict", "unique": false}, "created_on": {"blank": true, "creatable": false, "default": true, "editable": false, "help_text": "", "name": "Created on", "nullable": false, "readonly": true, "type": "datetime", "unique": false}, "currencies": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "", "name": "Currencies", "nullable": true, "readonly": false, "related_type": "to_many", "type": "related", "unique": false}, "default_currency": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "", "max_length": null, "name": "Default currency", "nullable": false, "readonly": false, "type": "string", "unique": false}, "description": {"blank": true, "creatable": true, "default": "", "editable": true, "help_text": "A short description", "max_length": null, "name": "Description", "nullable": false, "readonly": false, "type": "string", "unique": false}, "easy_mode": {"blank": false, "creatable": false, "default": false, "editable": false, "help_text": "Boolean data. Ex: True", "name": "Easy mode", "nullable": false, "readonly": true, "type": "boolean", "unique": false}, "external_id": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Use it to store external matching id", "max_length": 255, "name": "External ID", "nullable": true, "readonly": true, "type": "string", "unique": false}, "facebook": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "Facebook account: http://www.facebook.com/mypage ", "max_length": 200, "name": "Facebook", "nullable": true, "readonly": false, "type": "string", "unique": false}, "gid": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Gid", "nullable": false, "readonly": true, "type": "string", "unique": false}, "group_keys": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "A list of data. Ex: ['abc', 26.73, 8]", "name": "Group keys", "nullable": true, "readonly": true, "type": "list", "unique": false}, "handle_invoice_upload": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Will upload his invoices", "nullable": true, "readonly": true, "type": "boolean", "unique": false}, "id": {"blank": true, "creatable": false, "default": "", "editable": false, "help_text": "", "name": "ID", "nullable": false, "readonly": true, "type": "integer", "unique": true}, "in_sellers_directory": {"blank": false, "creatable": false, "default": true, "editable": false, "help_text": "", "name": "In sellers directory", "nullable": false, "readonly": true, "type": "boolean", "unique": false}, "keywords": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "A list of keywords separated by commas", "name": "Keywords", "nullable": true, "readonly": false, "type": "list", "unique": false}, "languages": {"blank": false, "choices": [{"help_text": "Afrikaans", "value": "af"}, {"help_text": "Arabic", "value": "ar"}, {"help_text": "Algerian Arabic", "value": "ar-dz"}, {"help_text": "Asturian", "value": "ast"}, {"help_text": "Azerbaijani", "value": "az"}, {"help_text": "Bulgarian", "value": "bg"}, {"help_text": "Belarusian", "value": "be"}, {"help_text": "Bengali", "value": "bn"}, {"help_text": "Breton", "value": "br"}, {"help_text": "Bosnian", "value": "bs"}, {"help_text": "Catalan", "value": "ca"}, {"help_text": "Czech", "value": "cs"}, {"help_text": "Welsh", "value": "cy"}, {"help_text": "Danish", "value": "da"}, {"help_text": "German", "value": "de"}, {"help_text": "Lower Sorbian", "value": "dsb"}, {"help_text": "Greek", "value": "el"}, {"help_text": "English", "value": "en"}, {"help_text": "Australian English", "value": "en-au"}, {"help_text": "British English", "value": "en-gb"}, {"help_text": "Esperanto", "value": "eo"}, {"help_text": "Spanish", "value": "es"}, {"help_text": "Argentinian Spanish", "value": "es-ar"}, {"help_text": "Colombian Spanish", "value": "es-co"}, {"help_text": "Mexican Spanish", "value": "es-mx"}, {"help_text": "Nicaraguan Spanish", "value": "es-ni"}, {"help_text": "Venezuelan Spanish", "value": "es-ve"}, {"help_text": "Estonian", "value": "et"}, {"help_text": "Basque", "value": "eu"}, {"help_text": "Persian", "value": "fa"}, {"help_text": "Finnish", "value": "fi"}, {"help_text": "French", "value": "fr"}, {"help_text": "Frisian", "value": "fy"}, {"help_text": "Irish", "value": "ga"}, {"help_text": "Scottish Gaelic", "value": "gd"}, {"help_text": "Galician", "value": "gl"}, {"help_text": "Hebrew", "value": "he"}, {"help_text": "Hindi", "value": "hi"}, {"help_text": "Croatian", "value": "hr"}, {"help_text": "Upper Sorbian", "value": "hsb"}, {"help_text": "Hungarian", "value": "hu"}, {"help_text": "Armenian", "value": "hy"}, {"help_text": "Interlingua", "value": "ia"}, {"help_text": "Indonesian", "value": "id"}, {"help_text": "Igbo", "value": "ig"}, {"help_text": "Ido", "value": "io"}, {"help_text": "Icelandic", "value": "is"}, {"help_text": "Italian", "value": "it"}, {"help_text": "Japanese", "value": "ja"}, {"help_text": "Georgian", "value": "ka"}, {"help_text": "Ka<PERSON>le", "value": "kab"}, {"help_text": "Kazakh", "value": "kk"}, {"help_text": "Khmer", "value": "km"}, {"help_text": "Kannada", "value": "kn"}, {"help_text": "Korean", "value": "ko"}, {"help_text": "Kyrgyz", "value": "ky"}, {"help_text": "Luxembourgish", "value": "lb"}, {"help_text": "Lithuanian", "value": "lt"}, {"help_text": "Latvian", "value": "lv"}, {"help_text": "Macedonian", "value": "mk"}, {"help_text": "Malayalam", "value": "ml"}, {"help_text": "Mongolian", "value": "mn"}, {"help_text": "Marathi", "value": "mr"}, {"help_text": "Malay", "value": "ms"}, {"help_text": "Burmese", "value": "my"}, {"help_text": "Norwegian Bokmål", "value": "nb"}, {"help_text": "Nepali", "value": "ne"}, {"help_text": "Dutch", "value": "nl"}, {"help_text": "Norwegian Nynorsk", "value": "nn"}, {"help_text": "Ossetic", "value": "os"}, {"help_text": "Punjabi", "value": "pa"}, {"help_text": "Polish", "value": "pl"}, {"help_text": "Portuguese", "value": "pt"}, {"help_text": "Brazilian Portuguese", "value": "pt-br"}, {"help_text": "Romanian", "value": "ro"}, {"help_text": "Russian", "value": "ru"}, {"help_text": "Slovak", "value": "sk"}, {"help_text": "Slovenian", "value": "sl"}, {"help_text": "Albanian", "value": "sq"}, {"help_text": "Serbian", "value": "sr"}, {"help_text": "Serbian Latin", "value": "sr-latn"}, {"help_text": "Swedish", "value": "sv"}, {"help_text": "Swahili", "value": "sw"}, {"help_text": "Tamil", "value": "ta"}, {"help_text": "Telugu", "value": "te"}, {"help_text": "Tajik", "value": "tg"}, {"help_text": "Thai", "value": "th"}, {"help_text": "Turkmen", "value": "tk"}, {"help_text": "Turkish", "value": "tr"}, {"help_text": "Tatar", "value": "tt"}, {"help_text": "Udmurt", "value": "udm"}, {"help_text": "Ukrainian", "value": "uk"}, {"help_text": "Urdu", "value": "ur"}, {"help_text": "Uzbek", "value": "uz"}, {"help_text": "Vietnamese", "value": "vi"}, {"help_text": "Simplified Chinese", "value": "zh-hans"}, {"help_text": "Traditional Chinese", "value": "zh-hant"}], "choices_to_hide": [], "creatable": true, "default": "No default provided.", "editable": true, "help_text": "List of the languages in which you want your products to be potentially shown", "name": "Languages", "nullable": true, "readonly": false, "type": "list", "unique": false, "virtual_choices": []}, "last_modified": {"blank": true, "creatable": false, "default": true, "editable": false, "help_text": "", "name": "Last modified", "nullable": false, "readonly": true, "type": "datetime", "unique": false}, "logo_image": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "A dictionary of data. Ex: {'price': 26.73, 'name': '<PERSON>'}", "name": "Logo image", "nullable": false, "readonly": true, "type": "dict", "unique": false}, "long_description": {"blank": true, "creatable": true, "default": "", "editable": true, "help_text": "A long description", "max_length": null, "name": "Long description", "nullable": false, "readonly": false, "type": "string", "unique": false}, "merchant_groups": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Merchant_groups", "nullable": true, "readonly": true, "related_type": "to_many", "type": "related", "unique": false}, "name": {"blank": false, "creatable": true, "default": null, "editable": true, "help_text": "", "max_length": 128, "name": "Merchant name", "nullable": false, "readonly": false, "type": "string", "unique": false}, "overall_score": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Avg Overall Score", "name": "Overall score", "nullable": true, "readonly": true, "type": "decimal", "unique": false}, "pinterest": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "Pinterest account: http://www.pinterest.com/mypage", "max_length": 200, "name": "Pinterest", "nullable": true, "readonly": false, "type": "string", "unique": false}, "prefered_language": {"blank": false, "choices": [{"help_text": "Afrikaans", "value": "af"}, {"help_text": "Arabic", "value": "ar"}, {"help_text": "Algerian Arabic", "value": "ar-dz"}, {"help_text": "Asturian", "value": "ast"}, {"help_text": "Azerbaijani", "value": "az"}, {"help_text": "Bulgarian", "value": "bg"}, {"help_text": "Belarusian", "value": "be"}, {"help_text": "Bengali", "value": "bn"}, {"help_text": "Breton", "value": "br"}, {"help_text": "Bosnian", "value": "bs"}, {"help_text": "Catalan", "value": "ca"}, {"help_text": "Czech", "value": "cs"}, {"help_text": "Welsh", "value": "cy"}, {"help_text": "Danish", "value": "da"}, {"help_text": "German", "value": "de"}, {"help_text": "Lower Sorbian", "value": "dsb"}, {"help_text": "Greek", "value": "el"}, {"help_text": "English", "value": "en"}, {"help_text": "Australian English", "value": "en-au"}, {"help_text": "British English", "value": "en-gb"}, {"help_text": "Esperanto", "value": "eo"}, {"help_text": "Spanish", "value": "es"}, {"help_text": "Argentinian Spanish", "value": "es-ar"}, {"help_text": "Colombian Spanish", "value": "es-co"}, {"help_text": "Mexican Spanish", "value": "es-mx"}, {"help_text": "Nicaraguan Spanish", "value": "es-ni"}, {"help_text": "Venezuelan Spanish", "value": "es-ve"}, {"help_text": "Estonian", "value": "et"}, {"help_text": "Basque", "value": "eu"}, {"help_text": "Persian", "value": "fa"}, {"help_text": "Finnish", "value": "fi"}, {"help_text": "French", "value": "fr"}, {"help_text": "Frisian", "value": "fy"}, {"help_text": "Irish", "value": "ga"}, {"help_text": "Scottish Gaelic", "value": "gd"}, {"help_text": "Galician", "value": "gl"}, {"help_text": "Hebrew", "value": "he"}, {"help_text": "Hindi", "value": "hi"}, {"help_text": "Croatian", "value": "hr"}, {"help_text": "Upper Sorbian", "value": "hsb"}, {"help_text": "Hungarian", "value": "hu"}, {"help_text": "Armenian", "value": "hy"}, {"help_text": "Interlingua", "value": "ia"}, {"help_text": "Indonesian", "value": "id"}, {"help_text": "Igbo", "value": "ig"}, {"help_text": "Ido", "value": "io"}, {"help_text": "Icelandic", "value": "is"}, {"help_text": "Italian", "value": "it"}, {"help_text": "Japanese", "value": "ja"}, {"help_text": "Georgian", "value": "ka"}, {"help_text": "Ka<PERSON>le", "value": "kab"}, {"help_text": "Kazakh", "value": "kk"}, {"help_text": "Khmer", "value": "km"}, {"help_text": "Kannada", "value": "kn"}, {"help_text": "Korean", "value": "ko"}, {"help_text": "Kyrgyz", "value": "ky"}, {"help_text": "Luxembourgish", "value": "lb"}, {"help_text": "Lithuanian", "value": "lt"}, {"help_text": "Latvian", "value": "lv"}, {"help_text": "Macedonian", "value": "mk"}, {"help_text": "Malayalam", "value": "ml"}, {"help_text": "Mongolian", "value": "mn"}, {"help_text": "Marathi", "value": "mr"}, {"help_text": "Malay", "value": "ms"}, {"help_text": "Burmese", "value": "my"}, {"help_text": "Norwegian Bokmål", "value": "nb"}, {"help_text": "Nepali", "value": "ne"}, {"help_text": "Dutch", "value": "nl"}, {"help_text": "Norwegian Nynorsk", "value": "nn"}, {"help_text": "Ossetic", "value": "os"}, {"help_text": "Punjabi", "value": "pa"}, {"help_text": "Polish", "value": "pl"}, {"help_text": "Portuguese", "value": "pt"}, {"help_text": "Brazilian Portuguese", "value": "pt-br"}, {"help_text": "Romanian", "value": "ro"}, {"help_text": "Russian", "value": "ru"}, {"help_text": "Slovak", "value": "sk"}, {"help_text": "Slovenian", "value": "sl"}, {"help_text": "Albanian", "value": "sq"}, {"help_text": "Serbian", "value": "sr"}, {"help_text": "Serbian Latin", "value": "sr-latn"}, {"help_text": "Swedish", "value": "sv"}, {"help_text": "Swahili", "value": "sw"}, {"help_text": "Tamil", "value": "ta"}, {"help_text": "Telugu", "value": "te"}, {"help_text": "Tajik", "value": "tg"}, {"help_text": "Thai", "value": "th"}, {"help_text": "Turkmen", "value": "tk"}, {"help_text": "Turkish", "value": "tr"}, {"help_text": "Tatar", "value": "tt"}, {"help_text": "Udmurt", "value": "udm"}, {"help_text": "Ukrainian", "value": "uk"}, {"help_text": "Urdu", "value": "ur"}, {"help_text": "Uzbek", "value": "uz"}, {"help_text": "Vietnamese", "value": "vi"}, {"help_text": "Simplified Chinese", "value": "zh-hans"}, {"help_text": "Traditional Chinese", "value": "zh-hant"}], "choices_to_hide": [], "creatable": true, "default": "fr", "editable": true, "help_text": "", "max_length": 7, "name": "Prefered language", "nullable": false, "readonly": false, "type": "string", "unique": false, "virtual_choices": []}, "prefered_language_localized": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Prefered language localized", "nullable": false, "readonly": true, "type": "string", "unique": false}, "product_import_report_email_addresses": {"blank": false, "creatable": true, "default": null, "editable": true, "help_text": "E-mails with comma, semicolon or pipe separators", "max_length": 255, "name": "Product import report email addresses", "nullable": true, "readonly": false, "type": "string", "unique": false}, "product_submission": {"blank": false, "choices": [{"help_text": "Not allowed", "value": "notAllowed"}, {"help_text": "Allowed with moderation", "value": "allowedWithModeration"}, {"help_text": "Allowed without moderation", "value": "allowedWithoutModeration"}], "choices_to_hide": [], "creatable": false, "default": "notAllowed", "editable": false, "help_text": "", "max_length": 24, "name": "Product submission", "nullable": false, "readonly": true, "type": "string", "unique": false, "virtual_choices": []}, "profile_image": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "A dictionary of data. Ex: {'price': 26.73, 'name': '<PERSON>'}", "name": "Profile image", "nullable": false, "readonly": true, "type": "dict", "unique": false}, "referral": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Who brought the merchant", "max_length": 128, "name": "Referral", "nullable": true, "readonly": true, "type": "string", "unique": false}, "resource_uri": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Resource uri", "nullable": false, "readonly": true, "type": "string", "unique": false}, "settings_last_modified": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Last modification date of the merchant settings", "nullable": true, "readonly": true, "type": "datetime", "unique": false}, "slug": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "", "max_length": 255, "name": "Merchant slug", "nullable": false, "readonly": false, "type": "string", "unique": true}, "status": {"blank": false, "choices": [{"help_text": "Inactive", "value": "0"}, {"help_text": "Pending Review", "value": "5"}, {"help_text": "Active", "value": "10"}, {"help_text": "Paused", "value": "20"}, {"help_text": "Stopped", "value": "30"}, {"help_text": "Deleted", "value": "90"}], "choices_to_hide": [], "creatable": false, "default": "0", "editable": false, "help_text": "", "max_length": 16, "name": "Status", "nullable": false, "readonly": true, "transitions": {"activate": {"from_states": ["0", "5", "20", "30"], "to_state": "10"}, "deactivate": {"from_states": ["30"], "to_state": "0"}, "delete_action": {"from_states": ["0", "30"], "to_state": "90"}, "pause": {"from_states": ["10"], "to_state": "20"}, "pending": {"from_states": ["0", "20", "30"], "to_state": "5"}, "stop": {"from_states": ["10", "20"], "to_state": "30"}, "unpause": {"from_states": ["20"], "to_state": "10"}}, "type": "string", "unique": false, "virtual_choices": []}, "status_comment": {"blank": true, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Status comment", "nullable": true, "readonly": true, "type": "string", "unique": false}, "status_localized": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Status localized", "nullable": false, "readonly": true, "type": "string", "unique": false}, "store_type": {"blank": false, "choices": [{"help_text": "Professional", "value": 1}, {"help_text": "Individual", "value": 2}, {"help_text": "Professional (Affiliation)", "value": 10}], "choices_to_hide": [], "creatable": false, "default": 1, "editable": false, "help_text": "", "name": "Store type", "nullable": false, "readonly": true, "type": "integer", "unique": false, "virtual_choices": []}, "store_type_localized": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Store type localized", "nullable": false, "readonly": true, "type": "string", "unique": false}, "timezone": {"blank": false, "choices": [{"help_text": "Africa/Abidjan", "value": "Africa/Abidjan"}, {"help_text": "Africa/Accra", "value": "Africa/Accra"}, {"help_text": "Africa/Addis Ababa", "value": "Africa/Addis_Ababa"}, {"help_text": "Africa/Algiers", "value": "Africa/Algiers"}, {"help_text": "Africa/Asmara", "value": "Africa/Asmara"}, {"help_text": "Africa/Bamako", "value": "Africa/Bamako"}, {"help_text": "Africa/Bangui", "value": "Africa/Bangui"}, {"help_text": "Africa/Banjul", "value": "Africa/Banjul"}, {"help_text": "Africa/Bissau", "value": "Africa/Bissau"}, {"help_text": "Africa/Blantyre", "value": "Africa/Blantyre"}, {"help_text": "Africa/Brazzaville", "value": "Africa/Brazzaville"}, {"help_text": "Africa/Bujumbura", "value": "Africa/Bujumbura"}, {"help_text": "Africa/Cairo", "value": "Africa/Cairo"}, {"help_text": "Africa/Casablanca", "value": "Africa/Casablanca"}, {"help_text": "Africa/Ceuta", "value": "Africa/Ceuta"}, {"help_text": "Africa/Conakry", "value": "Africa/Conakry"}, {"help_text": "Africa/Dakar", "value": "Africa/Dakar"}, {"help_text": "Africa/Dar es Salaam", "value": "Africa/Dar_es_Salaam"}, {"help_text": "Africa/Djibouti", "value": "Africa/Djibouti"}, {"help_text": "Africa/Douala", "value": "Africa/Douala"}, {"help_text": "Africa/El Aaiun", "value": "Africa/El_Aaiun"}, {"help_text": "Africa/Freetown", "value": "Africa/Freetown"}, {"help_text": "Africa/Gaborone", "value": "Africa/Gaborone"}, {"help_text": "Africa/Harare", "value": "Africa/Harare"}, {"help_text": "Africa/Johannesburg", "value": "Africa/Johannesburg"}, {"help_text": "Africa/Juba", "value": "Africa/Juba"}, {"help_text": "Africa/Kampala", "value": "Africa/Kampala"}, {"help_text": "Africa/Khartoum", "value": "Africa/Khartoum"}, {"help_text": "Africa/Kigali", "value": "Africa/Kigali"}, {"help_text": "Africa/Kinshasa", "value": "Africa/Kinshasa"}, {"help_text": "Africa/Lagos", "value": "Africa/Lagos"}, {"help_text": "Africa/Libreville", "value": "Africa/Libreville"}, {"help_text": "Africa/Lome", "value": "Africa/Lome"}, {"help_text": "Africa/Luanda", "value": "Africa/Luanda"}, {"help_text": "Africa/Lubumbashi", "value": "Africa/Lubumbashi"}, {"help_text": "Africa/Lusaka", "value": "Africa/Lusaka"}, {"help_text": "Africa/Malabo", "value": "Africa/Malabo"}, {"help_text": "Africa/Maputo", "value": "Africa/Maputo"}, {"help_text": "Africa/Maseru", "value": "Africa/Maseru"}, {"help_text": "Africa/Mbabane", "value": "Africa/Mbabane"}, {"help_text": "Africa/Mogadishu", "value": "Africa/Mogadishu"}, {"help_text": "Africa/Monrovia", "value": "Africa/Monrovia"}, {"help_text": "Africa/Nairobi", "value": "Africa/Nairobi"}, {"help_text": "Africa/Ndjamena", "value": "Africa/Ndjamena"}, {"help_text": "Africa/Niamey", "value": "Africa/Niamey"}, {"help_text": "Africa/Nouakchott", "value": "Africa/Nouakchott"}, {"help_text": "Africa/Ouagadougou", "value": "Africa/Ouagadougou"}, {"help_text": "Africa/Porto-Novo", "value": "Africa/Porto-Novo"}, {"help_text": "Africa/Sao Tome", "value": "Africa/Sao_Tome"}, {"help_text": "Africa/Tripoli", "value": "Africa/Tripoli"}, {"help_text": "Africa/Tunis", "value": "Africa/Tunis"}, {"help_text": "Africa/Windhoek", "value": "Africa/Windhoek"}, {"help_text": "America/Adak", "value": "America/Adak"}, {"help_text": "America/Anchorage", "value": "America/Anchorage"}, {"help_text": "America/Anguilla", "value": "America/Anguilla"}, {"help_text": "America/Antigua", "value": "America/Antigua"}, {"help_text": "America/Araguaina", "value": "America/Araguaina"}, {"help_text": "America/Argentina/Buenos Aires", "value": "America/Argentina/Buenos_Aires"}, {"help_text": "America/Argentina/Catamarca", "value": "America/Argentina/Catamarca"}, {"help_text": "America/Argentina/Cordoba", "value": "America/Argentina/Cordoba"}, {"help_text": "America/Argentina/Jujuy", "value": "America/Argentina/Jujuy"}, {"help_text": "America/Argentina/La Rioja", "value": "America/Argentina/La_Rioja"}, {"help_text": "America/Argentina/Mendoza", "value": "America/Argentina/Mendoza"}, {"help_text": "America/Argentina/Rio Gallegos", "value": "America/Argentina/Rio_Gallegos"}, {"help_text": "America/Argentina/Salta", "value": "America/Argentina/Salta"}, {"help_text": "America/Argentina/San Juan", "value": "America/Argentina/San_Juan"}, {"help_text": "America/Argentina/San Luis", "value": "America/Argentina/San_Luis"}, {"help_text": "America/Argentina/Tucuman", "value": "America/Argentina/Tucuman"}, {"help_text": "America/Argentina/Ushuaia", "value": "America/Argentina/Ushuaia"}, {"help_text": "America/Aruba", "value": "America/Aruba"}, {"help_text": "America/Asuncion", "value": "America/Asuncion"}, {"help_text": "America/Atikokan", "value": "America/Atikokan"}, {"help_text": "America/Bahia", "value": "America/Bahia"}, {"help_text": "America/Bahia Banderas", "value": "America/Bahia_Banderas"}, {"help_text": "America/Barbados", "value": "America/Barbados"}, {"help_text": "America/Belem", "value": "America/Belem"}, {"help_text": "America/Belize", "value": "America/Belize"}, {"help_text": "America/Blanc-Sablon", "value": "America/Blanc-Sablon"}, {"help_text": "America/Boa Vista", "value": "America/Boa_Vista"}, {"help_text": "America/Bogota", "value": "America/Bogota"}, {"help_text": "America/Boise", "value": "America/Boise"}, {"help_text": "America/Cambridge Bay", "value": "America/Cambridge_Bay"}, {"help_text": "America/Campo Grande", "value": "America/Campo_Grande"}, {"help_text": "America/Cancun", "value": "America/Cancun"}, {"help_text": "America/Caracas", "value": "America/Caracas"}, {"help_text": "America/Cayenne", "value": "America/Cayenne"}, {"help_text": "America/Cayman", "value": "America/Cayman"}, {"help_text": "America/Chicago", "value": "America/Chicago"}, {"help_text": "America/Chihuahua", "value": "America/Chihuahua"}, {"help_text": "America/Ciudad Juarez", "value": "America/Ciudad_Juarez"}, {"help_text": "America/Costa Rica", "value": "America/Costa_Rica"}, {"help_text": "America/Creston", "value": "America/Creston"}, {"help_text": "America/Cuiaba", "value": "America/Cuiaba"}, {"help_text": "America/Curacao", "value": "America/Curacao"}, {"help_text": "America/Danmarkshavn", "value": "America/Danmarkshavn"}, {"help_text": "America/Dawson", "value": "America/Dawson"}, {"help_text": "America/Dawson Creek", "value": "America/Dawson_Creek"}, {"help_text": "America/Denver", "value": "America/Denver"}, {"help_text": "America/Detroit", "value": "America/Detroit"}, {"help_text": "America/Dominica", "value": "America/Dominica"}, {"help_text": "America/Edmonton", "value": "America/Edmonton"}, {"help_text": "America/Eirunepe", "value": "America/Eirunepe"}, {"help_text": "America/El Salvador", "value": "America/El_Salvador"}, {"help_text": "America/Fort Nelson", "value": "America/Fort_Nelson"}, {"help_text": "America/Fortaleza", "value": "America/Fortaleza"}, {"help_text": "America/Glace Bay", "value": "America/Glace_Bay"}, {"help_text": "America/Goose Bay", "value": "America/Goose_Bay"}, {"help_text": "America/Grand Turk", "value": "America/Grand_Turk"}, {"help_text": "America/Grenada", "value": "America/Grenada"}, {"help_text": "America/Guadeloupe", "value": "America/Guadeloupe"}, {"help_text": "America/Guatemala", "value": "America/Guatemala"}, {"help_text": "America/Guayaquil", "value": "America/Guayaquil"}, {"help_text": "America/Guyana", "value": "America/Guyana"}, {"help_text": "America/Halifax", "value": "America/Halifax"}, {"help_text": "America/Havana", "value": "America/Havana"}, {"help_text": "America/Hermosillo", "value": "America/Hermosillo"}, {"help_text": "America/Indiana/Indianapolis", "value": "America/Indiana/Indianapolis"}, {"help_text": "America/Indiana/Knox", "value": "America/Indiana/Knox"}, {"help_text": "America/Indiana/Marengo", "value": "America/Indiana/Marengo"}, {"help_text": "America/Indiana/Petersburg", "value": "America/Indiana/Petersburg"}, {"help_text": "America/Indiana/Tell City", "value": "America/Indiana/Tell_City"}, {"help_text": "America/Indiana/Vevay", "value": "America/Indiana/Vevay"}, {"help_text": "America/Indiana/Vincennes", "value": "America/Indiana/Vincennes"}, {"help_text": "America/Indiana/Winamac", "value": "America/Indiana/Winamac"}, {"help_text": "America/Inuvik", "value": "America/Inuvik"}, {"help_text": "America/Iqaluit", "value": "America/Iqaluit"}, {"help_text": "America/Jamaica", "value": "America/Jamaica"}, {"help_text": "America/Juneau", "value": "America/Juneau"}, {"help_text": "America/Kentucky/Louisville", "value": "America/Kentucky/Louisville"}, {"help_text": "America/Kentucky/Monticello", "value": "America/Kentucky/Monticello"}, {"help_text": "America/Kralendijk", "value": "America/Kralendijk"}, {"help_text": "America/La Paz", "value": "America/La_Paz"}, {"help_text": "America/Lima", "value": "America/Lima"}, {"help_text": "America/Los Angeles", "value": "America/Los_Angeles"}, {"help_text": "America/Lower Princes", "value": "America/Lower_Princes"}, {"help_text": "America/Maceio", "value": "America/Maceio"}, {"help_text": "America/Managua", "value": "America/Managua"}, {"help_text": "America/Manaus", "value": "America/Manaus"}, {"help_text": "America/Marigot", "value": "America/Marigot"}, {"help_text": "America/Martinique", "value": "America/Martinique"}, {"help_text": "America/Matamoros", "value": "America/Matamoros"}, {"help_text": "America/Mazatlan", "value": "America/Mazatlan"}, {"help_text": "America/Menominee", "value": "America/Menominee"}, {"help_text": "America/Merida", "value": "America/Merida"}, {"help_text": "America/Metlakatla", "value": "America/Metlakatla"}, {"help_text": "America/Mexico City", "value": "America/Mexico_City"}, {"help_text": "America/Miquelon", "value": "America/Miquelon"}, {"help_text": "America/Moncton", "value": "America/Moncton"}, {"help_text": "America/Monterrey", "value": "America/Monterrey"}, {"help_text": "America/Montevideo", "value": "America/Montevideo"}, {"help_text": "America/Montserrat", "value": "America/Montserrat"}, {"help_text": "America/Nassau", "value": "America/Nassau"}, {"help_text": "America/New York", "value": "America/New_York"}, {"help_text": "America/Nome", "value": "America/Nome"}, {"help_text": "America/Noronha", "value": "America/Noronha"}, {"help_text": "America/North Dakota/Beulah", "value": "America/North_Dakota/Beulah"}, {"help_text": "America/North Dakota/Center", "value": "America/North_Dakota/Center"}, {"help_text": "America/North Dakota/New Salem", "value": "America/North_Dakota/New_Salem"}, {"help_text": "America/Nuuk", "value": "America/Nuuk"}, {"help_text": "America/Ojinaga", "value": "America/Ojinaga"}, {"help_text": "America/Panama", "value": "America/Panama"}, {"help_text": "America/Paramaribo", "value": "America/Paramaribo"}, {"help_text": "America/Phoenix", "value": "America/Phoenix"}, {"help_text": "America/Port-au-Prince", "value": "America/Port-au-Prince"}, {"help_text": "America/Port of Spain", "value": "America/Port_of_Spain"}, {"help_text": "America/Porto Velho", "value": "America/Porto_Velho"}, {"help_text": "America/Puerto Rico", "value": "America/Puerto_Rico"}, {"help_text": "America/Punta Arenas", "value": "America/Punta_Arenas"}, {"help_text": "America/Rankin <PERSON>let", "value": "America/Rankin_Inlet"}, {"help_text": "America/Recife", "value": "America/Recife"}, {"help_text": "America/Regina", "value": "America/Regina"}, {"help_text": "America/Resolute", "value": "America/Resolute"}, {"help_text": "America/Rio Branco", "value": "America/Rio_Branco"}, {"help_text": "America/Santarem", "value": "America/Santarem"}, {"help_text": "America/Santiago", "value": "America/Santiago"}, {"help_text": "America/Santo Domingo", "value": "America/Santo_Domingo"}, {"help_text": "America/Sao Paulo", "value": "America/Sao_Paulo"}, {"help_text": "America/Scoresbysund", "value": "America/Scoresbysund"}, {"help_text": "America/Sitka", "value": "America/Sitka"}, {"help_text": "America/St Barthelemy", "value": "America/St_Barthelemy"}, {"help_text": "America/St Johns", "value": "America/St_Johns"}, {"help_text": "America/St Kitts", "value": "America/St_Kitts"}, {"help_text": "America/St Lucia", "value": "America/St_Lucia"}, {"help_text": "America/St Thomas", "value": "America/St_Thomas"}, {"help_text": "America/St Vincent", "value": "America/St_Vincent"}, {"help_text": "America/Swift Current", "value": "America/Swift_Current"}, {"help_text": "America/Tegucigalpa", "value": "America/Tegucigalpa"}, {"help_text": "America/Thule", "value": "America/Thule"}, {"help_text": "America/Tijuana", "value": "America/Tijuana"}, {"help_text": "America/Toronto", "value": "America/Toronto"}, {"help_text": "America/Tortola", "value": "America/Tortola"}, {"help_text": "America/Vancouver", "value": "America/Vancouver"}, {"help_text": "America/Whitehorse", "value": "America/Whitehorse"}, {"help_text": "America/Winnipeg", "value": "America/Winnipeg"}, {"help_text": "America/Yakutat", "value": "America/Yakutat"}, {"help_text": "Antarctica/Casey", "value": "Antarctica/Casey"}, {"help_text": "Antarctica/Davis", "value": "Antarctica/Davis"}, {"help_text": "Antarctica/DumontDUrville", "value": "Antarctica/DumontDUrville"}, {"help_text": "Antarctica/Macquarie", "value": "Antarctica/Macquarie"}, {"help_text": "Antarctica/Mawson", "value": "Antarctica/Mawson"}, {"help_text": "Antarctica/McMurdo", "value": "Antarctica/McMurdo"}, {"help_text": "Antarctica/Palmer", "value": "Antarctica/Palmer"}, {"help_text": "Antarctica/Rothera", "value": "Antarctica/Rothera"}, {"help_text": "Antarctica/Syowa", "value": "Antarctica/Syowa"}, {"help_text": "Antarctica/Troll", "value": "Antarctica/Troll"}, {"help_text": "Antarctica/Vostok", "value": "Antarctica/Vostok"}, {"help_text": "Arctic/Longyearbyen", "value": "Arctic/Longyearbyen"}, {"help_text": "Asia/Aden", "value": "Asia/Aden"}, {"help_text": "Asia/Almaty", "value": "Asia/Almaty"}, {"help_text": "Asia/Amman", "value": "Asia/Amman"}, {"help_text": "Asia/Anadyr", "value": "Asia/Anadyr"}, {"help_text": "Asia/Aqtau", "value": "Asia/Aqtau"}, {"help_text": "Asia/Aqtobe", "value": "Asia/Aqtobe"}, {"help_text": "Asia/Ashgabat", "value": "Asia/Ashgabat"}, {"help_text": "Asia/Atyrau", "value": "Asia/Atyrau"}, {"help_text": "Asia/Baghdad", "value": "Asia/Baghdad"}, {"help_text": "Asia/Bahrain", "value": "Asia/Bahrain"}, {"help_text": "Asia/Baku", "value": "Asia/Baku"}, {"help_text": "Asia/Bangkok", "value": "Asia/Bangkok"}, {"help_text": "Asia/Barnaul", "value": "Asia/Barnaul"}, {"help_text": "Asia/Beirut", "value": "Asia/Beirut"}, {"help_text": "Asia/Bishkek", "value": "Asia/Bishkek"}, {"help_text": "Asia/Brunei", "value": "Asia/Brunei"}, {"help_text": "Asia/Chita", "value": "Asia/Chita"}, {"help_text": "Asia/Choibalsan", "value": "Asia/Choibalsan"}, {"help_text": "Asia/Colombo", "value": "Asia/Colombo"}, {"help_text": "Asia/Damascus", "value": "Asia/Damascus"}, {"help_text": "Asia/Dhaka", "value": "Asia/Dhaka"}, {"help_text": "Asia/Dili", "value": "Asia/Dili"}, {"help_text": "Asia/Dubai", "value": "Asia/Dubai"}, {"help_text": "Asia/Dushanbe", "value": "Asia/Dushanbe"}, {"help_text": "Asia/Famagusta", "value": "Asia/Famagusta"}, {"help_text": "Asia/Gaza", "value": "Asia/Gaza"}, {"help_text": "Asia/Hebron", "value": "Asia/Hebron"}, {"help_text": "Asia/Ho Chi <PERSON>", "value": "Asia/Ho_Chi_Minh"}, {"help_text": "Asia/Hong Kong", "value": "Asia/Hong_Kong"}, {"help_text": "Asia/Hovd", "value": "Asia/Hovd"}, {"help_text": "Asia/Irkutsk", "value": "Asia/Irkutsk"}, {"help_text": "Asia/Jakarta", "value": "Asia/Jakarta"}, {"help_text": "Asia/Jayapura", "value": "Asia/Jayapura"}, {"help_text": "Asia/Jerusalem", "value": "Asia/Jerusalem"}, {"help_text": "Asia/Kabul", "value": "Asia/Kabul"}, {"help_text": "Asia/Kamchatka", "value": "Asia/Kamchatka"}, {"help_text": "Asia/Karachi", "value": "Asia/Karachi"}, {"help_text": "Asia/Kathmandu", "value": "Asia/Kathmandu"}, {"help_text": "Asia/Khandyga", "value": "Asia/Khandyga"}, {"help_text": "Asia/Kolkata", "value": "Asia/Kolkata"}, {"help_text": "Asia/Krasnoyarsk", "value": "Asia/Krasnoyarsk"}, {"help_text": "Asia/Kuala Lumpur", "value": "Asia/Kuala_Lumpur"}, {"help_text": "Asia/Kuching", "value": "Asia/Kuching"}, {"help_text": "Asia/Kuwait", "value": "Asia/Kuwait"}, {"help_text": "Asia/Macau", "value": "Asia/Macau"}, {"help_text": "Asia/Magadan", "value": "Asia/Magadan"}, {"help_text": "Asia/Makassar", "value": "Asia/Makassar"}, {"help_text": "Asia/Manila", "value": "Asia/Manila"}, {"help_text": "Asia/Muscat", "value": "Asia/Muscat"}, {"help_text": "Asia/Nicosia", "value": "Asia/Nicosia"}, {"help_text": "Asia/Novokuznetsk", "value": "Asia/Novokuznetsk"}, {"help_text": "Asia/Novosibirsk", "value": "Asia/Novosibirsk"}, {"help_text": "Asia/Omsk", "value": "Asia/Omsk"}, {"help_text": "Asia/Oral", "value": "Asia/Oral"}, {"help_text": "Asia/Phnom Penh", "value": "Asia/Phnom_Penh"}, {"help_text": "Asia/Pontianak", "value": "Asia/Pontianak"}, {"help_text": "Asia/Pyongyang", "value": "Asia/Pyongyang"}, {"help_text": "Asia/Qatar", "value": "Asia/Qatar"}, {"help_text": "Asia/Qostanay", "value": "Asia/Qostanay"}, {"help_text": "Asia/Qyzylorda", "value": "Asia/Qyzylorda"}, {"help_text": "Asia/Riyadh", "value": "Asia/Riyadh"}, {"help_text": "Asia/Sakhalin", "value": "Asia/Sakhalin"}, {"help_text": "Asia/Samarkand", "value": "Asia/Samarkand"}, {"help_text": "Asia/Seoul", "value": "Asia/Seoul"}, {"help_text": "Asia/Shanghai", "value": "Asia/Shanghai"}, {"help_text": "Asia/Singapore", "value": "Asia/Singapore"}, {"help_text": "Asia/Srednekolymsk", "value": "Asia/Srednekolymsk"}, {"help_text": "Asia/Taipei", "value": "Asia/Taipei"}, {"help_text": "Asia/Tashkent", "value": "Asia/Tashkent"}, {"help_text": "Asia/Tbilisi", "value": "Asia/Tbilisi"}, {"help_text": "Asia/Tehran", "value": "Asia/Tehran"}, {"help_text": "Asia/Thimphu", "value": "Asia/Thimphu"}, {"help_text": "Asia/Tokyo", "value": "Asia/Tokyo"}, {"help_text": "Asia/Tomsk", "value": "Asia/Tomsk"}, {"help_text": "Asia/Ulaanbaatar", "value": "Asia/Ulaanbaatar"}, {"help_text": "Asia/Urumqi", "value": "Asia/Urumqi"}, {"help_text": "Asia/Ust-Nera", "value": "Asia/Ust-Nera"}, {"help_text": "Asia/Vientiane", "value": "Asia/Vientiane"}, {"help_text": "Asia/Vladivostok", "value": "Asia/Vladivostok"}, {"help_text": "Asia/Yakutsk", "value": "Asia/Yakutsk"}, {"help_text": "Asia/Yangon", "value": "Asia/Yangon"}, {"help_text": "Asia/Yekaterinburg", "value": "Asia/Yekaterinburg"}, {"help_text": "Asia/Yerevan", "value": "Asia/Yerevan"}, {"help_text": "Atlantic/Azores", "value": "Atlantic/Azores"}, {"help_text": "Atlantic/Bermuda", "value": "Atlantic/Bermuda"}, {"help_text": "Atlantic/Canary", "value": "Atlantic/Canary"}, {"help_text": "Atlantic/Cape Verde", "value": "Atlantic/Cape_Verde"}, {"help_text": "Atlantic/Faroe", "value": "Atlantic/Faroe"}, {"help_text": "Atlantic/Madeira", "value": "Atlantic/Madeira"}, {"help_text": "Atlantic/Reykjavik", "value": "Atlantic/Reykjavik"}, {"help_text": "Atlantic/South Georgia", "value": "Atlantic/South_Georgia"}, {"help_text": "Atlantic/St Helena", "value": "Atlantic/St_Helena"}, {"help_text": "Atlantic/Stanley", "value": "Atlantic/Stanley"}, {"help_text": "Australia/Adelaide", "value": "Australia/Adelaide"}, {"help_text": "Australia/Brisbane", "value": "Australia/Brisbane"}, {"help_text": "Australia/Broken Hill", "value": "Australia/Broken_Hill"}, {"help_text": "Australia/Darwin", "value": "Australia/Darwin"}, {"help_text": "Australia/Eucla", "value": "Australia/Eucla"}, {"help_text": "Australia/Hobart", "value": "Australia/Hobart"}, {"help_text": "Australia/Lindeman", "value": "Australia/Lindeman"}, {"help_text": "Australia/Lord Howe", "value": "Australia/Lord_Howe"}, {"help_text": "Australia/Melbourne", "value": "Australia/Melbourne"}, {"help_text": "Australia/Perth", "value": "Australia/Perth"}, {"help_text": "Australia/Sydney", "value": "Australia/Sydney"}, {"help_text": "Canada/Atlantic", "value": "Canada/Atlantic"}, {"help_text": "Canada/Central", "value": "Canada/Central"}, {"help_text": "Canada/Eastern", "value": "Canada/Eastern"}, {"help_text": "Canada/Mountain", "value": "Canada/Mountain"}, {"help_text": "Canada/Newfoundland", "value": "Canada/Newfoundland"}, {"help_text": "Canada/Pacific", "value": "Canada/Pacific"}, {"help_text": "Europe/Amsterdam", "value": "Europe/Amsterdam"}, {"help_text": "Europe/Andorra", "value": "Europe/Andorra"}, {"help_text": "Europe/Astrakhan", "value": "Europe/Astrakhan"}, {"help_text": "Europe/Athens", "value": "Europe/Athens"}, {"help_text": "Europe/Belgrade", "value": "Europe/Belgrade"}, {"help_text": "Europe/Berlin", "value": "Europe/Berlin"}, {"help_text": "Europe/Bratislava", "value": "Europe/Bratislava"}, {"help_text": "Europe/Brussels", "value": "Europe/Brussels"}, {"help_text": "Europe/Bucharest", "value": "Europe/Bucharest"}, {"help_text": "Europe/Budapest", "value": "Europe/Budapest"}, {"help_text": "Europe/Busingen", "value": "Europe/Busingen"}, {"help_text": "Europe/Chisinau", "value": "Europe/Chisinau"}, {"help_text": "Europe/Copenhagen", "value": "Europe/Copenhagen"}, {"help_text": "Europe/Dublin", "value": "Europe/Dublin"}, {"help_text": "Europe/Gibraltar", "value": "Europe/Gibraltar"}, {"help_text": "Europe/Guernsey", "value": "Europe/Guernsey"}, {"help_text": "Europe/Helsinki", "value": "Europe/Helsinki"}, {"help_text": "Europe/Isle of Man", "value": "Europe/Isle_of_Man"}, {"help_text": "Europe/Istanbul", "value": "Europe/Istanbul"}, {"help_text": "Europe/Jersey", "value": "Europe/Jersey"}, {"help_text": "Europe/Kaliningrad", "value": "Europe/Kaliningrad"}, {"help_text": "Europe/Kirov", "value": "Europe/Kirov"}, {"help_text": "Europe/Kyiv", "value": "Europe/Kyiv"}, {"help_text": "Europe/Lisbon", "value": "Europe/Lisbon"}, {"help_text": "Europe/Ljubljana", "value": "Europe/Ljubljana"}, {"help_text": "Europe/London", "value": "Europe/London"}, {"help_text": "Europe/Luxembourg", "value": "Europe/Luxembourg"}, {"help_text": "Europe/Madrid", "value": "Europe/Madrid"}, {"help_text": "Europe/Malta", "value": "Europe/Malta"}, {"help_text": "Europe/Mariehamn", "value": "Europe/Mariehamn"}, {"help_text": "Europe/Minsk", "value": "Europe/Minsk"}, {"help_text": "Europe/Monaco", "value": "Europe/Monaco"}, {"help_text": "Europe/Moscow", "value": "Europe/Moscow"}, {"help_text": "Europe/Oslo", "value": "Europe/Oslo"}, {"help_text": "Europe/Paris", "value": "Europe/Paris"}, {"help_text": "Europe/Podgorica", "value": "Europe/Podgorica"}, {"help_text": "Europe/Prague", "value": "Europe/Prague"}, {"help_text": "Europe/Riga", "value": "Europe/Riga"}, {"help_text": "Europe/Rome", "value": "Europe/Rome"}, {"help_text": "Europe/Samara", "value": "Europe/Samara"}, {"help_text": "Europe/San Marino", "value": "Europe/San_Marino"}, {"help_text": "Europe/Sarajevo", "value": "Europe/Sarajevo"}, {"help_text": "Europe/Saratov", "value": "Europe/Saratov"}, {"help_text": "Europe/Simferopol", "value": "Europe/Simferopol"}, {"help_text": "Europe/Skopje", "value": "Europe/Skopje"}, {"help_text": "Europe/Sofia", "value": "Europe/Sofia"}, {"help_text": "Europe/Stockholm", "value": "Europe/Stockholm"}, {"help_text": "Europe/Tallinn", "value": "Europe/Tallinn"}, {"help_text": "Europe/Tirane", "value": "Europe/Tirane"}, {"help_text": "Europe/Ulyanovsk", "value": "Europe/Ulyanovsk"}, {"help_text": "Europe/Vaduz", "value": "Europe/Vaduz"}, {"help_text": "Europe/Vatican", "value": "Europe/Vatican"}, {"help_text": "Europe/Vienna", "value": "Europe/Vienna"}, {"help_text": "Europe/Vilnius", "value": "Europe/Vilnius"}, {"help_text": "Europe/Volgograd", "value": "Europe/Volgograd"}, {"help_text": "Europe/Warsaw", "value": "Europe/Warsaw"}, {"help_text": "Europe/Zagreb", "value": "Europe/Zagreb"}, {"help_text": "Europe/Zurich", "value": "Europe/Zurich"}, {"help_text": "GMT", "value": "GMT"}, {"help_text": "Indian/Antananarivo", "value": "Indian/Antananarivo"}, {"help_text": "Indian/Chagos", "value": "Indian/Chagos"}, {"help_text": "Indian/Christmas", "value": "Indian/Christmas"}, {"help_text": "Indian/Cocos", "value": "Indian/Cocos"}, {"help_text": "Indian/Comoro", "value": "Indian/Comoro"}, {"help_text": "Indian/Kerguelen", "value": "Indian/Kerguelen"}, {"help_text": "Indian/Mahe", "value": "Indian/Mahe"}, {"help_text": "Indian/Maldives", "value": "Indian/Maldives"}, {"help_text": "Indian/Mauritius", "value": "Indian/Mauritius"}, {"help_text": "Indian/Mayotte", "value": "Indian/Mayotte"}, {"help_text": "Indian/Reunion", "value": "Indian/Reunion"}, {"help_text": "Pacific/Apia", "value": "Pacific/Apia"}, {"help_text": "Pacific/Auckland", "value": "Pacific/Auckland"}, {"help_text": "Pacific/Bougainville", "value": "Pacific/Bougainville"}, {"help_text": "Pacific/Chatham", "value": "Pacific/Chatham"}, {"help_text": "Pacific/Chuuk", "value": "Pacific/Chuuk"}, {"help_text": "Pacific/Easter", "value": "Pacific/Easter"}, {"help_text": "Pacific/Efate", "value": "Pacific/Efate"}, {"help_text": "Pacific/Fakaofo", "value": "Pacific/Fakaofo"}, {"help_text": "Pacific/Fiji", "value": "Pacific/Fiji"}, {"help_text": "Pacific/Funafuti", "value": "Pacific/Funafuti"}, {"help_text": "Pacific/Galapagos", "value": "Pacific/Galapagos"}, {"help_text": "Pacific/Gambier", "value": "Pacific/Gambier"}, {"help_text": "Pacific/Guadalcanal", "value": "Pacific/Guadalcanal"}, {"help_text": "Pacific/Guam", "value": "Pacific/Guam"}, {"help_text": "Pacific/Honolulu", "value": "Pacific/Honolulu"}, {"help_text": "Pacific/Kanton", "value": "Pacific/Kanton"}, {"help_text": "Pacific/Kiritimati", "value": "Pacific/Kiritimati"}, {"help_text": "Pacific/Kosrae", "value": "Pacific/Kosrae"}, {"help_text": "Pacific/Kwajalein", "value": "Pacific/Kwajalein"}, {"help_text": "Pacific/Majuro", "value": "Pacific/Majuro"}, {"help_text": "Pacific/Marquesas", "value": "Pacific/Marquesas"}, {"help_text": "Pacific/Midway", "value": "Pacific/Midway"}, {"help_text": "Pacific/Nauru", "value": "Pacific/Nauru"}, {"help_text": "Pacific/Niue", "value": "Pacific/Niue"}, {"help_text": "Pacific/Norfolk", "value": "Pacific/Norfolk"}, {"help_text": "Pacific/Noumea", "value": "Pacific/Noumea"}, {"help_text": "Pacific/Pago Pago", "value": "Pacific/Pago_Pago"}, {"help_text": "Pacific/Palau", "value": "Pacific/Palau"}, {"help_text": "Pacific/Pitcairn", "value": "Pacific/Pitcairn"}, {"help_text": "Pacific/Pohnpei", "value": "Pacific/Pohnpei"}, {"help_text": "Pacific/Port Moresby", "value": "Pacific/Port_Moresby"}, {"help_text": "Pacific/Rarotonga", "value": "Pacific/Rarotonga"}, {"help_text": "Pacific/Saipan", "value": "Pacific/Saipan"}, {"help_text": "Pacific/Tahiti", "value": "Pacific/Tahiti"}, {"help_text": "Pacific/Tarawa", "value": "Pacific/Tarawa"}, {"help_text": "Pacific/Tongatapu", "value": "Pacific/Tongatapu"}, {"help_text": "Pacific/Wake", "value": "Pacific/Wake"}, {"help_text": "Pacific/Wallis", "value": "Pacific/Wallis"}, {"help_text": "US/Alaska", "value": "US/Alaska"}, {"help_text": "US/Arizona", "value": "US/Arizona"}, {"help_text": "US/Central", "value": "US/Central"}, {"help_text": "US/Eastern", "value": "US/Eastern"}, {"help_text": "US/Hawaii", "value": "US/Hawaii"}, {"help_text": "US/Mountain", "value": "US/Mountain"}, {"help_text": "US/Pacific", "value": "US/Pacific"}, {"help_text": "UTC", "value": "UTC"}], "choices_to_hide": [], "creatable": true, "default": "Europe/Paris", "editable": true, "help_text": "Time zone of the merchant", "max_length": 63, "name": "Timezone", "nullable": false, "readonly": false, "type": "string", "unique": false, "virtual_choices": []}, "title": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "A short title for the store that we may use as the title on your homepage.", "max_length": 256, "name": "Title", "nullable": true, "readonly": false, "type": "string", "unique": false}, "twitter": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "Twitter Username (Without @)", "max_length": 256, "name": "Twitter", "nullable": true, "readonly": false, "type": "string", "unique": false}, "type": {"blank": false, "creatable": false, "default": "m", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Type", "nullable": false, "readonly": true, "type": "string", "unique": false}, "url": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "", "max_length": 200, "name": "Merchant url", "nullable": true, "readonly": false, "type": "string", "unique": false}}, "filtering": {"api_key": "iexact", "application": 2, "created_on": 1, "default_currency": 1, "external_id": 1, "id": 1, "last_modified": 1, "merchant_groups": 2, "name": 1, "overall_score": 1, "product_submission": 1, "settings_last_modified": 1, "slug": "iexact", "status": 1, "store_type": 1}, "methods": [{"doc_html": "", "doc_text": "", "regex": "/v1/merchant/:id/getValidatedOrders/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/merchant/mine/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/merchant/:id/shipping_config/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/merchant/:id/commission_settings/"}, {"doc_html": "Will check for the store activation<br>@Allowed Methods: [GET]<br>@Return: Merchant data + errors as an attribute", "doc_text": "Will check for the store activation\n@Allowed Methods: [GET]\n@Return: Merchant data + errors as an attribute", "regex": "/v1/merchant/:id/check_activation/"}, {"doc_html": "Update store status to stopped<br>@Allowed Methods: [POST]<br>@Return: Merchant data", "doc_text": "Update store status to stopped\n@Allowed Methods: [POST]\n@Return: Merchant data", "regex": "/v1/merchant/:id/stop/"}, {"doc_html": "Update store status to paused<br>@Allowed Methods: [GET, POST]<br>@Return: Merchant data", "doc_text": "Update store status to paused\n@Allowed Methods: [GET, POST]\n@Return: Merchant data", "regex": "/v1/merchant/:id/pause/"}, {"doc_html": "Update store status to activated<br>@Allowed Methods: [GET, POST]<br>@Return: Merchant data", "doc_text": "Update store status to activated\n@Allowed Methods: [GET, POST]\n@Return: Merchant data", "regex": "/v1/merchant/:id/unpause/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/merchant/:id/inbox/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/merchant/:id/outbox/"}, {"doc_html": "Return managers (user objects) for the merchant. Only contains users<br>listed in the permissions pages. Not application staffs.<br>@Allowed Methods: [GET]<br>@Return: Users list", "doc_text": "Return managers (user objects) for the merchant. Only contains users\nlisted in the permissions pages. Not application staffs.\n@Allowed Methods: [GET]\n@Return: Users list", "regex": "/v1/merchant/:id/managers/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/merchant/:id/tax_settings/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/merchant/:id/getValidatedOrders/export/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/merchant/:id/transactions/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/merchant/:id/activate/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/merchant/:id/product_offer_channel/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/merchant/:id/product_catalog_channel/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/merchant/:id/groups/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/merchant/:id/maintenance/reset_stocks/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/merchant/:id/maintenance/batch-reset-stocks/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/merchant/:id/maintenance/batch-trash/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/merchant/:id/maintenance/batch-deactivate/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/merchant/:id/attributes/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/merchant/:id/company/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/merchant/export/"}], "ordering": ["api_key", "application", "created_on", "default_currency", "external_id", "id", "last_modified", "merchant_groups", "name", "overall_score", "product_submission", "settings_last_modified", "slug", "status", "store_type"]}