{"allowed_detail_http_methods": ["get", "post", "put", "delete", "patch"], "allowed_list_http_methods": ["get", "post"], "default_format": "application/json", "default_limit": 20, "doc": "Available countries. This is a read only resource.", "fields": {"code": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "max_length": 3, "name": "Code", "nullable": false, "readonly": true, "type": "string", "unique": true}, "factor": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Specifies the difference of the currency to default one.", "name": "Factor", "nullable": true, "readonly": true, "type": "decimal", "unique": false}, "factor_last_update": {"blank": false, "creatable": false, "default": true, "editable": false, "help_text": "", "name": "Factor last update", "nullable": true, "readonly": true, "type": "datetime", "unique": false}, "is_active": {"blank": false, "creatable": false, "default": true, "editable": false, "help_text": "The currency will be available.", "name": "Active", "nullable": false, "readonly": true, "type": "boolean", "unique": false}, "is_default": {"blank": false, "creatable": false, "default": false, "editable": false, "help_text": "Make this the default currency.", "name": "<PERSON><PERSON><PERSON>", "nullable": false, "readonly": true, "type": "boolean", "unique": false}, "name": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Name", "nullable": false, "readonly": true, "type": "string", "unique": false}, "resource_uri": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Resource uri", "nullable": false, "readonly": true, "type": "string", "unique": false}, "symbol": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "max_length": 10, "name": "Symbol", "nullable": true, "readonly": true, "type": "string", "unique": false}}, "filtering": {"code": 1}, "methods": [{"doc_html": "Cached version of GET /v1/currency/ request<br><br>accepted parameter: *bool* force<br>meaning: force fetching ignoring cache.", "doc_text": "Cached version of GET /v1/currency/ request\n\naccepted parameter: *bool* force\nmeaning: force fetching ignoring cache.", "regex": "/v1/currency/all/"}], "ordering": ["code"]}