{"allowed_detail_http_methods": ["get", "put", "patch", "delete"], "allowed_list_http_methods": ["get", "post"], "default_format": "application/json", "default_limit": 20, "doc": "A Cart is the entry point for an Order. A cart contains cart items. To get the current user cart, call the /mine method.", "editable_for_statuses": ["0", "10", "20", "50", "100"], "fields": {"abandoned": {"blank": false, "creatable": false, "default": false, "editable": false, "help_text": "", "name": "Abandoned", "nullable": true, "readonly": true, "type": "boolean", "unique": false}, "amount_subtotal": {"blank": false, "creatable": false, "default": "0.00", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Amount subtotal", "nullable": false, "readonly": true, "type": "string", "unique": false}, "anonymous_session": {"blank": false, "creatable": false, "default": null, "editable": false, "help_text": "", "max_length": 255, "name": "Anonymous session", "nullable": false, "readonly": true, "type": "string", "unique": false}, "application": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Application", "nullable": false, "readonly": true, "related_type": "to_one", "type": "related", "unique": false}, "billing_address": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "Billing Country", "name": "Billing address", "nullable": true, "readonly": false, "related_type": "to_one", "type": "related", "unique": false}, "country": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "Shipping Country", "name": "Country", "nullable": false, "readonly": false, "related_type": "to_one", "type": "related", "unique": false}, "created_on": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Created on", "nullable": true, "readonly": true, "type": "datetime", "unique": false}, "currency": {"blank": false, "creatable": true, "default": "No default provided.", "editable": false, "help_text": "", "max_length": null, "name": "<PERSON><PERSON><PERSON><PERSON>", "nullable": false, "readonly": true, "type": "string", "unique": false}, "date_to_archive": {"blank": false, "creatable": false, "default": null, "editable": false, "help_text": "", "name": "Date to archive", "nullable": true, "readonly": true, "type": "datetime", "unique": false}, "debug": {"blank": false, "creatable": false, "default": false, "editable": false, "help_text": "If True, will create a Order in debug mode", "name": "Debug", "nullable": false, "readonly": true, "type": "boolean", "unique": false}, "discount_amount_vat_excluded": {"blank": false, "creatable": false, "default": "0.00", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Discount amount vat excluded", "nullable": false, "readonly": true, "type": "string", "unique": false}, "discount_amount_vat_included": {"blank": false, "creatable": false, "default": "0.00", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Discount amount vat included", "nullable": false, "readonly": true, "type": "string", "unique": false}, "entered_discount_codes": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Entered discount codes", "name": "Entered discount codes", "nullable": true, "readonly": true, "type": "list", "unique": false}, "external_id": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Use it to store external matching id", "max_length": 255, "name": "External ID", "nullable": true, "readonly": true, "type": "string", "unique": false}, "id": {"blank": true, "creatable": false, "default": "", "editable": false, "help_text": "", "name": "ID", "nullable": false, "readonly": true, "type": "integer", "unique": true}, "items_count": {"blank": false, "creatable": false, "default": 0, "editable": false, "help_text": "Integer data. Ex: 2673", "name": "Items count", "nullable": false, "readonly": true, "type": "integer", "unique": false}, "last_updated": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Last updated", "nullable": true, "readonly": true, "type": "datetime", "unique": false}, "need_shipping": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Boolean data. Ex: True", "name": "Need shipping", "nullable": false, "readonly": true, "type": "boolean", "unique": false}, "resource_uri": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Resource uri", "nullable": false, "readonly": true, "type": "string", "unique": false}, "selected_payment_backend": {"blank": false, "choices": [{"help_text": "External", "value": "external"}, {"help_text": "<PERSON><PERSON>", "value": "hipay_tpp"}, {"help_text": "PSP Gateway", "value": "psp_gateway"}, {"help_text": "Be-2-<PERSON>", "value": "be2bill"}, {"help_text": "Ingenico", "value": "ingenico"}], "choices_to_hide": [], "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Payment backend to use on order creation", "max_length": 255, "name": "Selected payment backend", "nullable": true, "readonly": true, "type": "string", "unique": false, "virtual_choices": []}, "selected_payment_method": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "", "name": "Payment method", "nullable": true, "readonly": false, "related_type": "to_one", "type": "related", "unique": false}, "selected_payment_term": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "", "name": "Payment term", "nullable": true, "readonly": false, "related_type": "to_one", "type": "related", "unique": false}, "selected_payment_type": {"blank": false, "choices": [{"help_text": "Prepayment", "value": "prepayment"}, {"help_text": "Term payment", "value": "term_payment"}], "choices_to_hide": [], "creatable": false, "default": "prepayment", "editable": false, "help_text": "Payment type to use on order creation", "max_length": 15, "name": "Selected payment type", "nullable": false, "readonly": true, "type": "string", "unique": false, "virtual_choices": []}, "session_id": {"blank": false, "creatable": false, "default": null, "editable": false, "help_text": "", "max_length": null, "name": "Session", "nullable": false, "readonly": true, "type": "string", "unique": false}, "shipping_address": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "Shipping Country", "name": "Shipping address", "nullable": true, "readonly": false, "related_type": "to_one", "type": "related", "unique": false}, "shipping_amount": {"blank": false, "creatable": false, "default": "0.00", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Shipping amount", "nullable": false, "readonly": true, "type": "string", "unique": false}, "shipping_details": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Shipping details", "nullable": false, "readonly": true, "type": "string", "unique": false}, "status": {"blank": false, "choices": [{"help_text": "Empty", "value": "0"}, {"help_text": "Active", "value": "10"}, {"help_text": "<PERSON><PERSON>", "value": "20"}, {"help_text": "Done", "value": "30"}, {"help_text": "Shipping Not Possible", "value": "50"}, {"help_text": "Partially Sold Out", "value": "100"}, {"help_text": "Cancelled", "value": "1000"}], "choices_to_hide": [], "creatable": false, "default": "0", "editable": false, "help_text": "", "max_length": 16, "name": "Status", "nullable": false, "readonly": true, "transitions": {"active": {"from_states": ["0", "20", "50"], "to_state": "10"}, "back_in_stock": {"from_states": ["100"], "to_state": "10"}, "cancel": {"from_states": ["0", "10", "20", "50"], "to_state": "1000"}, "empty": {"from_states": ["10", "20", "50"], "to_state": "0"}, "partially_sold_out": {"from_states": ["10"], "to_state": "100"}, "shipping_possible": {"from_states": ["50"], "to_state": "10"}, "terminate": {"from_states": ["20"], "to_state": "30"}, "validate": {"from_states": ["10"], "to_state": "20"}, "wrong_shipping": {"from_states": ["10", "20"], "to_state": "50"}}, "type": "string", "unique": false, "virtual_choices": []}, "status_localized": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Status localized", "nullable": false, "readonly": true, "type": "string", "unique": false}, "tax_on_products": {"blank": false, "creatable": false, "default": "0.00", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Tax on products", "nullable": false, "readonly": true, "type": "string", "unique": false}, "total_amount": {"blank": false, "creatable": false, "default": "0.00", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Total amount", "nullable": false, "readonly": true, "type": "string", "unique": false}, "total_amount_before_discount": {"blank": false, "creatable": false, "default": "0.00", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Total amount before discount", "nullable": false, "readonly": true, "type": "string", "unique": false}, "total_amount_before_voucher": {"blank": false, "creatable": false, "default": "0.00", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Total amount before voucher", "nullable": false, "readonly": true, "type": "string", "unique": false}, "total_amount_vat_excluded": {"blank": false, "creatable": false, "default": "0.00", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Total amount vat excluded", "nullable": false, "readonly": true, "type": "string", "unique": false}, "total_amount_vat_included": {"blank": false, "creatable": false, "default": "0.00", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Total amount vat included", "nullable": false, "readonly": true, "type": "string", "unique": false}, "user": {"blank": false, "creatable": true, "default": "No default provided.", "editable": false, "help_text": "", "name": "User", "nullable": true, "readonly": true, "related_type": "to_one", "type": "related", "unique": false}, "user_first_name": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "User first name", "nullable": true, "readonly": true, "type": "string", "unique": false}, "user_last_name": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "User last name", "nullable": true, "readonly": true, "type": "string", "unique": false}, "voucher_amount": {"blank": false, "creatable": false, "default": "0.00", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Voucher amount", "nullable": false, "readonly": true, "type": "string", "unique": false}}, "filtering": {"abandoned": "exact", "application": "exact", "created_on": 1, "currency": 1, "debug": "exact", "id": 1, "last_updated": 1, "status": 1, "user": 2}, "methods": [{"doc_html": "Create an order from the Cart.<br><br>@params:<br>&nbsp;&nbsp;&nbsp;&nbsp;- credit_use: Decimal. Amount to be use from user credit balance<br>&nbsp;&nbsp;&nbsp;&nbsp;- payment_info_id: Integer. Id of the payment card if pay with<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;registered card<br>&nbsp;&nbsp;&nbsp;&nbsp;- pre_auth_id: Integer. Id of the PreAuthorization object from the<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;payment backend<br><br>@Allowed Methods: [POST]<br>@Return: An Order Object", "doc_text": "Create an order from the Cart.\n\n@params:\n    - credit_use: Decimal. Amount to be use from user credit balance\n    - payment_info_id: Integer. Id of the payment card if pay with\n      registered card\n    - pre_auth_id: Integer. Id of the PreAuthorization object from the\n      payment backend\n\n@Allowed Methods: [POST]\n@Return: An Order Object", "regex": "/v1/cart/:id/create_order/"}, {"doc_html": "Cancel the Cart<br><br>@Allowed Methods: [POST]<br>@Return: The updated Cart obj", "doc_text": "Cancel the Cart\n\n@Allowed Methods: [POST]\n@Return: The updated Cart obj", "regex": "/v1/cart/:id/cancel/"}, {"doc_html": "[LEGACY] Return the Cart for the current user.<br>@Allowed Methods: [GET]<br>@Return: A Cart Object", "doc_text": "[LEGACY] Return the Cart for the current user.\n@Allowed Methods: [GET]\n@Return: A Cart Object", "regex": "/v1/cart/mine/"}, {"doc_html": "Add a discount code to the cart.<br><br>@params:<br>&nbsp;&nbsp;&nbsp;&nbsp;- discount_code: String. The discount code to add to the cart.<br><br>@Allowed Methods: [POST]<br>@Return: The updated Cart object", "doc_text": "Add a discount code to the cart.\n\n@params:\n    - discount_code: String. The discount code to add to the cart.\n\n@Allowed Methods: [POST]\n@Return: The updated Cart object", "regex": "/v1/cart/:id/add_discount_code/"}, {"doc_html": "Remove a discount code from the cart.<br><br>@params:<br>&nbsp;&nbsp;&nbsp;&nbsp;- discount_code: String. The discount code to remove from the cart.<br><br>@Allowed Methods: [POST]<br>@Return: The updated Cart object", "doc_text": "Remove a discount code from the cart.\n\n@params:\n    - discount_code: String. The discount code to remove from the cart.\n\n@Allowed Methods: [POST]\n@Return: The updated Cart object", "regex": "/v1/cart/:id/remove_discount_code/"}, {"doc_html": "Using GET, list available shipping options for current cart and fetch<br>them if not fetched yet.<br>Using POST, select the user chosen providers<br>@Allowed Methods: [GET, POST]<br>@Return: A list of shipping options", "doc_text": "Using GET, list available shipping options for current cart and fetch\nthem if not fetched yet.\nUsing POST, select the user chosen providers\n@Allowed Methods: [GET, POST]\n@Return: A list of shipping options", "regex": "/v1/cart/:id/shipping_options/"}, {"doc_html": "Using GET, list selected shipping options for current cart<br>@Return: A list of shipping options", "doc_text": "Using GET, list selected shipping options for current cart\n@Return: A list of shipping options", "regex": "/v1/cart/:id/shipping_options/selected/"}, {"doc_html": "Force update the shipping providers for current cart<br><br>@Allowed Methods: [POST]<br>@Parameters:<br>- [merchant]: limit recompute to given merchant's items. Resource URI<br>expected.<br>@Return: A list of ShippingChoice splitted per merchant", "doc_text": "Force update the shipping providers for current cart\n\n@Allowed Methods: [POST]\n@Parameters:\n- [merchant]: limit recompute to given merchant's items. Resource URI\nexpected.\n@Return: A list of ShippingChoice splitted per merchant", "regex": "/v1/cart/:id/shipping_options/update/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/cart/:id/shipping_options/per_merchant/"}, {"doc_html": "Link the provided anonymous cart with the buyer (user) from the token.<br>Cart_id and anonymous_session are mandatory to retrieve the corresponding cart.<br><br>Will return 404 when:<br>- The cart doesn't exist<br>- The cart has already been assigned<br><br>@params:<br>&nbsp;&nbsp;&nbsp;&nbsp;- cart_id: Int. The cart id.<br>&nbsp;&nbsp;&nbsp;&nbsp;- anonymous_session: String. The anonymous session.<br><br>@Allowed Methods: [POST]<br>@Return: http response 202", "doc_text": "Link the provided anonymous cart with the buyer (user) from the token.\nCart_id and anonymous_session are mandatory to retrieve the corresponding cart.\n\nWill return 404 when:\n- The cart doesn't exist\n- The cart has already been assigned\n\n@params:\n    - cart_id: Int. The cart id.\n    - anonymous_session: String. The anonymous session.\n\n@Allowed Methods: [POST]\n@Return: http response 202", "regex": "/v1/cart/assign-buyer/"}, {"doc_html": "Create an order from the Cart.<br><br>@params:<br>&nbsp;&nbsp;&nbsp;&nbsp;- credit_use: Decimal. Amount to be use from user credit balance<br>&nbsp;&nbsp;&nbsp;&nbsp;- payment_info_id: Integer. Id of the payment card if pay with<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;registered card<br>&nbsp;&nbsp;&nbsp;&nbsp;- pre_auth_id: Integer. Id of the PreAuthorization object from the<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;payment backend<br><br>@Allowed Methods: [POST]<br>@Return: An Order Object", "doc_text": "Create an order from the Cart.\n\n@params:\n    - credit_use: Decimal. Amount to be use from user credit balance\n    - payment_info_id: Integer. Id of the payment card if pay with\n      registered card\n    - pre_auth_id: Integer. Id of the PreAuthorization object from the\n      payment backend\n\n@Allowed Methods: [POST]\n@Return: An Order Object", "regex": "/v1/cart/:id/createOrder/"}, {"doc_html": "Check cart validity. returns a json objects with:<br>{<br>&nbsp;&nbsp;&nbsp;&nbsp;\"validity\": true,<br>&nbsp;&nbsp;&nbsp;&nbsp;\"errors\":{}<br>}<br><br>or<br>{<br>&nbsp;&nbsp;&nbsp;&nbsp;\"validity\": true<br>&nbsp;&nbsp;&nbsp;&nbsp;\"errors\": {<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\":item-id\": {<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"code\": 50009,<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"message\": \"Not Enough stock\",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"extra\": {<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;...<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;},<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;...<br>&nbsp;&nbsp;&nbsp;&nbsp;}<br>}<br><br>@Allowed Methods: [GET]<br>@Return: The validity check report", "doc_text": "Check cart validity. returns a json objects with:\n{\n    \"validity\": true,\n    \"errors\":{}\n}\n\nor\n{\n    \"validity\": true\n    \"errors\": {\n        \":item-id\": {\n            \"code\": 50009,\n            \"message\": \"Not Enough stock\",\n            \"extra\": {\n                ...\n            }\n        },\n        ...\n    }\n}\n\n@Allowed Methods: [GET]\n@Return: The validity check report", "regex": "/v1/cart/:id/check_validity/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/cart/:id/discount_summary/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/cart/:id/recompute-discounts/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/cart/:id/create-shipping/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/cart/:id/shipping-suggestion/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/cart/export/"}], "ordering": ["id", "created_on"]}