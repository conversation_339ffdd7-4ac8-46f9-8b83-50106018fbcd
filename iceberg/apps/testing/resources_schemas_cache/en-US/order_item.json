{"allowed_detail_http_methods": ["get", "post", "put", "delete", "patch"], "allowed_list_http_methods": ["get", "post"], "default_format": "application/json", "default_limit": 20, "doc": "An order item is the equivalent of a cart item for a merchant order.", "editable_for_statuses": {"0": ["gift", "bundled", "extra_info"], "10": ["gift", "bundled", "extra_info"], "100": ["invoiceable_quantity", "extra_info"], "1000": ["extra_info"], "1010": ["extra_info"], "110": ["extra_info"], "120": ["invoiceable_quantity", "extra_info"], "160": ["invoiceable_quantity", "extra_info"], "2000": ["invoiceable_quantity", "extra_info"], "27": ["gift", "bundled", "extra_info"], "30": ["extra_info"], "3000": [], "40": ["extra_info"], "50": ["extra_info"], "60": ["gift", "bundled", "extra_info"], "70": ["gift", "bundled", "extra_info"], "80": ["invoiceable_quantity", "extra_info"], "85": ["invoiceable_quantity", "extra_info"], "90": ["invoiceable_quantity", "extra_info"]}, "fields": {"amount": {"blank": false, "creatable": false, "default": "0", "editable": false, "help_text": "Fixed precision numeric data. Ex: 26.73", "name": "Amount", "nullable": true, "readonly": true, "type": "decimal", "unique": false}, "amount_vat_included": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Amount vat included", "nullable": true, "readonly": true, "type": "string", "unique": false}, "bundled": {"blank": false, "creatable": true, "default": false, "editable": true, "help_text": "", "name": "Bundled", "nullable": false, "readonly": false, "type": "boolean", "unique": false}, "color": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Color", "nullable": true, "readonly": true, "type": "string", "unique": false}, "created_on": {"blank": true, "creatable": false, "default": true, "editable": false, "help_text": "", "name": "Created on", "nullable": false, "readonly": true, "type": "datetime", "unique": false}, "currency": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "A single related resource. Can be either a URI or set of nested resource data.", "name": "<PERSON><PERSON><PERSON><PERSON>", "nullable": false, "readonly": true, "related_type": "to_one", "type": "related", "unique": false}, "custom_name": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "max_length": 255, "name": "Custom name", "nullable": true, "readonly": true, "type": "string", "unique": false}, "customization_file": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "max_length": 1024, "name": "File uploaded for product customization", "nullable": true, "readonly": true, "type": "string", "unique": false}, "customization_value": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "max_length": 255, "name": "Value given for product customization", "nullable": true, "readonly": true, "type": "string", "unique": false}, "delivery_dates": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Delivery_dates", "nullable": true, "readonly": true, "related_type": "to_many", "type": "related", "unique": false}, "discount_amount_financed_by_application_with_tax": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Discount amount financed by application with tax", "nullable": true, "readonly": true, "type": "string", "unique": false}, "discount_amount_vat_included": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Discount amount vat included", "nullable": true, "readonly": true, "type": "string", "unique": false}, "discounts": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Discount data as it was at order time", "name": "Discount data", "nullable": true, "readonly": true, "type": "list", "unique": false}, "external_id": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Use it to store external matching id", "max_length": 255, "name": "External ID", "nullable": true, "readonly": true, "type": "string", "unique": false}, "extra_info": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "", "name": "Extra infos", "nullable": true, "readonly": false, "type": "dict", "unique": false}, "gift": {"blank": false, "creatable": true, "default": false, "editable": true, "help_text": "", "name": "Gift", "nullable": false, "readonly": false, "type": "boolean", "unique": false}, "gtin": {"blank": false, "creatable": false, "default": "", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Gtin", "nullable": true, "readonly": true, "type": "string", "unique": false}, "id": {"blank": true, "creatable": false, "default": "", "editable": false, "help_text": "", "name": "ID", "nullable": false, "readonly": true, "type": "integer", "unique": true}, "image_url": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "max_length": 1024, "name": "Image URL", "nullable": true, "readonly": true, "type": "string", "unique": false}, "invoiceable_quantity": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "Quantity of item available for invoice", "name": "Invoiceable quantity", "nullable": false, "readonly": false, "type": "integer", "unique": false}, "invoiced_quantity": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Quantity of item invoiced", "name": "Invoiced quantity", "nullable": false, "readonly": true, "type": "integer", "unique": false}, "item_image_url": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "max_length": 1024, "name": "Image URL", "nullable": true, "readonly": true, "type": "string", "unique": false}, "item_type": {"blank": false, "choices": [{"help_text": "Product", "value": "product"}, {"help_text": "Tax", "value": "tax"}, {"help_text": "Fees", "value": "fees"}, {"help_text": "Discount", "value": "discount"}, {"help_text": "Shipping", "value": "shipping"}], "choices_to_hide": [], "creatable": false, "default": "product", "editable": false, "help_text": "", "max_length": 20, "name": "Item type", "nullable": false, "readonly": true, "type": "string", "unique": false, "virtual_choices": []}, "last_modified": {"blank": true, "creatable": false, "default": true, "editable": false, "help_text": "", "name": "Last modified", "nullable": false, "readonly": true, "type": "datetime", "unique": false}, "max_invoiceable": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Maximum value possible for invoiceable_quantity", "name": "Max invoiceable", "nullable": false, "readonly": true, "type": "integer", "unique": false}, "merchant_order": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Merchant order", "nullable": false, "readonly": true, "related_type": "to_one", "type": "related", "unique": false}, "name": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Name", "nullable": true, "readonly": true, "type": "string", "unique": false}, "number_of_people": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Number of people", "nullable": true, "readonly": true, "type": "integer", "unique": false}, "offer_absolute_url": {"blank": false, "creatable": false, "default": "", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Offer absolute url", "nullable": true, "readonly": true, "type": "string", "unique": false}, "offer_external_id": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Offer external id", "nullable": true, "readonly": true, "type": "string", "unique": false}, "offer_id": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Offer id", "nullable": true, "readonly": true, "type": "string", "unique": false}, "ordered_offer": {"blank": false, "creatable": false, "default": null, "editable": false, "help_text": "Offer item as it was at order time", "name": "Ordered offer", "nullable": true, "readonly": true, "type": "dict", "unique": false}, "ordered_product": {"blank": false, "creatable": false, "default": null, "editable": false, "help_text": "Product as it was at order time", "name": "Ordered product", "nullable": true, "readonly": true, "type": "dict", "unique": false}, "ordered_variation": {"blank": false, "creatable": false, "default": null, "editable": false, "help_text": "Offer variation as it was at order time", "name": "Ordered variation", "nullable": true, "readonly": true, "type": "dict", "unique": false}, "previous_price": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Fixed precision numeric data. Ex: 26.73", "name": "Previous price", "nullable": true, "readonly": true, "type": "decimal", "unique": false}, "previous_price_without_vat": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Fixed precision numeric data. Ex: 26.73", "name": "Previous price without vat", "nullable": true, "readonly": true, "type": "decimal", "unique": false}, "price": {"blank": false, "creatable": false, "default": "0", "editable": false, "help_text": "", "name": "Price without vat", "nullable": false, "readonly": true, "type": "decimal", "unique": false}, "price_vat_included": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Price vat included", "nullable": true, "readonly": true, "type": "string", "unique": false}, "product": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "A single related resource. Can be either a URI or set of nested resource data.", "name": "Product", "nullable": true, "readonly": true, "related_type": "to_one", "type": "related", "unique": false}, "product_id": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Integer data. Ex: 2673", "name": "Product id", "nullable": false, "readonly": true, "type": "integer", "unique": false}, "product_offer": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Product offer", "nullable": true, "readonly": true, "related_type": "to_one", "type": "related", "unique": false}, "product_variation": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Product offer variation", "nullable": true, "readonly": true, "related_type": "to_one", "type": "related", "unique": false}, "quantity": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Quantity of item", "name": "Quantity", "nullable": false, "readonly": true, "type": "integer", "unique": false}, "received": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Received", "nullable": true, "readonly": true, "type": "datetime", "unique": false}, "resource_uri": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Resource uri", "nullable": false, "readonly": true, "type": "string", "unique": false}, "shipped_on": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Shipped on", "nullable": true, "readonly": true, "type": "datetime", "unique": false}, "shipping": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Shipping", "nullable": false, "readonly": true, "type": "decimal", "unique": false}, "size": {"blank": false, "creatable": false, "default": "", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Size", "nullable": true, "readonly": true, "type": "string", "unique": false}, "sku": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "max_length": 255, "name": "SKU of the ordered item", "nullable": true, "readonly": true, "type": "string", "unique": false}, "status": {"blank": false, "choices": [{"help_text": "Initial", "value": "0"}, {"help_text": "Expired", "value": "10"}, {"help_text": "Payment Pending", "value": "27"}, {"help_text": "Not authorized", "value": "30"}, {"help_text": "Authorization cancelled", "value": "40"}, {"help_text": "Authorized (?)", "value": "50"}, {"help_text": "Payment Authorized", "value": "60"}, {"help_text": "Paid", "value": "70"}, {"help_text": "Confirmed", "value": "80"}, {"help_text": "Processed", "value": "85"}, {"help_text": "<PERSON><PERSON>", "value": "90"}, {"help_text": "Received", "value": "100"}, {"help_text": "Finalized", "value": "110"}, {"help_text": "Return in progress", "value": "120"}, {"help_text": "Returned", "value": "160"}, {"help_text": "Cancelled", "value": "2000"}, {"help_text": "Deleted", "value": "3000"}, {"help_text": "Payment failure", "value": "1000"}, {"help_text": "Payment Partial failure", "value": "1010"}], "choices_to_hide": [], "creatable": false, "default": "0", "editable": false, "help_text": "", "max_length": 16, "name": "Status", "nullable": false, "readonly": true, "transitions": {"authorize": {"from_states": ["0", "27"], "to_state": "60"}, "cancel": {"from_states": ["0", "40", "60", "30", "1010", "1000", "27", "70", "80", "85"], "to_state": "2000"}, "confirm": {"from_states": ["60", "70"], "to_state": "80"}, "delete_action": {"from_states": ["0", "10", "1000", "2000", "27"], "to_state": "3000"}, "expire": {"from_states": ["0", "60", "27"], "to_state": "10"}, "finalize": {"from_states": ["80", "90", "100", "160", "85"], "to_state": "110"}, "pay": {"from_states": ["60", "27"], "to_state": "70"}, "payment_failure": {"from_states": ["0", "27", "60"], "to_state": "1000"}, "pending_payment": {"from_states": ["0"], "to_state": "27"}, "process": {"from_states": ["80"], "to_state": "85"}, "receive": {"from_states": ["90"], "to_state": "100"}, "return_action": {"from_states": ["120"], "to_state": "160"}, "return_in_progress": {"from_states": ["90", "100", "110"], "to_state": "120"}, "send": {"from_states": ["80"], "to_state": "90"}}, "type": "string", "unique": false, "virtual_choices": []}, "status_localized": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Status localized", "nullable": true, "readonly": true, "type": "string", "unique": false}, "stock": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Integer data. Ex: 2673", "name": "Stock", "nullable": true, "readonly": true, "type": "integer", "unique": false}, "tax_rate": {"blank": false, "creatable": false, "default": null, "editable": false, "help_text": "Tax rate in %", "name": "Tax rate", "nullable": true, "readonly": true, "type": "decimal", "unique": false}, "tax_rate_key": {"blank": false, "creatable": false, "default": null, "editable": false, "help_text": "", "max_length": 30, "name": "Tax rate key", "nullable": true, "readonly": true, "type": "string", "unique": false}, "variation": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Variation", "nullable": true, "readonly": true, "type": "string", "unique": false}, "variation_external_id": {"blank": false, "creatable": false, "default": "", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Variation external id", "nullable": false, "readonly": true, "type": "string", "unique": false}, "variation_kind": {"blank": false, "creatable": false, "default": "", "editable": false, "help_text": "A list of data. Ex: ['abc', 26.73, 8]", "name": "Variation kind", "nullable": false, "readonly": true, "type": "list", "unique": false}, "variation_name": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Variation name", "nullable": true, "readonly": true, "type": "string", "unique": false}, "variation_sku": {"blank": false, "creatable": false, "default": "", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Variation sku", "nullable": true, "readonly": true, "type": "string", "unique": false}, "variation_stock": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Integer data. Ex: 2673", "name": "Variation stock", "nullable": true, "readonly": true, "type": "integer", "unique": false}, "vat": {"blank": false, "creatable": false, "default": "0", "editable": false, "help_text": "", "name": "Vat", "nullable": false, "readonly": true, "type": "decimal", "unique": false}, "workflow": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "A single related resource. Can be either a URI or set of nested resource data.", "name": "Workflow", "nullable": false, "readonly": true, "related_type": "to_one", "type": "related", "unique": false}}, "filtering": {"created_on": 1, "currency": 1, "id": 1, "invoiceable_quantity": 1, "invoiced_quantity": 1, "last_modified": 1, "merchant_order": 2, "ordered_offer__sku": "exact", "sku": 1, "status": "in"}, "methods": [{"doc_html": "Indicate that the OrderItem has been sent<br>@Allowed Methods: [POST]<br>@Return: Updated OrderItem", "doc_text": "Indicate that the OrderItem has been sent\n@Allowed Methods: [POST]\n@Return: Updated OrderItem", "regex": "/v1/order_item/:id/send/"}, {"doc_html": "Indicate that the OrderItem has been received<br>@Allowed Methods: [POST]<br>@Return: Updated OrderItem", "doc_text": "Indicate that the OrderItem has been received\n@Allowed Methods: [POST]\n@Return: Updated OrderItem", "regex": "/v1/order_item/:id/receive/"}, {"doc_html": "Cancel the OrderItem<br>@Allowed Methods: [POST]<br>@Return: Updated OrderItem", "doc_text": "Cancel the OrderItem\n@Allowed Methods: [POST]\n@Return: Updated OrderItem", "regex": "/v1/order_item/:id/cancel/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/order_item/:id/return/"}, {"doc_html": "", "doc_text": "", "regex": "/v1/order_item/:id/process/"}], "ordering": ["created_on", "currency", "id", "invoiceable_quantity", "invoiced_quantity", "last_modified", "merchant_order", "sku", "status"]}