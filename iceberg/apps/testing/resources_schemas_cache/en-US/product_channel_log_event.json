{"allowed_detail_http_methods": ["get", "post", "put", "delete", "patch"], "allowed_list_http_methods": ["get", "post"], "default_format": "application/json", "default_limit": 20, "fields": {"action": {"blank": false, "choices": [{"help_text": "Pass", "value": "pass"}, {"help_text": "Drop", "value": "drop"}, {"help_text": "Ignore", "value": "ignore"}], "choices_to_hide": [], "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "max_length": 20, "name": "Action", "nullable": true, "readonly": true, "type": "string", "unique": false, "virtual_choices": []}, "channel": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Channel", "nullable": true, "readonly": true, "related_type": "to_one", "type": "related", "unique": false}, "event": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "max_length": 100, "name": "Event", "nullable": true, "readonly": true, "type": "string", "unique": false}, "event_data": {"blank": true, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Event data", "nullable": true, "readonly": true, "type": "dict", "unique": false}, "generic_msg": {"blank": false, "choices": [{"help_text": "Product missing in storage", "value": "product_missing_in_storage"}, {"help_text": "Channel synced", "value": "channel_synced"}, {"help_text": "Channel syncing", "value": "channel_syncing"}, {"help_text": "Channel sync failed", "value": "channel_sync_failed"}, {"help_text": "Max TTL reached", "value": "max_ttl_reached"}, {"help_text": "An error occured while handling an incoming event", "value": "error_while_handling_incoming_event"}, {"help_text": "Incoming event successfully handled", "value": "incoming_event_handled"}, {"help_text": "An error occured while handling the channel outputs", "value": "error_while_handling_outputs"}, {"help_text": "An error occured while generating a channel output", "value": "error_while_generating_output"}, {"help_text": "Output successfully generated", "value": "output_generated"}, {"help_text": "No handler for received event", "value": "no_handler_for_event"}, {"help_text": "Unknown event", "value": "unknown_event"}], "choices_to_hide": [], "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "max_length": 255, "name": "Generic msg", "nullable": true, "readonly": true, "type": "string", "unique": false, "virtual_choices": []}, "id": {"blank": true, "creatable": false, "default": "", "editable": false, "help_text": "", "name": "ID", "nullable": false, "readonly": true, "type": "integer", "unique": true}, "log_level": {"blank": false, "choices": [{"help_text": "Debug", "value": 10}, {"help_text": "Info", "value": 20}, {"help_text": "<PERSON><PERSON>", "value": 30}, {"help_text": "Error", "value": 40}, {"help_text": "Fatal", "value": 50}, {"help_text": "Critical", "value": 50}], "choices_to_hide": [], "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Log level", "nullable": false, "readonly": true, "type": "integer", "unique": false, "virtual_choices": []}, "log_level_localized": {"blank": true, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Log level localized", "nullable": false, "readonly": true, "type": "string", "unique": false}, "log_type": {"blank": false, "choices": [{"help_text": "Log about event propagation", "value": 10}, {"help_text": "Log about event generation", "value": 20}, {"help_text": "Log about channel binding", "value": 30}], "choices_to_hide": [], "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Log type", "nullable": false, "readonly": true, "type": "integer", "unique": false, "virtual_choices": []}, "log_type_localized": {"blank": true, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Log type localized", "nullable": false, "readonly": true, "type": "string", "unique": false}, "message": {"blank": true, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Message", "nullable": true, "readonly": true, "type": "string", "unique": false}, "msg": {"blank": false, "creatable": false, "default": "", "editable": false, "help_text": "", "max_length": null, "name": "Msg", "nullable": true, "readonly": true, "type": "string", "unique": false}, "outgoing": {"blank": false, "creatable": false, "default": false, "editable": false, "help_text": "", "name": "Is it an outgoing/outbound event ?", "nullable": false, "readonly": true, "type": "boolean", "unique": false}, "output_backend_id": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Output backend id", "nullable": true, "readonly": true, "type": "integer", "unique": false}, "output_backend_name": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "max_length": 100, "name": "Output backend name", "nullable": true, "readonly": true, "type": "string", "unique": false}, "resource_uri": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Resource uri", "nullable": false, "readonly": true, "type": "string", "unique": false}, "storage_backend_id": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Storage backend id", "nullable": true, "readonly": true, "type": "integer", "unique": false}, "storage_backend_name": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "max_length": 100, "name": "Storage backend name", "nullable": true, "readonly": true, "type": "string", "unique": false}, "timestamp": {"blank": true, "creatable": false, "default": true, "editable": false, "help_text": "", "name": "Timestamp", "nullable": false, "readonly": true, "type": "datetime", "unique": false}}, "filtering": {"channel": 2, "event": 1, "generic_msg": 1, "id": 1, "log_level": 1, "log_type": 1, "outgoing": "exact", "timestamp": 1}, "methods": [], "ordering": ["timestamp"]}