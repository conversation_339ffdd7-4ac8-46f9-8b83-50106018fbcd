{"allowed_detail_http_methods": ["get", "post", "put", "delete", "patch"], "allowed_list_http_methods": ["get", "post"], "default_format": "application/json", "default_limit": 20, "default_values": {"abandoned_and_cancelled_carts_lifetime": "180", "acceptance_phase_duration": 15, "aggregate_merchant_invoice_compensations": false, "algolia_admin_api_key_id": null, "algolia_application_id": null, "allow_merchant_products": false, "allow_parcel_receive_action_by_merchant": false, "allow_proforma": false, "allow_trash_status": true, "allow_zero_amount_offer": false, "api_always_return_data": false, "are_commission_rules_vat_included": true, "asynch_merchant_order_confirmation": true, "asynch_order_creation": true, "asynch_payment_authorize": true, "auto_cancelling_time": 167, "auto_collect_on_confirm": true, "auto_generate_invoice": true, "auto_offer_activation": true, "auto_process_commission_transactions": true, "auto_process_license_transactions": true, "auto_process_marketing_operation_transactions": true, "auto_process_store_adjustement_transactions": true, "automatic_discounts_recompute": true, "automatic_shipping_recompute": true, "cart_abandon_threshold": "60", "cart_notification_limit_time": 14, "daily_orders_recap_merchants_list": "", "default_tax_rate_on_commissions": "20.00", "disable_cached_stock": false, "display_store_adjustments_invoice": false, "done_carts_lifetime": "180", "download_images": false, "elasticsearch_batch_update_max_number_of_items": 500, "elasticsearch_credentials": {}, "elasticsearch_index_settings": {"index": {"analysis": {"analyzer": {"izberg_search": {"filter": ["lowercase", "asciifolding"], "tokenizer": "standard", "type": "custom"}}}}, "number_of_replicas": 0, "number_of_shards": 1}, "elasticsearch_ssl_version": {"ssl_version": "TLSv1_2"}, "empty_cart_lifetime": "30", "enable_balances": true, "enable_commission_transactions": true, "enable_discount_engine": true, "enable_external_discount": false, "enable_extra_fee_setting": false, "enable_kyc_v2": true, "enable_merchant_review_moderation": false, "enable_message_attachments": true, "enable_package_tracking_v1": true, "enable_package_tracking_v2": true, "enable_refunds": true, "enable_returns": true, "enable_vat_b2c_eu_engine": true, "enabled_cart_notifications": [], "enabled_product_fields": null, "enabled_product_offer_fields": null, "enabled_product_variation_fields": null, "enforce_zone_rate_link_integrity": true, "extra_fee_amount": "5.00", "extra_fee_tax_rate": "0.20", "extra_fee_threshold": "200.00", "hide_invoice_issuer_mention": false, "invoice_mandatory_legal_notices": false, "invoices_visible_to_merchants_at_generation": true, "is_closed_catalog": false, "mandatory_localized_offer_fields": [], "mandatory_localized_product_fields": [], "mandatory_offer_fields": [], "mandatory_product_fields": [], "mandatory_shipping_on_items": true, "mapper_enabled_offer_fields": ["name", "sku", "parent_sku", "description", "stock", "marketplace_product_id", "external_id", "variations", "availability", "variation_type", "price", "currency", "gtin", "product_external_id", "language", "cancellation", "condition", "color", "end_selling_date", "max_order_quantity", "min_order_quantity", "private_notes", "merchant_url", "number_of_items", "number_of_units", "offer_latitude", "offer_longitude", "previous_price", "eco_tax", "tax_rate_included", "tax_rate_to_apply", "product_tax_group", "restock_date", "shipping_info", "start_selling_date", "merchant_id", "merchant_external_id", "warranty", "weight_numeral", "wrappable", "wrapping", "variation_name", "size", "capacity", "material", "shipped_from_eu_vat_zone", "shipped_from_country", "shipped_from_region", "tax_rates", "keep_images"], "mapper_enabled_product_and_offer_fields": [], "mapper_enabled_product_fields": [], "mapper_high_priority_offer_fields": ["sku", "parent_sku", "gtin", "external_id", "product_external_id", "stock", "price", "previous_price", "eco_tax"], "mapper_pre_download_feed": true, "mapper_switch_off": false, "mapper_uses_izberg_user_agent": true, "marketplace_contact_email": "<EMAIL>", "merchant_auto_activation": true, "merchant_handle_invoice_upload": true, "merchant_invoice_notes_per_vat_rate": {}, "moderated_seller_cashouts": false, "number_thresholds_prices": 0, "order_items_aggregation": true, "order_regulation": true, "order_workflow_v2": true, "other_offers": true, "partial_collect_enabled": false, "payment_early_pending_auth_expiration_time": 24, "payment_rolling_reserve_amount": "500.00", "product_attribute_limit": 200, "product_offer_moderation_enabled": false, "selector_product_offers_ids_limit": 1000, "seller_backoffice_base_url": null, "shipping_engine": "auto", "show_customer_email_to_merchant": false, "tax_handler": "izberg"}, "doc": "Application settings", "editable_for_statuses": [], "fields": {"application": {"blank": false, "creatable": true, "default": "No default provided.", "editable": false, "help_text": "", "name": "Application", "nullable": true, "readonly": true, "related_type": "to_one", "type": "related", "unique": false}, "application_configuration": {"blank": false, "creatable": true, "default": "No default provided.", "editable": false, "help_text": "", "name": "Application configuration", "nullable": false, "readonly": true, "related_type": "to_one", "type": "related", "unique": false}, "group": {"blank": false, "choices": [{"help_text": "Product", "value": "product"}, {"help_text": "<PERSON><PERSON>", "value": "cart"}, {"help_text": "Order", "value": "order"}, {"help_text": "Tax", "value": "tax"}, {"help_text": "Notification", "value": "notification"}, {"help_text": "Payment", "value": "payment"}, {"help_text": "Promotion", "value": "promotion"}, {"help_text": "Application", "value": "application"}, {"help_text": "Merchant", "value": "merchant"}, {"help_text": "Url", "value": "url"}, {"help_text": "Commission", "value": "commission"}, {"help_text": "Invoice", "value": "invoice"}, {"help_text": "Shipping", "value": "shipping"}, {"help_text": "Return", "value": "return"}, {"help_text": "Transaction", "value": "transaction"}, {"help_text": "<PERSON><PERSON>", "value": "mapper"}, {"help_text": "Message", "value": "message"}], "choices_to_hide": [], "creatable": false, "default": null, "editable": false, "help_text": "", "max_length": 20, "name": "Group", "nullable": true, "readonly": true, "type": "string", "unique": false, "virtual_choices": []}, "id": {"blank": true, "creatable": false, "default": "", "editable": false, "help_text": "", "name": "ID", "nullable": false, "readonly": true, "type": "integer", "unique": true}, "key": {"blank": false, "choices": [{"help_text": "Address to use when sending emails on behalf of the application.", "value": "marketplace_contact_email"}, {"help_text": "Should the API always return data for this application.", "value": "api_always_return_data"}, {"help_text": "Carts maximum lifetime before being considered as abandoned in days. (default 180)", "value": "abandoned_and_cancelled_carts_lifetime"}, {"help_text": "Carts maximum lifetime before being considered as abandoned in days.", "value": "cart_abandon_threshold"}, {"help_text": "Time before cart expiration for cart notification (In days).", "value": "cart_notification_limit_time"}, {"help_text": "Maximum done carts lifetime before being archived in days. (default 180)", "value": "done_carts_lifetime"}, {"help_text": "Empty carts maximum lifetime before self destruct in days (default 30 days)", "value": "empty_cart_lifetime"}, {"help_text": "Enabled cart notifications (* for all)", "value": "enabled_cart_notifications"}, {"help_text": "Enable extra fee setting", "value": "enable_extra_fee_setting"}, {"help_text": "Extra Fee Amount", "value": "extra_fee_amount"}, {"help_text": "Product amount threshold (incl. VAT) below which the fee is applied", "value": "extra_fee_threshold"}, {"help_text": "Tax rate applied to extra fees (e.g., 0.20 for 20%)", "value": "extra_fee_tax_rate"}, {"help_text": "Commission rules VAT included", "value": "are_commission_rules_vat_included"}, {"help_text": "Aggregate merchant invoice compensations", "value": "aggregate_merchant_invoice_compensations"}, {"help_text": "Notes to display on merchant invoices depending on applicable vat rate", "value": "merchant_invoice_notes_per_vat_rate"}, {"help_text": "Invoices are visible to merchants at generation", "value": "invoices_visible_to_merchants_at_generation"}, {"help_text": "Hide invoice issuer mention", "value": "hide_invoice_issuer_mention"}, {"help_text": "Automatically generate invoice", "value": "auto_generate_invoice"}, {"help_text": "Make legal notices mandatory for invoice submission", "value": "invoice_mandatory_legal_notices"}, {"help_text": "Allow Proforma process on customer invoices", "value": "allow_proforma"}, {"help_text": "Invoice upload is handle by the Merchant", "value": "merchant_handle_invoice_upload"}, {"help_text": "Display store adjustment line in invoice", "value": "display_store_adjustments_invoice"}, {"help_text": "The merchants are automatically activated when all the mandatory infos are filled", "value": "merchant_auto_activation"}, {"help_text": "Enable the kyc v2 api?", "value": "enable_kyc_v2"}, {"help_text": "Merchant reviews are automatically approved unless you activate moderation system.", "value": "enable_merchant_review_moderation"}, {"help_text": "Automatically export confirmed orders as csv file.", "value": "daily_orders_recap_merchants_list"}, {"help_text": "Transaction acceptance phase duration in days", "value": "acceptance_phase_duration"}, {"help_text": "Show the customer's email to the merchant", "value": "show_customer_email_to_merchant"}, {"help_text": "Aggregate order items using quantity field.", "value": "order_items_aggregation"}, {"help_text": "Define lifetime of an authorized merchant order before auto-cancelation.", "value": "auto_cancelling_time"}, {"help_text": "Activate order workflow v2", "value": "order_workflow_v2"}, {"help_text": "Activate discount engine", "value": "enable_discount_engine"}, {"help_text": "Asynch merchant order confirmation", "value": "asynch_merchant_order_confirmation"}, {"help_text": "Asynch order creation from cart", "value": "asynch_order_creation"}, {"help_text": "Order Regulation on authorized", "value": "order_regulation"}, {"help_text": "Moderation on seller cash-out", "value": "moderated_seller_cashouts"}, {"help_text": "Enable partial capture when confirming merchant-orders", "value": "partial_collect_enabled"}, {"help_text": "Override expiration delay for pending_authorization orders. (0 for disabled)", "value": "payment_early_pending_auth_expiration_time"}, {"help_text": "Preserve rolling reserve of XXX on application balance", "value": "payment_rolling_reserve_amount"}, {"help_text": "Asynch payment authorize", "value": "asynch_payment_authorize"}, {"help_text": "Automatically collect payment on merchant order confirmation.", "value": "auto_collect_on_confirm"}, {"help_text": "Algolia admin API key", "value": "algolia_admin_api_key_id"}, {"help_text": "Algolia application id", "value": "algolia_application_id"}, {"help_text": "Are merchants allowed to create products in closed catalog", "value": "allow_merchant_products"}, {"help_text": "Allow \"trashed\" status on models", "value": "allow_trash_status"}, {"help_text": "Should the offer be auto-activated (if valid) at import ?", "value": "auto_offer_activation"}, {"help_text": "Disable stock caching", "value": "disable_cached_stock"}, {"help_text": "Enabled product fields", "value": "enabled_product_fields"}, {"help_text": "Enabled product offer fields", "value": "enabled_product_offer_fields"}, {"help_text": "Enabled product variation fields", "value": "enabled_product_variation_fields"}, {"help_text": "Is it a closed catalog?", "value": "is_closed_catalog"}, {"help_text": "Mandatory fields for localized infos of offers to be valid", "value": "mandatory_localized_offer_fields"}, {"help_text": "Mandatory fields for localized infos of products to be valid", "value": "mandatory_localized_product_fields"}, {"help_text": "Mandatory fields for an offer to be valid", "value": "mandatory_offer_fields"}, {"help_text": "Mandatory fields for a product to be valid", "value": "mandatory_product_fields"}, {"help_text": "Limit of product attributes", "value": "product_attribute_limit"}, {"help_text": "Enable operator moderation on merchant product offers", "value": "product_offer_moderation_enabled"}, {"help_text": "Allow zero-amount offer", "value": "allow_zero_amount_offer"}, {"help_text": "Download images", "value": "download_images"}, {"help_text": "Add other offers in product api result", "value": "other_offers"}, {"help_text": "Number of Offer & Variation thresholds prices", "value": "number_thresholds_prices"}, {"help_text": "Limit of product offers ids in a product family selector", "value": "selector_product_offers_ids_limit"}, {"help_text": "Elasticsearch Credentials", "value": "elasticsearch_credentials"}, {"help_text": "Elasticsearch SSL Version", "value": "elasticsearch_ssl_version"}, {"help_text": "Elasticsearch Batch Update Max Number of Items", "value": "elasticsearch_batch_update_max_number_of_items"}, {"help_text": "Elasticsearch Index Settings", "value": "elasticsearch_index_settings"}, {"help_text": "Automatically recompute discount amount on cart modification", "value": "automatic_discounts_recompute"}, {"help_text": "Allow external discount on cart item", "value": "enable_external_discount"}, {"help_text": "Default tax rate applicable on commissions for new merchants", "value": "default_tax_rate_on_commissions"}, {"help_text": "Enable VAT B2C EU engine", "value": "enable_vat_b2c_eu_engine"}, {"help_text": "Enforce zone <-> rate link integrity", "value": "enforce_zone_rate_link_integrity"}, {"help_text": "Tax Handler", "value": "tax_handler"}, {"help_text": "Seller Backoffice Base URL", "value": "seller_backoffice_base_url"}, {"help_text": "Allows merchants to mark parcel as received", "value": "allow_parcel_receive_action_by_merchant"}, {"help_text": "Automatically recompute shipping fees on cart modification. (default True)", "value": "automatic_shipping_recompute"}, {"help_text": "Enable package tracking v1?", "value": "enable_package_tracking_v1"}, {"help_text": "Enable package tracking v2?", "value": "enable_package_tracking_v2"}, {"help_text": "Shipping engine mode.", "value": "shipping_engine"}, {"help_text": "Is shipping mandatory on each item to validate a cart ?", "value": "mandatory_shipping_on_items"}, {"help_text": "Enable Returns?", "value": "enable_returns"}, {"help_text": "Enable Refunds?", "value": "enable_refunds"}, {"help_text": "Auto-process commission transactions ?", "value": "auto_process_commission_transactions"}, {"help_text": "Enable Balances?", "value": "enable_balances"}, {"help_text": "Enable commission transaction types ?", "value": "enable_commission_transactions"}, {"help_text": "Auto-process store adjustment transactions ?", "value": "auto_process_store_adjustement_transactions"}, {"help_text": "Auto-process license transactions ?", "value": "auto_process_license_transactions"}, {"help_text": "Auto-process marketing operation transactions ?", "value": "auto_process_marketing_operation_transactions"}, {"help_text": "Enabled offer fields in mapper", "value": "mapper_enabled_offer_fields"}, {"help_text": "Enabled product fields in mapper", "value": "mapper_enabled_product_fields"}, {"help_text": "Enabled product and offer fields in mapper", "value": "mapper_enabled_product_and_offer_fields"}, {"help_text": "Admissible fields for high priority queue", "value": "mapper_high_priority_offer_fields"}, {"help_text": "Temporarily switch off mapper", "value": "mapper_switch_off"}, {"help_text": "Mapper uses IZBERG user agent for requests", "value": "mapper_uses_<PERSON><PERSON><PERSON>_user_agent"}, {"help_text": "Download mapper feed content before parsing", "value": "mapper_pre_download_feed"}, {"help_text": "Enable attachments on messages", "value": "enable_message_attachments"}], "choices_to_hide": [], "creatable": true, "default": "No default provided.", "editable": true, "help_text": "", "max_length": 255, "name": "Key", "nullable": false, "readonly": false, "type": "string", "unique": false, "virtual_choices": []}, "read_permission": {"blank": true, "choices": [{"help_text": "IZBERG Staff", "value": 100}, {"help_text": "Application Admin", "value": 90}, {"help_text": "Application Staff", "value": 80}, {"help_text": "Merchant", "value": 50}, {"help_text": "Public", "value": 0}], "choices_to_hide": [], "creatable": false, "default": "", "editable": false, "help_text": "", "name": "Read permission", "nullable": false, "readonly": true, "type": "integer", "unique": false, "virtual_choices": []}, "resource_uri": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Resource uri", "nullable": false, "readonly": true, "type": "string", "unique": false}, "status": {"blank": false, "choices": [{"help_text": "<PERSON><PERSON>", "value": "valid"}, {"help_text": "Invalid", "value": "invalid"}, {"help_text": "Deleted", "value": "deleted"}], "choices_to_hide": [], "creatable": false, "default": "invalid", "editable": false, "help_text": "", "max_length": 16, "name": "Status", "nullable": false, "readonly": true, "transitions": {"_invalidate": {"from_states": ["invalid", "valid"], "to_state": "invalid"}, "_validate": {"from_states": ["invalid", "valid"], "to_state": "valid"}, "delete_action": {"from_states": ["valid", "invalid"], "to_state": "deleted"}}, "type": "string", "unique": false, "virtual_choices": []}, "value": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Value", "nullable": true, "readonly": false, "type": "string", "unique": false}, "value_type": {"blank": false, "choices": [{"help_text": "String", "value": "str"}, {"help_text": "Text", "value": "text"}, {"help_text": "Integer", "value": "int"}, {"help_text": "Decimal", "value": "decimal"}, {"help_text": "Price", "value": "price"}, {"help_text": "Boolean", "value": "bool"}, {"help_text": "Datetime", "value": "datetime"}, {"help_text": "Date", "value": "date"}, {"help_text": "Time", "value": "time"}, {"help_text": "String List", "value": "str_list"}, {"help_text": "Int List", "value": "int_list"}, {"help_text": "Json", "value": "json"}], "choices_to_hide": [], "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "max_length": 20, "name": "Value type", "nullable": true, "readonly": true, "type": "string", "unique": false, "virtual_choices": []}, "write_permission": {"blank": true, "choices": [{"help_text": "IZBERG Staff", "value": 100}, {"help_text": "Application Admin", "value": 90}, {"help_text": "Application Staff", "value": 80}, {"help_text": "Merchant", "value": 50}], "choices_to_hide": [], "creatable": false, "default": "", "editable": false, "help_text": "", "name": "Write permission", "nullable": false, "readonly": true, "type": "integer", "unique": false, "virtual_choices": []}}, "filtering": {"application": 2, "application_configuration": 1, "key": 1}, "legacy_default_values": {"download_images": true, "enable_commission_transactions": false, "enable_vat_b2c_eu_engine": false}, "legacy_switch_dates": {"download_images": "2020-09-07T00:00:00+02:00", "enable_commission_transactions": "2021-07-01T00:00:00+02:00", "enable_vat_b2c_eu_engine": "2021-07-01T00:00:00+02:00"}, "methods": [], "ordering": ["application", "application_configuration", "key"]}