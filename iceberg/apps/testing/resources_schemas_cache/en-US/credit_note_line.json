{"allowed_detail_http_methods": ["get", "post", "put", "delete", "patch"], "allowed_list_http_methods": ["get", "post"], "default_format": "application/json", "default_limit": 20, "doc": "Credit Note Line", "editable_for_statuses": ["active"], "fields": {"created_on": {"blank": true, "creatable": false, "default": true, "editable": false, "help_text": "", "name": "Created on", "nullable": false, "readonly": true, "type": "datetime", "unique": false}, "credit_note": {"blank": false, "creatable": true, "default": "No default provided.", "editable": false, "help_text": "", "name": "Credit note", "nullable": false, "readonly": true, "related_type": "to_one", "type": "related", "unique": false}, "description": {"blank": false, "creatable": true, "default": "", "editable": true, "help_text": "", "max_length": null, "name": "Description", "nullable": true, "readonly": false, "type": "string", "unique": false}, "end_date": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "If applicable, end date of the line", "name": "End date", "nullable": true, "readonly": false, "type": "datetime", "unique": false}, "external_id": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "Use it to store external matching id", "max_length": 255, "name": "External ID", "nullable": true, "readonly": false, "type": "string", "unique": false}, "external_status": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "", "max_length": 255, "name": "External status", "nullable": true, "readonly": false, "type": "string", "unique": false}, "extra_infos": {"blank": true, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "Store custom extra info", "name": "Extra infos", "nullable": true, "readonly": false, "type": "json", "unique": false}, "id": {"blank": true, "creatable": false, "default": "", "editable": false, "help_text": "", "name": "ID", "nullable": false, "readonly": true, "type": "integer", "unique": true}, "image": {"blank": true, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "max_length": 100, "name": "Image", "nullable": true, "readonly": true, "type": "string", "unique": false}, "image_height": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Image height", "nullable": true, "readonly": true, "type": "integer", "unique": false}, "image_width": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Image width", "nullable": true, "readonly": true, "type": "integer", "unique": false}, "invoice_line": {"blank": true, "creatable": true, "default": "No default provided.", "editable": false, "help_text": "", "name": "Invoice line", "nullable": true, "readonly": true, "related_type": "to_one", "type": "related", "unique": false}, "last_modified": {"blank": true, "creatable": false, "default": true, "editable": false, "help_text": "", "name": "Last modified", "nullable": false, "readonly": true, "type": "datetime", "unique": false}, "line_type": {"blank": false, "choices": [{"help_text": "Product", "value": "product"}, {"help_text": "Shipping", "value": "shipping"}, {"help_text": "Extra fee", "value": "extra_fee"}], "choices_to_hide": [], "creatable": true, "default": "product", "editable": true, "help_text": "", "max_length": 32, "name": "Type of line", "nullable": false, "readonly": false, "type": "string", "unique": false, "virtual_choices": []}, "merchant_order": {"blank": true, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "", "name": "Merchant order", "nullable": true, "readonly": false, "related_type": "to_one", "type": "related", "unique": false}, "name": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "", "max_length": 255, "name": "Name", "nullable": true, "readonly": false, "type": "string", "unique": false}, "order": {"blank": true, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Order", "nullable": true, "readonly": true, "related_type": "to_one", "type": "related", "unique": false}, "order_id_number": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Order id number", "nullable": true, "readonly": true, "type": "string", "unique": false}, "order_item": {"blank": true, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "", "name": "Order item", "nullable": true, "readonly": false, "related_type": "to_one", "type": "related", "unique": false}, "original_price": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Original price", "nullable": true, "readonly": true, "type": "decimal", "unique": false}, "original_unit_price": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "", "name": "Original price per unit", "nullable": true, "readonly": false, "type": "decimal", "unique": false}, "price": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "", "name": "Price", "nullable": true, "readonly": true, "type": "decimal", "unique": false}, "quantity": {"blank": true, "creatable": true, "default": "", "editable": true, "help_text": "", "name": "Quantity (in unit)", "nullable": false, "readonly": false, "type": "decimal", "unique": false}, "resource_uri": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Resource uri", "nullable": false, "readonly": true, "type": "string", "unique": false}, "sku": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "", "max_length": 255, "name": "S<PERSON>", "nullable": true, "readonly": false, "type": "string", "unique": false}, "start_date": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "If applicable, start date of the line", "name": "Start date", "nullable": true, "readonly": false, "type": "datetime", "unique": false}, "status": {"blank": false, "choices": [{"help_text": "Active", "value": "active"}, {"help_text": "Deleted", "value": "deleted"}], "choices_to_hide": [], "creatable": false, "default": "active", "editable": false, "help_text": "", "max_length": 16, "name": "Status", "nullable": false, "readonly": true, "transitions": {"delete_action": {"from_states": ["active"], "to_state": "deleted"}}, "type": "string", "unique": false, "virtual_choices": []}, "status_display": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Status display", "nullable": true, "readonly": true, "type": "string", "unique": false}, "tax_rate": {"blank": false, "creatable": true, "default": null, "editable": true, "help_text": "", "name": "Tax rate", "nullable": true, "readonly": false, "type": "decimal", "unique": false}, "unit": {"blank": false, "choices": [{"help_text": "g", "value": "g"}, {"help_text": "kg", "value": "kg"}, {"help_text": "m", "value": "m"}, {"help_text": "m2", "value": "sq_m"}, {"help_text": "l", "value": "l"}, {"help_text": "h", "value": "hour"}, {"help_text": "min", "value": "minute"}], "choices_to_hide": [], "creatable": true, "default": "No default provided.", "editable": true, "help_text": "", "max_length": 32, "name": "Unit for price per unit", "nullable": true, "readonly": false, "type": "string", "unique": false, "virtual_choices": []}, "unit_display": {"blank": false, "creatable": false, "default": "No default provided.", "editable": false, "help_text": "Unicode string data. Ex: \"Hello World\"", "max_length": null, "name": "Unit display", "nullable": true, "readonly": true, "type": "string", "unique": false}, "unit_price": {"blank": false, "creatable": true, "default": "No default provided.", "editable": true, "help_text": "", "name": "Price per unit", "nullable": true, "readonly": false, "type": "decimal", "unique": false}}, "filtering": {"created_on": 1, "credit_note": 2, "id": 1, "last_modified": 1, "line_type": 1, "merchant_order": 2, "order": 2}, "methods": [], "ordering": ["created_on", "credit_note", "id", "last_modified", "line_type", "merchant_order", "order"]}