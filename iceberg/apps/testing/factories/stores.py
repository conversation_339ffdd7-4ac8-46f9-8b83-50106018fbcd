# -*- coding: utf-8 -*-


from decimal import Decimal

import factory
from apps.address.constants import UE_COUNTRY_CODES
from apps.stores.models import Merchant, MerchantBankAccount, MerchantImage
from faker import Faker
from reference.status import (
    ADDRESS_STATUS_ACTIVE,
    MERCHANT_STATUS_ACTIVE,
    MERCHANT_STATUS_INACTIVE,
)


class MerchantFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = "stores.Merchant"

    status = MERCHANT_STATUS_INACTIVE
    name = factory.Faker("company")
    application = factory.SubFactory(
        "apps.testing.factories.ApplicationFactory",
        language=factory.SelfAttribute("..prefered_language"),
        default_currency=factory.SelfAttribute("..default_currency"),
    )
    default_currency = factory.SubFactory("apps.testing.factories.CurrencyFactory")
    prefered_language = factory.fuzzy.FuzzyChoice(["fr", "en", "es", "it"])
    slug = factory.Sequence(lambda n: "merchant-{0}".format(n))

    @classmethod
    def build_for_application(cls, application, **kwargs):
        forbidden_kwargs = ("application", "default_currency", "prefered_language")
        for arg in forbidden_kwargs:
            if arg in kwargs:
                raise Exception(
                    f"Providing {arg} in build_for_application is forbidden"
                )
        return cls.build(
            application=application,
            default_currency=application.default_currency,
            prefered_language=application.language,
            **kwargs,
        )

    @classmethod
    def create_for_application(cls, application, **kwargs):
        forbidden_kwargs = ("application", "default_currency", "prefered_language")
        for arg in forbidden_kwargs:
            if arg in kwargs:
                raise Exception(
                    f"Providing {arg} in create_for_application is forbidden"
                )
        return cls.create(
            application=application,
            default_currency=application.default_currency,
            prefered_language=application.language,
            **kwargs,
        )

    @classmethod
    def create_without_running_post_creation_hook(cls, **kwargs):
        # This method is a little hack to create a merchant without unwanted stuff
        # (cf. Merchant.save()).
        id = kwargs.get("id", None)

        if id is None:
            id = Merchant.objects.count() + 1

        return cls.create(
            id=id,
            from_api=True,
            **kwargs,
        )

    @classmethod
    def _build(cls, model_class, *args, **kwargs):
        id_provided = kwargs.get("id", None) is not None
        obj = model_class(*args, **kwargs)
        if id_provided:
            obj.actions.create_default_tax_settings()
        return obj

    @classmethod
    def _create(
        cls, model_class, _app_settings=None, _payment_backend=None, *args, **kwargs
    ):
        id_provided = kwargs.get("id", None) is not None
        from_api = kwargs.pop("from_api", False)
        obj = model_class(*args, **kwargs)
        obj.save(from_api=from_api)
        if id_provided and not from_api:
            obj.post_creation_hook()
        return obj


class MerchantBankAccountFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = "stores.MerchantBankAccount"

    status = MerchantBankAccount.ACTIVE
    account_owner_name = factory.Faker("name", locale="fr_FR")
    account_owner_address = factory.Faker("address", locale="fr_FR")
    account_IBAN = "***************************"
    account_BIC = "BNPAFRPP"
    merchant = factory.SubFactory(
        MerchantFactory,
        status=MERCHANT_STATUS_ACTIVE,
    )


class MerchantAddressFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = "stores.MerchantAddress"

    status = ADDRESS_STATUS_ACTIVE
    contact_social_reason = factory.fuzzy.FuzzyChoice(["1", "2", "3"])
    contact_first_name = factory.Faker("first_name", locale="fr_FR")
    contact_last_name = factory.Faker("last_name", locale="fr_FR")
    contact_email = factory.Faker("safe_email")
    address = factory.Faker("address", locale="fr_FR")
    address2 = factory.Faker("secondary_address")
    zipcode = factory.Faker("zipcode")
    city = factory.Faker("city", locale="fr_FR")
    state = factory.Faker("department_name", locale="fr_FR")
    language = factory.fuzzy.FuzzyChoice(["fr", "en", "es", "it"])
    phone = factory.Faker("phone_number")
    country = factory.SubFactory("apps.testing.factories.CountryFactory")
    is_in_eu_vat_zone = factory.LazyAttribute(
        lambda obj: obj.country and obj.country.code in UE_COUNTRY_CODES
    )
    merchant = factory.SubFactory(
        MerchantFactory,
        application__country=factory.SelfAttribute("...country"),
        prefered_language=factory.SelfAttribute("..language"),
        status=MERCHANT_STATUS_ACTIVE,
    )
    is_in_eu_vat_zone = factory.LazyAttribute(
        lambda obj: obj.country and obj.country.code in UE_COUNTRY_CODES
    )


class MerchantImageFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = "stores.MerchantImage"

    image_path = factory.django.ImageField(
        filename="sample.jpg", width=1, height=1, color="green", format="JPEG"
    )
    image_width = 1
    image_height = 1
    image_type = factory.fuzzy.FuzzyChoice(
        [MerchantImage.PROFILE, MerchantImage.LOGO, MerchantImage.COVER]
    )
    merchant = factory.SubFactory(
        MerchantFactory,
        status=MERCHANT_STATUS_ACTIVE,
    )


class MerchantCommissionSettingsFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = "stores.MerchantCommissionSettings"

    applicable_tax_rate = Decimal("20.0")
    application = factory.SubFactory("apps.testing.factories.ApplicationFactory")
    merchant = factory.SubFactory(
        "apps.testing.factories.MerchantFactory",
        application=factory.SelfAttribute("..application"),
        default_currency=factory.SelfAttribute("..application.default_currency"),
        prefered_language=factory.SelfAttribute("..application.language"),
        status=MERCHANT_STATUS_ACTIVE,
    )

    @staticmethod
    def _create(cls, merchant=None, application=None, **kwargs):
        """
        Multi fields get_or_create are not implemented by factoryboy, so
        doing it manually
        """
        return cls.objects.get_or_create(
            merchant=merchant,
            application=application,
            defaults=kwargs,
        )[0]


class AbstractMerchantBalanceTransactionFactory(factory.django.DjangoModelFactory):
    """
    Subtransactions are made on best effort basis, with no consistency
    guaranteed.
    If you need fully concsistent transactions, create a main transaction
    instead.
    """

    class Meta:
        abstract = True

    currency = factory.SubFactory("apps.testing.factories.CurrencyFactory")
    merchant = factory.SubFactory(
        MerchantFactory,
        default_currency=factory.SelfAttribute("..currency"),
        status=MERCHANT_STATUS_ACTIVE,
        application=factory.SelfAttribute("..application"),
    )
    application = factory.SubFactory(
        "apps.testing.factories.ApplicationFactory",
        default_currency=factory.SelfAttribute("..currency"),
    )


class MerchantOrderSellMerchantSubTransactionFactory(
    AbstractMerchantBalanceTransactionFactory
):
    """
    Subtransactions are made on best effort basis, with no consistency
    guaranteed.
    If you need fully concsistent transactions, create a main transaction
    instead.
    """

    class Meta:
        model = "stores.BalanceTransaction"

    amount = factory.Faker("pydecimal", positive=True, left_digits=10, right_digits=2)
    transaction = factory.SubFactory(
        "apps.testing.factories.MerchantOrderSellTransactionFactory",
        currency=factory.SelfAttribute("..currency"),
        application=factory.SelfAttribute("..application"),
        merchant=factory.SelfAttribute("..merchant"),
        # do not pass amount, as subtransaction is only a part of full amount
    )


class CustomerInvoiceSellMerchantSubTransactionFactory(
    AbstractMerchantBalanceTransactionFactory
):
    """
    Subtransactions are made on best effort basis, with no consistency
    guaranteed.
    If you need fully concsistent transactions, create a main transaction
    instead.
    """

    class Meta:
        model = "stores.BalanceTransaction"

    amount = factory.Faker("pydecimal", positive=True, left_digits=10, right_digits=2)
    transaction = factory.SubFactory(
        "apps.testing.factories.CustomerInvoiceSellTransactionFactory",
        currency=factory.SelfAttribute("..currency"),
        application=factory.SelfAttribute("..application"),
        merchant=factory.SelfAttribute("..merchant"),
        # do not pass amount, as subtransaction is only a part of full amount
    )


class MerchantOrderRefundMerchantSubTransactionFactory(
    AbstractMerchantBalanceTransactionFactory
):
    """
    Subtransactions are made on best effort basis, with no consistency
    guaranteed.
    If you need fully concsistent transactions, create a main transaction
    instead.
    """

    class Meta:
        model = "stores.BalanceTransaction"

    amount = factory.LazyAttribute(
        lambda o: -1 * Faker().pydecimal(positive=True, left_digits=4, right_digits=2)
    )
    transaction = factory.SubFactory(
        "apps.testing.factories.MerchantOrderRefundTransactionFactory",
        currency=factory.SelfAttribute("..currency"),
        application=factory.SelfAttribute("..application"),
        merchant=factory.SelfAttribute("..merchant"),
        # do not pass amount, as subtransaction is only a part of full amount
    )


class CustomerInvoiceRefundMerchantSubTransactionFactory(
    AbstractMerchantBalanceTransactionFactory
):
    """
    Subtransactions are made on best effort basis, with no consistency
    guaranteed.
    If you need fully concsistent transactions, create a main transaction
    instead.
    """

    class Meta:
        model = "stores.BalanceTransaction"

    amount = factory.LazyAttribute(
        lambda o: -1 * Faker().pydecimal(positive=True, left_digits=4, right_digits=2)
    )
    transaction = factory.SubFactory(
        "apps.testing.factories.CustomerInvoiceRefundTransactionFactory",
        currency=factory.SelfAttribute("..currency"),
        application=factory.SelfAttribute("..application"),
        merchant=factory.SelfAttribute("..merchant"),
        # do not pass amount, as subtransaction is only a part of full amount
    )


class PromotionMerchantSubTransactionFactory(AbstractMerchantBalanceTransactionFactory):
    """
    Subtransactions are made on best effort basis, with no consistency
    guaranteed.
    If you need fully concsistent transactions, create a main transaction
    instead.
    """

    class Meta:
        model = "stores.BalanceTransaction"

    amount = factory.Faker("pydecimal", positive=True, left_digits=10, right_digits=2)
    transaction = factory.SubFactory(
        "apps.testing.factories.PromotionTransactionFactory",
        currency=factory.SelfAttribute("..currency"),
        application=factory.SelfAttribute("..application"),
        merchant=factory.SelfAttribute("..merchant"),
        amount=factory.SelfAttribute("..amount"),
    )


class PromotionRefundMerchantSubTransactionFactory(
    AbstractMerchantBalanceTransactionFactory
):
    """
    Subtransactions are made on best effort basis, with no consistency
    guaranteed.
    If you need fully concsistent transactions, create a main transaction
    instead.
    """

    class Meta:
        model = "stores.BalanceTransaction"

    amount = factory.LazyAttribute(
        lambda o: -1 * Faker().pydecimal(positive=True, left_digits=4, right_digits=2)
    )
    transaction = factory.SubFactory(
        "apps.testing.factories.PromotionRefundTransactionFactory",
        currency=factory.SelfAttribute("..currency"),
        application=factory.SelfAttribute("..application"),
        merchant=factory.SelfAttribute("..merchant"),
        amount=factory.LazyAttribute(lambda o: -1 * o.factory_parent.amount),
    )


class CashoutMerchantSubTransactionFactory(AbstractMerchantBalanceTransactionFactory):
    """
    Subtransactions are made on best effort basis, with no consistency
    guaranteed.
    If you need fully concsistent transactions, create a main transaction
    instead.
    """

    class Meta:
        model = "stores.BalanceTransaction"

    amount = factory.LazyAttribute(
        lambda o: -1 * Faker().pydecimal(positive=True, left_digits=4, right_digits=2)
    )
    transaction = factory.SubFactory(
        "apps.testing.factories.MerchantCashoutTransactionFactory",
        currency=factory.SelfAttribute("..currency"),
        application=factory.SelfAttribute("..application"),
        merchant=factory.SelfAttribute("..merchant"),
        amount=factory.SelfAttribute("..amount"),
    )


class StoreAdjustmentMerchantSubTransactionFactory(
    AbstractMerchantBalanceTransactionFactory
):
    """
    Subtransactions are made on best effort basis, with no consistency
    guaranteed.
    If you need fully concsistent transactions, create a main transaction
    instead.
    """

    class Meta:
        model = "stores.BalanceTransaction"

    amount = factory.Faker("pydecimal", positive=True, left_digits=10, right_digits=2)
    transaction = factory.SubFactory(
        "apps.testing.factories.StoreAdjustmentTransactionFactory",
        currency=factory.SelfAttribute("..currency"),
        application=factory.SelfAttribute("..application"),
        merchant=factory.SelfAttribute("..merchant"),
        amount=factory.LazyAttribute(lambda o: -1 * o.factory_parent.amount),
    )


class LicenseMerchantSubTransactionFactory(AbstractMerchantBalanceTransactionFactory):
    """
    Subtransactions are made on best effort basis, with no consistency
    guaranteed.
    If you need fully concsistent transactions, create a main transaction
    instead.
    """

    class Meta:
        model = "stores.BalanceTransaction"

    amount = 0
    transaction = factory.SubFactory(
        "apps.testing.factories.LicenseTransactionFactory",
        currency=factory.SelfAttribute("..currency"),
        application=factory.SelfAttribute("..application"),
        merchant=factory.SelfAttribute("..merchant"),
        amount=0,
    )


class MarketingOperationMerchantSubTransactionFactory(
    AbstractMerchantBalanceTransactionFactory
):
    """
    Subtransactions are made on best effort basis, with no consistency
    guaranteed.
    If you need fully concsistent transactions, create a main transaction
    instead.
    """

    class Meta:
        model = "stores.BalanceTransaction"

    amount = factory.LazyAttribute(
        lambda o: -1 * Faker().pydecimal(positive=True, left_digits=4, right_digits=2)
    )
    transaction = factory.SubFactory(
        "apps.testing.factories.MarketingOperationTransactionFactory",
        currency=factory.SelfAttribute("..currency"),
        application=factory.SelfAttribute("..application"),
        merchant=factory.SelfAttribute("..merchant"),
        amount=factory.LazyAttribute(lambda o: -1 * o.factory_parent.amount),
    )


class MerchantPaymentBalanceFactory(factory.django.DjangoModelFactory):
    """
    Subtransactions are made on best effort basis, with no consistency
    guaranteed.
    If you need fully concsistent transactions, create a main transaction
    instead.
    """

    class Meta:
        model = "stores.PaymentBalance"

    currency = factory.SubFactory("apps.testing.factories.CurrencyFactory")
    merchant = factory.SubFactory(
        "apps.testing.factories.MerchantFactory",
        default_currency=factory.SelfAttribute("..currency"),
        status=MERCHANT_STATUS_ACTIVE,
    )
