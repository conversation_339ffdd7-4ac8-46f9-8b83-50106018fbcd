{"application": {"id": "int", "pk": "int", "resource_uri": "Resource uri unicode string"}, "are_taxes_included": "bool", "created_on": "str", "credit_note_lines": [{"created_on": "str", "credit_note": {"id": "int", "pk": "int", "resource_uri": "Resource uri unicode string"}, "description": "Empty unicode string", "end_date": "NoneType", "external_id": "NoneType", "external_status": "NoneType", "extra_infos": {}, "id": "int", "image": "NoneType", "image_height": "NoneType", "image_width": "NoneType", "invoice_line": "NoneType", "last_modified": "str", "line_type": "str", "merchant_order": "Resource uri unicode string", "name": "NoneType", "order": "Resource uri unicode string", "order_id_number": "str", "order_item": "Resource uri unicode string", "original_price": "NoneType", "original_unit_price": "NoneType", "price": "str", "quantity": "str", "resource_uri": "Resource uri unicode string", "sku": "str", "start_date": "NoneType", "status": "str", "status_display": "str", "tax_rate": "str", "unit": "NoneType", "unit_display": "NoneType", "unit_price": "str"}], "currency": "Resource uri unicode string", "description": "Empty unicode string", "external_id": "NoneType", "external_status": "NoneType", "extra_infos": {}, "file_source": "str", "file_url": "Empty unicode string", "id": "int", "id_number": "NoneType", "id_number_template": "str", "invoice": "NoneType", "issued_on": "str", "issuer": {"id": "int", "pk": "int", "resource_uri": "Resource uri unicode string"}, "issuer_address": "str", "issuer_billing_address": {"address": "str", "address2": "str", "city": "str", "country": "str", "state": "str", "zipcode": "str"}, "issuer_email": "NoneType", "issuer_extra_legal_infos": "Empty unicode string", "issuer_legal_form": "NoneType", "issuer_legal_infos": "Empty unicode string", "issuer_logo": "NoneType", "issuer_naf_code": "NoneType", "issuer_name": "str", "issuer_phone_number": "NoneType", "issuer_registration_number": "NoneType", "issuer_share_capital": "NoneType", "issuer_siren_number": "NoneType", "issuer_siret_number": "NoneType", "issuer_vat_number": "NoneType", "language": "str", "last_modified": "str", "legal_notices": "Empty unicode string", "meta": {}, "numbering_method": "str", "payment_details": "NoneType", "payment_method": "NoneType", "payment_method_display": "NoneType", "payment_status": "str", "payment_terms": "Empty unicode string", "pdf_file": "NoneType", "period_from": "NoneType", "period_to": "NoneType", "receiver": {"id": "int", "pk": "int", "resource_uri": "Resource uri unicode string"}, "receiver_address": "str", "receiver_billing_address": {"address": "str", "address2": "str", "city": "str", "country": "str", "first_name": "str", "last_name": "str", "state": "str", "zipcode": "str"}, "receiver_email": "str", "receiver_extra_data": {}, "receiver_legal_infos": "Empty unicode string", "receiver_name": "str", "receiver_phone_number": "str", "refund": "Resource uri unicode string", "resource_uri": "Resource uri unicode string", "shipping_amount": "str", "status": "str", "subtotal_amount": "str", "taxes_on_total_amount": [{"base_amount": "str", "tax_amount": "str", "tax_id": "NoneType", "tax_name": "str", "tax_rate": "str"}], "title": "NoneType", "total_amount": "str", "total_amount_with_taxes": "str", "total_amount_without_taxes": "str", "uploaded_file": "NoneType", "warranty_notes": "Empty unicode string"}