import csv
import json
import logging
import os
import time
from datetime import timedelta
from typing import List

import boto3
import botocore
from django.conf import settings
from django.contrib.admin.utils import NestedObjects
from django.db import models, transaction
from django.db.models import QuerySet
from django.utils.timezone import now

logger = logging.getLogger(__name__)


class BaseGarbageCollector:
    """
    Class abstract base class for garbage collectors
    """

    DRY_RUN = True

    DELETE_BY_BATCH = True

    NB_DAYS = 365  # purge instance created before nb days
    LIMIT_STR = None

    COPY_S3_BEFORE_DELETE = False
    HAS_TO_DELETE_S3_FILES = False

    BATCH_SIZE = 100
    SLEEP_TIME = 0.1

    HEADERS = [
        "instance_id",
        "class_name",
        "application_id",
        "timestamp",
        "files",
        "json",
    ]

    def __init__(self, application_id=None, **kwargs):
        self.application_id = application_id

        self.dry_run = self.DRY_RUN
        self.delete_by_batch = self.DELETE_BY_BATCH

        self.nb_days = self.NB_DAYS
        self.limit_str = self.LIMIT_STR

        self.batch_size = self.BATCH_SIZE
        self.sleep_time = self.SLEEP_TIME

        self.copy_s3_before_delete = self.COPY_S3_BEFORE_DELETE
        self.has_to_delete_s3_files = self.HAS_TO_DELETE_S3_FILES

        for key, value in kwargs.items():
            setattr(self, key, value)

        self.expire_date = now() - timedelta(days=self.nb_days)

        self.client_s3 = boto3.client("s3")
        self.i_batch = None

    @property
    def headers(self):
        return self.HEADERS + (
            ["nested_architecture", "nb_cascade_deleted"] if self.dry_run else []
        )

    # --- GET ---
    def get_all_queryset(self) -> List[QuerySet]:
        """
        Methode which need to be implemented to have the list of all queryset to
        purge, including orphans.
        """
        raise NotImplementedError

    def apply_application_filter(self, all_queryset: List[QuerySet]) -> List[QuerySet]:
        """
        Apply filter on the application id if it is implemented
        """
        if self.application_id is None:
            return all_queryset

        all_queryset_return = []

        for queryset in all_queryset:
            fields_names = [field.name for field in queryset.model._meta.get_fields()]
            if not ("application_id" in fields_names or "application" in fields_names):
                raise Exception("No application attribute found")

            all_queryset_return.append(
                queryset.filter(application_id=self.application_id)
            )

        return all_queryset_return

    def iterator_batch_queryset(self, queryset: QuerySet) -> List:
        batch = []
        for instance in queryset.iterator():
            batch.append(instance)
            if len(batch) >= self.batch_size:
                yield batch
                batch = []
        if batch:
            yield batch

    def collect_s3_keys_for_instance(self, instance) -> List[str]:
        raise NotImplementedError

    def get_csv_filename(self, instance_or_mode_name):
        """
        Get the csv filename : garbage-collector-app-45-product.csv
        """
        if isinstance(instance_or_mode_name, str):
            instance_or_mode_name = instance_or_mode_name.lower()
        else:
            instance_or_mode_name = instance_or_mode_name.__class__.__name__.lower()
        return (
            f"garbage-collector-app-{self.application_id}-{instance_or_mode_name}.csv"
        )

    def get_nested_object(self, instance):
        """
        Get a list of nested objects from an instance
        Not use in this abstract class but usefully in children classes
        """
        nested_object = NestedObjects("default")
        nested_object.collect([instance])
        cascade_delete_object = nested_object.nested()

        cascade_delete_object.remove(instance)

        return cascade_delete_object

    def _flatten_list(self, to_flatten, already_flattened=None):
        """
        Flat a list for the function `get_nested_object`
        """
        if already_flattened is None:
            already_flattened = []
        for item in to_flatten:
            if isinstance(item, (list, tuple, QuerySet)):
                self._flatten_list(item, already_flattened)
            else:
                already_flattened.append(item)

        return already_flattened

    def iter_dates_to_divide_queryset_by_month(
        self, queryset, attr_date, delta_nb_days=30
    ):
        oldest_instance = queryset.earliest(attr_date)
        if not oldest_instance:
            return
        start_date = getattr(oldest_instance, attr_date)
        delta = timedelta(days=delta_nb_days)

        while start_date < self.expire_date:
            end_date = min(start_date + delta, self.expire_date)
            yield start_date, end_date
            start_date = end_date

    # --- EXPORT ---

    def instance_to_csv(self, instance) -> list:
        """
        Extract information from an instance to create csv line
        Json information are extracted in `instance_to_json`
        """
        csv_line = [
            instance.id,
            instance.__class__.__name__,
            self.application_id,
            now(),
            json.dumps(
                [key for key in self.collect_s3_keys_for_instance(instance) if key]
            ),
            json.dumps(self.instance_to_json(instance)),
        ]
        if self.dry_run:
            nested_cascade = self.get_nested_object(instance)
            csv_line += [
                str(nested_cascade)[: self.limit_str],
                len(self._flatten_list(nested_cascade)),
            ]
        return csv_line

    def instance_to_json(self, instance) -> dict:
        """
        Convert an instance to a dictionary to be saved as json in csv file
        Return only important information
        By default export nothing
        """
        return {}

    def instance_to_files_fields(self, instance):
        return [
            getattr(instance, field.name).name  # convert FileField
            for field in instance.__class__._meta.get_fields()
            if isinstance(field, models.FileField)
        ]

    def export_batch(self, batch):
        """
        Save data from instance to a csv file that can be uploaded to an S3 later
        Write in csv file in "append" method so call this method often
        """
        file_exists = os.path.isfile(self.get_csv_filename(batch[0]))
        with open(self.get_csv_filename(batch[0]), "a") as csvfile:
            writer = csv.writer(csvfile)
            if not file_exists:
                writer.writerow(self.headers)
            for instance in batch:
                writer.writerow(self.instance_to_csv(instance))

    def s3_key_exists(self, key_file):
        try:
            self.client_s3.head_object(
                Bucket=settings.AWS_STORAGE_BUCKET_NAME, Key=key_file
            )
            return True
        except botocore.exceptions.ClientError as e:
            if e.response["Error"]["Code"] == "404":
                return False
            raise e

    def delete_s3_files(self, batch):
        """
        Consider in child class to delete s3 files
        If no file, juste write 'pass'
        No try, except because caught in run
        """
        for instance in batch:
            keys_files = self.collect_s3_keys_for_instance(instance)
            for key_file in keys_files:
                if key_file is None or not key_file:
                    pass
                elif self.dry_run:
                    logger.info(f"[DRY RUN] Delete file on S3 AWS {key_file}")
                elif self.has_to_delete_s3_files and self.s3_key_exists(key_file):
                    if self.copy_s3_before_delete:
                        logger.info(
                            f"[TRASH] Move file in trash on S3 AWS {key_file} -> "
                            f"trash_purge/{key_file}"
                        )
                        self.client_s3.copy(
                            {
                                "Bucket": settings.AWS_STORAGE_BUCKET_NAME,
                                "Key": key_file,
                            },
                            settings.AWS_STORAGE_BUCKET_NAME,
                            f"trash_purge/{key_file}",
                        )
                    else:
                        logger.info(f"[DELETE] File in trash on S3 AWS {key_file}")
                    self.client_s3.delete_object(
                        Bucket=settings.AWS_STORAGE_BUCKET_NAME, Key=key_file
                    )

    # --- DELETE---
    def delete_batch(self, batch):
        """
        Delete the instance, for now only a Dry Run
        """
        if self.dry_run:
            logger.info(
                f"({self.i_batch}) [DRY RUN] Delete : {batch[0].__class__.__name__} "
                f"batch of {len(batch)} instances"
            )
        elif self.delete_by_batch:
            logger.info(
                f"({self.i_batch}) Delete : {batch[0].__class__.__name__} by batch of "
                f"{len(batch)} instances"
            )
            batch[0].__class__.objects.filter(
                id__in=[instance.id for instance in batch]
            ).delete()
        else:
            for instance in batch:
                logger.info(
                    f"({self.i_batch}) Delete : {instance.__class__.__name__} by "
                    f"instance : {instance.id}"
                )
                instance.delete()

    # --- RUN ---
    def run(self, limit_nb_instances=None, limit_nb_batch=None):
        """
        Run routine
        Iter through all queryset
        Export csv information and delete S3 files and instance
        Save final csv file on AWS S3 bucket
        """
        all_queryset = self.get_all_queryset()
        all_queryset = self.apply_application_filter(all_queryset)

        count_by_model = {}  # to count for limit by model, {'i_batch', 'nb_instances'}

        for queryset in all_queryset:
            model_name = queryset.model.__name__
            count_by_model.setdefault(model_name, {"i_batch": 0, "nb_instances": 0})
            logger.info(f"\n\n--- {model_name} ---")

            if not self.can_continue_running_iteration(
                count_by_model[model_name]["nb_instances"],
                limit_nb_instances,
                count_by_model[model_name]["i_batch"],
                limit_nb_batch,
            ):
                continue  # to avoid to load queryset

            try:
                for batch in self.iterator_batch_queryset(queryset):
                    if not self.can_continue_running_iteration(
                        count_by_model[model_name]["nb_instances"],
                        limit_nb_instances,
                        count_by_model[model_name]["i_batch"],
                        limit_nb_batch,
                    ):
                        logger.info(f"Break at batch {count_by_model[model_name]}")
                        break

                    count_by_model[model_name]["nb_instances"] += len(batch)
                    count_by_model[model_name]["i_batch"] += 1
                    self.i_batch = count_by_model[model_name]["i_batch"]

                    # transaction.atomic treat the data in block if anything goes wrong
                    # it roll back the block
                    with transaction.atomic():
                        self.export_batch(batch)
                        self.delete_s3_files(batch)
                        self.delete_batch(batch)
                    time.sleep(self.sleep_time)

            except Exception as e:
                logger.info(f"\nError: {e}")
                error = e
            else:
                error = None

            if not os.path.isfile(self.get_csv_filename(model_name)):
                logger.info("\n> No CSV saved: no src file created")
            else:
                today = now()
                src_csv_filename = self.get_csv_filename(model_name)

                dst_csv_filename = (
                    f"garbage-collector/{self.application_id}/"
                    f"{model_name.lower()}/{today.year}-{today.month}-{today.day}/"
                    f"{today.hour}_{today.minute}_{src_csv_filename}"
                )
                logger.info(
                    f"\n> Start CSV upload from {src_csv_filename} to {dst_csv_filename}"
                )
                self.client_s3.upload_file(
                    src_csv_filename,
                    settings.AWS_STORAGE_BUCKET_NAME,
                    dst_csv_filename,
                )

                logger.info(
                    f"\n> Finish CSV upload from {src_csv_filename} to {dst_csv_filename}"
                )

            if error:
                raise error

    @staticmethod
    def can_continue_running_iteration(
        nb_instances, limit_nb_instances, i_batch, limit_nb_batch
    ):
        return (limit_nb_instances is None or nb_instances < limit_nb_instances) and (
            limit_nb_batch is None or i_batch < limit_nb_batch
        )
