from apps.tasking.actions.actions_manager import ActionManager
from apps.tasking.api.async_action.cursor_pagination import CursorPagination
from apps.tasking.exceptions import InvalidCursor
from apps.tasking.tests.async_action.fixtures import SampleAsyncAction
from apps.tasking.tests.async_action.utils import create_async_action
from drf_izberg.tests.base import BaseSerializerAPITestCase
from rest_framework.response import Response


class CursorPaginationTestCase(BaseSerializerAPITestCase):
    def generate_actions(self, size=50):
        for _ in range(0, size):
            create_async_action(
                SampleAsyncAction,
                application=self.request.application,
                lock_action=False,
            )

    def test_pagination(self):
        self.request.query_params = {}
        self.generate_actions(size=10)

        paginator = CursorPagination(self.request)
        limit = paginator.get_limit()
        cursor = paginator.get_cursor()

        self.assertEqual(limit, 20)
        self.assertEqual(cursor, "0-0")

        next_cursor, actions = ActionManager.list(cursor, limit)
        result = paginator.get_paginated_response(actions, next_cursor)

        self.assertTrue(isinstance(result, Response))
        data = result.data
        self.assertIn("meta", data)
        self.assertEqual(
            dict(data["meta"]),  # it's an Ordereddict
            {
                "limit": 20,
                "cursor": "0-0",
                "next": None,
            },
        )

    def test_pagination_next_link(self):
        self.request.query_params = {}
        self.generate_actions(size=25)

        paginator = CursorPagination(self.request)
        limit = paginator.get_limit()
        cursor = paginator.get_cursor()
        self.assertEqual(limit, 20)
        self.assertEqual(cursor, "0-0")

        next_cursor, actions = ActionManager.list(cursor, limit)
        result = paginator.get_paginated_response(actions, next_cursor)

        self.assertTrue(isinstance(result, Response))
        data = result.data
        self.assertIn("meta", data)
        self.assertEqual(
            dict(data["meta"]),
            {
                "limit": 20,
                "cursor": "0-0",
                "next": f"http://testserver/?cursor={next_cursor}&limit=20",
            },
        )

    def test_pagination_change_limit(self):
        self.request.query_params = {"limit": 5}
        self.generate_actions(size=6)

        paginator = CursorPagination(self.request)
        limit = paginator.get_limit()
        cursor = paginator.get_cursor()

        self.assertEqual(limit, 5)
        self.assertEqual(cursor, "0-0")

        next_cursor, actions = ActionManager.list(cursor, limit)
        result = paginator.get_paginated_response(actions, next_cursor)

        self.assertTrue(isinstance(result, Response))
        data = result.data
        self.assertIn("meta", data)
        self.assertEqual(data["meta"]["limit"], 5)
        self.assertEqual(len(data["objects"]), 5)

    def test_pagination_second_page(self):
        self.request.query_params = {"limit": 5}
        self.generate_actions(size=10)

        paginator = CursorPagination(self.request)
        limit = paginator.get_limit()
        cursor = paginator.get_cursor()

        self.assertEqual(limit, 5)
        self.assertEqual(cursor, "0-0")

        next_cursor, actions = ActionManager.list(cursor, limit)
        result = paginator.get_paginated_response(actions, next_cursor)

        self.assertTrue(isinstance(result, Response))
        data = result.data
        self.assertIn("meta", data)
        self.assertEqual(data["meta"]["limit"], 5)
        self.assertIn(next_cursor, data["meta"]["next"])
        self.assertEqual(len(data["objects"]), 5)
        first_actions = set([obj.id for obj in data["objects"]])

        self.request.query_params = {"limit": 5, "cursor": next_cursor}
        paginator = CursorPagination(self.request)
        limit = paginator.get_limit()
        cursor = paginator.get_cursor()

        self.assertEqual(limit, 5)
        self.assertEqual(cursor, next_cursor)

        next_cursor, actions = ActionManager.list(cursor, limit)
        result = paginator.get_paginated_response(actions, next_cursor)

        self.assertTrue(isinstance(result, Response))
        data = result.data
        self.assertIn("meta", data)
        self.assertEqual(data["meta"]["limit"], 5)
        self.assertIn(next_cursor, data["meta"]["next"])
        self.assertEqual(len(data["objects"]), 5)
        second_actions = set([obj.id for obj in data["objects"]])

        self.assertFalse(first_actions.intersection(second_actions))

    def test_pagination_wrong_cursor(self):
        self.request.query_params = {"cursor": 10}

        paginator = CursorPagination(self.request)
        with self.assertRaises(InvalidCursor) as exc_info:
            paginator.get_cursor()

        self.assertEqual(exc_info.exception.args[0], "Invalid cursor provided.")
