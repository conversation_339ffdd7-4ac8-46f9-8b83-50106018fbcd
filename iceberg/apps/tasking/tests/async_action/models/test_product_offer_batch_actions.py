from apps.channels.channel_settings import LOW_PRIORITY_QUEUE
from apps.tasking.actions.actions_manager import ActionManager
from apps.tasking.models.drf import (
    ProductOfferBatchActivateAction,
    ProductOfferBatchApplyDefaultTaxSettingsAction,
    ProductOfferBatchBanAction,
    ProductOfferBatchDeactivateAction,
    ProductOfferBatchRemoveAllManuallyManagedFieldsAction,
    ProductOfferBatchSubmitAction,
    ProductOfferBatchTrashAction,
    ProductOfferBatchUnBanAction,
    ProductOfferBatchUntrashAction,
    ProductOfferBatchUpdateStockFieldAction,
)
from apps.tasking.tests.async_action.utils import DEFAULT_CURSOR, create_async_action
from apps.testing.factories import (
    ApplicationFactory,
    MerchantFactory,
    ProductOfferFactory,
    TaxRateAssignmentFactory,
)
from django.test.utils import override_settings
from ims.tests import BaseTestCase
from mock import ANY
from reference.status import (
    PRODUCT_OFFER_STATUS_ACTIVE,
    PRODUCT_OFFER_STATUS_BANNED,
    PRODUCT_OFFER_STATUS_DRAFT,
    PRODUCT_OFFER_STATUS_INACTIVE,
    PRODUCT_OFFER_STATUS_PENDING,
    PRODUCT_OFFER_STATUS_TRASHED,
)


class ProductOfferBatchActivateActionTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.app = ApplicationFactory(id=1)
        self.merchant = MerchantFactory.create_for_application(application=self.app)
        self.offers = ProductOfferFactory.create_batch(
            size=5,
            application=self.app,
            merchant=self.merchant,
            currency=self.app.default_currency,
        )
        self.offers_ids = [offer.id for offer in self.offers]

    def tearDown(self):
        _, actions = ActionManager.list(DEFAULT_CURSOR)
        for action in actions:
            if action.actions.lock.is_locked():
                action.actions.lock.unlock()
            action.actions.delete()

    def test_initialize(self):
        action = ProductOfferBatchActivateAction(
            application_id=self.app.id, merchant_id=self.merchant.id
        )

        action.initialize(self.offers_ids)

        self.assertEqual(action.get_total_ids(), len(self.offers_ids))
        self.assertEqual(action.get_modified_counter(), 0)
        self.assertEqual(action.get_ignored_counter(), 0)
        self.assertEqual(action.get_errored_counter(), 0)

    def test_get_as_dict(self):
        action = create_async_action(
            ProductOfferBatchActivateAction,
            application=self.app,
            merchant=self.merchant,
            ids=self.offers_ids,
        )

        result = action.get_as_dict()

        self.assertEqual(
            result,
            {
                "id": action.id,
                "status": action.get_status(),
                "created_on": ANY,
                "action_name": action.action_name,
                "lock_action": True,
                "ended_on": None,
                "total_offers": len(self.offers_ids),
                "modified_offers": action.get_modified_counter(),
                "ignored_offers": action.get_ignored_counter(),
                "errored_offers": action.get_errored_counter(),
                "result": {},
            },
        )

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @override_settings(CELERY_TASK_EAGER_PROPAGATES_EXCEPTIONS=True)
    def test_run_action(self):
        action = ProductOfferBatchActivateAction(
            application_id=self.app.id, merchant_id=self.merchant.id
        )

        result = action.run_action(self.offers_ids, dry_run=False)

        self.assertEqual(result.id, action.id)

        for offer in self.offers:
            offer.refresh_from_db()
            self.assertEqual(offer.status, PRODUCT_OFFER_STATUS_ACTIVE)

    def test_launch_task(self):
        action = create_async_action(
            ProductOfferBatchActivateAction,
            application=self.app,
            merchant=self.merchant,
            ids=self.offers_ids,
        )

        result = action.launch_task(
            "activate_if_valid",
            LOW_PRIORITY_QUEUE,
            self.app.id,
            self.merchant.id,
            method_kwargs={"ignore_perms": True},
        )

        self.assertEqual(
            result,
            {
                "id": action.id,
                "status": action.get_status(),
                "created_on": ANY,
                "action_name": action.action_name,
                "lock_action": True,
                "ended_on": ANY,
                "total_offers": len(self.offers_ids),
                "modified_offers": len(self.offers_ids),
                "ignored_offers": 0,
                "errored_offers": 0,
                "result": {},
            },
        )

        for offer in self.offers:
            offer.refresh_from_db()
            self.assertEqual(offer.status, PRODUCT_OFFER_STATUS_ACTIVE)

    def test_fetch_offer_on_other_application_raise(self):
        action = ProductOfferBatchActivateAction(
            application_id=self.app.id, merchant_id=self.merchant.id
        )

        with self.assertRaises(Exception) as exc_info:
            action.fetch(ProductOfferFactory().id, self.app.id, self.merchant.id)

        self.assertEqual(
            exc_info.exception.args[0],
            "Offer's application does not match task's application",
        )

    def test_fetch_offer_on_other_merchant_raise(self):
        offer = ProductOfferFactory.create_for_merchant(merchant=self.merchant)
        merchant_2 = MerchantFactory.create_for_application(application=self.app)

        action = ProductOfferBatchActivateAction(
            application_id=self.app.id,
            merchant_id=merchant_2.id,
        )

        with self.assertRaises(Exception) as exc_info:
            action.fetch(offer.id, self.app.id, merchant_2.id)

        self.assertEqual(
            exc_info.exception.args[0],
            "Offer's merchant does not match task's merchant",
        )


class ProductOfferBatchDeactivateActionTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.app = ApplicationFactory(id=1)
        self.merchant = MerchantFactory.create_for_application(application=self.app)
        self.offers = [
            ProductOfferFactory.create_for_merchant(
                self.merchant,
                status=PRODUCT_OFFER_STATUS_ACTIVE,
            )
            for _ in range(5)
        ]
        self.offers_ids = [offer.id for offer in self.offers]

    def tearDown(self):
        _, actions = ActionManager.list(DEFAULT_CURSOR)
        for action in actions:
            if action.actions.lock.is_locked():
                action.actions.lock.unlock()
            action.actions.delete()

    def test_initialize(self):
        action = ProductOfferBatchDeactivateAction(
            application_id=self.app.id, merchant_id=self.merchant.id
        )

        action.initialize(self.offers_ids)

        self.assertEqual(action.get_total_ids(), len(self.offers_ids))
        self.assertEqual(action.get_modified_counter(), 0)
        self.assertEqual(action.get_ignored_counter(), 0)
        self.assertEqual(action.get_errored_counter(), 0)

    def test_get_as_dict(self):
        action = create_async_action(
            ProductOfferBatchDeactivateAction,
            application=self.app,
            merchant=self.merchant,
            ids=self.offers_ids,
        )

        result = action.get_as_dict()

        self.assertEqual(
            result,
            {
                "id": action.id,
                "status": action.get_status(),
                "created_on": ANY,
                "action_name": action.action_name,
                "lock_action": True,
                "ended_on": None,
                "total_offers": len(self.offers_ids),
                "modified_offers": action.get_modified_counter(),
                "ignored_offers": action.get_ignored_counter(),
                "errored_offers": action.get_errored_counter(),
                "result": {},
            },
        )

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @override_settings(CELERY_TASK_EAGER_PROPAGATES_EXCEPTIONS=True)
    def test_run_action(self):
        action = ProductOfferBatchDeactivateAction(
            application_id=self.app.id, merchant_id=self.merchant.id
        )

        result = action.run_action(self.offers_ids, dry_run=False)

        self.assertEqual(result.id, action.id)

        for offer in self.offers:
            offer.refresh_from_db()
            self.assertEqual(offer.status, PRODUCT_OFFER_STATUS_INACTIVE)

    def test_launch_task(self):
        action = create_async_action(
            ProductOfferBatchDeactivateAction,
            application=self.app,
            merchant=self.merchant,
            ids=self.offers_ids,
        )

        result = action.launch_task(
            "deactivate_if_available",
            LOW_PRIORITY_QUEUE,
            self.app.id,
            self.merchant.id,
            method_kwargs={"ignore_perms": True},
        )

        self.assertEqual(
            result,
            {
                "id": action.id,
                "status": action.get_status(),
                "created_on": ANY,
                "action_name": action.action_name,
                "lock_action": True,
                "ended_on": ANY,
                "total_offers": len(self.offers_ids),
                "modified_offers": len(self.offers_ids),
                "ignored_offers": 0,
                "errored_offers": 0,
                "result": {},
            },
        )

        for offer in self.offers:
            offer.refresh_from_db()
            self.assertEqual(offer.status, PRODUCT_OFFER_STATUS_INACTIVE)

    def test_fetch_offer_on_other_application_raise(self):
        action = ProductOfferBatchActivateAction(
            application_id=self.app.id, merchant_id=self.merchant.id
        )

        with self.assertRaises(Exception) as exc_info:
            action.fetch(ProductOfferFactory().id, self.app.id, self.merchant.id)

        self.assertEqual(
            exc_info.exception.args[0],
            "Offer's application does not match task's application",
        )

    def test_fetch_offer_on_other_merchant_raise(self):
        offer = ProductOfferFactory.create_for_merchant(merchant=self.merchant)
        merchant_2 = MerchantFactory.create_for_application(application=self.app)

        action = ProductOfferBatchActivateAction(
            application_id=self.app.id,
            merchant_id=merchant_2.id,
        )

        with self.assertRaises(Exception) as exc_info:
            action.fetch(offer.id, self.app.id, merchant_2.id)

        self.assertEqual(
            exc_info.exception.args[0],
            "Offer's merchant does not match task's merchant",
        )


class ProductOfferBatchBanActionTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.app = ApplicationFactory(id=1)
        self.merchant = MerchantFactory.create_for_application(application=self.app)
        self.offers = [
            ProductOfferFactory.create_for_merchant(self.merchant) for _ in range(5)
        ]
        self.offers_ids = [offer.id for offer in self.offers]

    def tearDown(self):
        _, actions = ActionManager.list(DEFAULT_CURSOR)
        for action in actions:
            if action.actions.lock.is_locked():
                action.actions.lock.unlock()
            action.actions.delete()

    def test_initialize(self):
        action = ProductOfferBatchBanAction(
            application_id=self.app.id, merchant_id=self.merchant.id
        )

        action.initialize(self.offers_ids)

        self.assertEqual(action.get_total_ids(), len(self.offers_ids))
        self.assertEqual(action.get_modified_counter(), 0)
        self.assertEqual(action.get_ignored_counter(), 0)
        self.assertEqual(action.get_errored_counter(), 0)

    def test_get_as_dict(self):
        action = create_async_action(
            ProductOfferBatchBanAction,
            application=self.app,
            merchant=self.merchant,
            ids=self.offers_ids,
        )

        result = action.get_as_dict()

        self.assertEqual(
            result,
            {
                "id": action.id,
                "status": action.get_status(),
                "created_on": ANY,
                "action_name": action.action_name,
                "lock_action": True,
                "ended_on": None,
                "total_offers": len(self.offers_ids),
                "modified_offers": action.get_modified_counter(),
                "ignored_offers": action.get_ignored_counter(),
                "errored_offers": action.get_errored_counter(),
                "result": {},
            },
        )

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @override_settings(CELERY_TASK_EAGER_PROPAGATES_EXCEPTIONS=True)
    def test_run_action(self):
        action = ProductOfferBatchBanAction(
            application_id=self.app.id, merchant_id=self.merchant.id
        )

        result = action.run_action(self.offers_ids, dry_run=False)

        self.assertEqual(result.id, action.id)

        for offer in self.offers:
            offer.refresh_from_db()
            self.assertEqual(offer.status, PRODUCT_OFFER_STATUS_BANNED)

    def test_launch_task(self):
        action = create_async_action(
            ProductOfferBatchBanAction,
            application=self.app,
            merchant=self.merchant,
            ids=self.offers_ids,
        )

        result = action.launch_task(
            "ban_if_available",
            LOW_PRIORITY_QUEUE,
            self.app.id,
            self.merchant.id,
            method_kwargs={"ignore_perms": True},
        )

        self.assertEqual(
            result,
            {
                "id": action.id,
                "status": action.get_status(),
                "created_on": ANY,
                "action_name": action.action_name,
                "lock_action": True,
                "ended_on": ANY,
                "total_offers": len(self.offers_ids),
                "modified_offers": len(self.offers_ids),
                "ignored_offers": 0,
                "errored_offers": 0,
                "result": {},
            },
        )

        for offer in self.offers:
            offer.refresh_from_db()
            self.assertEqual(offer.status, PRODUCT_OFFER_STATUS_BANNED)

    def test_fetch_offer_on_other_application_raise(self):
        action = ProductOfferBatchActivateAction(
            application_id=self.app.id, merchant_id=self.merchant.id
        )

        with self.assertRaises(Exception) as exc_info:
            action.fetch(ProductOfferFactory().id, self.app.id, self.merchant.id)

        self.assertEqual(
            exc_info.exception.args[0],
            "Offer's application does not match task's application",
        )

    def test_fetch_offer_on_other_merchant_raise(self):
        offer = ProductOfferFactory.create_for_merchant(merchant=self.merchant)
        merchant_2 = MerchantFactory.create_for_application(application=self.app)

        action = ProductOfferBatchActivateAction(
            application_id=self.app.id,
            merchant_id=merchant_2.id,
        )

        with self.assertRaises(Exception) as exc_info:
            action.fetch(offer.id, self.app.id, merchant_2.id)

        self.assertEqual(
            exc_info.exception.args[0],
            "Offer's merchant does not match task's merchant",
        )


class ProductOfferBatchUnBanActionTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.app = ApplicationFactory(id=1)
        self.merchant = MerchantFactory.create_for_application(application=self.app)
        self.offers = [
            ProductOfferFactory.create_for_merchant(
                self.merchant, status=PRODUCT_OFFER_STATUS_BANNED
            )
            for _ in range(5)
        ]
        self.offers_ids = [offer.id for offer in self.offers]

    def tearDown(self):
        _, actions = ActionManager.list(DEFAULT_CURSOR)
        for action in actions:
            if action.actions.lock.is_locked():
                action.actions.lock.unlock()
            action.actions.delete()

    def test_initialize(self):
        action = ProductOfferBatchUnBanAction(
            application_id=self.app.id, merchant_id=self.merchant.id
        )

        action.initialize(self.offers_ids)

        self.assertEqual(action.get_total_ids(), len(self.offers_ids))
        self.assertEqual(action.get_modified_counter(), 0)
        self.assertEqual(action.get_ignored_counter(), 0)
        self.assertEqual(action.get_errored_counter(), 0)

    def test_get_as_dict(self):
        action = create_async_action(
            ProductOfferBatchUnBanAction,
            application=self.app,
            merchant=self.merchant,
            ids=self.offers_ids,
        )

        result = action.get_as_dict()

        self.assertEqual(
            result,
            {
                "id": action.id,
                "status": action.get_status(),
                "created_on": ANY,
                "action_name": action.action_name,
                "lock_action": True,
                "ended_on": None,
                "total_offers": len(self.offers_ids),
                "modified_offers": action.get_modified_counter(),
                "ignored_offers": action.get_ignored_counter(),
                "errored_offers": action.get_errored_counter(),
                "result": {},
            },
        )

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @override_settings(CELERY_TASK_EAGER_PROPAGATES_EXCEPTIONS=True)
    def test_run_action(self):
        action = ProductOfferBatchUnBanAction(
            application_id=self.app.id, merchant_id=self.merchant.id
        )

        result = action.run_action(self.offers_ids, dry_run=False)

        self.assertEqual(result.id, action.id)

        for offer in self.offers:
            offer.refresh_from_db()
            self.assertEqual(offer.status, PRODUCT_OFFER_STATUS_INACTIVE)

    def test_launch_task(self):
        action = create_async_action(
            ProductOfferBatchUnBanAction,
            application=self.app,
            merchant=self.merchant,
            ids=self.offers_ids,
        )

        result = action.launch_task(
            "unban_if_available",
            LOW_PRIORITY_QUEUE,
            self.app.id,
            self.merchant.id,
            method_kwargs={"ignore_perms": True},
        )

        self.assertEqual(
            result,
            {
                "id": action.id,
                "status": action.get_status(),
                "created_on": ANY,
                "action_name": action.action_name,
                "lock_action": True,
                "ended_on": ANY,
                "total_offers": len(self.offers_ids),
                "modified_offers": len(self.offers_ids),
                "ignored_offers": 0,
                "errored_offers": 0,
                "result": {},
            },
        )

        for offer in self.offers:
            offer.refresh_from_db()
            self.assertEqual(offer.status, PRODUCT_OFFER_STATUS_INACTIVE)

    def test_fetch_offer_on_other_application_raise(self):
        action = ProductOfferBatchActivateAction(
            application_id=self.app.id, merchant_id=self.merchant.id
        )

        with self.assertRaises(Exception) as exc_info:
            action.fetch(ProductOfferFactory().id, self.app.id, self.merchant.id)

        self.assertEqual(
            exc_info.exception.args[0],
            "Offer's application does not match task's application",
        )

    def test_fetch_offer_on_other_merchant_raise(self):
        offer = ProductOfferFactory.create_for_merchant(merchant=self.merchant)
        merchant_2 = MerchantFactory.create_for_application(application=self.app)

        action = ProductOfferBatchActivateAction(
            application_id=self.app.id,
            merchant_id=merchant_2.id,
        )

        with self.assertRaises(Exception) as exc_info:
            action.fetch(offer.id, self.app.id, merchant_2.id)

        self.assertEqual(
            exc_info.exception.args[0],
            "Offer's merchant does not match task's merchant",
        )


class ProductOfferBatchSubmitActionTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.app = ApplicationFactory(id=1)
        self.merchant = MerchantFactory.create_for_application(application=self.app)
        self.offers = [
            ProductOfferFactory.create_for_merchant(self.merchant) for _ in range(5)
        ]
        self.offers_ids = [offer.id for offer in self.offers]

    def tearDown(self):
        _, actions = ActionManager.list(DEFAULT_CURSOR)
        for action in actions:
            if action.actions.lock.is_locked():
                action.actions.lock.unlock()
            action.actions.delete()

    def test_initialize(self):
        action = ProductOfferBatchSubmitAction(
            application_id=self.app.id, merchant_id=self.merchant.id
        )

        action.initialize(self.offers_ids)

        self.assertEqual(action.get_total_ids(), len(self.offers_ids))
        self.assertEqual(action.get_modified_counter(), 0)
        self.assertEqual(action.get_ignored_counter(), 0)
        self.assertEqual(action.get_errored_counter(), 0)

    def test_get_as_dict(self):
        action = create_async_action(
            ProductOfferBatchSubmitAction,
            application=self.app,
            merchant=self.merchant,
            ids=self.offers_ids,
        )

        result = action.get_as_dict()

        self.assertEqual(
            result,
            {
                "id": action.id,
                "status": action.get_status(),
                "created_on": ANY,
                "action_name": action.action_name,
                "lock_action": True,
                "ended_on": None,
                "total_offers": len(self.offers_ids),
                "modified_offers": action.get_modified_counter(),
                "ignored_offers": action.get_ignored_counter(),
                "errored_offers": action.get_errored_counter(),
                "result": {},
            },
        )

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @override_settings(CELERY_TASK_EAGER_PROPAGATES_EXCEPTIONS=True)
    def test_run_action(self):
        action = ProductOfferBatchSubmitAction(
            application_id=self.app.id, merchant_id=self.merchant.id
        )

        result = action.run_action(self.offers_ids, dry_run=False)

        self.assertEqual(result.id, action.id)

        for offer in self.offers:
            offer.refresh_from_db()
            self.assertEqual(offer.status, PRODUCT_OFFER_STATUS_PENDING)

    def test_launch_task(self):
        action = create_async_action(
            ProductOfferBatchSubmitAction,
            application=self.app,
            merchant=self.merchant,
            ids=self.offers_ids,
        )

        result = action.launch_task(
            "submit_if_valid",
            LOW_PRIORITY_QUEUE,
            self.app.id,
            self.merchant.id,
            method_kwargs={"ignore_perms": True},
        )

        self.assertEqual(
            result,
            {
                "id": action.id,
                "status": action.get_status(),
                "created_on": ANY,
                "action_name": action.action_name,
                "lock_action": True,
                "ended_on": ANY,
                "total_offers": len(self.offers_ids),
                "modified_offers": len(self.offers_ids),
                "ignored_offers": 0,
                "errored_offers": 0,
                "result": {},
            },
        )

        for offer in self.offers:
            offer.refresh_from_db()
            self.assertEqual(offer.status, PRODUCT_OFFER_STATUS_PENDING)

    def test_fetch_offer_on_other_application_raise(self):
        action = ProductOfferBatchActivateAction(
            application_id=self.app.id, merchant_id=self.merchant.id
        )

        with self.assertRaises(Exception) as exc_info:
            action.fetch(ProductOfferFactory().id, self.app.id, self.merchant.id)

        self.assertEqual(
            exc_info.exception.args[0],
            "Offer's application does not match task's application",
        )

    def test_fetch_offer_on_other_merchant_raise(self):
        offer = ProductOfferFactory.create_for_merchant(merchant=self.merchant)
        merchant_2 = MerchantFactory.create_for_application(application=self.app)

        action = ProductOfferBatchActivateAction(
            application_id=self.app.id,
            merchant_id=merchant_2.id,
        )

        with self.assertRaises(Exception) as exc_info:
            action.fetch(offer.id, self.app.id, merchant_2.id)

        self.assertEqual(
            exc_info.exception.args[0],
            "Offer's merchant does not match task's merchant",
        )


class ProductOfferBatchTrashActionTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.app = ApplicationFactory(id=1)
        self.merchant = MerchantFactory.create_for_application(application=self.app)
        self.offers = [
            ProductOfferFactory.create_for_merchant(self.merchant) for _ in range(5)
        ]
        self.offers_ids = [offer.id for offer in self.offers]

    def tearDown(self):
        _, actions = ActionManager.list(DEFAULT_CURSOR)
        for action in actions:
            if action.actions.lock.is_locked():
                action.actions.lock.unlock()
            action.actions.delete()

    def test_initialize(self):
        action = ProductOfferBatchTrashAction(
            application_id=self.app.id, merchant_id=self.merchant.id
        )

        action.initialize(self.offers_ids)

        self.assertEqual(action.get_total_ids(), len(self.offers_ids))
        self.assertEqual(action.get_modified_counter(), 0)
        self.assertEqual(action.get_ignored_counter(), 0)
        self.assertEqual(action.get_errored_counter(), 0)

    def test_get_as_dict(self):
        action = create_async_action(
            ProductOfferBatchTrashAction,
            application=self.app,
            merchant=self.merchant,
            ids=self.offers_ids,
        )

        result = action.get_as_dict()

        self.assertEqual(
            result,
            {
                "id": action.id,
                "status": action.get_status(),
                "created_on": ANY,
                "action_name": action.action_name,
                "lock_action": True,
                "ended_on": None,
                "total_offers": len(self.offers_ids),
                "modified_offers": action.get_modified_counter(),
                "ignored_offers": action.get_ignored_counter(),
                "errored_offers": action.get_errored_counter(),
                "result": {},
            },
        )

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @override_settings(CELERY_TASK_EAGER_PROPAGATES_EXCEPTIONS=True)
    def test_run_action(self):
        action = ProductOfferBatchTrashAction(
            application_id=self.app.id, merchant_id=self.merchant.id
        )

        result = action.run_action(self.offers_ids, dry_run=False)

        self.assertEqual(result.id, action.id)

        for offer in self.offers:
            offer.refresh_from_db()
            self.assertEqual(offer.status, PRODUCT_OFFER_STATUS_TRASHED)

    def test_launch_task(self):
        action = create_async_action(
            ProductOfferBatchTrashAction,
            application=self.app,
            merchant=self.merchant,
            ids=self.offers_ids,
        )

        result = action.launch_task(
            "trash",
            LOW_PRIORITY_QUEUE,
            self.app.id,
            self.merchant.id,
            is_workflow_transition=True,
        )

        self.assertEqual(
            result,
            {
                "id": action.id,
                "status": action.get_status(),
                "created_on": ANY,
                "action_name": action.action_name,
                "lock_action": True,
                "ended_on": ANY,
                "total_offers": len(self.offers_ids),
                "modified_offers": len(self.offers_ids),
                "ignored_offers": 0,
                "errored_offers": 0,
                "result": {},
            },
        )

        for offer in self.offers:
            offer.refresh_from_db()
            self.assertEqual(offer.status, PRODUCT_OFFER_STATUS_TRASHED)

    def test_fetch_offer_on_other_application_raise(self):
        action = ProductOfferBatchActivateAction(
            application_id=self.app.id, merchant_id=self.merchant.id
        )

        with self.assertRaises(Exception) as exc_info:
            action.fetch(ProductOfferFactory().id, self.app.id, self.merchant.id)

        self.assertEqual(
            exc_info.exception.args[0],
            "Offer's application does not match task's application",
        )

    def test_fetch_offer_on_other_merchant_raise(self):
        offer = ProductOfferFactory.create_for_merchant(merchant=self.merchant)
        merchant_2 = MerchantFactory.create_for_application(application=self.app)

        action = ProductOfferBatchActivateAction(
            application_id=self.app.id,
            merchant_id=merchant_2.id,
        )

        with self.assertRaises(Exception) as exc_info:
            action.fetch(offer.id, self.app.id, merchant_2.id)

        self.assertEqual(
            exc_info.exception.args[0],
            "Offer's merchant does not match task's merchant",
        )


class ProductOfferBatchUntrashActionTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.app = ApplicationFactory(id=1)
        self.merchant = MerchantFactory.create_for_application(application=self.app)
        self.offers = [
            ProductOfferFactory.create_for_merchant(
                self.merchant, status=PRODUCT_OFFER_STATUS_TRASHED
            )
            for _ in range(5)
        ]
        self.offers_ids = [offer.id for offer in self.offers]

    def tearDown(self):
        _, actions = ActionManager.list(DEFAULT_CURSOR)
        for action in actions:
            if action.actions.lock.is_locked():
                action.actions.lock.unlock()
            action.actions.delete()

    def test_initialize(self):
        action = ProductOfferBatchUntrashAction(
            application_id=self.app.id, merchant_id=self.merchant.id
        )

        action.initialize(self.offers_ids)

        self.assertEqual(action.get_total_ids(), len(self.offers_ids))
        self.assertEqual(action.get_modified_counter(), 0)
        self.assertEqual(action.get_ignored_counter(), 0)
        self.assertEqual(action.get_errored_counter(), 0)

    def test_get_as_dict(self):
        action = create_async_action(
            ProductOfferBatchUntrashAction,
            application=self.app,
            merchant=self.merchant,
            ids=self.offers_ids,
        )

        result = action.get_as_dict()

        self.assertEqual(
            result,
            {
                "id": action.id,
                "status": action.get_status(),
                "created_on": ANY,
                "action_name": action.action_name,
                "lock_action": True,
                "ended_on": None,
                "total_offers": len(self.offers_ids),
                "modified_offers": action.get_modified_counter(),
                "ignored_offers": action.get_ignored_counter(),
                "errored_offers": action.get_errored_counter(),
                "result": {},
            },
        )

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @override_settings(CELERY_TASK_EAGER_PROPAGATES_EXCEPTIONS=True)
    def test_run_action(self):
        action = ProductOfferBatchUntrashAction(
            application_id=self.app.id, merchant_id=self.merchant.id
        )

        result = action.run_action(self.offers_ids, dry_run=False)

        self.assertEqual(result.id, action.id)

        for offer in self.offers:
            offer.refresh_from_db()
            self.assertEqual(offer.status, PRODUCT_OFFER_STATUS_DRAFT)

    def test_launch_task(self):
        action = create_async_action(
            ProductOfferBatchUntrashAction,
            application=self.app,
            merchant=self.merchant,
            ids=self.offers_ids,
        )

        result = action.launch_task(
            "untrash",
            LOW_PRIORITY_QUEUE,
            self.app.id,
            self.merchant.id,
            is_workflow_transition=True,
        )

        self.assertEqual(
            result,
            {
                "id": action.id,
                "status": action.get_status(),
                "created_on": ANY,
                "action_name": action.action_name,
                "lock_action": True,
                "ended_on": ANY,
                "total_offers": len(self.offers_ids),
                "modified_offers": len(self.offers_ids),
                "ignored_offers": 0,
                "errored_offers": 0,
                "result": {},
            },
        )

        for offer in self.offers:
            offer.refresh_from_db()
            self.assertEqual(offer.status, PRODUCT_OFFER_STATUS_DRAFT)

    def test_fetch_offer_on_other_application_raise(self):
        action = ProductOfferBatchActivateAction(
            application_id=self.app.id, merchant_id=self.merchant.id
        )

        with self.assertRaises(Exception) as exc_info:
            action.fetch(ProductOfferFactory().id, self.app.id, self.merchant.id)

        self.assertEqual(
            exc_info.exception.args[0],
            "Offer's application does not match task's application",
        )

    def test_fetch_offer_on_other_merchant_raise(self):
        offer = ProductOfferFactory.create_for_merchant(merchant=self.merchant)
        merchant_2 = MerchantFactory.create_for_application(application=self.app)

        action = ProductOfferBatchActivateAction(
            application_id=self.app.id,
            merchant_id=merchant_2.id,
        )

        with self.assertRaises(Exception) as exc_info:
            action.fetch(offer.id, self.app.id, merchant_2.id)

        self.assertEqual(
            exc_info.exception.args[0],
            "Offer's merchant does not match task's merchant",
        )


class ProductOfferBatchApplyDefaultTaxSettingsActionTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.app = ApplicationFactory(id=1, country__code="BR")
        self.merchant = MerchantFactory.create_for_application(application=self.app)
        self.offers = [
            ProductOfferFactory.create_for_merchant(
                self.merchant,
                shipped_from_eu_vat_zone=False,
                shipped_from_region="somewhere",
                sku=f"sku{i + 1}",
            )
            for i in range(5)
        ]
        self.tax = TaxRateAssignmentFactory(application=self.app, country__code="FR")
        self.merchant.tax_settings.enabled_tax_zones = [self.tax.tax_zone.zone_key]
        self.merchant.tax_settings.default_shipped_from_country = (
            self.tax.tax_zone.country
        )
        self.merchant.tax_settings.default_shipped_from_eu_vat_zone = True
        self.merchant.tax_settings.default_shipped_from_region = "bla"
        self.merchant.tax_settings.default_offer_tax_rates = [
            {
                "zone_key": self.tax.tax_zone.zone_key,
                "rate_key": self.tax.tax_rate.rate_key,
            }
        ]
        self.merchant.tax_settings.save()
        self.offers_ids = [offer.id for offer in self.offers]

    def tearDown(self):
        _, actions = ActionManager.list(DEFAULT_CURSOR)
        for action in actions:
            if action.actions.lock.is_locked():
                action.actions.lock.unlock()
            action.actions.delete()

    def test_initialize(self):
        action = ProductOfferBatchApplyDefaultTaxSettingsAction(
            application_id=self.app.id, merchant_id=self.merchant.id
        )

        action.initialize(self.offers_ids)

        self.assertEqual(action.get_total_ids(), len(self.offers_ids))
        self.assertEqual(action.get_modified_counter(), 0)
        self.assertEqual(action.get_ignored_counter(), 0)
        self.assertEqual(action.get_errored_counter(), 0)

    def test_get_as_dict(self):
        action = create_async_action(
            ProductOfferBatchApplyDefaultTaxSettingsAction,
            application=self.app,
            merchant=self.merchant,
            ids=self.offers_ids,
        )

        result = action.get_as_dict()

        self.assertEqual(
            result,
            {
                "id": action.id,
                "status": action.get_status(),
                "created_on": ANY,
                "action_name": action.action_name,
                "lock_action": True,
                "ended_on": None,
                "total_offers": len(self.offers_ids),
                "modified_offers": action.get_modified_counter(),
                "ignored_offers": action.get_ignored_counter(),
                "errored_offers": action.get_errored_counter(),
                "result": {},
            },
        )

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @override_settings(CELERY_TASK_EAGER_PROPAGATES_EXCEPTIONS=True)
    def test_run_action(self):
        action = ProductOfferBatchApplyDefaultTaxSettingsAction(
            application_id=self.app.id, merchant_id=self.merchant.id
        )

        result = action.run_action(self.offers_ids, dry_run=False)

        self.assertEqual(result.id, action.id)

        for offer in self.offers:
            offer.refresh_from_db()
            self.assertEqual(offer.shipped_from_country.code, "FR")
            self.assertTrue(offer.shipped_from_eu_vat_zone)
            self.assertEqual(offer.shipped_from_region, "bla")
            self.assertEqual(
                offer.tax_rates,
                [
                    {
                        "zone_key": self.tax.tax_zone.zone_key,
                        "rate_key": self.tax.tax_rate.rate_key,
                    }
                ],
            )

    def test_launch_task(self):
        action = create_async_action(
            ProductOfferBatchApplyDefaultTaxSettingsAction,
            application=self.app,
            merchant=self.merchant,
            ids=self.offers_ids,
        )

        result = action.launch_task(
            "apply_default_tax_data",
            LOW_PRIORITY_QUEUE,
            self.app.id,
            self.merchant.id,
        )

        self.assertEqual(
            result,
            {
                "id": action.id,
                "status": action.get_status(),
                "created_on": ANY,
                "action_name": action.action_name,
                "lock_action": True,
                "ended_on": ANY,
                "total_offers": len(self.offers_ids),
                "modified_offers": len(self.offers_ids),
                "ignored_offers": 0,
                "errored_offers": 0,
                "result": {},
            },
        )

        for offer in self.offers:
            offer.refresh_from_db()
            self.assertEqual(offer.shipped_from_country.code, "FR")
            self.assertTrue(offer.shipped_from_eu_vat_zone)
            self.assertEqual(offer.shipped_from_region, "bla")
            self.assertEqual(
                offer.tax_rates,
                [
                    {
                        "zone_key": self.tax.tax_zone.zone_key,
                        "rate_key": self.tax.tax_rate.rate_key,
                    }
                ],
            )

    def test_fetch_offer_on_other_application_raise(self):
        action = ProductOfferBatchActivateAction(
            application_id=self.app.id, merchant_id=self.merchant.id
        )

        with self.assertRaises(Exception) as exc_info:
            action.fetch(ProductOfferFactory().id, self.app.id, self.merchant.id)

        self.assertEqual(
            exc_info.exception.args[0],
            "Offer's application does not match task's application",
        )

    def test_fetch_offer_on_other_merchant_raise(self):
        offer = ProductOfferFactory.create_for_merchant(merchant=self.merchant)
        merchant_2 = MerchantFactory.create_for_application(application=self.app)

        action = ProductOfferBatchActivateAction(
            application_id=self.app.id,
            merchant_id=merchant_2.id,
        )

        with self.assertRaises(Exception) as exc_info:
            action.fetch(offer.id, self.app.id, merchant_2.id)

        self.assertEqual(
            exc_info.exception.args[0],
            "Offer's merchant does not match task's merchant",
        )


class ProductOfferBatchRemoveAllManuallyManagedFieldsActionTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.app = ApplicationFactory(id=1)
        self.merchant = MerchantFactory.create_for_application(application=self.app)
        self.offers = [
            ProductOfferFactory.create_for_merchant(
                self.merchant, manually_managed_fields=["foo", "bar", "test"]
            )
            for _ in range(5)
        ]
        self.offers_ids = [offer.id for offer in self.offers]

    def tearDown(self):
        _, actions = ActionManager.list(DEFAULT_CURSOR)
        for action in actions:
            if action.actions.lock.is_locked():
                action.actions.lock.unlock()
            action.actions.delete()

    def test_initialize(self):
        action = ProductOfferBatchRemoveAllManuallyManagedFieldsAction(
            application_id=self.app.id, merchant_id=self.merchant.id
        )

        action.initialize(self.offers_ids)

        self.assertEqual(action.get_total_ids(), len(self.offers_ids))
        self.assertEqual(action.get_modified_counter(), 0)
        self.assertEqual(action.get_ignored_counter(), 0)
        self.assertEqual(action.get_errored_counter(), 0)

    def test_get_as_dict(self):
        action = create_async_action(
            ProductOfferBatchRemoveAllManuallyManagedFieldsAction,
            application=self.app,
            merchant=self.merchant,
            ids=self.offers_ids,
        )

        result = action.get_as_dict()

        self.assertEqual(
            result,
            {
                "id": action.id,
                "status": action.get_status(),
                "created_on": ANY,
                "action_name": action.action_name,
                "lock_action": True,
                "ended_on": None,
                "total_offers": len(self.offers_ids),
                "modified_offers": action.get_modified_counter(),
                "ignored_offers": action.get_ignored_counter(),
                "errored_offers": action.get_errored_counter(),
                "result": {},
            },
        )

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @override_settings(CELERY_TASK_EAGER_PROPAGATES_EXCEPTIONS=True)
    def test_run_action(self):
        action = ProductOfferBatchRemoveAllManuallyManagedFieldsAction(
            application_id=self.app.id, merchant_id=self.merchant.id
        )

        result = action.run_action(self.offers_ids, dry_run=False)

        self.assertEqual(result.id, action.id)

        for offer in self.offers:
            offer.refresh_from_db()
            self.assertEqual(offer.manually_managed_fields, [])

    def test_launch_task(self):
        action = create_async_action(
            ProductOfferBatchRemoveAllManuallyManagedFieldsAction,
            application=self.app,
            merchant=self.merchant,
            ids=self.offers_ids,
        )

        result = action.launch_task(
            "remove_all_manually_managed_fields",
            LOW_PRIORITY_QUEUE,
            self.app.id,
            self.merchant.id,
        )

        self.assertEqual(
            result,
            {
                "id": action.id,
                "status": action.get_status(),
                "created_on": ANY,
                "action_name": action.action_name,
                "lock_action": True,
                "ended_on": ANY,
                "total_offers": len(self.offers_ids),
                "modified_offers": len(self.offers_ids),
                "ignored_offers": 0,
                "errored_offers": 0,
                "result": {},
            },
        )

        for offer in self.offers:
            offer.refresh_from_db()
            self.assertEqual(offer.manually_managed_fields, [])

    def test_fetch_offer_on_other_application_raise(self):
        action = ProductOfferBatchActivateAction(
            application_id=self.app.id, merchant_id=self.merchant.id
        )

        with self.assertRaises(Exception) as exc_info:
            action.fetch(ProductOfferFactory().id, self.app.id, self.merchant.id)

        self.assertEqual(
            exc_info.exception.args[0],
            "Offer's application does not match task's application",
        )

    def test_fetch_offer_on_other_merchant_raise(self):
        offer = ProductOfferFactory.create_for_merchant(merchant=self.merchant)
        merchant_2 = MerchantFactory.create_for_application(application=self.app)

        action = ProductOfferBatchActivateAction(
            application_id=self.app.id,
            merchant_id=merchant_2.id,
        )

        with self.assertRaises(Exception) as exc_info:
            action.fetch(offer.id, self.app.id, merchant_2.id)

        self.assertEqual(
            exc_info.exception.args[0],
            "Offer's merchant does not match task's merchant",
        )


class ProductOfferBatchUpdateStockFieldActionTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.app = ApplicationFactory(id=1)
        self.merchant = MerchantFactory.create_for_application(application=self.app)
        self.offers = [
            ProductOfferFactory.create_for_merchant(self.merchant, stock=5)
            for _ in range(5)
        ]
        self.offers_ids = [offer.id for offer in self.offers]

    def tearDown(self):
        _, actions = ActionManager.list(DEFAULT_CURSOR)
        for action in actions:
            if action.actions.lock.is_locked():
                action.actions.lock.unlock()
            action.actions.delete()

    def test_initialize(self):
        action = ProductOfferBatchUpdateStockFieldAction(
            application_id=self.app.id, merchant_id=self.merchant.id
        )

        action.initialize(self.offers_ids)

        self.assertEqual(action.get_total_ids(), len(self.offers_ids))
        self.assertEqual(action.get_modified_counter(), 0)
        self.assertEqual(action.get_ignored_counter(), 0)
        self.assertEqual(action.get_errored_counter(), 0)

    def test_get_as_dict(self):
        action = create_async_action(
            ProductOfferBatchUpdateStockFieldAction,
            application=self.app,
            merchant=self.merchant,
            ids=self.offers_ids,
            new_stock=20,
        )

        result = action.get_as_dict()

        self.assertEqual(
            result,
            {
                "id": action.id,
                "status": action.get_status(),
                "created_on": ANY,
                "action_name": action.action_name,
                "new_stock": 20,
                "lock_action": True,
                "ended_on": None,
                "total_offers": len(self.offers_ids),
                "modified_offers": action.get_modified_counter(),
                "ignored_offers": action.get_ignored_counter(),
                "errored_offers": action.get_errored_counter(),
                "result": {},
            },
        )

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @override_settings(CELERY_TASK_EAGER_PROPAGATES_EXCEPTIONS=True)
    def test_run_action(self):
        action = ProductOfferBatchUpdateStockFieldAction(
            application_id=self.app.id, merchant_id=self.merchant.id
        )

        result = action.run_action(self.offers_ids, dry_run=False, new_stock=20)

        self.assertEqual(result.id, action.id)

        for offer in self.offers:
            offer.refresh_from_db()
            self.assertEqual(offer.status, PRODUCT_OFFER_STATUS_DRAFT)

    def test_launch_task(self):
        action = create_async_action(
            ProductOfferBatchUpdateStockFieldAction,
            application=self.app,
            merchant=self.merchant,
            ids=self.offers_ids,
        )

        result = action.launch_task(
            "update_stock_field",
            LOW_PRIORITY_QUEUE,
            self.app.id,
            self.merchant.id,
            method_kwargs={"new_stock": 20},
        )

        self.assertEqual(
            result,
            {
                "id": action.id,
                "status": action.get_status(),
                "created_on": ANY,
                "action_name": action.action_name,
                "lock_action": True,
                "ended_on": ANY,
                "total_offers": len(self.offers_ids),
                "modified_offers": len(self.offers_ids),
                "ignored_offers": 0,
                "errored_offers": 0,
                "result": {},
            },
        )

        for offer in self.offers:
            offer.refresh_from_db()
            self.assertEqual(offer.status, PRODUCT_OFFER_STATUS_DRAFT)

    def test_fetch_offer_on_other_application_raise(self):
        action = ProductOfferBatchActivateAction(
            application_id=self.app.id, merchant_id=self.merchant.id
        )

        with self.assertRaises(Exception) as exc_info:
            action.fetch(ProductOfferFactory().id, self.app.id, self.merchant.id)

        self.assertEqual(
            exc_info.exception.args[0],
            "Offer's application does not match task's application",
        )

    def test_fetch_offer_on_other_merchant_raise(self):
        offer = ProductOfferFactory.create_for_merchant(merchant=self.merchant)
        merchant_2 = MerchantFactory.create_for_application(application=self.app)

        action = ProductOfferBatchActivateAction(
            application_id=self.app.id,
            merchant_id=merchant_2.id,
        )

        with self.assertRaises(Exception) as exc_info:
            action.fetch(offer.id, self.app.id, merchant_2.id)

        self.assertEqual(
            exc_info.exception.args[0],
            "Offer's merchant does not match task's merchant",
        )
