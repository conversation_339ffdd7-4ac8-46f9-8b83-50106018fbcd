import datetime
from typing import Any, List, Optional, Union

from apps.tasking.exceptions import ActionDoesNotExist
from apps.tasking.models.drf.base import BaseAsyncAction


class AbstractBaseMerchantCatalogAction(BaseAsyncAction):
    def initialize(
        self,
        offers_ids: List[int],
        variations_ids: List[int],
        not_crawled_since: Optional[datetime.datetime] = None,
        lock_action: Optional[bool] = True,
    ):
        self.actions.client.create_pipeline(allow_uninitialized=True)
        super().initialize(
            extra_metas={
                "total_offers": len(offers_ids),
                "total_variations": len(variations_ids),
                "not_crawled_since": not_crawled_since,
            },
            lock_action=lock_action,
        )
        self.increment_done_offers_counter(0)
        self.increment_done_variations_counter(0)
        self.actions.client.execute_pipeline()

        if offers_ids:
            self.save_offer_ids(offers_ids)
        if variations_ids:
            self.save_variation_ids(variations_ids)

    def get_number_of_offers(self) -> Union[int, None]:
        """To keep Tastypie compatibility"""
        raw_value = self.get_meta("total_offers")
        return int(raw_value) if raw_value else None

    def get_number_of_variations(self) -> Union[int, None]:
        """To keep Tastypie compatibility"""
        raw_value = self.get_meta("total_variations")
        return int(raw_value) if raw_value else None

    def get_not_crawled_since(self) -> Union[None, datetime.datetime]:
        """To keep Tastypie compatibility"""
        return self.get_meta("not_crawled_since")

    def get_done_offers_counter(self) -> Union[int, None]:
        raw_value = self.actions.get("done_offers")
        return int(raw_value) if raw_value else None

    def increment_done_offers_counter(self, value: int) -> Optional[Any]:
        return self.actions.increment_key("done_offers", value)

    def get_done_variations_counter(self) -> Union[int, None]:
        raw_value = self.actions.get("done_variations")
        return int(raw_value) if raw_value else None

    def increment_done_variations_counter(self, value: int) -> Optional[Any]:
        return self.actions.increment_key("done_variations", value)

    def save_offer_ids(self, ids: List[int]) -> int:
        return self.actions.chunk_add_to_set("offer_ids", ids)

    def get_offer_ids(self, count: Optional[int] = 1) -> List[int]:
        return list(map(int, self.actions.pop_from_set("offer_ids", count)))

    def save_variation_ids(self, ids: List[int]) -> int:
        return self.actions.chunk_add_to_set("variation_ids", ids)

    def get_variation_ids(self, count: Optional[int] = 1) -> List[int]:
        return list(map(int, self.actions.pop_from_set("variation_ids", count)))

    def get_as_dict(self) -> dict:
        self.actions.client.create_pipeline()
        self.get_metas()
        self.get_result()
        self.get_done_offers_counter()
        self.get_done_variations_counter()
        (
            metas,
            result,  # from parent
            done_offers,
            done_variations,
        ) = self.actions.client.execute_pipeline()

        if not metas:
            raise ActionDoesNotExist(self.action_name, self.id)

        metas = self.actions.deserialize(metas)
        total_offers = metas.pop("total_offers")
        total_variations = metas.pop("total_variations")
        ended_on = metas.pop("ended_on", None)
        result = self.actions.deserialize(result)
        ret = {
            "total_offers": (int(total_offers) if total_offers is not None else 0),
            "done_offers": (int(done_offers) if done_offers is not None else 0),
            "total_variations": (
                int(total_variations) if total_variations is not None else 0
            ),
            "done_variations": (
                int(done_variations) if done_variations is not None else 0
            ),
            "id": self.id,
            "ended_on": ended_on,
            "result": result,
        }
        ret.update(metas)
        return ret


class MerchantCatalogDeactivateAction(AbstractBaseMerchantCatalogAction):
    action_name = "merchant-batch-deactivate"
    task_path = "apps.stores.tasks.merchant_batch_deactivate"


class MerchantCatalogTrashAction(AbstractBaseMerchantCatalogAction):
    action_name = "merchant-batch-trash"
    task_path = "apps.stores.tasks.merchant_batch_trash"
