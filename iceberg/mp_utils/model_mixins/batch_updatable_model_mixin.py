# -*- coding: utf-8 -*-
""" BatchUpdatableModelMixin for Django models """

import logging
from copy import copy
from decimal import Decimal

from django.db.models.query import QuerySet
from django.utils.translation import gettext_lazy as _

logger = logging.getLogger(__name__)

DEFAULT_ALLOWED_MODES = ["assign"]


class BatchUpdateBaseException(Exception):
    pass


class BatchUpdateNotAllowedException(BatchUpdateBaseException):
    """Raised when attempting to batch-update a non batch-updatable field"""

    def __init__(self, field_name):
        message = _("Field '%s' is not batch updatable") % field_name
        super(BatchUpdateNotAllowedException, self).__init__(message)


class BatchCallNotAllowedException(BatchUpdateBaseException):
    """Raised when attempting to batch-call a non batch-callable function"""

    def __init__(self, function_name, allowed_functions):
        message = _("Function '%s' is not batch callable. Allowed functions are %s") % (
            function_name,
            ", ".join(allowed_functions.keys()),
        )
        super(BatchCallNotAllowedException, self).__init__(message)


class BatchCallWrongArgumentsException(BatchUpdateBaseException):
    """Raised when attempting to batch-call a function with wrong arguments"""

    def __init__(self, function_name, required_args, optional_args=None):
        message = _("Wrong arguments for '%s'. Required args are %s.") % (
            function_name,
            ", ".join(required_args),
        )
        super(BatchCallWrongArgumentsException, self).__init__(message)


class BatchUpdateModeNotAllowedException(BatchUpdateBaseException):
    """Raised when attempting to batch-update a field in an unavailable mode"""

    def __init__(self, field_name, mode, allowed_modes):
        message = _(
            "Field '%s' is not batch updatable with mode '%s'. Allowed modes are %s"
        ) % (field_name, mode, allowed_modes)
        super(BatchUpdateModeNotAllowedException, self).__init__(message)


class BatchUpdateModeUnknownException(BatchUpdateBaseException):
    """Raised when attempting to batch-update a field in an unknown mode"""

    def __init__(self, mode, known_modes):
        message = _("Unknown batch update mode '%s'. Known modes are %s") % (
            mode,
            known_modes,
        )
        super(BatchUpdateModeUnknownException, self).__init__(message)


class BatchCallUnknownFunctionException(BatchUpdateBaseException):
    """Raised when attempting to batch-call in an unknown function_name"""

    def __init__(self, function_name):
        message = _("Unknown function '%s'") % (function_name)
        super(BatchCallUnknownFunctionException, self).__init__(message)


class BatchUpdateWrongTypeValueForModeException(BatchUpdateBaseException):
    """
    Raised when attempting to batch-update a field with a value type that is not
    handled by mode
    """

    def __init__(self, mode, unhandled_type, handled_types):
        message = _("Unhandled type %s for mode '%s'. Handled types are %s") % (
            unhandled_type,
            mode,
            handled_types,
        )
        super(BatchUpdateWrongTypeValueForModeException, self).__init__(message)


class BatchUpdateFiltersOrQuerySetRequiredException(BatchUpdateBaseException):
    """Raised when attempting to batch-update a field with no filters nor queryset"""

    def __init__(self, message=None):
        message = message or _(
            "Some filters or queryset are required for batch-update."
        )
        super(BatchUpdateFiltersOrQuerySetRequiredException, self).__init__(message)


class BatchUpdateQuerySetNotMatchingException(BatchUpdateBaseException):
    """
    Raised when attempting to batch-update a field of a queryset that doesnt match the
    model
    """

    def __init__(self, queryset_model, expected_model):
        message = _("Given queryset is on model %s, not on %s.") % (
            queryset_model,
            expected_model,
        )
        super(BatchUpdateQuerySetNotMatchingException, self).__init__(message)


class BatchUpdateWrongValue(BatchUpdateBaseException):
    """Raised when attempting to batch-update a field with a bad value"""

    def __init__(self, value, field_name):
        message = _("Wrong value %s for %s.") % (value, field_name)
        super(BatchUpdateWrongValue, self).__init__(message)


class BatchUpdatableModelMixin:
    """
    Mixin allowing batch updates of some model fields.
    You must define the batch-updatable fields in the BATCH_UPDATABLE_FIELDS dict,
    with allowed modes as value.
    Ex. : BATCH_UPDATABLE_FIELDS = {"stock":["assign"]} will allow the batch-assignment
    of the field 'stock'
          BATCH_UPDATABLE_FIELDS = {"keywords":["add"]} will allow the addition of
          value to the field 'keywords'

    At the end of any batch update, the hook _post_batch_update_hook is called,
    allowing you to do some specific actions (typically clearing cache of updated
    objects)
    Ex:
    def _post_batch_update_hook(cls, field_name, value, filters_or_queryset, mode,
                                number_updated, modified_ids):
        cls.clear_cache(modified_ids)

    You can also define some per field hooks that should be named :
    "_post_batch_update_hook_[field_name]".
    Ex:
    def _post_batch_update_hook_keywords(cls, field_name, value, filters_or_queryset,
                                         mode, number_updated, modified_ids):
        for updated_id in modified_ids:
            # triggering a signal called keywords_updated for each updated_id
            keywords_updated.send(object_id=updated_id)
    """

    MODE_CHOICES = [
        "assign",
        "add",
        "substract",
        "add_unique",
        "remove",
        "remove_all",
        "append",
        "prepend",
        "replace",
    ]

    MODE_TO_HANDLED_TYPES = {
        "assign": [],  # works for every type
        "add": [int, Decimal, list, set],
        "substract": [int, Decimal],
        "add_unique": [list],
        "remove": [list, set],
        "remove_all": [list],
        "append": [bytes, str],
        "prepend": [bytes, str],
        "replace": [list, tuple],  # list or tuple of 2 items
    }

    BATCH_UPDATABLE_FIELDS = {}

    # is the Model.objects.update function allowed (for 'assign' mode) ?
    BATCH_UPDATABLE_ALLOW_ATOMIC_UPDATE = True

    # should we do a queryset.only(...) or is the full obj needed somewhere ?
    BATCH_UPDATE_QUERYSET_RETRIEVE_FULL_OBJECT = False

    # which extra fields need to be retrieved (in addition to the
    # updated field) ?
    BATCH_UPDATE_QUERYSET_EXTRA_FIELDS_TO_RETRIEVE = []

    BATCH_CALLABLE_FUNCTIONS = {}

    @classmethod
    def _update_obj_field(cls, obj, field_name, value, mode):
        """
        Updates the field 'field_name' of obj with 'value' depending on
        the 'mode'
        """
        current_value = getattr(obj, field_name)
        original_value = copy(current_value)

        if mode == "assign":
            new_value = value
        elif mode == "add":
            new_value = current_value + value
        elif mode == "substract":
            new_value = current_value - value
        elif mode == "add_unique":
            for val in value:
                if val not in current_value:
                    current_value.append(val)
            new_value = current_value
        elif mode == "remove":
            if current_value:
                for val in value:
                    if val in current_value:
                        current_value.remove(val)
            new_value = current_value
        elif mode == "remove_all":
            if current_value:
                while val in value:
                    if val in current_value:
                        current_value.remove(val)
            new_value = current_value
        elif mode == "append":
            if not current_value:
                new_value = value
            else:
                new_value = current_value + value
        elif mode == "prepend":
            if not current_value:
                new_value = value
            else:
                new_value = value + current_value
        elif mode == "replace":
            if current_value:
                new_value = current_value.replace(value[0], value[1])
            else:
                new_value = current_value

        # should not raise FieldDoesNotExist (checked before)
        field = cls._meta.get_field(field_name)

        field.run_validators(new_value)  # might raise Validation error

        setattr(obj, field_name, new_value)

        both_null = not new_value and not original_value
        has_changed = not both_null and new_value != original_value
        return has_changed

    def _save_obj_after_batch_updated_field(self, field_name, value, mode):
        """default save function after modification of obj during a batch update"""
        self.save(update_fields=[field_name])

    @classmethod
    def _batch_update_value(cls, field_name, value, filters_or_queryset, mode):
        """
        Add 'value' of field 'field_name' to existing value for all objects matching
        queryset
        """
        if isinstance(filters_or_queryset, QuerySet):
            queryset = filters_or_queryset
        else:
            queryset = cls.objects.filter(**filters_or_queryset)

        if not cls.BATCH_UPDATE_QUERYSET_RETRIEVE_FULL_OBJECT:
            fields_to_retrieve = [
                field_name
            ] + cls.BATCH_UPDATE_QUERYSET_EXTRA_FIELDS_TO_RETRIEVE
            logger.debug(
                "_batch_update_value fields_to_retrieve=%s" % fields_to_retrieve
            )

            # no need to retrieve all fields
            queryset = queryset.only(*fields_to_retrieve)

        if mode == "assign" and cls.BATCH_UPDATABLE_ALLOW_ATOMIC_UPDATE:
            # 'assign' mode can be done with .update()

            #  should not raise FieldDoesNotExist (checked before)
            field = cls._meta.get_field(field_name)

            # might raise Validation error
            field.run_validators(value)
            # TODO check choices (run_validators doesnt check choices...)
            modified_ids = list(
                queryset.exclude(**{field_name: value})
                .only("id")
                .values_list("id", flat=True)
            )
            number_updated = queryset.update(**{field_name: value})
            return number_updated, modified_ids
        else:
            # all other modes needs to loop
            updated_ids = []
            modified_ids = []
            object_ids = queryset.values_list("id", flat=True)
            for obj_id in object_ids.iterator():
                obj = cls.objects.get(id=obj_id)
                has_changed = cls._update_obj_field(obj, field_name, value, mode)
                if has_changed:
                    obj._save_obj_after_batch_updated_field(field_name, value, mode)
                    modified_ids.append(obj.id)
                updated_ids.append(obj.id)

            return len(updated_ids), modified_ids

    @classmethod
    def _pre_batch_update_hook(cls, field_name, value, filters_or_queryset, mode):
        """
        Global pre batch update hook. Result is transmitted to _post_batch_update_hook
        """
        return

    @classmethod
    def _pre_batch_call_hook(cls, function_name, function_params, filters_or_queryset):
        """
        Global pre batch call hook.
        """
        return

    @classmethod
    def _post_batch_update_hook(
        cls,
        field_name,
        value,
        filters_or_queryset,
        mode,
        number_updated,
        modified_ids,
        pre_hook_result,
    ):
        """
        Global post batch update hook.
        """
        return

    @classmethod
    def _post_batch_call_hook(
        cls,
        function_name,
        function_params,
        filters_or_queryset,
        number_updated,
        modified_ids,
        pre_hook_result,
    ):
        """
        Global post batch call hook.
        """
        return

    @classmethod
    def _validate_batch_update_arguments(
        cls, field_name, value, filters_or_queryset, mode
    ):
        """
        @summary: Validate received arguments
        @result: cleaned value
        """
        if field_name not in cls.BATCH_UPDATABLE_FIELDS:
            raise BatchUpdateNotAllowedException(field_name)

        if mode not in cls.MODE_CHOICES:
            raise BatchUpdateModeUnknownException(mode, cls.MODE_CHOICES)

        if isinstance(filters_or_queryset, QuerySet):
            if filters_or_queryset.query.model != cls:
                raise BatchUpdateQuerySetNotMatchingException(
                    filters_or_queryset.query.model, cls
                )
        elif not filters_or_queryset:
            raise BatchUpdateFiltersOrQuerySetRequiredException()

        if isinstance(cls.BATCH_UPDATABLE_FIELDS, dict):
            # dict BATCH_UPDATABLE_FIELDS may have 'mode' restriction in "mode"
            mode_restricted_to = cls.BATCH_UPDATABLE_FIELDS[field_name] or []
            if mode_restricted_to and mode not in mode_restricted_to:
                raise BatchUpdateModeNotAllowedException(
                    field_name, mode, mode_restricted_to
                )

        # check if field exists (might raise FieldDoesNotExist)
        field = cls._meta.get_field(field_name)

        can_retrieve_model = (
            field.is_relation and field.many_to_one and field.related_model
        )
        if can_retrieve_model and value is not None:
            try:
                # we expect the pk
                value = field.related_model.objects.get(pk=value)
            except field.related_model.DoesNotExist:
                raise BatchUpdateWrongValue(value, field_name)

        # done later : field.run_validators(value) ## might raise Validation error

        if (
            cls.MODE_TO_HANDLED_TYPES[mode]
            and type(value) not in cls.MODE_TO_HANDLED_TYPES[mode]
        ):
            raise BatchUpdateWrongTypeValueForModeException(
                mode, type(value), cls.MODE_TO_HANDLED_TYPES[mode]
            )

        if mode == "replace" and len(value) != 2:
            raise BatchUpdateBaseException(
                _("Batch update with mode 'replace' expects a value length of 2")
            )

        return value

    @classmethod
    def batch_update_field(cls, field_name, value, filters_or_queryset, mode=None):
        """
        Update 'field_name' with given 'value' to objects
        corresponding to 'filters_or_queryset'

        """
        mode = mode or "assign"

        value = cls._validate_batch_update_arguments(
            field_name, value, filters_or_queryset, mode
        )

        pre_hook_result = cls._pre_batch_update_hook(
            field_name, value, filters_or_queryset, mode
        )

        number_updated, modified_ids = cls._batch_update_value(
            field_name, value, filters_or_queryset, mode
        )

        # calling global post update hook
        cls._post_batch_update_hook(
            field_name,
            value,
            filters_or_queryset,
            mode,
            number_updated,
            modified_ids,
            pre_hook_result,
        )

        # calling field specific post update hook (if exists)
        post_hook_function_name = "_post_batch_update_hook_%s" % field_name
        specific_field_post_hook = getattr(cls, post_hook_function_name, None)
        if specific_field_post_hook:
            specific_field_post_hook(
                field_name,
                value,
                filters_or_queryset,
                mode,
                number_updated,
                modified_ids,
            )

        return number_updated, modified_ids

    @classmethod
    def _validate_batch_call_arguments(
        cls, function_name, function_params, filters_or_queryset
    ):
        """
        Validate received arguments for batch_call_function
        """
        if function_name not in cls.BATCH_CALLABLE_FUNCTIONS:
            raise BatchCallNotAllowedException(
                function_name, cls.BATCH_CALLABLE_FUNCTIONS
            )

        if isinstance(filters_or_queryset, QuerySet):
            if filters_or_queryset.query.model != cls:
                raise BatchUpdateQuerySetNotMatchingException(
                    filters_or_queryset.query.model, cls
                )
        elif not filters_or_queryset:
            raise BatchUpdateFiltersOrQuerySetRequiredException()

        if getattr(cls, function_name, None) is None:
            raise BatchCallUnknownFunctionException(function_name)

        if isinstance(cls.BATCH_CALLABLE_FUNCTIONS, dict):
            # dict BATCH_CALLABLE_FUNCTIONS
            required_args = cls.BATCH_CALLABLE_FUNCTIONS[function_name].get(
                "required_args", []
            )
            for required_arg in required_args:
                if required_arg not in function_params:
                    raise BatchCallWrongArgumentsException(function_name, required_args)
            not_empty_args = cls.BATCH_CALLABLE_FUNCTIONS[function_name].get(
                "not_empty_args", []
            )
            for not_empty_arg in not_empty_args:
                if not_empty_arg in function_params and function_params.get(
                    not_empty_arg
                ) in ["", None, []]:
                    raise BatchCallWrongArgumentsException(
                        function_name, not_empty_args
                    )

    @classmethod
    def _call_obj_function(cls, obj_id, function_name, function_params):
        """
        Calls the function_name 'function_params' on the object and returns
        True if obj has changed
        """
        obj = cls.objects.get(id=obj_id)
        function = getattr(obj, function_name)
        res = function(**function_params) if function_params else function()
        # if function returns None, considering obj has changed
        return res or (res is None)

    @classmethod
    def _batch_call_function(cls, function_name, function_params, filters_or_queryset):
        """
        Call 'function_name' with params 'function_params'
        for all objects matching queryset
        """
        if isinstance(filters_or_queryset, QuerySet):
            queryset = filters_or_queryset
        else:
            queryset = cls.objects.filter(**filters_or_queryset)

        updated_ids = []
        modified_ids = []

        object_ids = list(queryset.values_list("id", flat=True))
        for obj_id in object_ids:
            has_changed = cls._call_obj_function(obj_id, function_name, function_params)
            if has_changed:
                modified_ids.append(obj_id)
            updated_ids.append(obj_id)

        return len(updated_ids), modified_ids

    @classmethod
    def batch_call_function(cls, function_name, function_params, filters_or_queryset):
        """
        Call 'function_name' with given 'function_params' to
        objects corresponding to 'filters_or_queryset'
        """
        cls._validate_batch_call_arguments(
            function_name, function_params, filters_or_queryset
        )

        pre_hook_result = cls._pre_batch_call_hook(
            function_name, function_params, filters_or_queryset
        )

        number_updated, modified_ids = cls._batch_call_function(
            function_name, function_params, filters_or_queryset
        )

        # calling global post update hook
        cls._post_batch_call_hook(
            function_name,
            function_params,
            filters_or_queryset,
            number_updated,
            modified_ids,
            pre_hook_result,
        )

        # calling field specific post update hook (if exists)
        post_hook_function_name = "_post_batch_call_hook_%s" % function_name
        specific_field_post_hook = getattr(cls, post_hook_function_name, None)
        if specific_field_post_hook:
            specific_field_post_hook(
                function_name,
                function_params,
                filters_or_queryset,
                number_updated,
                modified_ids,
            )

        return number_updated, modified_ids


__all__ = (
    "BatchUpdateBaseException",
    "BatchUpdateNotAllowedException",
    "BatchUpdateModeNotAllowedException",
    "BatchUpdateModeUnknownException",
    "BatchUpdateWrongTypeValueForModeException",
    "BatchUpdateFiltersOrQuerySetRequiredException",
    "BatchUpdateQuerySetNotMatchingException",
    "BatchUpdatableModelMixin",
    "BatchCallNotAllowedException",
    "BatchCallUnknownFunctionException",
    "BatchCallWrongArgumentsException",
)
